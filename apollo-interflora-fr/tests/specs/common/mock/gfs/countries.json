[{"Id": 2, "Enabled": true, "CountryCode": "AD", "Name": "Andorra", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 376, "UnitId": 191, "CountryTimes": [{"Id": 1383, "CountryCode": "AD", "DayOfWeek": 1, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T18:00:00", "SameDayDeadline": "2024-02-29T12:00:00"}, {"Id": 1384, "CountryCode": "AD", "DayOfWeek": 2, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T18:00:00", "SameDayDeadline": "2024-02-29T12:00:00"}, {"Id": 1385, "CountryCode": "AD", "DayOfWeek": 3, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T18:00:00", "SameDayDeadline": "2024-02-29T12:00:00"}, {"Id": 1386, "CountryCode": "AD", "DayOfWeek": 4, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T18:00:00", "SameDayDeadline": "2024-02-29T12:00:00"}, {"Id": 1387, "CountryCode": "AD", "DayOfWeek": 5, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T18:00:00", "SameDayDeadline": "2024-02-29T12:00:00"}, {"Id": 1388, "CountryCode": "AD", "DayOfWeek": 6, "OpenTime": "2024-02-29T09:00:00", "CloseTime": "2024-02-29T16:00:00", "SameDayDeadline": "2024-02-29T11:00:00"}], "LastUpdate": "2024-10-22T08:03:13"}, {"Id": 3, "Enabled": true, "CountryCode": "AE", "Name": "United Arab Emirates", "TimeZone": 165, "CurrencyId": 7, "DialingCode": 971, "UnitId": 212, "CountryTimes": [{"Id": 1389, "CountryCode": "AE", "DayOfWeek": 1, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}, {"Id": 1390, "CountryCode": "AE", "DayOfWeek": 2, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}, {"Id": 1391, "CountryCode": "AE", "DayOfWeek": 3, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}, {"Id": 1392, "CountryCode": "AE", "DayOfWeek": 4, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}, {"Id": 1393, "CountryCode": "AE", "DayOfWeek": 0, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}, {"Id": 1394, "CountryCode": "AE", "DayOfWeek": 6, "OpenTime": "2022-09-16T09:00:00", "CloseTime": "2022-09-16T17:00:00", "SameDayDeadline": "2022-09-16T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 4, "Enabled": true, "CountryCode": "AF", "Name": "Afghanistan", "TimeZone": 175, "CurrencyId": 7, "DialingCode": 93, "UnitId": 212, "CountryTimes": [{"Id": 1395, "CountryCode": "AF", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1396, "CountryCode": "AF", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1397, "CountryCode": "AF", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1398, "CountryCode": "AF", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1399, "CountryCode": "AF", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1400, "CountryCode": "AF", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2024-10-22T08:01:40"}, {"Id": 5, "Enabled": true, "CountryCode": "AG", "Name": "Antigua & Barbuda", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 809, "UnitId": 213, "CountryTimes": [{"Id": 1401, "CountryCode": "AG", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1402, "CountryCode": "AG", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1403, "CountryCode": "AG", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1404, "CountryCode": "AG", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1405, "CountryCode": "AG", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1406, "CountryCode": "AG", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2024-10-22T08:04:50"}, {"Id": 6, "Enabled": false, "CountryCode": "AI", "Name": "<PERSON><PERSON><PERSON>", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 809, "UnitId": 213, "CountryTimes": [{"Id": 1407, "CountryCode": "AI", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1408, "CountryCode": "AI", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1409, "CountryCode": "AI", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1410, "CountryCode": "AI", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1411, "CountryCode": "AI", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1412, "CountryCode": "AI", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T17:46:25"}, {"Id": 7, "Enabled": true, "CountryCode": "AL", "Name": "Albania", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 355, "UnitId": 199, "CountryTimes": [{"Id": 1413, "CountryCode": "AL", "DayOfWeek": 1, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T18:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 1414, "CountryCode": "AL", "DayOfWeek": 2, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T18:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 1415, "CountryCode": "AL", "DayOfWeek": 3, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T18:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 1416, "CountryCode": "AL", "DayOfWeek": 4, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T18:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 1417, "CountryCode": "AL", "DayOfWeek": 5, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T18:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 1418, "CountryCode": "AL", "DayOfWeek": 6, "OpenTime": "2024-02-26T09:00:00", "CloseTime": "2024-02-26T16:00:00", "SameDayDeadline": "2024-02-26T12:00:00"}], "LastUpdate": "2024-10-22T08:02:07"}, {"Id": 8, "Enabled": true, "CountryCode": "AM", "Name": "Armenia", "TimeZone": 170, "CurrencyId": 7, "DialingCode": 374, "UnitId": 207, "CountryTimes": [{"Id": 1420, "CountryCode": "AM", "DayOfWeek": 1, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 1421, "CountryCode": "AM", "DayOfWeek": 2, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 1422, "CountryCode": "AM", "DayOfWeek": 3, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 1423, "CountryCode": "AM", "DayOfWeek": 4, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 1424, "CountryCode": "AM", "DayOfWeek": 5, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2802, "CountryCode": "AM", "DayOfWeek": 6, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T13:45:00"}, {"Id": 3798, "CountryCode": "AM", "DayOfWeek": 0, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T13:45:00"}], "LastUpdate": "2024-10-22T08:05:36"}, {"Id": 9, "Enabled": false, "CountryCode": "AN", "Name": "Netherlands Antilles", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 599, "UnitId": 203, "CountryTimes": [{"Id": 1425, "CountryCode": "AN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1426, "CountryCode": "AN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1427, "CountryCode": "AN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1428, "CountryCode": "AN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1429, "CountryCode": "AN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1430, "CountryCode": "AN", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T17:06:28"}, {"Id": 10, "Enabled": false, "CountryCode": "AO", "Name": "Angola", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 0, "UnitId": 214, "CountryTimes": [{"Id": 1431, "CountryCode": "AO", "DayOfWeek": 1, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T16:30:00", "SameDayDeadline": "2015-10-02T12:00:00"}, {"Id": 1432, "CountryCode": "AO", "DayOfWeek": 2, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T16:30:00", "SameDayDeadline": "2015-10-02T12:00:00"}, {"Id": 1433, "CountryCode": "AO", "DayOfWeek": 3, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T16:30:00", "SameDayDeadline": "2015-10-02T12:00:00"}, {"Id": 1434, "CountryCode": "AO", "DayOfWeek": 4, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T16:30:00", "SameDayDeadline": "2015-10-02T12:00:00"}, {"Id": 1435, "CountryCode": "AO", "DayOfWeek": 5, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T16:30:00", "SameDayDeadline": "2015-10-02T12:00:00"}, {"Id": 1436, "CountryCode": "AO", "DayOfWeek": 6, "OpenTime": "2015-10-02T08:30:00", "CloseTime": "2015-10-02T12:00:00", "SameDayDeadline": "2015-10-02T10:30:00"}], "LastUpdate": "2024-10-22T08:03:39"}, {"Id": 11, "Enabled": false, "CountryCode": "AQ", "Name": "Antarctica", "TimeZone": 65, "CurrencyId": 7, "DialingCode": 672, "UnitId": 199, "CountryTimes": [{"Id": 1437, "CountryCode": "AQ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1438, "CountryCode": "AQ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1439, "CountryCode": "AQ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1440, "CountryCode": "AQ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1441, "CountryCode": "AQ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1442, "CountryCode": "AQ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2024-10-22T08:04:29"}, {"Id": 12, "Enabled": true, "CountryCode": "AR", "Name": "Argentina", "TimeZone": 70, "CurrencyId": 7, "DialingCode": 54, "UnitId": 213, "CountryTimes": [{"Id": 1443, "CountryCode": "AR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1444, "CountryCode": "AR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1445, "CountryCode": "AR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1446, "CountryCode": "AR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1447, "CountryCode": "AR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1448, "CountryCode": "AR", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2024-10-22T08:05:14"}, {"Id": 13, "Enabled": false, "CountryCode": "AS", "Name": "American Samoa", "TimeZone": 1, "CurrencyId": 7, "DialingCode": 684, "UnitId": 204, "CountryTimes": [{"Id": 1449, "CountryCode": "AS", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1450, "CountryCode": "AS", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1451, "CountryCode": "AS", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1452, "CountryCode": "AS", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1453, "CountryCode": "AS", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1454, "CountryCode": "AS", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 14, "Enabled": true, "CountryCode": "AT", "Name": "Austria", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 43, "UnitId": 184, "CountryTimes": [{"Id": 3640, "CountryCode": "AT", "DayOfWeek": 3, "OpenTime": "2015-06-05T08:00:00", "CloseTime": "2015-06-05T18:00:00", "SameDayDeadline": "2015-06-05T14:00:00"}, {"Id": 3641, "CountryCode": "AT", "DayOfWeek": 4, "OpenTime": "2015-06-05T08:00:00", "CloseTime": "2015-06-05T18:00:00", "SameDayDeadline": "2015-06-05T14:00:00"}, {"Id": 3642, "CountryCode": "AT", "DayOfWeek": 5, "OpenTime": "2015-06-05T08:00:00", "CloseTime": "2015-06-05T18:00:00", "SameDayDeadline": "2015-06-05T14:00:00"}, {"Id": 3643, "CountryCode": "AT", "DayOfWeek": 6, "OpenTime": "2015-08-27T09:00:00", "CloseTime": "2015-08-27T12:00:00", "SameDayDeadline": "2015-08-27T11:00:00"}, {"Id": 3745, "CountryCode": "AT", "DayOfWeek": 1, "OpenTime": "2015-06-05T08:00:00", "CloseTime": "2015-06-05T18:00:00", "SameDayDeadline": "2015-06-05T14:00:00"}, {"Id": 3746, "CountryCode": "AT", "DayOfWeek": 2, "OpenTime": "2015-06-05T08:00:00", "CloseTime": "2015-06-05T18:00:00", "SameDayDeadline": "2015-06-05T14:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 15, "Enabled": true, "CountryCode": "AU", "Name": "Australia", "TimeZone": 255, "CurrencyId": 7, "DialingCode": 61, "UnitId": 185, "CountryTimes": [{"Id": 3674, "CountryCode": "AU", "DayOfWeek": 1, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T17:00:00", "SameDayDeadline": "2021-04-27T14:00:00"}, {"Id": 3675, "CountryCode": "AU", "DayOfWeek": 2, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T17:00:00", "SameDayDeadline": "2021-04-27T14:00:00"}, {"Id": 3676, "CountryCode": "AU", "DayOfWeek": 3, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T17:00:00", "SameDayDeadline": "2021-04-27T14:00:00"}, {"Id": 3677, "CountryCode": "AU", "DayOfWeek": 4, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T17:00:00", "SameDayDeadline": "2021-04-27T14:00:00"}, {"Id": 3678, "CountryCode": "AU", "DayOfWeek": 5, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T17:00:00", "SameDayDeadline": "2021-04-27T14:00:00"}, {"Id": 3679, "CountryCode": "AU", "DayOfWeek": 6, "OpenTime": "2021-04-27T08:00:00", "CloseTime": "2021-04-27T13:00:00", "SameDayDeadline": "2021-04-27T10:00:00"}], "LastUpdate": "2024-10-22T08:06:22"}, {"Id": 16, "Enabled": true, "CountryCode": "AW", "Name": "Aruba", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 297, "UnitId": 203, "CountryTimes": [{"Id": 1467, "CountryCode": "AW", "DayOfWeek": 1, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 1468, "CountryCode": "AW", "DayOfWeek": 2, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 1469, "CountryCode": "AW", "DayOfWeek": 3, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 1470, "CountryCode": "AW", "DayOfWeek": 4, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 1471, "CountryCode": "AW", "DayOfWeek": 5, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 1472, "CountryCode": "AW", "DayOfWeek": 6, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T12:00:00"}], "LastUpdate": "2024-10-22T08:05:59"}, {"Id": 17, "Enabled": true, "CountryCode": "AZ", "Name": "Azerbaijan", "TimeZone": 170, "CurrencyId": 7, "DialingCode": 994, "UnitId": 211, "CountryTimes": [{"Id": 1473, "CountryCode": "AZ", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 1474, "CountryCode": "AZ", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 1475, "CountryCode": "AZ", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 1476, "CountryCode": "AZ", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 1477, "CountryCode": "AZ", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 1478, "CountryCode": "AZ", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:30:00"}], "LastUpdate": "2024-10-22T08:07:06"}, {"Id": 18, "Enabled": true, "CountryCode": "BA", "Name": "Bosnia & Herzegovina", "TimeZone": 100, "CurrencyId": 7, "DialingCode": 387, "UnitId": 199, "CountryTimes": [{"Id": 1479, "CountryCode": "BA", "DayOfWeek": 1, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1480, "CountryCode": "BA", "DayOfWeek": 2, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1481, "CountryCode": "BA", "DayOfWeek": 3, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1482, "CountryCode": "BA", "DayOfWeek": 4, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1483, "CountryCode": "BA", "DayOfWeek": 5, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1484, "CountryCode": "BA", "DayOfWeek": 6, "OpenTime": "2023-10-21T08:00:00", "CloseTime": "2023-10-21T21:00:00", "SameDayDeadline": "2023-10-21T11:00:00"}, {"Id": 2806, "CountryCode": "BA", "DayOfWeek": 0, "OpenTime": "2023-10-21T00:00:00", "CloseTime": "2023-10-21T00:00:00", "SameDayDeadline": "2023-10-21T00:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 19, "Enabled": true, "CountryCode": "BB", "Name": "Barbados", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 1246, "UnitId": 213, "CountryTimes": [{"Id": 1485, "CountryCode": "BB", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1486, "CountryCode": "BB", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1487, "CountryCode": "BB", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1488, "CountryCode": "BB", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1489, "CountryCode": "BB", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 20, "Enabled": false, "CountryCode": "BD", "Name": "Bangladesh", "TimeZone": 195, "CurrencyId": 7, "DialingCode": 880, "UnitId": 212, "CountryTimes": [{"Id": 1491, "CountryCode": "BD", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1492, "CountryCode": "BD", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1493, "CountryCode": "BD", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1494, "CountryCode": "BD", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1495, "CountryCode": "BD", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1496, "CountryCode": "BD", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2024-10-22T08:08:13"}, {"Id": 21, "Enabled": true, "CountryCode": "BE", "Name": "Belgium", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 32, "UnitId": 186, "CountryTimes": [{"Id": 1497, "CountryCode": "BE", "DayOfWeek": 1, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T18:00:00", "SameDayDeadline": "2024-07-08T14:00:00"}, {"Id": 1498, "CountryCode": "BE", "DayOfWeek": 2, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T18:00:00", "SameDayDeadline": "2024-07-08T14:00:00"}, {"Id": 1499, "CountryCode": "BE", "DayOfWeek": 3, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T18:00:00", "SameDayDeadline": "2024-07-08T14:00:00"}, {"Id": 1500, "CountryCode": "BE", "DayOfWeek": 4, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T18:00:00", "SameDayDeadline": "2024-07-08T14:00:00"}, {"Id": 1501, "CountryCode": "BE", "DayOfWeek": 5, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T18:00:00", "SameDayDeadline": "2024-07-08T14:00:00"}, {"Id": 3809, "CountryCode": "BE", "DayOfWeek": 6, "OpenTime": "2024-07-08T10:00:00", "CloseTime": "2024-07-08T12:00:00", "SameDayDeadline": "2024-07-08T12:00:00"}], "LastUpdate": "2025-08-08T10:02:40.993"}, {"Id": 22, "Enabled": false, "CountryCode": "BF", "Name": "Burkina Faso", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 226, "UnitId": 199, "CountryTimes": [{"Id": 1503, "CountryCode": "BF", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1504, "CountryCode": "BF", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1505, "CountryCode": "BF", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1506, "CountryCode": "BF", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1507, "CountryCode": "BF", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1508, "CountryCode": "BF", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 23, "Enabled": false, "CountryCode": "BG", "Name": "Bulgaria", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 359, "UnitId": 199, "CountryTimes": [{"Id": 1509, "CountryCode": "BG", "DayOfWeek": 1, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T17:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1510, "CountryCode": "BG", "DayOfWeek": 2, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T17:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1511, "CountryCode": "BG", "DayOfWeek": 3, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T17:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1512, "CountryCode": "BG", "DayOfWeek": 4, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T17:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1513, "CountryCode": "BG", "DayOfWeek": 5, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T17:00:00", "SameDayDeadline": "2023-10-21T15:00:00"}, {"Id": 1514, "CountryCode": "BG", "DayOfWeek": 6, "OpenTime": "2023-10-21T09:00:00", "CloseTime": "2023-10-21T13:00:00", "SameDayDeadline": "2023-10-21T11:30:00"}], "LastUpdate": "2025-04-23T10:20:29.463"}, {"Id": 24, "Enabled": true, "CountryCode": "BH", "Name": "Bahrain", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 973, "UnitId": 212, "CountryTimes": [{"Id": 1515, "CountryCode": "BH", "DayOfWeek": 1, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1516, "CountryCode": "BH", "DayOfWeek": 2, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1517, "CountryCode": "BH", "DayOfWeek": 3, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1518, "CountryCode": "BH", "DayOfWeek": 4, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1520, "CountryCode": "BH", "DayOfWeek": 6, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T11:30:00"}], "LastUpdate": "2024-10-22T08:07:51"}, {"Id": 25, "Enabled": false, "CountryCode": "BI", "Name": "Burundi", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 257, "UnitId": 199, "CountryTimes": [{"Id": 1521, "CountryCode": "BI", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1522, "CountryCode": "BI", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1523, "CountryCode": "BI", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1524, "CountryCode": "BI", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1525, "CountryCode": "BI", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1526, "CountryCode": "BI", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 26, "Enabled": false, "CountryCode": "BJ", "Name": "Benin", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 229, "UnitId": 199, "CountryTimes": [{"Id": 1527, "CountryCode": "BJ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1528, "CountryCode": "BJ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1529, "CountryCode": "BJ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1530, "CountryCode": "BJ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1531, "CountryCode": "BJ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1532, "CountryCode": "BJ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}, {"Id": 2965, "CountryCode": "BJ", "DayOfWeek": 1, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}, {"Id": 2967, "CountryCode": "BJ", "DayOfWeek": 3, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}, {"Id": 2968, "CountryCode": "BJ", "DayOfWeek": 4, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}, {"Id": 2969, "CountryCode": "BJ", "DayOfWeek": 5, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}, {"Id": 2972, "CountryCode": "BJ", "DayOfWeek": 2, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}, {"Id": 2976, "CountryCode": "BJ", "DayOfWeek": 6, "OpenTime": "2004-10-26T16:00:00", "CloseTime": "2004-10-26T19:00:00", "SameDayDeadline": "2004-10-26T12:00:00"}], "LastUpdate": "2023-08-07T12:41:05"}, {"Id": 27, "Enabled": false, "CountryCode": "BM", "Name": "Bermuda", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 809, "UnitId": 213, "CountryTimes": [{"Id": 1533, "CountryCode": "BM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1534, "CountryCode": "BM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1535, "CountryCode": "BM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1536, "CountryCode": "BM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1537, "CountryCode": "BM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1538, "CountryCode": "BM", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T17:41:57"}, {"Id": 28, "Enabled": true, "CountryCode": "BN", "Name": "Brunei Darussalam", "TimeZone": 215, "CurrencyId": 7, "DialingCode": 673, "UnitId": 220, "CountryTimes": [{"Id": 1539, "CountryCode": "BN", "DayOfWeek": 1, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:30:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1540, "CountryCode": "BN", "DayOfWeek": 2, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:30:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1541, "CountryCode": "BN", "DayOfWeek": 3, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:30:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1542, "CountryCode": "BN", "DayOfWeek": 4, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:30:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1543, "CountryCode": "BN", "DayOfWeek": 5, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:30:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1544, "CountryCode": "BN", "DayOfWeek": 6, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T11:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 29, "Enabled": true, "CountryCode": "BO", "Name": "Bolivia", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 591, "UnitId": 213, "CountryTimes": [{"Id": 1545, "CountryCode": "BO", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1546, "CountryCode": "BO", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1547, "CountryCode": "BO", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1548, "CountryCode": "BO", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1549, "CountryCode": "BO", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1550, "CountryCode": "BO", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2023-07-18T19:55:06"}, {"Id": 30, "Enabled": true, "CountryCode": "BR", "Name": "Brazil", "TimeZone": 65, "CurrencyId": 7, "DialingCode": 55, "UnitId": 213, "CountryTimes": [{"Id": 1551, "CountryCode": "BR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1552, "CountryCode": "BR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1553, "CountryCode": "BR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1554, "CountryCode": "BR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1555, "CountryCode": "BR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1556, "CountryCode": "BR", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 31, "Enabled": false, "CountryCode": "BS", "Name": "Bahama Islands", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 1242, "UnitId": 213, "CountryTimes": [], "LastUpdate": "2023-08-07T17:44:51"}, {"Id": 32, "Enabled": false, "CountryCode": "BT", "Name": "Bhutan", "TimeZone": 195, "CurrencyId": 7, "DialingCode": 975, "UnitId": 212, "CountryTimes": [{"Id": 1563, "CountryCode": "BT", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1564, "CountryCode": "BT", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1565, "CountryCode": "BT", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1566, "CountryCode": "BT", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1567, "CountryCode": "BT", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1568, "CountryCode": "BT", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 33, "Enabled": false, "CountryCode": "BV", "Name": "Bouvet Is.", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 47, "UnitId": 199, "CountryTimes": [{"Id": 1569, "CountryCode": "BV", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1570, "CountryCode": "BV", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1571, "CountryCode": "BV", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1572, "CountryCode": "BV", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1573, "CountryCode": "BV", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1574, "CountryCode": "BV", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 34, "Enabled": false, "CountryCode": "BW", "Name": "Botswana", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 267, "UnitId": 214, "CountryTimes": [{"Id": 1575, "CountryCode": "BW", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1576, "CountryCode": "BW", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1577, "CountryCode": "BW", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1578, "CountryCode": "BW", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1579, "CountryCode": "BW", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1580, "CountryCode": "BW", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2023-08-07T17:40:17"}, {"Id": 35, "Enabled": false, "CountryCode": "BY", "Name": "Belarus", "TimeZone": 115, "CurrencyId": 22, "DialingCode": 375, "UnitId": 272, "CountryTimes": [{"Id": 1581, "CountryCode": "BY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 1582, "CountryCode": "BY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 1583, "CountryCode": "BY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 1584, "CountryCode": "BY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 1585, "CountryCode": "BY", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 1586, "CountryCode": "BY", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}], "LastUpdate": "2022-03-04T09:45:38.243"}, {"Id": 36, "Enabled": true, "CountryCode": "BZ", "Name": "Belize", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 1587, "CountryCode": "BZ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1588, "CountryCode": "BZ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1589, "CountryCode": "BZ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1590, "CountryCode": "BZ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1591, "CountryCode": "BZ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1592, "CountryCode": "BZ", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 37, "Enabled": true, "CountryCode": "CA", "Name": "Canada", "TimeZone": 35, "CurrencyId": 2, "DialingCode": 1, "UnitId": 213, "CountryTimes": [{"Id": 1593, "CountryCode": "CA", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1594, "CountryCode": "CA", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1595, "CountryCode": "CA", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1596, "CountryCode": "CA", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1597, "CountryCode": "CA", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 3766, "CountryCode": "CA", "DayOfWeek": 6, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T13:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 38, "Enabled": false, "CountryCode": "CC", "Name": "Cocos (Keeling) Islands", "TimeZone": 203, "CurrencyId": 7, "DialingCode": 672, "UnitId": 212, "CountryTimes": [{"Id": 1599, "CountryCode": "CC", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1600, "CountryCode": "CC", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1601, "CountryCode": "CC", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1602, "CountryCode": "CC", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1603, "CountryCode": "CC", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1604, "CountryCode": "CC", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T17:36:41"}, {"Id": 39, "Enabled": false, "CountryCode": "CF", "Name": "Central African Rep.", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 236, "UnitId": 199, "CountryTimes": [{"Id": 1605, "CountryCode": "CF", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1606, "CountryCode": "CF", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1607, "CountryCode": "CF", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1608, "CountryCode": "CF", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1609, "CountryCode": "CF", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1610, "CountryCode": "CF", "DayOfWeek": 6, "OpenTime": "2000-01-01T09:00:00", "CloseTime": "2000-01-01T09:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 40, "Enabled": false, "CountryCode": "CG", "Name": "Congo", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 242, "UnitId": 199, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 41, "Enabled": true, "CountryCode": "CH", "Name": "Switzerland", "TimeZone": 110, "CurrencyId": 3, "DialingCode": 41, "UnitId": 187, "CountryTimes": [{"Id": 3724, "CountryCode": "CH", "DayOfWeek": 1, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3725, "CountryCode": "CH", "DayOfWeek": 2, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3726, "CountryCode": "CH", "DayOfWeek": 3, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3727, "CountryCode": "CH", "DayOfWeek": 4, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3728, "CountryCode": "CH", "DayOfWeek": 5, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3730, "CountryCode": "CH", "DayOfWeek": 6, "OpenTime": "2016-02-15T08:00:00", "CloseTime": "2016-02-15T13:00:00", "SameDayDeadline": "2016-02-15T13:00:00"}, {"Id": 3792, "CountryCode": "CH", "DayOfWeek": 1, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3793, "CountryCode": "CH", "DayOfWeek": 2, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3794, "CountryCode": "CH", "DayOfWeek": 3, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3795, "CountryCode": "CH", "DayOfWeek": 4, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3796, "CountryCode": "CH", "DayOfWeek": 5, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 42, "Enabled": true, "CountryCode": "CI", "Name": "Ivory Coast", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 225, "UnitId": 199, "CountryTimes": [{"Id": 1618, "CountryCode": "CI", "DayOfWeek": 2, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T17:15:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 1619, "CountryCode": "CI", "DayOfWeek": 3, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T17:15:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 1620, "CountryCode": "CI", "DayOfWeek": 4, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T17:15:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 1621, "CountryCode": "CI", "DayOfWeek": 5, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T17:15:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 1622, "CountryCode": "CI", "DayOfWeek": 6, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T12:30:00", "SameDayDeadline": "2023-10-30T11:00:00"}, {"Id": 2983, "CountryCode": "CI", "DayOfWeek": 1, "OpenTime": "2023-10-30T08:30:00", "CloseTime": "2023-10-30T17:15:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 2984, "CountryCode": "CI", "DayOfWeek": 2, "OpenTime": "2023-10-30T14:00:00", "CloseTime": "2023-10-30T18:00:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 2985, "CountryCode": "CI", "DayOfWeek": 3, "OpenTime": "2023-10-30T14:00:00", "CloseTime": "2023-10-30T18:00:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 2986, "CountryCode": "CI", "DayOfWeek": 4, "OpenTime": "2023-10-30T14:00:00", "CloseTime": "2023-10-30T18:00:00", "SameDayDeadline": "2023-10-30T15:00:00"}, {"Id": 2987, "CountryCode": "CI", "DayOfWeek": 5, "OpenTime": "2023-10-30T14:00:00", "CloseTime": "2023-10-30T18:00:00", "SameDayDeadline": "2023-10-30T15:00:00"}], "LastUpdate": "2023-09-14T17:01:00"}, {"Id": 43, "Enabled": true, "CountryCode": "CK", "Name": "Cook Islands", "TimeZone": 2, "CurrencyId": 7, "DialingCode": 682, "UnitId": 204, "CountryTimes": [{"Id": 1623, "CountryCode": "CK", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1624, "CountryCode": "CK", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1625, "CountryCode": "CK", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1626, "CountryCode": "CK", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1627, "CountryCode": "CK", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1628, "CountryCode": "CK", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 44, "Enabled": true, "CountryCode": "CL", "Name": "Chile", "TimeZone": 56, "CurrencyId": 7, "DialingCode": 56, "UnitId": 213, "CountryTimes": [{"Id": 1629, "CountryCode": "CL", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1630, "CountryCode": "CL", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1631, "CountryCode": "CL", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1632, "CountryCode": "CL", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1633, "CountryCode": "CL", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1634, "CountryCode": "CL", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 45, "Enabled": false, "CountryCode": "CM", "Name": "Cameroon", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 237, "UnitId": 199, "CountryTimes": [{"Id": 1635, "CountryCode": "CM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1636, "CountryCode": "CM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1637, "CountryCode": "CM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1638, "CountryCode": "CM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1639, "CountryCode": "CM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1640, "CountryCode": "CM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 46, "Enabled": true, "CountryCode": "CN", "Name": "China", "TimeZone": 210, "CurrencyId": 30, "DialingCode": 86, "UnitId": 222, "CountryTimes": [{"Id": 1641, "CountryCode": "CN", "DayOfWeek": 1, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1642, "CountryCode": "CN", "DayOfWeek": 2, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1643, "CountryCode": "CN", "DayOfWeek": 3, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1644, "CountryCode": "CN", "DayOfWeek": 4, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1645, "CountryCode": "CN", "DayOfWeek": 5, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1646, "CountryCode": "CN", "DayOfWeek": 6, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 47, "Enabled": true, "CountryCode": "CO", "Name": "Colombia", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 57, "UnitId": 213, "CountryTimes": [{"Id": 1647, "CountryCode": "CO", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1648, "CountryCode": "CO", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1649, "CountryCode": "CO", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1650, "CountryCode": "CO", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1651, "CountryCode": "CO", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1652, "CountryCode": "CO", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 48, "Enabled": true, "CountryCode": "CR", "Name": "Costa Rica", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 506, "UnitId": 213, "CountryTimes": [{"Id": 1653, "CountryCode": "CR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1654, "CountryCode": "CR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1655, "CountryCode": "CR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1656, "CountryCode": "CR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1657, "CountryCode": "CR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1658, "CountryCode": "CR", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 49, "Enabled": true, "CountryCode": "CU", "Name": "Cuba", "TimeZone": 35, "CurrencyId": 7, "DialingCode": 0, "UnitId": 199, "CountryTimes": [{"Id": 1659, "CountryCode": "CU", "DayOfWeek": 1, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1660, "CountryCode": "CU", "DayOfWeek": 2, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1661, "CountryCode": "CU", "DayOfWeek": 3, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1662, "CountryCode": "CU", "DayOfWeek": 4, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1663, "CountryCode": "CU", "DayOfWeek": 5, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1664, "CountryCode": "CU", "DayOfWeek": 6, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T18:00:00", "SameDayDeadline": "2023-10-26T11:30:00"}], "LastUpdate": "2020-11-03T13:50:54.31"}, {"Id": 50, "Enabled": false, "CountryCode": "CV", "Name": "Cape Verde", "TimeZone": 83, "CurrencyId": 7, "DialingCode": 238, "UnitId": 206, "CountryTimes": [{"Id": 1665, "CountryCode": "CV", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1666, "CountryCode": "CV", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1667, "CountryCode": "CV", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1668, "CountryCode": "CV", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1669, "CountryCode": "CV", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1670, "CountryCode": "CV", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 51, "Enabled": false, "CountryCode": "CX", "Name": "Christmas Island", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 672, "UnitId": 213, "CountryTimes": [{"Id": 1671, "CountryCode": "CX", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1672, "CountryCode": "CX", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1673, "CountryCode": "CX", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1674, "CountryCode": "CX", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1675, "CountryCode": "CX", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1676, "CountryCode": "CX", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 52, "Enabled": true, "CountryCode": "CY", "Name": "Cyprus", "TimeZone": 120, "CurrencyId": 7, "DialingCode": 357, "UnitId": 212, "CountryTimes": [{"Id": 1677, "CountryCode": "CY", "DayOfWeek": 1, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T18:30:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1678, "CountryCode": "CY", "DayOfWeek": 2, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T18:30:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1679, "CountryCode": "CY", "DayOfWeek": 3, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T18:30:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1680, "CountryCode": "CY", "DayOfWeek": 4, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T18:30:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1681, "CountryCode": "CY", "DayOfWeek": 5, "OpenTime": "2018-08-15T08:00:00", "CloseTime": "2018-08-15T18:30:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 1682, "CountryCode": "CY", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T14:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 53, "Enabled": true, "CountryCode": "CZ", "Name": "Czech Republic", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 420, "UnitId": 274, "CountryTimes": [{"Id": 1683, "CountryCode": "CZ", "DayOfWeek": 1, "OpenTime": "2017-04-11T08:30:00", "CloseTime": "2017-04-11T17:00:00", "SameDayDeadline": "2017-04-11T11:30:00"}, {"Id": 1684, "CountryCode": "CZ", "DayOfWeek": 2, "OpenTime": "2017-04-11T08:30:00", "CloseTime": "2017-04-11T17:00:00", "SameDayDeadline": "2017-04-11T11:30:00"}, {"Id": 1685, "CountryCode": "CZ", "DayOfWeek": 3, "OpenTime": "2017-04-11T08:30:00", "CloseTime": "2017-04-11T17:00:00", "SameDayDeadline": "2017-04-11T11:30:00"}, {"Id": 1686, "CountryCode": "CZ", "DayOfWeek": 4, "OpenTime": "2017-04-11T08:30:00", "CloseTime": "2017-04-11T17:00:00", "SameDayDeadline": "2017-04-11T11:30:00"}, {"Id": 1687, "CountryCode": "CZ", "DayOfWeek": 5, "OpenTime": "2017-04-11T08:30:00", "CloseTime": "2017-04-11T17:00:00", "SameDayDeadline": "2017-04-11T11:30:00"}, {"Id": 2988, "CountryCode": "CZ", "DayOfWeek": 6, "OpenTime": "2017-04-11T09:00:00", "CloseTime": "2017-04-11T11:00:00", "SameDayDeadline": "2017-04-11T10:30:00"}], "LastUpdate": "2017-03-30T07:01:45.56"}, {"Id": 54, "Enabled": true, "CountryCode": "DE", "Name": "Germany", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 49, "UnitId": 188, "CountryTimes": [{"Id": 1689, "CountryCode": "DE", "DayOfWeek": 1, "OpenTime": "2021-06-18T07:30:00", "CloseTime": "2021-06-18T18:30:00", "SameDayDeadline": "2021-06-18T15:00:00"}, {"Id": 1690, "CountryCode": "DE", "DayOfWeek": 2, "OpenTime": "2021-06-18T07:30:00", "CloseTime": "2021-06-18T18:30:00", "SameDayDeadline": "2021-06-18T15:00:00"}, {"Id": 1691, "CountryCode": "DE", "DayOfWeek": 3, "OpenTime": "2021-06-18T07:30:00", "CloseTime": "2021-06-18T18:30:00", "SameDayDeadline": "2021-06-18T15:00:00"}, {"Id": 1692, "CountryCode": "DE", "DayOfWeek": 4, "OpenTime": "2021-06-18T07:30:00", "CloseTime": "2021-06-18T18:30:00", "SameDayDeadline": "2021-06-18T15:00:00"}, {"Id": 1693, "CountryCode": "DE", "DayOfWeek": 5, "OpenTime": "2021-06-18T07:30:00", "CloseTime": "2021-06-18T18:30:00", "SameDayDeadline": "2021-06-18T15:00:00"}, {"Id": 1694, "CountryCode": "DE", "DayOfWeek": 6, "OpenTime": "2019-10-07T08:00:00", "CloseTime": "2019-10-07T12:00:00", "SameDayDeadline": "2019-10-07T10:45:00"}], "LastUpdate": "2023-04-06T12:19:54"}, {"Id": 55, "Enabled": false, "CountryCode": "DJ", "Name": "Djibouti", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 253, "UnitId": 199, "CountryTimes": [{"Id": 1695, "CountryCode": "DJ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1696, "CountryCode": "DJ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1697, "CountryCode": "DJ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1698, "CountryCode": "DJ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1699, "CountryCode": "DJ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1700, "CountryCode": "DJ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 56, "Enabled": true, "CountryCode": "DK", "Name": "Denmark", "TimeZone": 105, "CurrencyId": 5, "DialingCode": 45, "UnitId": 189, "CountryTimes": [{"Id": 1701, "CountryCode": "DK", "DayOfWeek": 1, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T17:00:00", "SameDayDeadline": "2022-10-18T14:00:00"}, {"Id": 1702, "CountryCode": "DK", "DayOfWeek": 2, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T17:00:00", "SameDayDeadline": "2022-10-18T14:00:00"}, {"Id": 1703, "CountryCode": "DK", "DayOfWeek": 3, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T17:00:00", "SameDayDeadline": "2022-10-18T14:00:00"}, {"Id": 1704, "CountryCode": "DK", "DayOfWeek": 4, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T17:00:00", "SameDayDeadline": "2022-10-18T14:00:00"}, {"Id": 1705, "CountryCode": "DK", "DayOfWeek": 5, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T17:00:00", "SameDayDeadline": "2022-10-18T14:00:00"}, {"Id": 1706, "CountryCode": "DK", "DayOfWeek": 6, "OpenTime": "2022-10-18T09:00:00", "CloseTime": "2022-10-18T12:00:00", "SameDayDeadline": "2022-10-18T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 57, "Enabled": false, "CountryCode": "DM", "Name": "Dominica", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 767, "UnitId": 213, "CountryTimes": [{"Id": 1707, "CountryCode": "DM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1708, "CountryCode": "DM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1709, "CountryCode": "DM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1710, "CountryCode": "DM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1711, "CountryCode": "DM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1712, "CountryCode": "DM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 58, "Enabled": true, "CountryCode": "DO", "Name": "Dominican Republic", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 809, "UnitId": 213, "CountryTimes": [{"Id": 1713, "CountryCode": "DO", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1714, "CountryCode": "DO", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1715, "CountryCode": "DO", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1716, "CountryCode": "DO", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1717, "CountryCode": "DO", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1718, "CountryCode": "DO", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 59, "Enabled": true, "CountryCode": "DZ", "Name": "Algeria", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 213, "UnitId": 199, "CountryTimes": [{"Id": 1719, "CountryCode": "DZ", "DayOfWeek": 1, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}, {"Id": 1720, "CountryCode": "DZ", "DayOfWeek": 2, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}, {"Id": 1721, "CountryCode": "DZ", "DayOfWeek": 3, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}, {"Id": 1722, "CountryCode": "DZ", "DayOfWeek": 4, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}, {"Id": 1723, "CountryCode": "DZ", "DayOfWeek": 5, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}, {"Id": 1724, "CountryCode": "DZ", "DayOfWeek": 6, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T11:00:00"}, {"Id": 2804, "CountryCode": "DZ", "DayOfWeek": 0, "OpenTime": "2023-09-18T00:00:00", "CloseTime": "2023-09-18T00:00:00", "SameDayDeadline": "2023-09-18T00:00:00"}, {"Id": 2954, "CountryCode": "DZ", "DayOfWeek": 5, "OpenTime": "2023-09-21T08:00:00", "CloseTime": "2023-09-21T21:00:00", "SameDayDeadline": "2023-09-21T15:00:00"}], "LastUpdate": "2024-10-22T08:02:28"}, {"Id": 60, "Enabled": true, "CountryCode": "EC", "Name": "Ecuador", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 593, "UnitId": 213, "CountryTimes": [{"Id": 1725, "CountryCode": "EC", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1726, "CountryCode": "EC", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1727, "CountryCode": "EC", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1728, "CountryCode": "EC", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1729, "CountryCode": "EC", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1730, "CountryCode": "EC", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 61, "Enabled": true, "CountryCode": "EE", "Name": "Estonia", "TimeZone": 125, "CurrencyId": 7, "DialingCode": 372, "UnitId": 190, "CountryTimes": [{"Id": 1731, "CountryCode": "EE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T16:00:00"}, {"Id": 1732, "CountryCode": "EE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T16:00:00"}, {"Id": 1733, "CountryCode": "EE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T16:00:00"}, {"Id": 1734, "CountryCode": "EE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T16:00:00"}, {"Id": 1735, "CountryCode": "EE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T16:00:00"}, {"Id": 1736, "CountryCode": "EE", "DayOfWeek": 6, "OpenTime": "2015-02-16T09:00:00", "CloseTime": "2015-02-16T12:00:00", "SameDayDeadline": "2015-02-16T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 62, "Enabled": true, "CountryCode": "EG", "Name": "Egypt", "TimeZone": 120, "CurrencyId": 7, "DialingCode": 20, "UnitId": 199, "CountryTimes": [{"Id": 1737, "CountryCode": "EG", "DayOfWeek": 1, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1738, "CountryCode": "EG", "DayOfWeek": 2, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1739, "CountryCode": "EG", "DayOfWeek": 3, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1740, "CountryCode": "EG", "DayOfWeek": 4, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1741, "CountryCode": "EG", "DayOfWeek": 5, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T15:00:00"}, {"Id": 1742, "CountryCode": "EG", "DayOfWeek": 6, "OpenTime": "2023-10-26T09:00:00", "CloseTime": "2023-10-26T21:00:00", "SameDayDeadline": "2023-10-26T11:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 63, "Enabled": false, "CountryCode": "ER", "Name": "Eritrea", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 291, "UnitId": 199, "CountryTimes": [{"Id": 1743, "CountryCode": "ER", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1744, "CountryCode": "ER", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1745, "CountryCode": "ER", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1746, "CountryCode": "ER", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1747, "CountryCode": "ER", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1748, "CountryCode": "ER", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:30:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2815, "CountryCode": "ER", "DayOfWeek": 0, "OpenTime": "2002-07-22T08:00:00", "CloseTime": "2002-07-22T12:30:00", "SameDayDeadline": "2002-07-22T09:30:00"}, {"Id": 3005, "CountryCode": "ER", "DayOfWeek": 1, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3006, "CountryCode": "ER", "DayOfWeek": 2, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3007, "CountryCode": "ER", "DayOfWeek": 3, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3008, "CountryCode": "ER", "DayOfWeek": 4, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3009, "CountryCode": "ER", "DayOfWeek": 5, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3134, "CountryCode": "ER", "DayOfWeek": 6, "OpenTime": "2004-10-27T14:30:00", "CloseTime": "2004-10-27T20:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}], "LastUpdate": "2023-08-07T11:31:52"}, {"Id": 64, "Enabled": true, "CountryCode": "ES", "Name": "Spain", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 34, "UnitId": 191, "CountryTimes": [{"Id": 3662, "CountryCode": "ES", "DayOfWeek": 1, "OpenTime": "2023-11-16T17:00:00", "CloseTime": "2023-11-16T20:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3663, "CountryCode": "ES", "DayOfWeek": 2, "OpenTime": "2023-11-16T17:00:00", "CloseTime": "2023-11-16T20:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3664, "CountryCode": "ES", "DayOfWeek": 3, "OpenTime": "2023-11-16T17:00:00", "CloseTime": "2023-11-16T20:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3665, "CountryCode": "ES", "DayOfWeek": 4, "OpenTime": "2023-11-16T17:00:00", "CloseTime": "2023-11-16T20:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3666, "CountryCode": "ES", "DayOfWeek": 5, "OpenTime": "2023-11-16T17:00:00", "CloseTime": "2023-11-16T20:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3667, "CountryCode": "ES", "DayOfWeek": 6, "OpenTime": "2023-11-16T09:00:00", "CloseTime": "2023-11-16T13:00:00", "SameDayDeadline": "2023-11-16T11:00:00"}, {"Id": 3716, "CountryCode": "ES", "DayOfWeek": 1, "OpenTime": "2023-11-16T10:00:00", "CloseTime": "2023-11-16T14:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3717, "CountryCode": "ES", "DayOfWeek": 2, "OpenTime": "2023-11-16T10:00:00", "CloseTime": "2023-11-16T14:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3718, "CountryCode": "ES", "DayOfWeek": 3, "OpenTime": "2023-11-16T10:00:00", "CloseTime": "2023-11-16T14:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3719, "CountryCode": "ES", "DayOfWeek": 4, "OpenTime": "2023-11-16T10:00:00", "CloseTime": "2023-11-16T14:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}, {"Id": 3720, "CountryCode": "ES", "DayOfWeek": 5, "OpenTime": "2023-11-16T10:00:00", "CloseTime": "2023-11-16T14:00:00", "SameDayDeadline": "2023-11-16T13:00:00"}], "LastUpdate": "2022-10-17T11:25:21.267"}, {"Id": 65, "Enabled": false, "CountryCode": "ET", "Name": "Ethiopia", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 251, "UnitId": 199, "CountryTimes": [{"Id": 1755, "CountryCode": "ET", "DayOfWeek": 1, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1756, "CountryCode": "ET", "DayOfWeek": 2, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1757, "CountryCode": "ET", "DayOfWeek": 3, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1758, "CountryCode": "ET", "DayOfWeek": 4, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1759, "CountryCode": "ET", "DayOfWeek": 5, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1760, "CountryCode": "ET", "DayOfWeek": 6, "OpenTime": "2019-09-26T08:00:00", "CloseTime": "2019-09-26T18:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}], "LastUpdate": "2023-08-04T10:58:56"}, {"Id": 66, "Enabled": true, "CountryCode": "FI", "Name": "Finland", "TimeZone": 125, "CurrencyId": 7, "DialingCode": 358, "UnitId": 192, "CountryTimes": [{"Id": 1761, "CountryCode": "FI", "DayOfWeek": 1, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T18:00:00", "SameDayDeadline": "2022-10-24T14:00:00"}, {"Id": 1762, "CountryCode": "FI", "DayOfWeek": 2, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T18:00:00", "SameDayDeadline": "2022-10-24T14:00:00"}, {"Id": 1763, "CountryCode": "FI", "DayOfWeek": 3, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T18:00:00", "SameDayDeadline": "2022-10-24T14:00:00"}, {"Id": 1764, "CountryCode": "FI", "DayOfWeek": 4, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T18:00:00", "SameDayDeadline": "2022-10-24T14:00:00"}, {"Id": 1765, "CountryCode": "FI", "DayOfWeek": 5, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T18:00:00", "SameDayDeadline": "2022-10-24T14:00:00"}, {"Id": 3764, "CountryCode": "FI", "DayOfWeek": 6, "OpenTime": "2022-10-24T09:00:00", "CloseTime": "2022-10-24T14:00:00", "SameDayDeadline": "2022-10-24T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 67, "Enabled": true, "CountryCode": "FJ", "Name": "Fiji", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 679, "UnitId": 204, "CountryTimes": [{"Id": 1767, "CountryCode": "FJ", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1768, "CountryCode": "FJ", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1769, "CountryCode": "FJ", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1770, "CountryCode": "FJ", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1771, "CountryCode": "FJ", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1772, "CountryCode": "FJ", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 68, "Enabled": false, "CountryCode": "FK", "Name": "Falkland Islands (Malvinas)", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 500, "UnitId": 212, "CountryTimes": [{"Id": 1773, "CountryCode": "FK", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1774, "CountryCode": "FK", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1775, "CountryCode": "FK", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1776, "CountryCode": "FK", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1777, "CountryCode": "FK", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1778, "CountryCode": "FK", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 69, "Enabled": false, "CountryCode": "FM", "Name": "Micronesia", "TimeZone": 260, "CurrencyId": 7, "DialingCode": 691, "UnitId": 213, "CountryTimes": [{"Id": 1779, "CountryCode": "FM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1780, "CountryCode": "FM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1781, "CountryCode": "FM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1782, "CountryCode": "FM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1783, "CountryCode": "FM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1784, "CountryCode": "FM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 70, "Enabled": true, "CountryCode": "FO", "Name": "Faroe Islands", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 298, "UnitId": 189, "CountryTimes": [{"Id": 1785, "CountryCode": "FO", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1786, "CountryCode": "FO", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1787, "CountryCode": "FO", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1788, "CountryCode": "FO", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1789, "CountryCode": "FO", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1790, "CountryCode": "FO", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 71, "Enabled": true, "CountryCode": "FR", "Name": "France", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 33, "UnitId": 193, "CountryTimes": [{"Id": 1791, "CountryCode": "FR", "DayOfWeek": 1, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 1792, "CountryCode": "FR", "DayOfWeek": 2, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 1793, "CountryCode": "FR", "DayOfWeek": 3, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 1794, "CountryCode": "FR", "DayOfWeek": 4, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 1795, "CountryCode": "FR", "DayOfWeek": 5, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 1796, "CountryCode": "FR", "DayOfWeek": 6, "OpenTime": "2024-07-08T09:00:00", "CloseTime": "2024-07-08T19:00:00", "SameDayDeadline": "2024-07-08T16:45:00"}, {"Id": 3173, "CountryCode": "FR", "DayOfWeek": 0, "OpenTime": "2024-07-08T00:00:00", "CloseTime": "2024-07-08T00:00:00", "SameDayDeadline": "2024-07-08T00:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 72, "Enabled": false, "CountryCode": "GA", "Name": "Gabon", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 241, "UnitId": 199, "CountryTimes": [{"Id": 1797, "CountryCode": "GA", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1798, "CountryCode": "GA", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1799, "CountryCode": "GA", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1800, "CountryCode": "GA", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 1801, "CountryCode": "GA", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 3049, "CountryCode": "GA", "DayOfWeek": 1, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}, {"Id": 3050, "CountryCode": "GA", "DayOfWeek": 2, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}, {"Id": 3051, "CountryCode": "GA", "DayOfWeek": 3, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}, {"Id": 3052, "CountryCode": "GA", "DayOfWeek": 4, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}, {"Id": 3055, "CountryCode": "GA", "DayOfWeek": 5, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}, {"Id": 3056, "CountryCode": "GA", "DayOfWeek": 5, "OpenTime": "2004-10-27T15:00:00", "CloseTime": "2004-10-27T19:30:00", "SameDayDeadline": "2004-10-27T11:30:00"}], "LastUpdate": "2023-08-07T12:45:43"}, {"Id": 73, "Enabled": false, "CountryCode": "GD", "Name": "Grenada", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 473, "UnitId": 212, "CountryTimes": [{"Id": 1803, "CountryCode": "GD", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1804, "CountryCode": "GD", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1805, "CountryCode": "GD", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1806, "CountryCode": "GD", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1807, "CountryCode": "GD", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1808, "CountryCode": "GD", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 74, "Enabled": true, "CountryCode": "GE", "Name": "Georgia", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 995, "UnitId": 207, "CountryTimes": [{"Id": 1809, "CountryCode": "GE", "DayOfWeek": 1, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T14:30:00"}, {"Id": 1810, "CountryCode": "GE", "DayOfWeek": 2, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T14:30:00"}, {"Id": 1811, "CountryCode": "GE", "DayOfWeek": 3, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T14:30:00"}, {"Id": 1812, "CountryCode": "GE", "DayOfWeek": 4, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T14:30:00"}, {"Id": 1813, "CountryCode": "GE", "DayOfWeek": 5, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T14:30:00"}, {"Id": 1814, "CountryCode": "GE", "DayOfWeek": 6, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": "2023-01-30T13:30:00"}, {"Id": 3802, "CountryCode": "GE", "DayOfWeek": 0, "OpenTime": "2023-01-30T10:00:00", "CloseTime": "2023-01-30T21:00:00", "SameDayDeadline": null}], "LastUpdate": "2023-01-30T10:37:13"}, {"Id": 75, "Enabled": true, "CountryCode": "GF", "Name": "French Guyana", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 594, "UnitId": 193, "CountryTimes": [{"Id": 1815, "CountryCode": "GF", "DayOfWeek": 1, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1816, "CountryCode": "GF", "DayOfWeek": 2, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1817, "CountryCode": "GF", "DayOfWeek": 3, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1818, "CountryCode": "GF", "DayOfWeek": 4, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1819, "CountryCode": "GF", "DayOfWeek": 5, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1820, "CountryCode": "GF", "DayOfWeek": 6, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 76, "Enabled": false, "CountryCode": "GG", "Name": "Channel Is. (Guernsey)", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 44, "UnitId": 212, "CountryTimes": [{"Id": 2839, "CountryCode": "GG", "DayOfWeek": 1, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}, {"Id": 2840, "CountryCode": "GG", "DayOfWeek": 2, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}, {"Id": 2841, "CountryCode": "GG", "DayOfWeek": 3, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}, {"Id": 2842, "CountryCode": "GG", "DayOfWeek": 4, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}, {"Id": 2843, "CountryCode": "GG", "DayOfWeek": 5, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}, {"Id": 2844, "CountryCode": "GG", "DayOfWeek": 6, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T17:00:00", "SameDayDeadline": "2002-10-28T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 77, "Enabled": false, "CountryCode": "GH", "Name": "Ghana", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 233, "UnitId": 212, "CountryTimes": [{"Id": 1821, "CountryCode": "GH", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1822, "CountryCode": "GH", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1823, "CountryCode": "GH", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1824, "CountryCode": "GH", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1825, "CountryCode": "GH", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 1826, "CountryCode": "GH", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T17:28:30"}, {"Id": 78, "Enabled": true, "CountryCode": "GI", "Name": "Gibraltar", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 350, "UnitId": 212, "CountryTimes": [{"Id": 1827, "CountryCode": "GI", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1828, "CountryCode": "GI", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1829, "CountryCode": "GI", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1830, "CountryCode": "GI", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1831, "CountryCode": "GI", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1832, "CountryCode": "GI", "DayOfWeek": 6, "OpenTime": "2015-02-16T09:30:00", "CloseTime": "2015-02-16T13:00:00", "SameDayDeadline": "2015-02-16T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 79, "Enabled": true, "CountryCode": "GL", "Name": "Greenland", "TimeZone": 73, "CurrencyId": 7, "DialingCode": 299, "UnitId": 189, "CountryTimes": [{"Id": 1833, "CountryCode": "GL", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1834, "CountryCode": "GL", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1835, "CountryCode": "GL", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1836, "CountryCode": "GL", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1837, "CountryCode": "GL", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1838, "CountryCode": "GL", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 80, "Enabled": false, "CountryCode": "GM", "Name": "Gambia", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 220, "UnitId": 199, "CountryTimes": [{"Id": 1839, "CountryCode": "GM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1840, "CountryCode": "GM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1841, "CountryCode": "GM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1842, "CountryCode": "GM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1843, "CountryCode": "GM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1844, "CountryCode": "GM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T13:04:33"}, {"Id": 81, "Enabled": false, "CountryCode": "GN", "Name": "Guinea", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 224, "UnitId": 212, "CountryTimes": [{"Id": 1845, "CountryCode": "GN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1846, "CountryCode": "GN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1847, "CountryCode": "GN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1848, "CountryCode": "GN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1849, "CountryCode": "GN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1850, "CountryCode": "GN", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 82, "Enabled": true, "CountryCode": "GP", "Name": "Guadeloupe", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 590, "UnitId": 193, "CountryTimes": [{"Id": 1851, "CountryCode": "GP", "DayOfWeek": 1, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T18:30:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1852, "CountryCode": "GP", "DayOfWeek": 2, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T18:30:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1853, "CountryCode": "GP", "DayOfWeek": 3, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T18:30:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1854, "CountryCode": "GP", "DayOfWeek": 4, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T18:30:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1855, "CountryCode": "GP", "DayOfWeek": 5, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T18:30:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 1856, "CountryCode": "GP", "DayOfWeek": 6, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 83, "Enabled": false, "CountryCode": "GQ", "Name": "Equatorial Guinea", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 240, "UnitId": 212, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 84, "Enabled": true, "CountryCode": "GR", "Name": "Greece", "TimeZone": 130, "CurrencyId": 7, "DialingCode": 30, "UnitId": 199, "CountryTimes": [{"Id": 1857, "CountryCode": "GR", "DayOfWeek": 1, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 1858, "CountryCode": "GR", "DayOfWeek": 2, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 1859, "CountryCode": "GR", "DayOfWeek": 3, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 1860, "CountryCode": "GR", "DayOfWeek": 4, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 1861, "CountryCode": "GR", "DayOfWeek": 5, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 1862, "CountryCode": "GR", "DayOfWeek": 6, "OpenTime": "2023-10-01T09:00:00", "CloseTime": "2023-10-01T21:00:00", "SameDayDeadline": "2023-10-01T11:00:00"}, {"Id": 2803, "CountryCode": "GR", "DayOfWeek": 0, "OpenTime": "2023-09-18T00:00:00", "CloseTime": "2023-09-18T00:00:00", "SameDayDeadline": "2023-09-18T00:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 85, "Enabled": false, "CountryCode": "GS", "Name": "Sth. Georgia & Sandwich Is.", "TimeZone": 75, "CurrencyId": 7, "DialingCode": 0, "UnitId": 212, "CountryTimes": [{"Id": 1863, "CountryCode": "GS", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1864, "CountryCode": "GS", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1865, "CountryCode": "GS", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1866, "CountryCode": "GS", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1867, "CountryCode": "GS", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1868, "CountryCode": "GS", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 86, "Enabled": true, "CountryCode": "GT", "Name": "Guatemala", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 502, "UnitId": 213, "CountryTimes": [{"Id": 1869, "CountryCode": "GT", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1870, "CountryCode": "GT", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1871, "CountryCode": "GT", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1872, "CountryCode": "GT", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1873, "CountryCode": "GT", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1874, "CountryCode": "GT", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2023-01-02T16:40:19"}, {"Id": 87, "Enabled": true, "CountryCode": "GU", "Name": "Guam", "TimeZone": 275, "CurrencyId": 7, "DialingCode": 671, "UnitId": 213, "CountryTimes": [{"Id": 1875, "CountryCode": "GU", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1876, "CountryCode": "GU", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1877, "CountryCode": "GU", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1878, "CountryCode": "GU", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1879, "CountryCode": "GU", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1880, "CountryCode": "GU", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 88, "Enabled": false, "CountryCode": "GW", "Name": "Guinea-Bissau", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 245, "UnitId": 212, "CountryTimes": [{"Id": 1881, "CountryCode": "GW", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1882, "CountryCode": "GW", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1883, "CountryCode": "GW", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1884, "CountryCode": "GW", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1885, "CountryCode": "GW", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1886, "CountryCode": "GW", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 89, "Enabled": true, "CountryCode": "GY", "Name": "Guyana", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 1887, "CountryCode": "GY", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1888, "CountryCode": "GY", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1889, "CountryCode": "GY", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1890, "CountryCode": "GY", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1891, "CountryCode": "GY", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1892, "CountryCode": "GY", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 90, "Enabled": true, "CountryCode": "HK", "Name": "Hong Kong", "TimeZone": 210, "CurrencyId": 30, "DialingCode": 852, "UnitId": 222, "CountryTimes": [{"Id": 1893, "CountryCode": "HK", "DayOfWeek": 1, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1894, "CountryCode": "HK", "DayOfWeek": 2, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1895, "CountryCode": "HK", "DayOfWeek": 3, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1896, "CountryCode": "HK", "DayOfWeek": 4, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1897, "CountryCode": "HK", "DayOfWeek": 5, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 1898, "CountryCode": "HK", "DayOfWeek": 6, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 91, "Enabled": false, "CountryCode": "HM", "Name": "Heard & Mc Donald Is.", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 692, "UnitId": 212, "CountryTimes": [{"Id": 1899, "CountryCode": "HM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1900, "CountryCode": "HM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1901, "CountryCode": "HM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1902, "CountryCode": "HM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1903, "CountryCode": "HM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1904, "CountryCode": "HM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 92, "Enabled": true, "CountryCode": "HN", "Name": "Honduras", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 504, "UnitId": 213, "CountryTimes": [{"Id": 1905, "CountryCode": "HN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1906, "CountryCode": "HN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1907, "CountryCode": "HN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1908, "CountryCode": "HN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1909, "CountryCode": "HN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1910, "CountryCode": "HN", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 93, "Enabled": true, "CountryCode": "HR", "Name": "Croatia", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 385, "UnitId": 199, "CountryTimes": [{"Id": 1911, "CountryCode": "HR", "DayOfWeek": 1, "OpenTime": "2023-09-18T07:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1912, "CountryCode": "HR", "DayOfWeek": 2, "OpenTime": "2023-09-18T07:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1913, "CountryCode": "HR", "DayOfWeek": 3, "OpenTime": "2023-09-18T07:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1914, "CountryCode": "HR", "DayOfWeek": 4, "OpenTime": "2023-09-18T07:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1915, "CountryCode": "HR", "DayOfWeek": 5, "OpenTime": "2023-09-18T07:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1916, "CountryCode": "HR", "DayOfWeek": 6, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T14:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 94, "Enabled": false, "CountryCode": "HT", "Name": "Haiti", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 509, "UnitId": 213, "CountryTimes": [{"Id": 1917, "CountryCode": "HT", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1918, "CountryCode": "HT", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1919, "CountryCode": "HT", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1920, "CountryCode": "HT", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1921, "CountryCode": "HT", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1922, "CountryCode": "HT", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2023-08-07T17:22:26"}, {"Id": 95, "Enabled": true, "CountryCode": "HU", "Name": "Hungary", "TimeZone": 95, "CurrencyId": 9, "DialingCode": 36, "UnitId": 196, "CountryTimes": [{"Id": 1923, "CountryCode": "HU", "DayOfWeek": 1, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T17:00:00", "SameDayDeadline": "2015-11-18T13:00:00"}, {"Id": 1924, "CountryCode": "HU", "DayOfWeek": 2, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T17:00:00", "SameDayDeadline": "2015-11-18T13:00:00"}, {"Id": 1925, "CountryCode": "HU", "DayOfWeek": 3, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T17:00:00", "SameDayDeadline": "2015-11-18T13:00:00"}, {"Id": 1926, "CountryCode": "HU", "DayOfWeek": 4, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T17:00:00", "SameDayDeadline": "2015-11-18T13:00:00"}, {"Id": 1927, "CountryCode": "HU", "DayOfWeek": 5, "OpenTime": "2016-07-04T09:00:00", "CloseTime": "2016-07-04T17:00:00", "SameDayDeadline": "2016-07-04T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 97, "Enabled": true, "CountryCode": "ID", "Name": "Indonesia", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 62, "UnitId": 220, "CountryTimes": [{"Id": 1929, "CountryCode": "ID", "DayOfWeek": 1, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 1930, "CountryCode": "ID", "DayOfWeek": 2, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 1931, "CountryCode": "ID", "DayOfWeek": 3, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 1932, "CountryCode": "ID", "DayOfWeek": 4, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 1933, "CountryCode": "ID", "DayOfWeek": 5, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 1934, "CountryCode": "ID", "DayOfWeek": 6, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T05:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 98, "Enabled": true, "CountryCode": "IE", "Name": "Ireland", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 353, "UnitId": 212, "CountryTimes": [{"Id": 1935, "CountryCode": "IE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1936, "CountryCode": "IE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1937, "CountryCode": "IE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1938, "CountryCode": "IE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1939, "CountryCode": "IE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 1940, "CountryCode": "IE", "DayOfWeek": 6, "OpenTime": "2015-02-16T09:00:00", "CloseTime": "2015-02-16T17:00:00", "SameDayDeadline": "2015-02-16T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 99, "Enabled": true, "CountryCode": "IL", "Name": "Israel", "TimeZone": 135, "CurrencyId": 7, "DialingCode": 972, "UnitId": 198, "CountryTimes": [{"Id": 1941, "CountryCode": "IL", "DayOfWeek": 1, "OpenTime": "2022-11-29T09:00:00", "CloseTime": "2022-11-29T18:00:00", "SameDayDeadline": "2022-11-29T16:00:00"}, {"Id": 1942, "CountryCode": "IL", "DayOfWeek": 2, "OpenTime": "2022-11-29T09:00:00", "CloseTime": "2022-11-29T18:00:00", "SameDayDeadline": "2022-11-29T16:00:00"}, {"Id": 1943, "CountryCode": "IL", "DayOfWeek": 3, "OpenTime": "2022-11-29T09:00:00", "CloseTime": "2022-11-29T18:00:00", "SameDayDeadline": "2022-11-29T16:00:00"}, {"Id": 1944, "CountryCode": "IL", "DayOfWeek": 4, "OpenTime": "2022-11-29T09:00:00", "CloseTime": "2022-11-29T18:00:00", "SameDayDeadline": "2022-11-29T16:00:00"}, {"Id": 1945, "CountryCode": "IL", "DayOfWeek": 5, "OpenTime": "2022-11-29T09:00:00", "CloseTime": "2022-11-29T14:00:00", "SameDayDeadline": "2022-11-29T10:00:00"}, {"Id": 1946, "CountryCode": "IL", "DayOfWeek": 0, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:00:00", "SameDayDeadline": "2015-11-18T16:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 100, "Enabled": true, "CountryCode": "IN", "Name": "India", "TimeZone": 190, "CurrencyId": 7, "DialingCode": 91, "UnitId": 276, "CountryTimes": [{"Id": 1953, "CountryCode": "IN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1954, "CountryCode": "IN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1955, "CountryCode": "IN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1956, "CountryCode": "IN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1957, "CountryCode": "IN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 3057, "CountryCode": "IN", "DayOfWeek": 6, "OpenTime": "2004-10-27T09:00:00", "CloseTime": "2004-10-27T18:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3058, "CountryCode": "IN", "DayOfWeek": 6, "OpenTime": "2004-10-27T09:00:00", "CloseTime": "2004-10-27T18:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3059, "CountryCode": "IN", "DayOfWeek": 6, "OpenTime": "2004-10-27T09:00:00", "CloseTime": "2004-10-27T18:00:00", "SameDayDeadline": "2004-10-27T12:00:00"}], "LastUpdate": "2018-04-23T09:40:34.403"}, {"Id": 101, "Enabled": false, "CountryCode": "IO", "Name": "British Indian Ocean Territory", "TimeZone": 195, "CurrencyId": 7, "DialingCode": 246, "UnitId": 212, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 102, "Enabled": false, "CountryCode": "IQ", "Name": "Iraq", "TimeZone": 158, "CurrencyId": 7, "DialingCode": 964, "UnitId": 199, "CountryTimes": [{"Id": 1959, "CountryCode": "IQ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1960, "CountryCode": "IQ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1961, "CountryCode": "IQ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1962, "CountryCode": "IQ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1963, "CountryCode": "IQ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1964, "CountryCode": "IQ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T13:04:40"}, {"Id": 103, "Enabled": true, "CountryCode": "IR", "Name": "Iran", "TimeZone": 160, "CurrencyId": 7, "DialingCode": 98, "UnitId": 199, "CountryTimes": [{"Id": 1965, "CountryCode": "IR", "DayOfWeek": 1, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1966, "CountryCode": "IR", "DayOfWeek": 2, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1967, "CountryCode": "IR", "DayOfWeek": 3, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1968, "CountryCode": "IR", "DayOfWeek": 4, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1969, "CountryCode": "IR", "DayOfWeek": 5, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T12:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 1970, "CountryCode": "IR", "DayOfWeek": 6, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}, {"Id": 2865, "CountryCode": "IR", "DayOfWeek": 0, "OpenTime": "2023-09-15T08:00:00", "CloseTime": "2023-09-15T20:00:00", "SameDayDeadline": null}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 104, "Enabled": true, "CountryCode": "IS", "Name": "Iceland", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 354, "UnitId": 189, "CountryTimes": [{"Id": 1971, "CountryCode": "IS", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1972, "CountryCode": "IS", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1973, "CountryCode": "IS", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1974, "CountryCode": "IS", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1975, "CountryCode": "IS", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 1976, "CountryCode": "IS", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 105, "Enabled": true, "CountryCode": "IT", "Name": "Italy", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 39, "UnitId": 200, "CountryTimes": [{"Id": 3687, "CountryCode": "IT", "DayOfWeek": 1, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3688, "CountryCode": "IT", "DayOfWeek": 2, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3689, "CountryCode": "IT", "DayOfWeek": 3, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3690, "CountryCode": "IT", "DayOfWeek": 4, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3691, "CountryCode": "IT", "DayOfWeek": 5, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3692, "CountryCode": "IT", "DayOfWeek": 6, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T19:00:00", "SameDayDeadline": "2023-10-13T17:30:00"}, {"Id": 3803, "CountryCode": "IT", "DayOfWeek": 0, "OpenTime": "2023-10-13T08:30:00", "CloseTime": "2023-10-13T12:30:00", "SameDayDeadline": "2023-10-13T12:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 106, "Enabled": false, "CountryCode": "JE", "Name": "Channel Is. (Jersey)", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 44, "UnitId": 212, "CountryTimes": [{"Id": 2845, "CountryCode": "JE", "DayOfWeek": 1, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T16:00:00", "SameDayDeadline": "2002-10-28T11:30:00"}, {"Id": 2846, "CountryCode": "JE", "DayOfWeek": 2, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T16:00:00", "SameDayDeadline": "2002-10-28T11:30:00"}, {"Id": 2847, "CountryCode": "JE", "DayOfWeek": 3, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T16:00:00", "SameDayDeadline": "2002-10-28T11:30:00"}, {"Id": 2848, "CountryCode": "JE", "DayOfWeek": 4, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T16:00:00", "SameDayDeadline": "2002-10-28T11:30:00"}, {"Id": 2849, "CountryCode": "JE", "DayOfWeek": 5, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T16:00:00", "SameDayDeadline": "2002-10-28T11:30:00"}, {"Id": 2850, "CountryCode": "JE", "DayOfWeek": 6, "OpenTime": "2002-10-28T09:00:00", "CloseTime": "2002-10-28T12:00:00", "SameDayDeadline": "2002-10-28T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 107, "Enabled": true, "CountryCode": "JM", "Name": "Jamaica", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 1983, "CountryCode": "JM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T15:00:00"}, {"Id": 1984, "CountryCode": "JM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T15:00:00"}, {"Id": 1985, "CountryCode": "JM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T15:00:00"}, {"Id": 1986, "CountryCode": "JM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T15:00:00"}, {"Id": 1987, "CountryCode": "JM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T15:00:00"}, {"Id": 1988, "CountryCode": "JM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 108, "Enabled": true, "CountryCode": "JO", "Name": "Jordan", "TimeZone": 130, "CurrencyId": 7, "DialingCode": 962, "UnitId": 199, "CountryTimes": [{"Id": 1989, "CountryCode": "JO", "DayOfWeek": 1, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1990, "CountryCode": "JO", "DayOfWeek": 2, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1991, "CountryCode": "JO", "DayOfWeek": 3, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 1992, "CountryCode": "JO", "DayOfWeek": 4, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 2810, "CountryCode": "JO", "DayOfWeek": 6, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}, {"Id": 2811, "CountryCode": "JO", "DayOfWeek": 0, "OpenTime": "2019-09-26T09:00:00", "CloseTime": "2019-09-26T20:00:00", "SameDayDeadline": "2019-09-26T15:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 109, "Enabled": true, "CountryCode": "JP", "Name": "Japan", "TimeZone": 235, "CurrencyId": 7, "DialingCode": 81, "UnitId": 201, "CountryTimes": [{"Id": 1995, "CountryCode": "JP", "DayOfWeek": 1, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}, {"Id": 1996, "CountryCode": "JP", "DayOfWeek": 2, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}, {"Id": 1997, "CountryCode": "JP", "DayOfWeek": 3, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}, {"Id": 1998, "CountryCode": "JP", "DayOfWeek": 4, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}, {"Id": 1999, "CountryCode": "JP", "DayOfWeek": 5, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}, {"Id": 3721, "CountryCode": "JP", "DayOfWeek": 6, "OpenTime": "2023-11-17T09:00:00", "CloseTime": "2023-11-17T18:00:00", "SameDayDeadline": "2023-11-17T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 110, "Enabled": true, "CountryCode": "KE", "Name": "Kenya", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 254, "UnitId": 212, "CountryTimes": [{"Id": 3781, "CountryCode": "KE", "DayOfWeek": 1, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}, {"Id": 3782, "CountryCode": "KE", "DayOfWeek": 2, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}, {"Id": 3783, "CountryCode": "KE", "DayOfWeek": 3, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}, {"Id": 3784, "CountryCode": "KE", "DayOfWeek": 4, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}, {"Id": 3785, "CountryCode": "KE", "DayOfWeek": 5, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}, {"Id": 3786, "CountryCode": "KE", "DayOfWeek": 6, "OpenTime": "2020-05-11T09:00:00", "CloseTime": "2020-05-11T14:00:00", "SameDayDeadline": "2020-05-11T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 111, "Enabled": false, "CountryCode": "KG", "Name": "Kyrgyzstan", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 996, "UnitId": 207, "CountryTimes": [{"Id": 2007, "CountryCode": "KG", "DayOfWeek": 1, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2008, "CountryCode": "KG", "DayOfWeek": 2, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2009, "CountryCode": "KG", "DayOfWeek": 3, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2010, "CountryCode": "KG", "DayOfWeek": 4, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2011, "CountryCode": "KG", "DayOfWeek": 5, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2012, "CountryCode": "KG", "DayOfWeek": 6, "OpenTime": "1900-01-01T10:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2017-02-09T13:20:39.957"}, {"Id": 112, "Enabled": true, "CountryCode": "KH", "Name": "Cambodia", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 855, "UnitId": 220, "CountryTimes": [{"Id": 2013, "CountryCode": "KH", "DayOfWeek": 1, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2014, "CountryCode": "KH", "DayOfWeek": 2, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2015, "CountryCode": "KH", "DayOfWeek": 3, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2016, "CountryCode": "KH", "DayOfWeek": 4, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2017, "CountryCode": "KH", "DayOfWeek": 5, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2018, "CountryCode": "KH", "DayOfWeek": 6, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T05:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 113, "Enabled": false, "CountryCode": "KI", "Name": "Kiribati", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 686, "UnitId": 185, "CountryTimes": [{"Id": 2019, "CountryCode": "KI", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2020, "CountryCode": "KI", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2021, "CountryCode": "KI", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2022, "CountryCode": "KI", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2023, "CountryCode": "KI", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2024, "CountryCode": "KI", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 114, "Enabled": false, "CountryCode": "KM", "Name": "Comoros", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 269, "UnitId": 212, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 115, "Enabled": false, "CountryCode": "KN", "Name": "St. Kitts and Nevis", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 869, "UnitId": 185, "CountryTimes": [{"Id": 2025, "CountryCode": "KN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2026, "CountryCode": "KN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2027, "CountryCode": "KN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2028, "CountryCode": "KN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2029, "CountryCode": "KN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2030, "CountryCode": "KN", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-11-10T10:29:34"}, {"Id": 116, "Enabled": false, "CountryCode": "KP", "Name": "Korea (North)", "TimeZone": 230, "CurrencyId": 7, "DialingCode": 850, "UnitId": 213, "CountryTimes": [{"Id": 2031, "CountryCode": "KP", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2032, "CountryCode": "KP", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2033, "CountryCode": "KP", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2034, "CountryCode": "KP", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2035, "CountryCode": "KP", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2036, "CountryCode": "KP", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2822, "CountryCode": "KP", "DayOfWeek": 0, "OpenTime": "2002-07-22T08:00:00", "CloseTime": "2002-07-22T18:00:00", "SameDayDeadline": "2002-07-22T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 117, "Enabled": true, "CountryCode": "KR", "Name": "Korea (South)", "TimeZone": 230, "CurrencyId": 7, "DialingCode": 82, "UnitId": 213, "CountryTimes": [{"Id": 2037, "CountryCode": "KR", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2038, "CountryCode": "KR", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2039, "CountryCode": "KR", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2040, "CountryCode": "KR", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2041, "CountryCode": "KR", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2042, "CountryCode": "KR", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2864, "CountryCode": "KR", "DayOfWeek": 0, "OpenTime": "2002-11-15T08:00:00", "CloseTime": "2002-11-15T18:00:00", "SameDayDeadline": "2002-11-15T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 118, "Enabled": true, "CountryCode": "KW", "Name": "Kuwait", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 965, "UnitId": 212, "CountryTimes": [{"Id": 2043, "CountryCode": "KW", "DayOfWeek": 1, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2044, "CountryCode": "KW", "DayOfWeek": 2, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2045, "CountryCode": "KW", "DayOfWeek": 3, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2046, "CountryCode": "KW", "DayOfWeek": 4, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2048, "CountryCode": "KW", "DayOfWeek": 6, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 119, "Enabled": true, "CountryCode": "KY", "Name": "Cayman Islands", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 2049, "CountryCode": "KY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2050, "CountryCode": "KY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2051, "CountryCode": "KY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2052, "CountryCode": "KY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2053, "CountryCode": "KY", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2054, "CountryCode": "KY", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 120, "Enabled": true, "CountryCode": "KZ", "Name": "Kazakhstan", "TimeZone": 201, "CurrencyId": 7, "DialingCode": 7, "UnitId": 207, "CountryTimes": [{"Id": 2055, "CountryCode": "KZ", "DayOfWeek": 1, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2056, "CountryCode": "KZ", "DayOfWeek": 2, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2057, "CountryCode": "KZ", "DayOfWeek": 3, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2058, "CountryCode": "KZ", "DayOfWeek": 4, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2059, "CountryCode": "KZ", "DayOfWeek": 5, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2060, "CountryCode": "KZ", "DayOfWeek": 6, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T12:45:00"}, {"Id": 3801, "CountryCode": "KZ", "DayOfWeek": 0, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T12:45:00"}], "LastUpdate": "2018-11-08T12:18:12.327"}, {"Id": 121, "Enabled": false, "CountryCode": "LA", "Name": "Laos", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 856, "UnitId": 212, "CountryTimes": [{"Id": 2061, "CountryCode": "LA", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2062, "CountryCode": "LA", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2063, "CountryCode": "LA", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2064, "CountryCode": "LA", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2065, "CountryCode": "LA", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2066, "CountryCode": "LA", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 122, "Enabled": true, "CountryCode": "LB", "Name": "Lebanon", "TimeZone": 130, "CurrencyId": 7, "DialingCode": 961, "UnitId": 199, "CountryTimes": [{"Id": 2067, "CountryCode": "LB", "DayOfWeek": 1, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 2068, "CountryCode": "LB", "DayOfWeek": 2, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 2069, "CountryCode": "LB", "DayOfWeek": 3, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 2070, "CountryCode": "LB", "DayOfWeek": 4, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 2071, "CountryCode": "LB", "DayOfWeek": 5, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T15:00:00"}, {"Id": 2072, "CountryCode": "LB", "DayOfWeek": 6, "OpenTime": "2023-10-01T08:00:00", "CloseTime": "2023-10-01T20:00:00", "SameDayDeadline": "2023-10-01T11:00:00"}, {"Id": 2812, "CountryCode": "LB", "DayOfWeek": 0, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T15:00:00", "SameDayDeadline": "2023-09-14T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 123, "Enabled": true, "CountryCode": "LC", "Name": "Saint Lucia", "TimeZone": 56, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 2073, "CountryCode": "LC", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2074, "CountryCode": "LC", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2075, "CountryCode": "LC", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2076, "CountryCode": "LC", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2077, "CountryCode": "LC", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2078, "CountryCode": "LC", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 124, "Enabled": true, "CountryCode": "LI", "Name": "Liechtenstein", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 423, "UnitId": 187, "CountryTimes": [{"Id": 3731, "CountryCode": "LI", "DayOfWeek": 1, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3732, "CountryCode": "LI", "DayOfWeek": 2, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3733, "CountryCode": "LI", "DayOfWeek": 3, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3734, "CountryCode": "LI", "DayOfWeek": 4, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3735, "CountryCode": "LI", "DayOfWeek": 5, "OpenTime": "2020-08-03T08:00:00", "CloseTime": "2020-08-03T12:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3736, "CountryCode": "LI", "DayOfWeek": 6, "OpenTime": "2016-02-15T08:00:00", "CloseTime": "2016-02-15T13:00:00", "SameDayDeadline": "2016-02-15T13:00:00"}, {"Id": 3787, "CountryCode": "LI", "DayOfWeek": 1, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3788, "CountryCode": "LI", "DayOfWeek": 2, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3789, "CountryCode": "LI", "DayOfWeek": 3, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3790, "CountryCode": "LI", "DayOfWeek": 4, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}, {"Id": 3791, "CountryCode": "LI", "DayOfWeek": 5, "OpenTime": "2020-08-03T13:00:00", "CloseTime": "2020-08-03T17:00:00", "SameDayDeadline": "2020-08-03T15:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 125, "Enabled": true, "CountryCode": "LK", "Name": "Sri Lanka", "TimeZone": 200, "CurrencyId": 7, "DialingCode": 0, "UnitId": 212, "CountryTimes": [{"Id": 2085, "CountryCode": "LK", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2086, "CountryCode": "LK", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2087, "CountryCode": "LK", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2088, "CountryCode": "LK", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2089, "CountryCode": "LK", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2090, "CountryCode": "LK", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 126, "Enabled": false, "CountryCode": "LR", "Name": "Liberia", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 231, "UnitId": 199, "CountryTimes": [{"Id": 2091, "CountryCode": "LR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2092, "CountryCode": "LR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2093, "CountryCode": "LR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2094, "CountryCode": "LR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2095, "CountryCode": "LR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2096, "CountryCode": "LR", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 127, "Enabled": false, "CountryCode": "LS", "Name": "Lesotho", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 266, "UnitId": 214, "CountryTimes": [{"Id": 2097, "CountryCode": "LS", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2098, "CountryCode": "LS", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2099, "CountryCode": "LS", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2100, "CountryCode": "LS", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2101, "CountryCode": "LS", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2102, "CountryCode": "LS", "DayOfWeek": 6, "OpenTime": "2015-10-02T09:00:00", "CloseTime": "2015-10-02T16:00:00", "SameDayDeadline": "2015-10-02T10:30:00"}], "LastUpdate": "2023-08-07T17:17:15"}, {"Id": 128, "Enabled": true, "CountryCode": "LT", "Name": "Lithuania", "TimeZone": 125, "CurrencyId": 12, "DialingCode": 370, "UnitId": 190, "CountryTimes": [{"Id": 2103, "CountryCode": "LT", "DayOfWeek": 1, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2104, "CountryCode": "LT", "DayOfWeek": 2, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2105, "CountryCode": "LT", "DayOfWeek": 3, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2106, "CountryCode": "LT", "DayOfWeek": 4, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2107, "CountryCode": "LT", "DayOfWeek": 5, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2108, "CountryCode": "LT", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 129, "Enabled": true, "CountryCode": "LU", "Name": "Luxembourg", "TimeZone": 105, "CurrencyId": 7, "DialingCode": 352, "UnitId": 277, "CountryTimes": [{"Id": 2109, "CountryCode": "LU", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2110, "CountryCode": "LU", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2111, "CountryCode": "LU", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2112, "CountryCode": "LU", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2113, "CountryCode": "LU", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2114, "CountryCode": "LU", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2018-09-17T08:55:08.683"}, {"Id": 130, "Enabled": true, "CountryCode": "LV", "Name": "Latvia", "TimeZone": 125, "CurrencyId": 13, "DialingCode": 371, "UnitId": 190, "CountryTimes": [{"Id": 2115, "CountryCode": "LV", "DayOfWeek": 1, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2116, "CountryCode": "LV", "DayOfWeek": 2, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2117, "CountryCode": "LV", "DayOfWeek": 3, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2118, "CountryCode": "LV", "DayOfWeek": 4, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2119, "CountryCode": "LV", "DayOfWeek": 5, "OpenTime": "2015-11-19T09:00:00", "CloseTime": "2015-11-19T18:00:00", "SameDayDeadline": "2015-11-19T16:00:00"}, {"Id": 2120, "CountryCode": "LV", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 131, "Enabled": false, "CountryCode": "LY", "Name": "Lybia", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 218, "UnitId": 199, "CountryTimes": [{"Id": 2121, "CountryCode": "LY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2122, "CountryCode": "LY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2123, "CountryCode": "LY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2124, "CountryCode": "LY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2126, "CountryCode": "LY", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T21:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}, {"Id": 2813, "CountryCode": "LY", "DayOfWeek": 0, "OpenTime": "2002-07-22T09:00:00", "CloseTime": "2002-07-22T21:00:00", "SameDayDeadline": "2002-07-22T10:00:00"}], "LastUpdate": "2023-08-07T17:15:43"}, {"Id": 132, "Enabled": true, "CountryCode": "MA", "Name": "Morocco", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 212, "UnitId": 199, "CountryTimes": [{"Id": 2127, "CountryCode": "MA", "DayOfWeek": 1, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T17:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 2128, "CountryCode": "MA", "DayOfWeek": 2, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T17:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 2129, "CountryCode": "MA", "DayOfWeek": 3, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T17:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 2131, "CountryCode": "MA", "DayOfWeek": 5, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T17:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 3087, "CountryCode": "MA", "DayOfWeek": 4, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T17:00:00", "SameDayDeadline": "2024-02-26T15:00:00"}, {"Id": 3089, "CountryCode": "MA", "DayOfWeek": 6, "OpenTime": "2024-02-26T08:00:00", "CloseTime": "2024-02-26T14:00:00", "SameDayDeadline": "2024-02-26T11:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 133, "Enabled": true, "CountryCode": "MC", "Name": "Monaco", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 377, "UnitId": 193, "CountryTimes": [{"Id": 2133, "CountryCode": "MC", "DayOfWeek": 1, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2134, "CountryCode": "MC", "DayOfWeek": 2, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2135, "CountryCode": "MC", "DayOfWeek": 3, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2136, "CountryCode": "MC", "DayOfWeek": 4, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2137, "CountryCode": "MC", "DayOfWeek": 5, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2138, "CountryCode": "MC", "DayOfWeek": 6, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}, {"Id": 3747, "CountryCode": "MC", "DayOfWeek": 0, "OpenTime": "2023-05-09T00:00:00", "CloseTime": "2023-05-09T00:00:00", "SameDayDeadline": "2023-05-09T00:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 134, "Enabled": true, "CountryCode": "MD", "Name": "Moldova", "TimeZone": 125, "CurrencyId": 7, "DialingCode": 373, "UnitId": 207, "CountryTimes": [{"Id": 2139, "CountryCode": "MD", "DayOfWeek": 1, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2140, "CountryCode": "MD", "DayOfWeek": 2, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2141, "CountryCode": "MD", "DayOfWeek": 3, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2142, "CountryCode": "MD", "DayOfWeek": 4, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2143, "CountryCode": "MD", "DayOfWeek": 5, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2144, "CountryCode": "MD", "DayOfWeek": 6, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T12:45:00"}, {"Id": 3800, "CountryCode": "MD", "DayOfWeek": 0, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T12:45:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 135, "Enabled": false, "CountryCode": "MG", "Name": "Madagascar", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 261, "UnitId": 199, "CountryTimes": [{"Id": 3708, "CountryCode": "MG", "DayOfWeek": 1, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:30:00", "SameDayDeadline": "2014-11-17T11:30:00"}, {"Id": 3709, "CountryCode": "MG", "DayOfWeek": 2, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:30:00", "SameDayDeadline": "2014-11-17T11:30:00"}, {"Id": 3710, "CountryCode": "MG", "DayOfWeek": 3, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:30:00", "SameDayDeadline": "2014-11-17T11:30:00"}, {"Id": 3711, "CountryCode": "MG", "DayOfWeek": 4, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:30:00", "SameDayDeadline": "2014-11-17T11:30:00"}, {"Id": 3712, "CountryCode": "MG", "DayOfWeek": 5, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:30:00", "SameDayDeadline": "2014-11-17T11:30:00"}, {"Id": 3713, "CountryCode": "MG", "DayOfWeek": 6, "OpenTime": "2014-11-17T07:00:00", "CloseTime": "2014-11-17T16:00:00", "SameDayDeadline": "2014-11-17T11:30:00"}], "LastUpdate": "2023-08-07T17:14:27"}, {"Id": 136, "Enabled": false, "CountryCode": "MH", "Name": "<PERSON>.", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 692, "UnitId": 213, "CountryTimes": [{"Id": 2151, "CountryCode": "MH", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2152, "CountryCode": "MH", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2153, "CountryCode": "MH", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2154, "CountryCode": "MH", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2155, "CountryCode": "MH", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2156, "CountryCode": "MH", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 138, "Enabled": true, "CountryCode": "MK", "Name": "North Macedonia", "TimeZone": 100, "CurrencyId": 7, "DialingCode": 389, "UnitId": 199, "CountryTimes": [{"Id": 2157, "CountryCode": "MK", "DayOfWeek": 1, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2158, "CountryCode": "MK", "DayOfWeek": 2, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2159, "CountryCode": "MK", "DayOfWeek": 3, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2160, "CountryCode": "MK", "DayOfWeek": 4, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2161, "CountryCode": "MK", "DayOfWeek": 5, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2162, "CountryCode": "MK", "DayOfWeek": 6, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2023-09-01T13:13:27"}, {"Id": 139, "Enabled": false, "CountryCode": "ML", "Name": "<PERSON><PERSON>", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 223, "UnitId": 214, "CountryTimes": [{"Id": 2163, "CountryCode": "ML", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2164, "CountryCode": "ML", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2165, "CountryCode": "ML", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2166, "CountryCode": "ML", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2167, "CountryCode": "ML", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2168, "CountryCode": "ML", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 140, "Enabled": true, "CountryCode": "MM", "Name": "Myanmar (Burma)", "TimeZone": 203, "CurrencyId": 7, "DialingCode": 95, "UnitId": 220, "CountryTimes": [{"Id": 2169, "CountryCode": "MM", "DayOfWeek": 1, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T10:00:00"}, {"Id": 2170, "CountryCode": "MM", "DayOfWeek": 2, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T10:00:00"}, {"Id": 2171, "CountryCode": "MM", "DayOfWeek": 3, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T10:00:00"}, {"Id": 2172, "CountryCode": "MM", "DayOfWeek": 4, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T10:00:00"}, {"Id": 2173, "CountryCode": "MM", "DayOfWeek": 5, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T10:00:00"}, {"Id": 2174, "CountryCode": "MM", "DayOfWeek": 6, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T16:00:00", "SameDayDeadline": "2015-09-29T05:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 141, "Enabled": false, "CountryCode": "MN", "Name": "Mongolia", "TimeZone": 227, "CurrencyId": 7, "DialingCode": 976, "UnitId": 204, "CountryTimes": [{"Id": 2175, "CountryCode": "MN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2176, "CountryCode": "MN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2177, "CountryCode": "MN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2178, "CountryCode": "MN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2179, "CountryCode": "MN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2180, "CountryCode": "MN", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 142, "Enabled": true, "CountryCode": "MO", "Name": "Macau", "TimeZone": 215, "CurrencyId": 7, "DialingCode": 853, "UnitId": 222, "CountryTimes": [{"Id": 2181, "CountryCode": "MO", "DayOfWeek": 1, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2182, "CountryCode": "MO", "DayOfWeek": 2, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2183, "CountryCode": "MO", "DayOfWeek": 3, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2184, "CountryCode": "MO", "DayOfWeek": 4, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2185, "CountryCode": "MO", "DayOfWeek": 5, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2186, "CountryCode": "MO", "DayOfWeek": 6, "OpenTime": "2015-09-29T10:00:00", "CloseTime": "2015-09-29T19:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2018-04-12T08:21:48.403"}, {"Id": 143, "Enabled": false, "CountryCode": "MP", "Name": "Northern Mariana Is.", "TimeZone": 275, "CurrencyId": 7, "DialingCode": 1670, "UnitId": 204, "CountryTimes": [{"Id": 2187, "CountryCode": "MP", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2188, "CountryCode": "MP", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2189, "CountryCode": "MP", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2190, "CountryCode": "MP", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2191, "CountryCode": "MP", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2192, "CountryCode": "MP", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 144, "Enabled": true, "CountryCode": "MQ", "Name": "Martinique", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 596, "UnitId": 193, "CountryTimes": [{"Id": 2193, "CountryCode": "MQ", "DayOfWeek": 1, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2194, "CountryCode": "MQ", "DayOfWeek": 2, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2195, "CountryCode": "MQ", "DayOfWeek": 3, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2196, "CountryCode": "MQ", "DayOfWeek": 4, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2197, "CountryCode": "MQ", "DayOfWeek": 5, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T19:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2198, "CountryCode": "MQ", "DayOfWeek": 6, "OpenTime": "2023-05-09T09:00:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2024-02-16T12:35:56"}, {"Id": 145, "Enabled": false, "CountryCode": "MR", "Name": "<PERSON><PERSON><PERSON><PERSON>", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 222, "UnitId": 214, "CountryTimes": [{"Id": 2199, "CountryCode": "MR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2200, "CountryCode": "MR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2201, "CountryCode": "MR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2202, "CountryCode": "MR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2203, "CountryCode": "MR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2204, "CountryCode": "MR", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 146, "Enabled": false, "CountryCode": "MS", "Name": "Montserrat", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 664, "UnitId": 212, "CountryTimes": [{"Id": 2205, "CountryCode": "MS", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2206, "CountryCode": "MS", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2207, "CountryCode": "MS", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2208, "CountryCode": "MS", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2209, "CountryCode": "MS", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2210, "CountryCode": "MS", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 147, "Enabled": true, "CountryCode": "MT", "Name": "Malta", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 356, "UnitId": 212, "CountryTimes": [{"Id": 2211, "CountryCode": "MT", "DayOfWeek": 1, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2212, "CountryCode": "MT", "DayOfWeek": 2, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2213, "CountryCode": "MT", "DayOfWeek": 3, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2214, "CountryCode": "MT", "DayOfWeek": 4, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T18:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2215, "CountryCode": "MT", "DayOfWeek": 5, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2216, "CountryCode": "MT", "DayOfWeek": 6, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T19:00:00", "SameDayDeadline": "2018-08-15T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 148, "Enabled": true, "CountryCode": "MU", "Name": "Mauritius", "TimeZone": 165, "CurrencyId": 7, "DialingCode": 0, "UnitId": 212, "CountryTimes": [{"Id": 2217, "CountryCode": "MU", "DayOfWeek": 1, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T16:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2218, "CountryCode": "MU", "DayOfWeek": 2, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T16:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2219, "CountryCode": "MU", "DayOfWeek": 3, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T16:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2220, "CountryCode": "MU", "DayOfWeek": 4, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T12:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2221, "CountryCode": "MU", "DayOfWeek": 5, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T16:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2222, "CountryCode": "MU", "DayOfWeek": 6, "OpenTime": "2018-08-15T09:00:00", "CloseTime": "2018-08-15T16:00:00", "SameDayDeadline": "2018-08-15T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 149, "Enabled": false, "CountryCode": "MV", "Name": "Maldives", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 960, "UnitId": 212, "CountryTimes": [{"Id": 2223, "CountryCode": "MV", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2224, "CountryCode": "MV", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2225, "CountryCode": "MV", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2226, "CountryCode": "MV", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2227, "CountryCode": "MV", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2228, "CountryCode": "MV", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:47:01"}, {"Id": 150, "Enabled": true, "CountryCode": "MW", "Name": "Malawi", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 265, "UnitId": 214, "CountryTimes": [{"Id": 2229, "CountryCode": "MW", "DayOfWeek": 1, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2230, "CountryCode": "MW", "DayOfWeek": 2, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2231, "CountryCode": "MW", "DayOfWeek": 3, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2232, "CountryCode": "MW", "DayOfWeek": 4, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2233, "CountryCode": "MW", "DayOfWeek": 5, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 151, "Enabled": true, "CountryCode": "MX", "Name": "Mexico", "TimeZone": 20, "CurrencyId": 7, "DialingCode": 52, "UnitId": 213, "CountryTimes": [{"Id": 2235, "CountryCode": "MX", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2236, "CountryCode": "MX", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2237, "CountryCode": "MX", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2238, "CountryCode": "MX", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2239, "CountryCode": "MX", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2240, "CountryCode": "MX", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 152, "Enabled": true, "CountryCode": "MY", "Name": "Malaysia", "TimeZone": 215, "CurrencyId": 31, "DialingCode": 60, "UnitId": 220, "CountryTimes": [{"Id": 2241, "CountryCode": "MY", "DayOfWeek": 1, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2242, "CountryCode": "MY", "DayOfWeek": 2, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2243, "CountryCode": "MY", "DayOfWeek": 3, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2244, "CountryCode": "MY", "DayOfWeek": 4, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2245, "CountryCode": "MY", "DayOfWeek": 5, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2246, "CountryCode": "MY", "DayOfWeek": 6, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T15:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 153, "Enabled": true, "CountryCode": "MZ", "Name": "Mozambique", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 258, "UnitId": 214, "CountryTimes": [{"Id": 2247, "CountryCode": "MZ", "DayOfWeek": 1, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2248, "CountryCode": "MZ", "DayOfWeek": 2, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2249, "CountryCode": "MZ", "DayOfWeek": 3, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2250, "CountryCode": "MZ", "DayOfWeek": 4, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2251, "CountryCode": "MZ", "DayOfWeek": 5, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 154, "Enabled": true, "CountryCode": "NA", "Name": "Namibia", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 264, "UnitId": 214, "CountryTimes": [{"Id": 2253, "CountryCode": "NA", "DayOfWeek": 1, "OpenTime": "2020-05-09T08:00:00", "CloseTime": "2020-05-09T15:00:00", "SameDayDeadline": "2020-05-09T11:00:00"}, {"Id": 2254, "CountryCode": "NA", "DayOfWeek": 2, "OpenTime": "2020-05-09T08:00:00", "CloseTime": "2020-05-09T15:00:00", "SameDayDeadline": "2020-05-09T11:00:00"}, {"Id": 2255, "CountryCode": "NA", "DayOfWeek": 3, "OpenTime": "2020-05-09T08:00:00", "CloseTime": "2020-05-09T15:00:00", "SameDayDeadline": "2020-05-09T11:00:00"}, {"Id": 2256, "CountryCode": "NA", "DayOfWeek": 4, "OpenTime": "2020-05-09T08:00:00", "CloseTime": "2020-05-09T15:00:00", "SameDayDeadline": "2020-05-09T11:00:00"}, {"Id": 2257, "CountryCode": "NA", "DayOfWeek": 5, "OpenTime": "2020-05-09T08:00:00", "CloseTime": "2020-05-09T15:00:00", "SameDayDeadline": "2020-05-09T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 155, "Enabled": true, "CountryCode": "NC", "Name": "New Caledonia", "TimeZone": 280, "CurrencyId": 7, "DialingCode": 687, "UnitId": 193, "CountryTimes": [{"Id": 2259, "CountryCode": "NC", "DayOfWeek": 1, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T18:00:00", "SameDayDeadline": "2024-02-16T01:00:00"}, {"Id": 2260, "CountryCode": "NC", "DayOfWeek": 2, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T18:00:00", "SameDayDeadline": "2024-02-16T01:00:00"}, {"Id": 2261, "CountryCode": "NC", "DayOfWeek": 3, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T18:00:00", "SameDayDeadline": "2024-02-16T01:00:00"}, {"Id": 2262, "CountryCode": "NC", "DayOfWeek": 4, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T18:00:00", "SameDayDeadline": "2024-02-16T01:00:00"}, {"Id": 2263, "CountryCode": "NC", "DayOfWeek": 5, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T18:00:00", "SameDayDeadline": "2024-02-16T01:00:00"}, {"Id": 2264, "CountryCode": "NC", "DayOfWeek": 6, "OpenTime": "2024-02-16T07:30:00", "CloseTime": "2024-02-16T13:00:00", "SameDayDeadline": "2024-02-16T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 156, "Enabled": false, "CountryCode": "NE", "Name": "Niger", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 227, "UnitId": 199, "CountryTimes": [{"Id": 2265, "CountryCode": "NE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2266, "CountryCode": "NE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2267, "CountryCode": "NE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2268, "CountryCode": "NE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2269, "CountryCode": "NE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2270, "CountryCode": "NE", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 157, "Enabled": false, "CountryCode": "NF", "Name": "Norfolk Is.", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 672, "UnitId": 185, "CountryTimes": [{"Id": 2271, "CountryCode": "NF", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2272, "CountryCode": "NF", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2273, "CountryCode": "NF", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2274, "CountryCode": "NF", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2275, "CountryCode": "NF", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2276, "CountryCode": "NF", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 158, "Enabled": true, "CountryCode": "NG", "Name": "Nigeria", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 0, "UnitId": 199, "CountryTimes": [{"Id": 2277, "CountryCode": "NG", "DayOfWeek": 1, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2278, "CountryCode": "NG", "DayOfWeek": 2, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2279, "CountryCode": "NG", "DayOfWeek": 3, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2280, "CountryCode": "NG", "DayOfWeek": 4, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2281, "CountryCode": "NG", "DayOfWeek": 5, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2282, "CountryCode": "NG", "DayOfWeek": 6, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 159, "Enabled": true, "CountryCode": "NI", "Name": "Nicaragua", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 505, "UnitId": 213, "CountryTimes": [{"Id": 2283, "CountryCode": "NI", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2284, "CountryCode": "NI", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2285, "CountryCode": "NI", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2286, "CountryCode": "NI", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2287, "CountryCode": "NI", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2288, "CountryCode": "NI", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 160, "Enabled": true, "CountryCode": "NL", "Name": "Netherlands", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 31, "UnitId": 203, "CountryTimes": [{"Id": 2289, "CountryCode": "NL", "DayOfWeek": 1, "OpenTime": "2020-12-24T08:30:00", "CloseTime": "2020-12-24T17:00:00", "SameDayDeadline": "2020-12-24T12:00:00"}, {"Id": 2290, "CountryCode": "NL", "DayOfWeek": 2, "OpenTime": "2020-12-15T08:30:00", "CloseTime": "2020-12-15T17:00:00", "SameDayDeadline": "2020-12-15T12:00:00"}, {"Id": 2291, "CountryCode": "NL", "DayOfWeek": 3, "OpenTime": "2020-12-15T08:30:00", "CloseTime": "2020-12-15T17:00:00", "SameDayDeadline": "2020-12-15T12:00:00"}, {"Id": 2292, "CountryCode": "NL", "DayOfWeek": 4, "OpenTime": "2020-12-15T08:30:00", "CloseTime": "2020-12-15T17:00:00", "SameDayDeadline": "2020-12-15T12:00:00"}, {"Id": 2293, "CountryCode": "NL", "DayOfWeek": 5, "OpenTime": "2020-12-15T08:30:00", "CloseTime": "2020-12-15T17:00:00", "SameDayDeadline": "2020-12-15T12:00:00"}, {"Id": 2294, "CountryCode": "NL", "DayOfWeek": 6, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T12:30:00", "SameDayDeadline": "2015-10-27T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 161, "Enabled": true, "CountryCode": "NO", "Name": "Norway", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 47, "UnitId": 3, "CountryTimes": [{"Id": 2295, "CountryCode": "NO", "DayOfWeek": 1, "OpenTime": "2023-08-29T10:00:00", "CloseTime": "2023-08-29T16:00:00", "SameDayDeadline": "2023-08-29T12:00:00"}, {"Id": 2296, "CountryCode": "NO", "DayOfWeek": 2, "OpenTime": "2023-08-29T10:00:00", "CloseTime": "2023-08-29T16:00:00", "SameDayDeadline": "2023-08-29T12:00:00"}, {"Id": 2297, "CountryCode": "NO", "DayOfWeek": 3, "OpenTime": "2023-08-29T10:00:00", "CloseTime": "2023-08-29T16:00:00", "SameDayDeadline": "2023-08-29T12:00:00"}, {"Id": 2298, "CountryCode": "NO", "DayOfWeek": 4, "OpenTime": "2023-08-29T10:00:00", "CloseTime": "2023-08-29T16:00:00", "SameDayDeadline": "2023-08-29T12:00:00"}, {"Id": 2299, "CountryCode": "NO", "DayOfWeek": 5, "OpenTime": "2023-08-29T10:00:00", "CloseTime": "2023-08-29T16:00:00", "SameDayDeadline": "2023-08-29T12:00:00"}, {"Id": 2300, "CountryCode": "NO", "DayOfWeek": 6, "OpenTime": "2023-08-29T09:00:00", "CloseTime": "2023-08-29T13:00:00", "SameDayDeadline": "2023-08-29T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 162, "Enabled": false, "CountryCode": "NP", "Name": "Nepal", "TimeZone": 193, "CurrencyId": 7, "DialingCode": 977, "UnitId": 212, "CountryTimes": [{"Id": 2301, "CountryCode": "NP", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2302, "CountryCode": "NP", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2303, "CountryCode": "NP", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2304, "CountryCode": "NP", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2305, "CountryCode": "NP", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2306, "CountryCode": "NP", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 163, "Enabled": false, "CountryCode": "NR", "Name": "Nauru", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 674, "UnitId": 199, "CountryTimes": [{"Id": 2307, "CountryCode": "NR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2308, "CountryCode": "NR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2309, "CountryCode": "NR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2310, "CountryCode": "NR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2311, "CountryCode": "NR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2312, "CountryCode": "NR", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 164, "Enabled": false, "CountryCode": "NU", "Name": "Niue", "TimeZone": 1, "CurrencyId": 7, "DialingCode": 683, "UnitId": 199, "CountryTimes": [{"Id": 2313, "CountryCode": "NU", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2314, "CountryCode": "NU", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2315, "CountryCode": "NU", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2316, "CountryCode": "NU", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2317, "CountryCode": "NU", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2318, "CountryCode": "NU", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:55:43"}, {"Id": 165, "Enabled": true, "CountryCode": "NY", "Name": "Cyprus (Turkey)", "TimeZone": 135, "CurrencyId": 7, "DialingCode": null, "UnitId": 211, "CountryTimes": [{"Id": 2851, "CountryCode": "NY", "DayOfWeek": 1, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T15:30:00"}, {"Id": 2852, "CountryCode": "NY", "DayOfWeek": 2, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T15:30:00"}, {"Id": 2853, "CountryCode": "NY", "DayOfWeek": 3, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T15:30:00"}, {"Id": 2854, "CountryCode": "NY", "DayOfWeek": 4, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T15:30:00"}, {"Id": 2855, "CountryCode": "NY", "DayOfWeek": 5, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T15:30:00"}, {"Id": 2856, "CountryCode": "NY", "DayOfWeek": 6, "OpenTime": "2002-11-04T08:00:00", "CloseTime": "2002-11-04T19:00:00", "SameDayDeadline": "2002-11-04T12:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 166, "Enabled": true, "CountryCode": "NZ", "Name": "New Zealand", "TimeZone": 290, "CurrencyId": 16, "DialingCode": 64, "UnitId": 204, "CountryTimes": [{"Id": 3650, "CountryCode": "NZ", "DayOfWeek": 1, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T17:00:00", "SameDayDeadline": "2015-09-28T14:00:00"}, {"Id": 3651, "CountryCode": "NZ", "DayOfWeek": 2, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T17:00:00", "SameDayDeadline": "2015-09-28T14:00:00"}, {"Id": 3652, "CountryCode": "NZ", "DayOfWeek": 3, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T17:00:00", "SameDayDeadline": "2015-09-28T14:00:00"}, {"Id": 3653, "CountryCode": "NZ", "DayOfWeek": 4, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T17:00:00", "SameDayDeadline": "2015-09-28T14:00:00"}, {"Id": 3654, "CountryCode": "NZ", "DayOfWeek": 5, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T17:00:00", "SameDayDeadline": "2015-09-28T14:00:00"}, {"Id": 3655, "CountryCode": "NZ", "DayOfWeek": 6, "OpenTime": "2015-09-28T09:00:00", "CloseTime": "2015-09-28T12:00:00", "SameDayDeadline": "2015-09-28T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 167, "Enabled": true, "CountryCode": "OM", "Name": "Oman", "TimeZone": 165, "CurrencyId": 7, "DialingCode": 968, "UnitId": 199, "CountryTimes": [{"Id": 2326, "CountryCode": "OM", "DayOfWeek": 2, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T20:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2327, "CountryCode": "OM", "DayOfWeek": 3, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T20:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 3072, "CountryCode": "OM", "DayOfWeek": 1, "OpenTime": "2023-09-14T16:00:00", "CloseTime": "2023-09-14T20:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 3075, "CountryCode": "OM", "DayOfWeek": 4, "OpenTime": "2023-09-14T16:00:00", "CloseTime": "2023-09-14T20:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 3076, "CountryCode": "OM", "DayOfWeek": 6, "OpenTime": "2023-09-14T16:00:00", "CloseTime": "2023-09-14T20:00:00", "SameDayDeadline": "2023-09-14T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 168, "Enabled": true, "CountryCode": "PA", "Name": "Panama", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 507, "UnitId": 213, "CountryTimes": [{"Id": 2331, "CountryCode": "PA", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2332, "CountryCode": "PA", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2333, "CountryCode": "PA", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2334, "CountryCode": "PA", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2335, "CountryCode": "PA", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2336, "CountryCode": "PA", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 169, "Enabled": true, "CountryCode": "PE", "Name": "Peru", "TimeZone": 45, "CurrencyId": 7, "DialingCode": 51, "UnitId": 213, "CountryTimes": [{"Id": 2337, "CountryCode": "PE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2338, "CountryCode": "PE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2339, "CountryCode": "PE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2340, "CountryCode": "PE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2341, "CountryCode": "PE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2342, "CountryCode": "PE", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 170, "Enabled": true, "CountryCode": "PF", "Name": "French Polynesia", "TimeZone": 2, "CurrencyId": 7, "DialingCode": 689, "UnitId": 193, "CountryTimes": [{"Id": 2343, "CountryCode": "PF", "DayOfWeek": 1, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T17:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2344, "CountryCode": "PF", "DayOfWeek": 2, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T17:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2345, "CountryCode": "PF", "DayOfWeek": 3, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T17:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2346, "CountryCode": "PF", "DayOfWeek": 4, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T17:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2347, "CountryCode": "PF", "DayOfWeek": 5, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T17:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2348, "CountryCode": "PF", "DayOfWeek": 6, "OpenTime": "2023-05-09T07:30:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 171, "Enabled": false, "CountryCode": "PG", "Name": "Papa New Guinea", "TimeZone": 275, "CurrencyId": 7, "DialingCode": 675, "UnitId": 193, "CountryTimes": [{"Id": 2349, "CountryCode": "PG", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2350, "CountryCode": "PG", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2351, "CountryCode": "PG", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2352, "CountryCode": "PG", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2353, "CountryCode": "PG", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2354, "CountryCode": "PG", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 172, "Enabled": true, "CountryCode": "PH", "Name": "Philippines", "TimeZone": 215, "CurrencyId": 7, "DialingCode": 63, "UnitId": 213, "CountryTimes": [{"Id": 2355, "CountryCode": "PH", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2356, "CountryCode": "PH", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2357, "CountryCode": "PH", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2358, "CountryCode": "PH", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2359, "CountryCode": "PH", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2360, "CountryCode": "PH", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 173, "Enabled": true, "CountryCode": "PK", "Name": "Pakistan", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 92, "UnitId": 212, "CountryTimes": [{"Id": 2361, "CountryCode": "PK", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2362, "CountryCode": "PK", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2363, "CountryCode": "PK", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2364, "CountryCode": "PK", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2366, "CountryCode": "PK", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 3765, "CountryCode": "PK", "DayOfWeek": 5, "OpenTime": "2018-08-10T09:00:00", "CloseTime": "2018-08-10T19:00:00", "SameDayDeadline": "2018-08-10T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 174, "Enabled": true, "CountryCode": "PL", "Name": "Poland", "TimeZone": 100, "CurrencyId": 7, "DialingCode": 48, "UnitId": 205, "CountryTimes": [{"Id": 2367, "CountryCode": "PL", "DayOfWeek": 1, "OpenTime": "2023-09-19T08:00:00", "CloseTime": "2023-09-19T20:00:00", "SameDayDeadline": "2023-09-19T16:00:00"}, {"Id": 2368, "CountryCode": "PL", "DayOfWeek": 2, "OpenTime": "2023-09-19T08:00:00", "CloseTime": "2023-09-19T20:00:00", "SameDayDeadline": "2023-09-19T16:00:00"}, {"Id": 2369, "CountryCode": "PL", "DayOfWeek": 3, "OpenTime": "2023-09-19T08:00:00", "CloseTime": "2023-09-19T20:00:00", "SameDayDeadline": "2023-09-19T16:00:00"}, {"Id": 2370, "CountryCode": "PL", "DayOfWeek": 4, "OpenTime": "2023-09-19T08:00:00", "CloseTime": "2023-09-19T20:00:00", "SameDayDeadline": "2023-09-19T16:00:00"}, {"Id": 2371, "CountryCode": "PL", "DayOfWeek": 5, "OpenTime": "2023-09-19T08:00:00", "CloseTime": "2023-09-19T20:00:00", "SameDayDeadline": "2023-09-19T16:00:00"}, {"Id": 2372, "CountryCode": "PL", "DayOfWeek": 6, "OpenTime": "2023-09-19T09:00:00", "CloseTime": "2023-09-19T16:00:00", "SameDayDeadline": "2023-09-19T13:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 175, "Enabled": true, "CountryCode": "PM", "Name": "Saint Pierre & Miquelon", "TimeZone": 73, "CurrencyId": 7, "DialingCode": 508, "UnitId": 193, "CountryTimes": [{"Id": 2373, "CountryCode": "PM", "DayOfWeek": 1, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2374, "CountryCode": "PM", "DayOfWeek": 2, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2375, "CountryCode": "PM", "DayOfWeek": 3, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2376, "CountryCode": "PM", "DayOfWeek": 4, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2377, "CountryCode": "PM", "DayOfWeek": 5, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T16:45:00"}, {"Id": 2378, "CountryCode": "PM", "DayOfWeek": 6, "OpenTime": "2023-05-09T09:30:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 176, "Enabled": false, "CountryCode": "PN", "Name": "Pitcairn Is.", "TimeZone": 4, "CurrencyId": 7, "DialingCode": 0, "UnitId": 204, "CountryTimes": [{"Id": 2379, "CountryCode": "PN", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2380, "CountryCode": "PN", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2381, "CountryCode": "PN", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2382, "CountryCode": "PN", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2383, "CountryCode": "PN", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2384, "CountryCode": "PN", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 177, "Enabled": true, "CountryCode": "PR", "Name": "Puerto Rico", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 1787, "UnitId": 213, "CountryTimes": [{"Id": 2385, "CountryCode": "PR", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2386, "CountryCode": "PR", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2387, "CountryCode": "PR", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2388, "CountryCode": "PR", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2389, "CountryCode": "PR", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2390, "CountryCode": "PR", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 178, "Enabled": false, "CountryCode": "PS", "Name": "Palestinian Territory", "TimeZone": 135, "CurrencyId": 7, "DialingCode": 970, "UnitId": 199, "CountryTimes": [], "LastUpdate": "2023-08-07T12:54:17"}, {"Id": 179, "Enabled": true, "CountryCode": "PT", "Name": "Portugal", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 351, "UnitId": 206, "CountryTimes": [{"Id": 2391, "CountryCode": "PT", "DayOfWeek": 1, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:30:00", "SameDayDeadline": "2015-11-18T11:30:00"}, {"Id": 2392, "CountryCode": "PT", "DayOfWeek": 2, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:30:00", "SameDayDeadline": "2015-11-18T11:30:00"}, {"Id": 2393, "CountryCode": "PT", "DayOfWeek": 3, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:30:00", "SameDayDeadline": "2015-11-18T11:30:00"}, {"Id": 2394, "CountryCode": "PT", "DayOfWeek": 4, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:30:00", "SameDayDeadline": "2015-11-18T11:30:00"}, {"Id": 2395, "CountryCode": "PT", "DayOfWeek": 5, "OpenTime": "2015-11-18T09:00:00", "CloseTime": "2015-11-18T18:30:00", "SameDayDeadline": "2015-11-18T11:30:00"}, {"Id": 3767, "CountryCode": "PT", "DayOfWeek": 6, "OpenTime": "2019-12-20T09:00:00", "CloseTime": "2019-12-20T13:00:00", "SameDayDeadline": "2019-12-20T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 180, "Enabled": false, "CountryCode": "PW", "Name": "<PERSON><PERSON>", "TimeZone": 235, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 2397, "CountryCode": "PW", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2398, "CountryCode": "PW", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2399, "CountryCode": "PW", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2400, "CountryCode": "PW", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2401, "CountryCode": "PW", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2402, "CountryCode": "PW", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T16:56:52"}, {"Id": 181, "Enabled": true, "CountryCode": "PY", "Name": "Paraguay", "TimeZone": 56, "CurrencyId": 7, "DialingCode": 595, "UnitId": 213, "CountryTimes": [{"Id": 2403, "CountryCode": "PY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 2404, "CountryCode": "PY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 2405, "CountryCode": "PY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 2406, "CountryCode": "PY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 2407, "CountryCode": "PY", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:00:00"}, {"Id": 2408, "CountryCode": "PY", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 182, "Enabled": false, "CountryCode": "QA", "Name": "Quatar", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 0, "UnitId": 212, "CountryTimes": [{"Id": 2409, "CountryCode": "QA", "DayOfWeek": 1, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2410, "CountryCode": "QA", "DayOfWeek": 2, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2411, "CountryCode": "QA", "DayOfWeek": 3, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2412, "CountryCode": "QA", "DayOfWeek": 4, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2413, "CountryCode": "QA", "DayOfWeek": 5, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T13:00:00"}, {"Id": 2414, "CountryCode": "QA", "DayOfWeek": 6, "OpenTime": "2018-08-15T10:00:00", "CloseTime": "2018-08-15T17:00:00", "SameDayDeadline": "2018-08-15T10:00:00"}], "LastUpdate": "2021-10-13T14:44:06.18"}, {"Id": 183, "Enabled": true, "CountryCode": "RE", "Name": "Reunion", "TimeZone": 165, "CurrencyId": 7, "DialingCode": 262, "UnitId": 193, "CountryTimes": [{"Id": 2415, "CountryCode": "RE", "DayOfWeek": 1, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T14:45:00"}, {"Id": 2416, "CountryCode": "RE", "DayOfWeek": 2, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T14:45:00"}, {"Id": 2417, "CountryCode": "RE", "DayOfWeek": 3, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T14:45:00"}, {"Id": 2418, "CountryCode": "RE", "DayOfWeek": 4, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T14:45:00"}, {"Id": 2419, "CountryCode": "RE", "DayOfWeek": 5, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T18:00:00", "SameDayDeadline": "2023-05-09T14:45:00"}, {"Id": 2420, "CountryCode": "RE", "DayOfWeek": 6, "OpenTime": "2023-05-09T08:00:00", "CloseTime": "2023-05-09T12:00:00", "SameDayDeadline": "2023-05-09T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 184, "Enabled": true, "CountryCode": "RO", "Name": "Romania", "TimeZone": 130, "CurrencyId": 7, "DialingCode": 40, "UnitId": 275, "CountryTimes": [{"Id": 2421, "CountryCode": "RO", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2422, "CountryCode": "RO", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2423, "CountryCode": "RO", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2424, "CountryCode": "RO", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2425, "CountryCode": "RO", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2426, "CountryCode": "RO", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T14:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2017-12-01T15:53:41.58"}, {"Id": 185, "Enabled": false, "CountryCode": "RU", "Name": "Russia / Moscow", "TimeZone": 145, "CurrencyId": 25, "DialingCode": 7, "UnitId": 207, "CountryTimes": [{"Id": 2427, "CountryCode": "RU", "DayOfWeek": 1, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2428, "CountryCode": "RU", "DayOfWeek": 2, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2429, "CountryCode": "RU", "DayOfWeek": 3, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2430, "CountryCode": "RU", "DayOfWeek": 4, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2431, "CountryCode": "RU", "DayOfWeek": 5, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 3175, "CountryCode": "RU", "DayOfWeek": 6, "OpenTime": "2019-02-05T10:00:00", "CloseTime": "2019-02-05T20:00:00", "SameDayDeadline": "2019-02-05T13:45:00"}, {"Id": 3797, "CountryCode": "RU", "DayOfWeek": 0, "OpenTime": "2021-02-02T10:00:00", "CloseTime": "2021-02-02T20:00:00", "SameDayDeadline": "2021-02-02T13:45:00"}], "LastUpdate": "2022-03-03T08:49:20.107"}, {"Id": 186, "Enabled": false, "CountryCode": "RW", "Name": "Rwanda", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 250, "UnitId": 199, "CountryTimes": [{"Id": 2433, "CountryCode": "RW", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2434, "CountryCode": "RW", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2435, "CountryCode": "RW", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2436, "CountryCode": "RW", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2437, "CountryCode": "RW", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2438, "CountryCode": "RW", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:54:14"}, {"Id": 187, "Enabled": true, "CountryCode": "SA", "Name": "Saudi Arabia", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 966, "UnitId": 199, "CountryTimes": [{"Id": 2440, "CountryCode": "SA", "DayOfWeek": 2, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2441, "CountryCode": "SA", "DayOfWeek": 3, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2442, "CountryCode": "SA", "DayOfWeek": 4, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2444, "CountryCode": "SA", "DayOfWeek": 6, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}, {"Id": 2817, "CountryCode": "SA", "DayOfWeek": 0, "OpenTime": "2023-09-18T00:00:00", "CloseTime": "2023-09-18T00:00:00", "SameDayDeadline": "2023-09-18T00:00:00"}, {"Id": 3159, "CountryCode": "SA", "DayOfWeek": 1, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3807, "CountryCode": "SA", "DayOfWeek": 5, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T20:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 188, "Enabled": false, "CountryCode": "SB", "Name": "Solomon <PERSON>.", "TimeZone": 280, "CurrencyId": 7, "DialingCode": 677, "UnitId": 213, "CountryTimes": [{"Id": 2445, "CountryCode": "SB", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2446, "CountryCode": "SB", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2447, "CountryCode": "SB", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2448, "CountryCode": "SB", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2449, "CountryCode": "SB", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2450, "CountryCode": "SB", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 189, "Enabled": false, "CountryCode": "SC", "Name": "Seychelles", "TimeZone": 165, "CurrencyId": 7, "DialingCode": 0, "UnitId": 212, "CountryTimes": [{"Id": 2451, "CountryCode": "SC", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2452, "CountryCode": "SC", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2453, "CountryCode": "SC", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2454, "CountryCode": "SC", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2455, "CountryCode": "SC", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2456, "CountryCode": "SC", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2023-08-07T16:52:10"}, {"Id": 190, "Enabled": false, "CountryCode": "SD", "Name": "Sudan", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 249, "UnitId": 199, "CountryTimes": [{"Id": 2457, "CountryCode": "SD", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2458, "CountryCode": "SD", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2459, "CountryCode": "SD", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2460, "CountryCode": "SD", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2461, "CountryCode": "SD", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2462, "CountryCode": "SD", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:52:58"}, {"Id": 191, "Enabled": true, "CountryCode": "SE", "Name": "Sweden", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 46, "UnitId": 208, "CountryTimes": [{"Id": 2463, "CountryCode": "SE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2464, "CountryCode": "SE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2465, "CountryCode": "SE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2466, "CountryCode": "SE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2467, "CountryCode": "SE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:30:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2468, "CountryCode": "SE", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T14:00:00", "SameDayDeadline": "2000-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 192, "Enabled": true, "CountryCode": "SG", "Name": "Singapore", "TimeZone": 215, "CurrencyId": 28, "DialingCode": 0, "UnitId": 220, "CountryTimes": [{"Id": 2469, "CountryCode": "SG", "DayOfWeek": 1, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2470, "CountryCode": "SG", "DayOfWeek": 2, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2471, "CountryCode": "SG", "DayOfWeek": 3, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2472, "CountryCode": "SG", "DayOfWeek": 4, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2473, "CountryCode": "SG", "DayOfWeek": 5, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T11:30:00"}, {"Id": 2474, "CountryCode": "SG", "DayOfWeek": 6, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T18:00:00", "SameDayDeadline": "2015-09-29T06:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 193, "Enabled": false, "CountryCode": "SH", "Name": "St. Helena", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 290, "UnitId": 212, "CountryTimes": [{"Id": 2475, "CountryCode": "SH", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2476, "CountryCode": "SH", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2477, "CountryCode": "SH", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2478, "CountryCode": "SH", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2479, "CountryCode": "SH", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2480, "CountryCode": "SH", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 194, "Enabled": true, "CountryCode": "SI", "Name": "Slovenia", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 386, "UnitId": 199, "CountryTimes": [{"Id": 2481, "CountryCode": "SI", "DayOfWeek": 1, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2482, "CountryCode": "SI", "DayOfWeek": 2, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2483, "CountryCode": "SI", "DayOfWeek": 3, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2484, "CountryCode": "SI", "DayOfWeek": 4, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2485, "CountryCode": "SI", "DayOfWeek": 5, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T19:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3806, "CountryCode": "SI", "DayOfWeek": 6, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T15:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 195, "Enabled": false, "CountryCode": "SJ", "Name": "Svalbard & Jan <PERSON> Is.", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 378, "UnitId": 199, "CountryTimes": [{"Id": 2487, "CountryCode": "SJ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2488, "CountryCode": "SJ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2489, "CountryCode": "SJ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2490, "CountryCode": "SJ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2491, "CountryCode": "SJ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2492, "CountryCode": "SJ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-09-30T10:43:43"}, {"Id": 196, "Enabled": true, "CountryCode": "SK", "Name": "Slovakia", "TimeZone": 95, "CurrencyId": 7, "DialingCode": 421, "UnitId": 199, "CountryTimes": [{"Id": 2493, "CountryCode": "SK", "DayOfWeek": 1, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2494, "CountryCode": "SK", "DayOfWeek": 2, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2495, "CountryCode": "SK", "DayOfWeek": 3, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2496, "CountryCode": "SK", "DayOfWeek": 4, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2497, "CountryCode": "SK", "DayOfWeek": 5, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3805, "CountryCode": "SK", "DayOfWeek": 6, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 197, "Enabled": false, "CountryCode": "SL", "Name": "Sierra Leone", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 232, "UnitId": 212, "CountryTimes": [{"Id": 2499, "CountryCode": "SL", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2500, "CountryCode": "SL", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2501, "CountryCode": "SL", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2502, "CountryCode": "SL", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2503, "CountryCode": "SL", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2504, "CountryCode": "SL", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 198, "Enabled": false, "CountryCode": "SM", "Name": "San Marino", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 378, "UnitId": 200, "CountryTimes": [{"Id": 2505, "CountryCode": "SM", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2506, "CountryCode": "SM", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2507, "CountryCode": "SM", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2508, "CountryCode": "SM", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2509, "CountryCode": "SM", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2510, "CountryCode": "SM", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 199, "Enabled": true, "CountryCode": "SN", "Name": "Senegal", "TimeZone": 85, "CurrencyId": 7, "DialingCode": 221, "UnitId": 199, "CountryTimes": [{"Id": 2511, "CountryCode": "SN", "DayOfWeek": 1, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2512, "CountryCode": "SN", "DayOfWeek": 2, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2513, "CountryCode": "SN", "DayOfWeek": 3, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2514, "CountryCode": "SN", "DayOfWeek": 4, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2515, "CountryCode": "SN", "DayOfWeek": 5, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 2516, "CountryCode": "SN", "DayOfWeek": 6, "OpenTime": "2023-09-18T09:00:00", "CloseTime": "2023-09-18T16:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}], "LastUpdate": "2023-04-27T12:58:05"}, {"Id": 200, "Enabled": false, "CountryCode": "SO", "Name": "Somaloa", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 252, "UnitId": 199, "CountryTimes": [{"Id": 2517, "CountryCode": "SO", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2518, "CountryCode": "SO", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2519, "CountryCode": "SO", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2520, "CountryCode": "SO", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2521, "CountryCode": "SO", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2522, "CountryCode": "SO", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:52:55"}, {"Id": 201, "Enabled": true, "CountryCode": "SR", "Name": "Suriname", "TimeZone": 65, "CurrencyId": 7, "DialingCode": 597, "UnitId": 203, "CountryTimes": [{"Id": 2523, "CountryCode": "SR", "DayOfWeek": 1, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 2524, "CountryCode": "SR", "DayOfWeek": 2, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 2525, "CountryCode": "SR", "DayOfWeek": 3, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 2526, "CountryCode": "SR", "DayOfWeek": 4, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 2527, "CountryCode": "SR", "DayOfWeek": 5, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 2528, "CountryCode": "SR", "DayOfWeek": 6, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T12:30:00", "SameDayDeadline": "2015-10-27T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 202, "Enabled": false, "CountryCode": "ST", "Name": "Sao Tome e Principe", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 239, "UnitId": 212, "CountryTimes": [{"Id": 2529, "CountryCode": "ST", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2530, "CountryCode": "ST", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2531, "CountryCode": "ST", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2532, "CountryCode": "ST", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2533, "CountryCode": "ST", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2534, "CountryCode": "ST", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 203, "Enabled": true, "CountryCode": "SV", "Name": "El Salvador", "TimeZone": 33, "CurrencyId": 7, "DialingCode": 503, "UnitId": 213, "CountryTimes": [{"Id": 2535, "CountryCode": "SV", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2536, "CountryCode": "SV", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2537, "CountryCode": "SV", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2538, "CountryCode": "SV", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2539, "CountryCode": "SV", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2540, "CountryCode": "SV", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 204, "Enabled": false, "CountryCode": "SY", "Name": "Syria", "TimeZone": 135, "CurrencyId": 7, "DialingCode": 963, "UnitId": 199, "CountryTimes": [{"Id": 2541, "CountryCode": "SY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2542, "CountryCode": "SY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2543, "CountryCode": "SY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2544, "CountryCode": "SY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2545, "CountryCode": "SY", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T11:30:00"}, {"Id": 2546, "CountryCode": "SY", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T22:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2023-08-07T12:52:19"}, {"Id": 205, "Enabled": false, "CountryCode": "SZ", "Name": "Swaziland", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 268, "UnitId": 214, "CountryTimes": [{"Id": 2547, "CountryCode": "SZ", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 2548, "CountryCode": "SZ", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 2549, "CountryCode": "SZ", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 2550, "CountryCode": "SZ", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 2551, "CountryCode": "SZ", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T16:30:00", "SameDayDeadline": "1900-01-01T13:00:00"}, {"Id": 2552, "CountryCode": "SZ", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2023-08-07T16:01:39"}, {"Id": 206, "Enabled": false, "CountryCode": "TC", "Name": "Turks & Caicos Is.", "TimeZone": 35, "CurrencyId": 7, "DialingCode": 649, "UnitId": 213, "CountryTimes": [{"Id": 2553, "CountryCode": "TC", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2554, "CountryCode": "TC", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2555, "CountryCode": "TC", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2556, "CountryCode": "TC", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2557, "CountryCode": "TC", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2558, "CountryCode": "TC", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 207, "Enabled": false, "CountryCode": "TD", "Name": "Chad", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 235, "UnitId": 199, "CountryTimes": [{"Id": 2559, "CountryCode": "TD", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2560, "CountryCode": "TD", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2561, "CountryCode": "TD", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2562, "CountryCode": "TD", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2563, "CountryCode": "TD", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2564, "CountryCode": "TD", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 208, "Enabled": false, "CountryCode": "TG", "Name": "Togo", "TimeZone": 90, "CurrencyId": 7, "DialingCode": 228, "UnitId": 199, "CountryTimes": [{"Id": 2565, "CountryCode": "TG", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2566, "CountryCode": "TG", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2567, "CountryCode": "TG", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2568, "CountryCode": "TG", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2569, "CountryCode": "TG", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2570, "CountryCode": "TG", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:51:58"}, {"Id": 209, "Enabled": true, "CountryCode": "TH", "Name": "Thailand", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 66, "UnitId": 199, "CountryTimes": [{"Id": 2571, "CountryCode": "TH", "DayOfWeek": 1, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T17:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2572, "CountryCode": "TH", "DayOfWeek": 2, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T17:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2573, "CountryCode": "TH", "DayOfWeek": 3, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T17:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2574, "CountryCode": "TH", "DayOfWeek": 4, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T17:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2575, "CountryCode": "TH", "DayOfWeek": 5, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T17:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2576, "CountryCode": "TH", "DayOfWeek": 6, "OpenTime": "2023-09-14T09:00:00", "CloseTime": "2023-09-14T12:00:00", "SameDayDeadline": "2023-09-14T11:30:00"}, {"Id": 3804, "CountryCode": "TH", "DayOfWeek": 0, "OpenTime": "2023-06-12T09:00:00", "CloseTime": "2023-06-12T12:00:00", "SameDayDeadline": "2023-06-12T00:00:00"}], "LastUpdate": "2017-09-30T22:00:01.617"}, {"Id": 210, "Enabled": false, "CountryCode": "TJ", "Name": "Tajikistaon", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 992, "UnitId": 207, "CountryTimes": [{"Id": 2577, "CountryCode": "TJ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2578, "CountryCode": "TJ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2579, "CountryCode": "TJ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2580, "CountryCode": "TJ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2581, "CountryCode": "TJ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2582, "CountryCode": "TJ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 211, "Enabled": false, "CountryCode": "TK", "Name": "Tokelau", "TimeZone": 2, "CurrencyId": 7, "DialingCode": 690, "UnitId": 213, "CountryTimes": [{"Id": 2583, "CountryCode": "TK", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2584, "CountryCode": "TK", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2585, "CountryCode": "TK", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2586, "CountryCode": "TK", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2587, "CountryCode": "TK", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2588, "CountryCode": "TK", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 212, "Enabled": true, "CountryCode": "TM", "Name": "Turkmenistan", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 993, "UnitId": 207, "CountryTimes": [{"Id": 2589, "CountryCode": "TM", "DayOfWeek": 1, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2590, "CountryCode": "TM", "DayOfWeek": 2, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2591, "CountryCode": "TM", "DayOfWeek": 3, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2592, "CountryCode": "TM", "DayOfWeek": 4, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2593, "CountryCode": "TM", "DayOfWeek": 5, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T13:45:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 213, "Enabled": true, "CountryCode": "TN", "Name": "Tunisia", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 216, "UnitId": 199, "CountryTimes": [{"Id": 2595, "CountryCode": "TN", "DayOfWeek": 1, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T18:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2596, "CountryCode": "TN", "DayOfWeek": 2, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T18:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2597, "CountryCode": "TN", "DayOfWeek": 3, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T18:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2598, "CountryCode": "TN", "DayOfWeek": 4, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T18:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2599, "CountryCode": "TN", "DayOfWeek": 5, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T18:00:00", "SameDayDeadline": "2023-09-14T15:00:00"}, {"Id": 2600, "CountryCode": "TN", "DayOfWeek": 6, "OpenTime": "2023-09-14T08:00:00", "CloseTime": "2023-09-14T16:00:00", "SameDayDeadline": "2023-09-14T12:00:00"}], "LastUpdate": "2023-09-02T11:07:44"}, {"Id": 214, "Enabled": true, "CountryCode": "TO", "Name": "Tonga", "TimeZone": 300, "CurrencyId": 7, "DialingCode": 676, "UnitId": 204, "CountryTimes": [{"Id": 2601, "CountryCode": "TO", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2602, "CountryCode": "TO", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2603, "CountryCode": "TO", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2604, "CountryCode": "TO", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2605, "CountryCode": "TO", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2606, "CountryCode": "TO", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 216, "Enabled": true, "CountryCode": "TR", "Name": "Turkey", "TimeZone": 135, "CurrencyId": 7, "DialingCode": 90, "UnitId": 211, "CountryTimes": [{"Id": 2613, "CountryCode": "TR", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 2614, "CountryCode": "TR", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 2615, "CountryCode": "TR", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 2616, "CountryCode": "TR", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 2617, "CountryCode": "TR", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T15:30:00"}, {"Id": 2618, "CountryCode": "TR", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T19:00:00", "SameDayDeadline": "1900-01-01T12:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 217, "Enabled": true, "CountryCode": "TT", "Name": "Trinidad & Tobago", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 2619, "CountryCode": "TT", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2620, "CountryCode": "TT", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2621, "CountryCode": "TT", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2622, "CountryCode": "TT", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2623, "CountryCode": "TT", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2624, "CountryCode": "TT", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 218, "Enabled": false, "CountryCode": "TV", "Name": "Tuvalu", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 688, "UnitId": 213, "CountryTimes": [{"Id": 2625, "CountryCode": "TV", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2626, "CountryCode": "TV", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2627, "CountryCode": "TV", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2628, "CountryCode": "TV", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2629, "CountryCode": "TV", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2630, "CountryCode": "TV", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 219, "Enabled": true, "CountryCode": "TW", "Name": "Taiwan", "TimeZone": 220, "CurrencyId": 7, "DialingCode": 886, "UnitId": 213, "CountryTimes": [{"Id": 2631, "CountryCode": "TW", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2632, "CountryCode": "TW", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2633, "CountryCode": "TW", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2634, "CountryCode": "TW", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2635, "CountryCode": "TW", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2636, "CountryCode": "TW", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 220, "Enabled": false, "CountryCode": "TZ", "Name": "Tanzania", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 255, "UnitId": 214, "CountryTimes": [{"Id": 2637, "CountryCode": "TZ", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2638, "CountryCode": "TZ", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2639, "CountryCode": "TZ", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2640, "CountryCode": "TZ", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2641, "CountryCode": "TZ", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2642, "CountryCode": "TZ", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 221, "Enabled": true, "CountryCode": "UA", "Name": "Ukraine", "TimeZone": 125, "CurrencyId": 26, "DialingCode": 380, "UnitId": 271, "CountryTimes": [{"Id": 2643, "CountryCode": "UA", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 2644, "CountryCode": "UA", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 2645, "CountryCode": "UA", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 2646, "CountryCode": "UA", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 2647, "CountryCode": "UA", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T14:00:00"}, {"Id": 2648, "CountryCode": "UA", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}], "LastUpdate": "2023-05-11T11:16:16"}, {"Id": 222, "Enabled": false, "CountryCode": "UG", "Name": "Uganda", "TimeZone": 155, "CurrencyId": 7, "DialingCode": 256, "UnitId": 199, "CountryTimes": [{"Id": 2649, "CountryCode": "UG", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2650, "CountryCode": "UG", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2651, "CountryCode": "UG", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2652, "CountryCode": "UG", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2653, "CountryCode": "UG", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2654, "CountryCode": "UG", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:51:55"}, {"Id": 223, "Enabled": true, "CountryCode": "UK", "Name": "United Kingdom", "TimeZone": 85, "CurrencyId": 8, "DialingCode": 44, "UnitId": 212, "CountryTimes": [{"Id": 2655, "CountryCode": "UK", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}, {"Id": 2656, "CountryCode": "UK", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}, {"Id": 2657, "CountryCode": "UK", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}, {"Id": 2658, "CountryCode": "UK", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}, {"Id": 2659, "CountryCode": "UK", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}, {"Id": 3723, "CountryCode": "UK", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:45:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 224, "Enabled": true, "CountryCode": "US", "Name": "USA", "TimeZone": 20, "CurrencyId": 22, "DialingCode": 1, "UnitId": 213, "CountryTimes": [{"Id": 2661, "CountryCode": "US", "DayOfWeek": 1, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T17:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}, {"Id": 2662, "CountryCode": "US", "DayOfWeek": 2, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T17:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}, {"Id": 2663, "CountryCode": "US", "DayOfWeek": 3, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T17:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}, {"Id": 2664, "CountryCode": "US", "DayOfWeek": 4, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T17:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}, {"Id": 2909, "CountryCode": "US", "DayOfWeek": 5, "OpenTime": "2019-01-28T09:00:00", "CloseTime": "2019-01-28T17:00:00", "SameDayDeadline": "2019-01-28T11:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 225, "Enabled": true, "CountryCode": "UY", "Name": "Uruguay", "TimeZone": 65, "CurrencyId": 7, "DialingCode": 598, "UnitId": 213, "CountryTimes": [{"Id": 2667, "CountryCode": "UY", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2668, "CountryCode": "UY", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2669, "CountryCode": "UY", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2670, "CountryCode": "UY", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2671, "CountryCode": "UY", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2672, "CountryCode": "UY", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 2831, "CountryCode": "UY", "DayOfWeek": 0, "OpenTime": "2002-07-23T10:00:00", "CloseTime": "2002-07-23T19:00:00", "SameDayDeadline": "2002-07-23T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 226, "Enabled": true, "CountryCode": "UZ", "Name": "Uzbekistan", "TimeZone": 185, "CurrencyId": 7, "DialingCode": 998, "UnitId": 207, "CountryTimes": [{"Id": 2673, "CountryCode": "UZ", "DayOfWeek": 1, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2674, "CountryCode": "UZ", "DayOfWeek": 2, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2675, "CountryCode": "UZ", "DayOfWeek": 3, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2676, "CountryCode": "UZ", "DayOfWeek": 4, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2677, "CountryCode": "UZ", "DayOfWeek": 5, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T15:00:00"}, {"Id": 2678, "CountryCode": "UZ", "DayOfWeek": 6, "OpenTime": "2019-02-05T09:00:00", "CloseTime": "2019-02-05T21:00:00", "SameDayDeadline": "2019-02-05T13:45:00"}, {"Id": 3799, "CountryCode": "UZ", "DayOfWeek": 0, "OpenTime": "2021-02-02T09:00:00", "CloseTime": "2021-02-02T21:00:00", "SameDayDeadline": "2021-02-02T13:45:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 227, "Enabled": false, "CountryCode": "VA", "Name": "Holy See (Vatican City State)", "TimeZone": 110, "CurrencyId": 7, "DialingCode": 379, "UnitId": 200, "CountryTimes": [{"Id": 2679, "CountryCode": "VA", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2680, "CountryCode": "VA", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2681, "CountryCode": "VA", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2682, "CountryCode": "VA", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2683, "CountryCode": "VA", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2684, "CountryCode": "VA", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 228, "Enabled": false, "CountryCode": "VC", "Name": "St. Vincent & Grenadines", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 0, "UnitId": 213, "CountryTimes": [{"Id": 2685, "CountryCode": "VC", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2686, "CountryCode": "VC", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2687, "CountryCode": "VC", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2688, "CountryCode": "VC", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2689, "CountryCode": "VC", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2690, "CountryCode": "VC", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:30:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T16:03:55"}, {"Id": 229, "Enabled": true, "CountryCode": "VE", "Name": "Venezuela", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 58, "UnitId": 213, "CountryTimes": [{"Id": 2691, "CountryCode": "VE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2692, "CountryCode": "VE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2693, "CountryCode": "VE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2694, "CountryCode": "VE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2695, "CountryCode": "VE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2696, "CountryCode": "VE", "DayOfWeek": 6, "OpenTime": "1900-01-01T07:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 230, "Enabled": false, "CountryCode": "VG", "Name": "Virgin Islands (British)", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 284, "UnitId": 212, "CountryTimes": [{"Id": 2697, "CountryCode": "VG", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2698, "CountryCode": "VG", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2699, "CountryCode": "VG", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2700, "CountryCode": "VG", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2701, "CountryCode": "VG", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2702, "CountryCode": "VG", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 231, "Enabled": true, "CountryCode": "VI", "Name": "Virgin Islands", "TimeZone": 55, "CurrencyId": 7, "DialingCode": 1340, "UnitId": 213, "CountryTimes": [{"Id": 2857, "CountryCode": "VI", "DayOfWeek": 1, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2858, "CountryCode": "VI", "DayOfWeek": 2, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2859, "CountryCode": "VI", "DayOfWeek": 3, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2860, "CountryCode": "VI", "DayOfWeek": 4, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2861, "CountryCode": "VI", "DayOfWeek": 4, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2862, "CountryCode": "VI", "DayOfWeek": 5, "OpenTime": "2002-11-15T09:00:00", "CloseTime": "2002-11-15T17:00:00", "SameDayDeadline": "2002-11-15T11:30:00"}, {"Id": 2863, "CountryCode": "VI", "DayOfWeek": 6, "OpenTime": "2002-11-15T07:00:00", "CloseTime": "2002-11-15T13:00:00", "SameDayDeadline": "2002-11-15T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 232, "Enabled": true, "CountryCode": "VN", "Name": "Vietnam", "TimeZone": 205, "CurrencyId": 7, "DialingCode": 84, "UnitId": 220, "CountryTimes": [{"Id": 2703, "CountryCode": "VN", "DayOfWeek": 1, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T17:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2704, "CountryCode": "VN", "DayOfWeek": 2, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T17:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2705, "CountryCode": "VN", "DayOfWeek": 3, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T17:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2706, "CountryCode": "VN", "DayOfWeek": 4, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T17:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2707, "CountryCode": "VN", "DayOfWeek": 5, "OpenTime": "2015-09-29T08:00:00", "CloseTime": "2015-09-29T17:00:00", "SameDayDeadline": "2015-09-29T10:30:00"}, {"Id": 2708, "CountryCode": "VN", "DayOfWeek": 6, "OpenTime": "2015-09-29T09:00:00", "CloseTime": "2015-09-29T14:00:00", "SameDayDeadline": "2015-09-29T05:30:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 233, "Enabled": true, "CountryCode": "VU", "Name": "Vanuatu", "TimeZone": 280, "CurrencyId": 7, "DialingCode": 0, "UnitId": 185, "CountryTimes": [{"Id": 2709, "CountryCode": "VU", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2710, "CountryCode": "VU", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2711, "CountryCode": "VU", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2712, "CountryCode": "VU", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2713, "CountryCode": "VU", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T17:30:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2714, "CountryCode": "VU", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 234, "Enabled": false, "CountryCode": "WF", "Name": "Wallis & Futuna Is.", "TimeZone": 285, "CurrencyId": 7, "DialingCode": 681, "UnitId": 212, "CountryTimes": [{"Id": 2715, "CountryCode": "WF", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2716, "CountryCode": "WF", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2717, "CountryCode": "WF", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2718, "CountryCode": "WF", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2719, "CountryCode": "WF", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2720, "CountryCode": "WF", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 235, "Enabled": true, "CountryCode": "WS", "Name": "Samoa", "TimeZone": 1, "CurrencyId": 7, "DialingCode": 0, "UnitId": 204, "CountryTimes": [{"Id": 2721, "CountryCode": "WS", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2722, "CountryCode": "WS", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2723, "CountryCode": "WS", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2724, "CountryCode": "WS", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2725, "CountryCode": "WS", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2726, "CountryCode": "WS", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T12:00:00", "SameDayDeadline": "1900-01-01T10:30:00"}, {"Id": 3168, "CountryCode": "WS", "DayOfWeek": 1, "OpenTime": "2004-10-27T13:30:00", "CloseTime": "2004-10-27T16:30:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3169, "CountryCode": "WS", "DayOfWeek": 2, "OpenTime": "2004-10-27T13:30:00", "CloseTime": "2004-10-27T16:30:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3170, "CountryCode": "WS", "DayOfWeek": 3, "OpenTime": "2004-10-27T13:30:00", "CloseTime": "2004-10-27T16:30:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3171, "CountryCode": "WS", "DayOfWeek": 4, "OpenTime": "2004-10-27T13:30:00", "CloseTime": "2004-10-27T16:30:00", "SameDayDeadline": "2004-10-27T12:00:00"}, {"Id": 3172, "CountryCode": "WS", "DayOfWeek": 5, "OpenTime": "2004-10-27T13:30:00", "CloseTime": "2004-10-27T16:30:00", "SameDayDeadline": "2004-10-27T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 236, "Enabled": false, "CountryCode": "YE", "Name": "Yemen", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 967, "UnitId": 199, "CountryTimes": [{"Id": 2727, "CountryCode": "YE", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2728, "CountryCode": "YE", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2729, "CountryCode": "YE", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2730, "CountryCode": "YE", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2731, "CountryCode": "YE", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2732, "CountryCode": "YE", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2023-08-07T12:51:50"}, {"Id": 237, "Enabled": false, "CountryCode": "YT", "Name": "Mayotte", "TimeZone": 150, "CurrencyId": 7, "DialingCode": 269, "UnitId": 212, "CountryTimes": [{"Id": 2733, "CountryCode": "YT", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2734, "CountryCode": "YT", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2735, "CountryCode": "YT", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2736, "CountryCode": "YT", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2737, "CountryCode": "YT", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2738, "CountryCode": "YT", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 239, "Enabled": true, "CountryCode": "ZA", "Name": "South Africa", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 27, "UnitId": 214, "CountryTimes": [{"Id": 2745, "CountryCode": "ZA", "DayOfWeek": 1, "OpenTime": "2023-03-14T08:00:00", "CloseTime": "2023-03-14T16:00:00", "SameDayDeadline": "2023-03-14T12:00:00"}, {"Id": 2746, "CountryCode": "ZA", "DayOfWeek": 2, "OpenTime": "2023-03-14T08:00:00", "CloseTime": "2023-03-14T16:00:00", "SameDayDeadline": "2023-03-14T12:00:00"}, {"Id": 2747, "CountryCode": "ZA", "DayOfWeek": 3, "OpenTime": "2023-03-14T08:00:00", "CloseTime": "2023-03-14T16:00:00", "SameDayDeadline": "2023-03-14T12:00:00"}, {"Id": 2748, "CountryCode": "ZA", "DayOfWeek": 4, "OpenTime": "2023-03-14T08:00:00", "CloseTime": "2023-03-14T16:00:00", "SameDayDeadline": "2023-03-14T12:00:00"}, {"Id": 2749, "CountryCode": "ZA", "DayOfWeek": 5, "OpenTime": "2023-03-14T08:00:00", "CloseTime": "2023-03-14T16:00:00", "SameDayDeadline": "2023-03-14T12:00:00"}], "LastUpdate": "2025-04-21T08:43:48.183"}, {"Id": 240, "Enabled": true, "CountryCode": "ZM", "Name": "Zambia", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 260, "UnitId": 214, "CountryTimes": [{"Id": 2751, "CountryCode": "ZM", "DayOfWeek": 1, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2752, "CountryCode": "ZM", "DayOfWeek": 2, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2753, "CountryCode": "ZM", "DayOfWeek": 3, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2754, "CountryCode": "ZM", "DayOfWeek": 4, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2755, "CountryCode": "ZM", "DayOfWeek": 5, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 241, "Enabled": true, "CountryCode": "ZW", "Name": "Zimbabwe", "TimeZone": 140, "CurrencyId": 7, "DialingCode": 263, "UnitId": 214, "CountryTimes": [{"Id": 2757, "CountryCode": "ZW", "DayOfWeek": 1, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2758, "CountryCode": "ZW", "DayOfWeek": 2, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2759, "CountryCode": "ZW", "DayOfWeek": 3, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2760, "CountryCode": "ZW", "DayOfWeek": 4, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}, {"Id": 2761, "CountryCode": "ZW", "DayOfWeek": 5, "OpenTime": "2023-04-05T08:00:00", "CloseTime": "2023-04-05T16:00:00", "SameDayDeadline": "2023-04-05T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 244, "Enabled": false, "CountryCode": "TL", "Name": "East Timour", "TimeZone": 230, "CurrencyId": 7, "DialingCode": 239, "UnitId": 213, "CountryTimes": [{"Id": 2607, "CountryCode": "TL", "DayOfWeek": 1, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2608, "CountryCode": "TL", "DayOfWeek": 2, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2609, "CountryCode": "TL", "DayOfWeek": 3, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2610, "CountryCode": "TL", "DayOfWeek": 4, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2611, "CountryCode": "TL", "DayOfWeek": 5, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T12:00:00"}, {"Id": 2612, "CountryCode": "TL", "DayOfWeek": 6, "OpenTime": "1900-01-01T09:00:00", "CloseTime": "1900-01-01T16:00:00", "SameDayDeadline": "1900-01-01T09:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 245, "Enabled": false, "CountryCode": "CD", "Name": "Congo, Democratic Republic", "TimeZone": 113, "CurrencyId": 7, "DialingCode": 242, "UnitId": 199, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 246, "Enabled": true, "CountryCode": "RS", "Name": "Serbia", "TimeZone": 100, "CurrencyId": 7, "DialingCode": 381, "UnitId": 199, "CountryTimes": [{"Id": 3453, "CountryCode": "RS", "DayOfWeek": 2, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3454, "CountryCode": "RS", "DayOfWeek": 3, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3455, "CountryCode": "RS", "DayOfWeek": 4, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3456, "CountryCode": "RS", "DayOfWeek": 5, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}, {"Id": 3457, "CountryCode": "RS", "DayOfWeek": 6, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T15:00:00", "SameDayDeadline": "2023-09-18T11:00:00"}, {"Id": 3543, "CountryCode": "RS", "DayOfWeek": 1, "OpenTime": "2023-09-18T08:00:00", "CloseTime": "2023-09-18T18:00:00", "SameDayDeadline": "2023-09-18T15:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 247, "Enabled": true, "CountryCode": "ME", "Name": "Montenegro", "TimeZone": 100, "CurrencyId": 7, "DialingCode": 382, "UnitId": 199, "CountryTimes": [{"Id": 3756, "CountryCode": "ME", "DayOfWeek": 1, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3758, "CountryCode": "ME", "DayOfWeek": 2, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3759, "CountryCode": "ME", "DayOfWeek": 3, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3760, "CountryCode": "ME", "DayOfWeek": 4, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3761, "CountryCode": "ME", "DayOfWeek": 5, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3762, "CountryCode": "ME", "DayOfWeek": 6, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}, {"Id": 3763, "CountryCode": "ME", "DayOfWeek": 0, "OpenTime": "2023-10-04T00:00:00", "CloseTime": "2023-10-04T00:00:00", "SameDayDeadline": "2023-10-04T00:00:00"}], "LastUpdate": "2023-10-04T16:55:08"}, {"Id": 251, "Enabled": false, "CountryCode": "EH", "Name": "Western Sahara", "TimeZone": 85, "CurrencyId": 7, "DialingCode": null, "UnitId": 199, "CountryTimes": [], "LastUpdate": "2023-08-07T12:51:52"}, {"Id": 252, "Enabled": true, "CountryCode": "CW", "Name": "Curaçao", "TimeZone": 50, "CurrencyId": 7, "DialingCode": 599, "UnitId": 203, "CountryTimes": [{"Id": 3748, "CountryCode": "CW", "DayOfWeek": 1, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 3749, "CountryCode": "CW", "DayOfWeek": 2, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 3750, "CountryCode": "CW", "DayOfWeek": 3, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 3751, "CountryCode": "CW", "DayOfWeek": 4, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 3752, "CountryCode": "CW", "DayOfWeek": 5, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T17:00:00", "SameDayDeadline": "2015-10-27T13:00:00"}, {"Id": 3753, "CountryCode": "CW", "DayOfWeek": 6, "OpenTime": "2015-10-27T08:30:00", "CloseTime": "2015-10-27T12:30:00", "SameDayDeadline": "2015-10-27T12:00:00"}], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 255, "Enabled": false, "CountryCode": "XX", "Name": "Test", "TimeZone": 0, "CurrencyId": 0, "DialingCode": 0, "UnitId": 199, "CountryTimes": [], "LastUpdate": "2014-06-12T09:30:35.123"}, {"Id": 257, "Enabled": true, "CountryCode": "P1", "Name": "P19000", "TimeZone": 110, "CurrencyId": 3, "DialingCode": 41, "UnitId": 265, "CountryTimes": [{"Id": 3739, "CountryCode": "P1", "DayOfWeek": 1, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T13:30:00"}, {"Id": 3740, "CountryCode": "P1", "DayOfWeek": 2, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T13:30:00"}, {"Id": 3741, "CountryCode": "P1", "DayOfWeek": 3, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T13:30:00"}, {"Id": 3742, "CountryCode": "P1", "DayOfWeek": 4, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T13:30:00"}, {"Id": 3743, "CountryCode": "P1", "DayOfWeek": 5, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T18:00:00", "SameDayDeadline": "1900-01-01T13:30:00"}, {"Id": 3744, "CountryCode": "P1", "DayOfWeek": 6, "OpenTime": "1900-01-01T08:00:00", "CloseTime": "1900-01-01T13:00:00", "SameDayDeadline": "1900-01-01T10:45:00"}], "LastUpdate": "2015-05-06T13:36:20.83"}, {"Id": 258, "Enabled": true, "CountryCode": "P2", "Name": "P29000", "TimeZone": 110, "CurrencyId": 3, "DialingCode": 41, "UnitId": 266, "CountryTimes": [], "LastUpdate": "2015-05-06T13:36:20.83"}, {"Id": 259, "Enabled": true, "CountryCode": "TE", "Name": "Test Country", "TimeZone": null, "CurrencyId": 7, "DialingCode": null, "UnitId": null, "CountryTimes": [], "LastUpdate": "2024-11-21T18:02:49.223"}]