const GQLFuneralAddress = require('../../models/FuneralAddress')
const Transformer = require('../Transformer')

/**
 * @extends {Transformer<FuneralAddressesResponse, GQLFuneralAddress, {}>}
 */
class FuneralAddressTransformer extends Transformer {
  transformToGQL() {
    const funeralAddress = new GQLFuneralAddress()

    funeralAddress.funeralId = this.apiData.FuneralUUID
    funeralAddress.memberId = this.apiData.MemberID || null
    funeralAddress.nameOfDeceased = this.apiData.DeceasedName
    funeralAddress.funeralDateUtc = this.apiData.BurialDate
    funeralAddress.funeralTime = this.apiData.BurialTime
    funeralAddress.churchName = this.apiData.ChurchName
    funeralAddress.address = this.apiData.ChurchAddress
    funeralAddress.postalCode = this.apiData.ChurchZip
    funeralAddress.city = this.apiData.ChurchCity

    return funeralAddress
  }

  /**
   * @param {FuneralAddressesResponse} funeralAddress
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLFuneralAddress}
   */
  static toGQL(funeralAddress, context, options = {}) {
    const transformer = new FuneralAddressTransformer(funeralAddress, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = FuneralAddressTransformer
