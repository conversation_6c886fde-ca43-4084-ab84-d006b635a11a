import config from '../../../config'
import CONST from '../../../const'

import { GQLUser, GQLOrderList } from '../../models'

import Transformer from '../Transformer'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
class UserTransformer extends Transformer<any, GQLUser, object> {
  /**
   * @returns {GQLUser}
   */
  transformToGQL() {
    const user = new GQLUser()

    user.email = this.apiData.email
    user.firstName = this.apiData.firstName
    user.id = this.apiData.id
    user.lastName = this.apiData.lastName
    user.username = this.apiData.email

    user.type = CONST.user.userType.b2c as GQLUserTypeEnum
    if (this.apiData.customerGroup.obj.key === config.commerceTools.customerCare.customerGroupKey) {
      user.type = CONST.user.userType.customerCare as GQLUserTypeEnum
    }

    user.orders = new GQLOrderList()

    return user
  }

  static toGQL(apiData: object, context: GraphQLContext) {
    const transformer = new UserTransformer(apiData, context)
    return transformer.transformToGQL()
  }
}

export = UserTransformer
