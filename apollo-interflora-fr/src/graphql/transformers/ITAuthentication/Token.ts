import CONST from '../../../const'
import config from '../../../config'
import { createJwt } from '../../../helpers'
import GQLToken from '../../models/Token'
import Transformer from '../Transformer'
import UserTransformer from './User'

interface TokenTransformerOptions {
  customer: object
}

class TokenTransformer extends Transformer<APIToken, GQLToken, TokenTransformerOptions> {
  transformToGQL() {
    const token = new GQLToken()

    const sessionId = this.context.auth.sessionId ?? this.context.session?.id

    const { token: accessToken, expiresAt: accessExpiresAt } = createJwt(
      this.apiData,
      CONST.user.tokenType.access,
      sessionId,
      this.context.siteId
    )

    token.value = accessToken
    token.lifetime = accessExpiresAt

    const { token: refreshToken, expiresAt: refreshExpiresAt } = createJwt(
      this.apiData,
      CONST.user.tokenType.refresh,
      sessionId,
      this.context.siteId
    )

    token.refreshToken = refreshToken
    token.refreshLifetime = refreshExpiresAt

    token.state = CONST.user.userRegistrationStates.OK

    token.createdAt = String(Date.now() + this.apiData.expires_in * 1000 - config.interflora.accessTokenLifeTime)

    if (this.options.customer) {
      token.user = UserTransformer.toGQL(this.options.customer, this.context)
    }

    return token
  }

  static toGQL(apiData: APIToken, context: GraphQLContext, options = {} as TokenTransformerOptions) {
    const transformer = new TokenTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

export = TokenTransformer
