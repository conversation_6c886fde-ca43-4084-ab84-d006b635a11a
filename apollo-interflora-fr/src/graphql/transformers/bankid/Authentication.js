const CONST = require('../../../const')
const GQLAuthentication = require('../../models/Authentication')
const { createHmac } = require('node:crypto')

/**
 * @param {BankIdAuthResponse} authResponse
 * @returns {string | null}
 */
function generateQrData(authResponse) {
  const secondsSinceStart = Math.floor((Date.now() - authResponse.startTime) / 1000)
  if (secondsSinceStart > CONST.bankId.ttl) {
    return null
  }

  const qrAuthCode = createHmac('sha256', authResponse.qrStartSecret).update(`${secondsSinceStart}`).digest('hex')

  return `bankid.${authResponse.qrStartToken}.${secondsSinceStart}.${qrAuthCode}`
}

class BankIdAuthenticationTransformer {
  /**
   * @param {BankIdAuthResponse} authResponse
   * @param {BankIdAuthentication} collectResponse
   * @param {GQLToken} token
   * @returns {GQLAuthentication}
   */
  static toGQL(authResponse, collectResponse, token = null) {
    const { status, hintCode } = collectResponse
    const authentication = new GQLAuthentication()
    authentication.autoStartToken = authResponse.autoStartToken
    authentication.referenceId = authResponse.orderRef
    authentication.startTime = Date.now()
    authentication.token = token
    authentication.hintCode = hintCode
    authentication.status = status.toUpperCase()

    if (status.toUpperCase() !== CONST.bankId.status.complete) {
      authentication.qrData = generateQrData(authResponse)
    }

    return authentication
  }

  /**
   * @param {Error} error
   * @returns {GQLAuthentication}
   */
  static fromError(error) {
    const authentication = new GQLAuthentication()
    authentication.hintCode = error?.message ?? 'An error occurred'
    authentication.status = CONST.bankId.status.failed

    return authentication
  }
}

module.exports = BankIdAuthenticationTransformer
