const config = require('../../../config')
const CONST = require('../../../const')
const GQLDeliveryTimeRange = require('../../models/DeliveryTimeRange')
const GQLDeliveryTimeRangesResult = require('../../models/DeliveryTimeRangesResult')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<Number[], GQLDeliveryTimeRangesResult, {}>}
 */
class DeliveryTimeRangeResultTransformer extends Transformer {
  transformToGQL() {
    const result = new GQLDeliveryTimeRangesResult()

    const hasWholedayMoment = this.apiData?.includes(CONST.commerceTools.moment.Wholeday) || false
    result.ranges = this.apiData.map((id, index) => {
      const deliveryTimeRange = new GQLDeliveryTimeRange()

      deliveryTimeRange.available = true
      deliveryTimeRange.id = `${id}`
      deliveryTimeRange.label = config.apps[this.context.siteId].momentLabels[id]
      deliveryTimeRange.isDefault = false

      if ((hasWholedayMoment && id === CONST.commerceTools.moment.Wholeday) || (!hasWholedayMoment && index === 0))
        deliveryTimeRange.isDefault = true
      return deliveryTimeRange
    })

    return result
  }

  /**
   * @param {Number[]} moments
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLDelivery}
   */
  static toGQL(moments, context, options = {}) {
    const transformer = new DeliveryTimeRangeResultTransformer(moments, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DeliveryTimeRangeResultTransformer
