const { GQLInvalidDestination, GQLRoute } = require('../../models')
const PriceTransformer = require('../commerceTools/Price')

const Transformer = require('../Transformer')

/** @typedef {import('../../resolvers/fr/_typedef')} */

/**
 * @extends {Transformer<APIValidateLocalite, GQLInvalidDestination, {cart: APICart}>}
 */
class InvalidDestinationTransformer extends Transformer {
  /**
   * @param {{}} apiData
   * @param {GraphQLContext} context
   * @param {{cart: APICart}} options
   * @returns {GQLInvalidDestination}
   */
  static toGQL(apiData, context, options) {
    const transformer = new InvalidDestinationTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @returns {GQLInvalidDestination}
   */
  transformToGQL() {
    const { cart } = this.options

    const invalidDestination = new GQLInvalidDestination()

    let categoryRoute
    if (this.apiData.alternativeCategoryUrl) {
      const categoryUrl = new URL(`http://domain/${this.apiData.alternativeCategoryUrl.replace(/^\/+/, '')}`)

      categoryRoute = new GQLRoute()
      categoryRoute.name = 'category'
      categoryRoute.params = [
        {
          name: 'slugCategory',
          value: categoryUrl.pathname.replace(/^\/+?c\//, ''),
        },
      ]

      for (const [name, value] of categoryUrl.searchParams) {
        categoryRoute.query.push({
          name,
          value,
        })
      }

      invalidDestination.alternativeCategoryRoute = categoryRoute
    }

    for (const accessory of this.apiData.alternativeAccessories || []) {
      invalidDestination.alternativeAccessories.push({
        customText: cart.messageRubanDeuil,
        quantity: accessory.Quantity || 1,
        removable: true,
        variant: { code: accessory.Reference },
        /**
         * Price is not available here, so if requested by GraphQL, it will crash (as it is mandatory).
         * Even a field resolver won't do the job because GraphQL cannot guarantee that the variant will be
         * resolved (along with the price). The only thing we could do, is to get the
         * full variant from Hybris, but that's useless for an unused field..
         *
         * So we're leaving as is for now, even if GraphQL is incorrect,
         * this field is never called, so there shouldn't be a problem.
         */
      })
    }

    if (this.apiData.alternativeProduct) {
      const normalizePrice = (price) => {
        return {
          value: {
            amount: price.value,
            currencyCode: price.currency,
            type: price.type,
          },
        }
      }
      invalidDestination.alternativeProducts.push({
        customText: cart.messageRubanDeuil,
        discount:
          this.apiData.alternativeProduct.discountedPrice &&
          PriceTransformer.toGQL(normalizePrice(this.apiData.alternativeProduct.discountedPrice)),
        price: PriceTransformer.toGQL(normalizePrice(this.apiData.alternativeProduct.price)),
        quantity: 1 /** @todo mono product api for now, so we're safe */,
        removable: true,
        variant: { code: this.apiData.alternativeProduct.reference },
      })
    }

    invalidDestination.isValid = this.apiData.isValid
    invalidDestination.zoneGeo = this.apiData.zoneGeo

    return invalidDestination
  }
}

module.exports = InvalidDestinationTransformer
