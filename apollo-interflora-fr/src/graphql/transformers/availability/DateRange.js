const config = require('../../../config')
const { addTZOffsetToDate, formatToAPI } = require('../../../helpers/dates')

const { DateRangeTransformer: HybrisDateRangeTransformer } = require('../../resolvers/fr-hybris/transformers')
const Transformer = require('../Transformer')

/**
 * @extends {Transformer<string[], GQLDateRange[], { days: Number }>}
 */
class DateRangeTransformer extends Transformer {
  transformToGQL() {
    const hybrisPayload = {
      excludedDates: [...this.apiData].map((d) => this._toAPI2Hybris(new Date(d))),
      firstDate: this._toAPI2Hybris(new Date()),
      lastDate: this._toAPI2Hybris(new Date(Date.now() + this.options.days * 24 * 3600 * 1000)),
    }

    return HybrisDateRangeTransformer.toGQL(hybrisPayload)
  }

  /**
   * @param {string[]} unavailabilities
   * @param {GraphQLContext} context
   * @param {{ days: Number }} options
   * @returns {GQLDateRange[]}
   */
  static toGQL(unavailabilities, context, options = { days: config.availability.daysToCheck }) {
    const transformer = new DateRangeTransformer(unavailabilities, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @param {string} s - "2022-08-14T22:00:00+00:00" is fo
   * @returns {Date}
   */
  _toAPI2Hybris(s) {
    const tz = config.apps[this.context.siteId].timezone
    return formatToAPI(addTZOffsetToDate(new Date(s), tz))
  }
}

module.exports = DateRangeTransformer
