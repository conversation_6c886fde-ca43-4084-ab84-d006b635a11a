const { hash } = require('../../../helpers/string')

const GQLTown = require('../../models/Town')
const Transformer = require('../Transformer')

/**
 * @typedef {{
 *  Name: string
 *  PostalCode: string
 * }} Place
 */

/**
 * @extends {Transformer<Place, GQLTown, {}>}
 */
class TownTransformer extends Transformer {
  transformToGQL() {
    const town = new GQLTown()

    town.label = this.apiData.Name
    town.postalCode = this.apiData.PostalCode
    town.province = this.apiData.Province

    // province may be missing from availability service
    if (!town.province && this.context.countryId === 'ES') {
      if (/^ceuta$/i.test(town.label)) {
        town.province = 'Ceuta'
      } else if (/^melilla/i.test(town.label)) {
        town.province = 'Melilla'
      }
    }

    if (!town.province && !town.postalCode) {
      this.context.log.error(this.apiData, `no postal code nor province found for ${this.apiData.Name}`)
    }

    town.id = hash(town)

    return town
  }

  /**
   * @param {Place} place
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLTown}
   */
  static toGQL(place, context, options = {}) {
    const transformer = new TownTransformer(place, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TownTransformer
