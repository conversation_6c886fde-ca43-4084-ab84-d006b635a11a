const CONST = require('../../../const')

const { GQLDelivery } = require('../../models')

const Transformer = require('../Transformer')
const ITUserAddressTransformer = require('./Address')
const ITUserPriceTransformer = require('./Price')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<string, GQLOrder, {orderId: string}>}
 */
class DeliveryTransformer extends Transformer {
  transformToGQL() {
    const delivery = new GQLDelivery()

    delivery.address = ITUserAddressTransformer.toGQL(this.apiData.address, this.context, {
      type: CONST.address.addressType.delivery,
    })
    delivery.availableDateRanges = this.availableDateRanges || []
    delivery.contact = this.apiData.contact
    delivery.date = new Date(this.apiData.date)
    if (this.apiData.discount) delivery.discount = ITUserPriceTransformer.toGQL(this.apiData.discount, this.context)
    delivery.maxDayDelivery = this.apiData.maxDayDelivery
    delivery.mode = this.apiData.mode ?? CONST.cart.deliveryMode.standard
    delivery.price = ITUserPriceTransformer.toGQL(this.apiData.price, this.context)
    delivery.rangeHour = this.apiData.rangeHour
    delivery.sender = ITUserAddressTransformer.toGQL(this.apiData.sender, this.context, {
      type: CONST.address.addressType.sender,
    })

    switch (this.apiData.status) {
      case 'CAPTURED':
        if (delivery.date.getTime() < Date.now() - 1000 * 3600 * 24) {
          delivery.status = CONST.order.deliveryStatus.FAC // delivered
        } else {
          delivery.status = CONST.order.deliveryStatus[11] // in progress
        }
        break
      case 'PENDING':
        delivery.status = CONST.order.deliveryStatus[10]
        break
      case 'REFUNDED':
      case 'VOIDED':
        delivery.status = CONST.order.deliveryStatus.ANN // cancelled
        break
      default:
        delivery.status = CONST.order.deliveryStatus.UNKNOWN
    }
    delivery.statusFallbackLabel = this.apiData.statusFallbackLabel || ''
    delivery.time = this.apiData.time
    delivery.trackingId = this.apiData.trackingId
    delivery.trackingUrl = this.apiData.trackingUrl
    delivery.useFixHour = this.apiData.useFixHour || false
    return delivery
  }

  /**
   * @param {object} delivery
   * @param {GraphQLContext} context
   * @param {object} options
   * @returns {GQLOrder}
   */
  static toGQL(delivery, context, options = {}) {
    const transformer = new DeliveryTransformer(delivery, context, options)
    return transformer.transformToGQL()
  }
}
module.exports = DeliveryTransformer
