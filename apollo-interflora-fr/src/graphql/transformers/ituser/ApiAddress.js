const CONST = require('../../../const')
const { getCountryName } = require('../../../helpers/countries')

const civilityMap = {
  [CONST.user.civility.mr]: 1,
  [CONST.user.civility.mme]: 2,
}

/**
 * @param {GQLInputAddress} gqlAddress
 * @param {GraphQLContext} context
 * @returns {any}
 */
const fromGQLInputAddress = (gqlAddress, context) => {
  const { civility, countryId, markDefault, postalCode, townId, townLabel } = gqlAddress
  const result = {
    ...gqlAddress,
    civility: civility ? civilityMap[civility] : undefined,
    country: countryId
      ? {
          id: countryId,
          label: getCountryName(countryId, context.language),
        }
      : undefined,
    default: markDefault || false,
    town:
      townId || townLabel
        ? {
            id: townId,
            label: townLabel,
            postalCode,
          }
        : undefined,
  }

  delete result.store
  delete result.markDefault
  delete result.townId
  delete result.townLabel
  delete result.countryId

  return result
}

module.exports = {
  fromGQLInputAddress,
}
