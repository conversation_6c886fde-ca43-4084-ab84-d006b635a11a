const { PhoneNumberUtil } = require('google-libphonenumber')

const Transformer = require('../Transformer')
const PhoneTransformer = require('../Phone')

const phoneUtil = PhoneNumberUtil.getInstance()

class ITUserPhoneTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData?.number) {
      return null
    }
    let countryCode

    if (!this.apiData.prefix) {
      try {
        const countryIds = [this.context.countryId]
        if (this.options.countryId) countryIds.unshift(this.options.countryId)

        for (const id of countryIds) {
          if (phoneUtil.isPossibleNumberString(this.apiData.number, id)) {
            countryCode = id
            break
          }
        }
      } catch (e) {
        // do nothing
      }
    }

    return PhoneTransformer.toGQL(`${this.apiData.prefix ?? ''}${this.apiData.number}`, this.context, {
      countryCode: countryCode ?? this.options.countryId,
    })
  }

  static toGQL(apiData, context, options) {
    const transformer = new ITUserPhoneTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ITUserPhoneTransformer
