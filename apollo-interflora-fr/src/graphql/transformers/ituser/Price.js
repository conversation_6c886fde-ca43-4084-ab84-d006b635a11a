const config = require('../../../config')

const { GQLPrice } = require('../../models')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<ITUserPrice, GQLPrice, {}>}
 */
class ITUserPriceTransformer extends Transformer {
  transformToGQL() {
    const price = new GQLPrice()

    price.currencyIso = this.apiData.currencyIso || config.apps[this.context.siteId].currency
    price.value = Math.round((this.apiData.value || 0) * 100)

    return price
  }

  static toGQL(apiData, context, options = {}) {
    const transformer = new ITUserPriceTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ITUserPriceTransformer
