const _ = require('lodash')
const countries = require('i18n-iso-countries')

const CONST = require('../../../const')

const { GQLAddress, GQLTown } = require('../../models')
const Transformer = require('../Transformer')
const CountryTransformer = require('../gfs/Country')
const ITUserPhoneTransformer = require('./Phone')

const civilities = _.invert(CONST.user.civility)

class ITUserAddressTransformer extends Transformer {
  transformToGQL() {
    const {
      address,
      addressId,
      address2,
      civility,
      company,
      country,
      default: isDefault,
      email,
      firstName,
      id,
      lastName,
      phone,
      postalCode,
      stored,
      town,
      twitterId,
      type,
    } = this.apiData

    const result = new GQLAddress()
    result.address = address
    result.addressId = addressId
    result.address2 = address2
    result.civility = civilities[civility] ? civility : undefined
    result.company = company

    {
      let countryId = country?.id
      if (!countryId && country?.label) countryId = countries.getAlpha2Code(country.label, 'it')

      if (countryId) {
        result.country = CountryTransformer.toGQL(countryId, this.context)
      }
    }
    result.default = isDefault
    result.email = email
    result.firstName = firstName
    result.id = id
    result.lastName = lastName
    result.phone = ITUserPhoneTransformer.toGQL(phone, this.context, {
      countryId: country?.id,
    })
    result.postalCode = postalCode || town?.postalCode
    result.stored = stored

    if (town) {
      const gqlTown = new GQLTown()
      gqlTown.id = town.id
      gqlTown.postalCode = town.postalCode || postalCode
      gqlTown.label = town.label
      result.town = gqlTown
    }

    result.twitterId = twitterId
    result.type = this.options.type || type

    return result
  }

  /**
   * @param {*} address
   * @param {GraphQLContext} context
   * @param {{
   *  type: string
   * }} options
   * @returns {GQLAddress}
   */
  static toGQL(address, context, options) {
    const transformer = new ITUserAddressTransformer(address, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ITUserAddressTransformer
