const CONST = require('../../../const')

const { GQLOrder, GQLProductVariant, GQLProduct, GQLImage } = require('../../models')

const Transformer = require('../Transformer')
const ITUserDeliveryTransformer = require('./Delivery')
const ITUserPriceTransformer = require('./Price')
const ITUserTransformer = require('./User.js')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<string, GQLOrder>}
 */
class ITUserOrderTransformer extends Transformer {
  transformToGQL() {
    const order = new GQLOrder()

    const { createdAt, delivery, id, number, paymentMode, productItems, total } = this.apiData

    order.accessoryItems = (this.apiData.accessoryItems || []).map((i) => this._item(i))
    order.availableEdit = this.apiData.availableEdit || false
    order.billUrl = this.apiData.billUrl
    order.canAttachOrder = false
    order.createdAt = new Date(createdAt)
    if (delivery) {
      order.delivery = ITUserDeliveryTransformer.toGQL(delivery, this.context)
      order.delivery.parentId = id
    }
    order.discountCodes = []
    // for the time being, the api returns a single string for discountCodes
    if (this.apiData.discountCodes)
      order.discountCodes = Array.isArray(this.apiData.discountCodes)
        ? this.apiData.discountCodes
        : [this.apiData.discountCodes]
    order.discountCodesPrice = ITUserPriceTransformer.toGQL(
      this.apiData.discountCodesPrice || {
        value: 0,
      },
      this.context
    )

    order.discountedTotal =
      (this.apiData.discountedTotal?.value || 0) > 0
        ? ITUserPriceTransformer.toGQL(this.apiData.discountedTotal, this.context)
        : ITUserPriceTransformer.toGQL(total, this.context)
    order.emailHasRegisteredUser = this.apiData.emailHasAccountNoGuest

    order.externalReference = this.apiData.externalReference
    order.id = number
    order.joinMessage = this.apiData.joinMessage
    order.number = id
    order.paymentMode = paymentMode

    order.productItems = (productItems || []).map((i) => this._item(i))
    order.reason = this.apiData.reason
    order.reasons = this.apiData.reasons || []
    order.total = ITUserPriceTransformer.toGQL(total, this.context)
    order.signature = this.apiData.signature
    order.user = this.apiData.user ? ITUserTransformer.toGQL(this.apiData.user, this.context) : null
    order.voucher = this.apiData.voucher

    return order
  }

  _item(apiItem) {
    const { discount, price, removable, total, ...item } = apiItem

    if (discount?.value) item.discount = ITUserPriceTransformer.toGQL(discount, this.context)
    item.price = ITUserPriceTransformer.toGQL(price, this.context)
    item.quantity = apiItem.quantity || 0
    item.removable = Boolean(removable)
    item.total = ITUserPriceTransformer.toGQL(total, this.context)

    {
      const product = new GQLProduct()

      const variant = new GQLProductVariant()
      variant.parent = product

      const images = []
      if (apiItem.productPicture) {
        for (const type of [
          CONST.images.types.gallery,
          CONST.images.types.primary,
          CONST.images.types.primaryThumbnail,
        ]) {
          const image = new GQLImage()
          image.altText = apiItem.productName || ''
          ;(image.format = CONST.images.formats.mobile), (image.type = type)
          image.order = 0
          image.url = apiItem.productPicture

          images.push(image)
        }
      }

      variant.code = apiItem.productKey
      variant.discount = item.discount
      variant.images = images
      variant.isFromWorkshop = false
      variant.label = apiItem.productName || ''
      variant.price = item.price
      variant.qualifiers = []
      variant.reinsurances = []
      variant.sku = apiItem.productKey
      variant.variationCode = CONST.product.variationCodes[0]

      product.classification = CONST.product.productClassifications.NONE
      product.code = apiItem.productKey
      product.key = apiItem.productKey
      product.name = apiItem.productName
      product.slugUrl = apiItem.productName || '#'
      product.variants = [variant]
      product._defaultVariants = [variant]

      // fake
      product.setLoaded(CONST.dataSources.commerceTools)
      product.setLoaded(CONST.dataSources.datocms)

      item.variant = variant
    }

    return item
  }

  /**
   * @param {object} order
   * @param {GraphQLContext} context
   * @param {object} options
   * @returns {GQLOrder}
   */
  static toGQL(order, context, options = {}) {
    const transformer = new ITUserOrderTransformer(order, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ITUserOrderTransformer
