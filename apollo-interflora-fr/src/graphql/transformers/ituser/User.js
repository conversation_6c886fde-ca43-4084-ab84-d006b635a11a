const countries = require('i18n-iso-countries')

const { GQLUser, GQLTown } = require('../../models')
const ITUserAddressTransformer = require('./Address')
const ITUserPhoneTransformer = require('./Phone')
const ITUserOrderTransformer = require('./Order')
const Transformer = require('../Transformer')
const CONST = require('../../../const')
/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<string, GQLOrder>}
 */
class ItUserUserTransformer extends Transformer {
  transformToGQL() {
    const user = new GQLUser()
    const {
      address,
      addressId,
      address2,
      birthDate,
      civility,
      company,
      companyNumber,
      country,
      defaultDeliveryAddress,
      defaultSenderAddress,
      email,
      firstName,
      fiscalCode,
      id,
      lastName,
      lastOrder,
      orders,
      pec,
      phone,
      postalCode,
      premium,
      role,
      town,
      type,
      username,
      vatNumber,
    } = this.apiData

    user.username = username
    user.email = email
    user.type = role === 'NUMEROVERDE' ? CONST.user.userType.customerCare : type

    user.countryId = country?.id
    if (!user.countryId && country?.label) {
      user.countryId = countries.getAlpha2Code(country.label, 'it')
    }

    user.id = id || username

    if (user.type === CONST.user.userType.customerCare) {
      user.orders = {
        order: [],
        total: 0,
      }
      return user
    }

    user.civility = ItUserUserTransformer.toGQLCivility(civility)
    user.address = address
    user.addressId = addressId
    user.address2 = address2
    const birth = new Date(birthDate)
    if (birth.getTime() > 0) {
      user.birthDate = birth
    }
    user.company = company
    user.companyNumber = companyNumber
    user.firstName = firstName
    user.fiscalCode = fiscalCode
    user.lastName = lastName
    user.lastOrder = lastOrder ? ITUserOrderTransformer.toGQL(lastOrder, this.context) : null
    user.orders = orders
      ? {
          orders: orders.orders.map((o) => ITUserOrderTransformer.toGQL(o, this.context)),
          total: orders.total,
        }
      : {
          orders: [],
          total: 0,
        }

    user.pec = pec
    if (phone) {
      user.phone = ITUserPhoneTransformer.toGQL(phone, this.context, {
        countryId: user.countryId,
      })
    }
    user.postalCode = postalCode

    const premiumDate = new Date(premium)
    if (premiumDate.getTime() > 0) {
      user.premium = premiumDate
    }

    if (town && town.label) {
      const townModel = new GQLTown()
      townModel.id = town.id
      townModel.label = town.label
      townModel.postalCode = town.postalCode
      user.town = townModel
    }

    if (defaultDeliveryAddress) {
      user.defaultDeliveryAddress = ITUserAddressTransformer.toGQL(defaultDeliveryAddress, this.context)
    }

    if (defaultSenderAddress) {
      user.defaultSenderAddress = ITUserAddressTransformer.toGQL(defaultSenderAddress, this.context)
    }

    user.vatNumber = vatNumber

    return user
  }

  static toGQLCivility(titleCode) {
    return Object.values(CONST.user.civility).includes(titleCode) ? titleCode : null
  }

  static toGQL(apiData, context) {
    const transformer = new ItUserUserTransformer(apiData, context)
    return transformer.transformToGQL()
  }
}

module.exports = ItUserUserTransformer
