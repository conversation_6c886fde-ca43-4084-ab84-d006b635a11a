class ITUserSessionTransformer {
  /**
   * @param {{
   *   createdAt: number,
   *   lifetime: number,
   *   refreshLifetime: number,
   *   refreshToken: number,
   *   user: GQLUser,
   *   value: string,
   * }} response
   * @returns {SessionApiData<{}>}
   */
  static fromResponse(response) {
    const { createdAt, lifetime, refreshLifetime, refreshToken, user, value } = response

    if (!user) {
      return
    }

    return {
      accessToken: {
        lifetime,
        value,
      },
      createdAt: parseInt(createdAt, 10) * 1000,
      legacy: true,
      refreshToken: {
        lifetime: refreshLifetime,
        value: refreshToken,
      },
      scope: null,
      userId: user.id,
      username: user.username,
    }
  }
}

module.exports = ITUserSessionTransformer
