const Transformer = require('../Transformer')

const { GQLLinkItem, GQLRoute, GQLSearchSuggestions } = require('../../models')

/** @typedef {import('../../datasources/bloomreach/_typedef')} */

/**
 * @typedef {{
 *  categorySlugs: Map<string, string|null>
 *  categoryTree: CategoryTreeRoot
 * }} BloomreachSearchSuggestionsTransformerOptions
 */

/**
 * @extends {Transformer<BloomreachAutoSuggestResponse, GQLSearchSuggestions, BloomreachSearchSuggestionsTransformerOptions>}
 */
class BloomreachSearchSuggestionsTransformer extends Transformer {
  transformToGQL() {
    const result = new GQLSearchSuggestions()
    const categorySlugs = this.options.categorySlugs ?? new Map()

    for (const group of this.apiData.suggestionGroups) {
      for (const querySuggestion of group.querySuggestions) {
        result.suggestions.push(querySuggestion.displayText)
      }

      for (const attributeSuggestion of (group.attributeSuggestions ?? []).filter(
        (s) => s.attributeType === 'category' && categorySlugs.get(s.value)
      )) {
        const slug = categorySlugs.get(attributeSuggestion.value)
        const parentSlug = this.options?.categoryTree?.find(slug)?.parent?.slug

        const link = new GQLLinkItem()
        link.label = attributeSuggestion.name

        link.url = new GQLRoute()
        link.url.name = parentSlug ? 'parent_category' : 'category'
        link.url.params.push({
          name: 'slugCategory',
          value: slug,
        })
        if (parentSlug) {
          link.url.params.push({
            name: 'slugParentCategory',
            value: parentSlug,
          })
        }
        link.url.params.push({
          name: 'url',
          value: `/c${parentSlug ? `/${parentSlug}` : ''}/${slug}`,
        })

        result.categories.push(link)
      }

      for (const searchSuggestion of group.searchSuggestions ?? []) {
        result.products.push({
          __typename: 'Product',
          code: searchSuggestion.pid,
        })
      }
    }

    return result
  }

  /**
   * @param {BloomreachAutoSuggestResponse} apiData
   * @param {GraphQLContext} context
   * @param {BloomreachSearchSuggestionsTransformerOptions} options
   * @returns {GQLSearchSuggestions}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new BloomreachSearchSuggestionsTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = BloomreachSearchSuggestionsTransformer
