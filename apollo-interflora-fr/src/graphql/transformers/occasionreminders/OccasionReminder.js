const Transformer = require('../Transformer')
const GQLOccasionReminder = require('../../models/OccasionReminder')

/**
 * Transformer for OccasionReminder -> GQLOccasionReminder
 */
class OccasionReminderTransformer extends Transformer {
  /**
   * Transforms a raw reminder object to a GQLOccasionReminder instance.
   * @returns {GQLOccasionReminder}
   */
  transformToGQL() {
    const gqlReminder = new GQLOccasionReminder()
    gqlReminder.id = this.apiData.id
    gqlReminder.recipientName = this.apiData.recipientName
    gqlReminder.occasionType = this.transformOccasionType(this.apiData.occasionType)
    gqlReminder.date = this.apiData.date
    gqlReminder.relation = this.apiData.relation

    return gqlReminder
  }

  static toGQL(apiData, context, options) {
    const transformer = new OccasionReminderTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }

  /**
   * Transforms the occasionType into a supported GraphQL enum value
   * @param {OccasionReminderType} value
   * @returns {string|null}
   */
  transformOccasionType(value) {
    return value?.toUpperCase?.() || null
  }
}

module.exports = OccasionReminderTransformer
