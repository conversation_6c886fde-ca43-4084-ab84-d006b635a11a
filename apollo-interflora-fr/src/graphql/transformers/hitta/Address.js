const Transformer = require('../Transformer')
const GQLAddress = require('../../models/Address')
const SVUserPhoneTransformer = require('../seuser/Phone')
const CONST = require('../../../const')

class AddressTransformer extends Transformer {
  transformToGQL() {
    const address = new GQLAddress()

    const civility = (attributes) => {
      const civility = attributes.find((attr) => attr.name === 'gender')?.value
      if (civility === 'M') {
        return CONST.user.civility.mr
      } else if (civility === 'F') {
        return CONST.user.civility.mme
      }
      return null
    }

    const firstAddress = this.apiData.address?.[0] ?? {}

    address.address = `${firstAddress.number} ${firstAddress.street}`
    address.civility = civility(this.apiData.attribute ?? [])
    address.id = this.apiData.id
    address.fullName = this.apiData.displayName ?? ''
    address.phone = SVUserPhoneTransformer.toGQL({ Phone: this.apiData.phone?.[0]?.callTo ?? null })
    address.postalCode = firstAddress.zipcode ?? null
    address.stored = false
    address.town = {
      id: this.apiData.id,
      label: firstAddress.city ?? '',
      postalCode: firstAddress.zipcode ?? null,
    }
    address.type = CONST.address.addressType.delivery

    return address
  }

  static toGQL(apiData, context, options) {
    const transformer = new AddressTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = AddressTransformer
