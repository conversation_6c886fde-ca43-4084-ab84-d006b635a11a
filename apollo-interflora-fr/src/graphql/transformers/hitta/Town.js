const Transformer = require('../Transformer')
const GQLTown = require('../../models/Town')
const { hash } = require('../../../helpers/string')

/**
 * @extends {Transformer<PSLocation, GQLTown, {}>}
 */
class TownTransformer extends Transformer {
  transformToGQL() {
    try {
      const town = new GQLTown()

      town.label = `${this.apiData.address.city.toUpperCase()} (${this.apiData.address.zipcode})`
      town.postalCode = this.apiData.address.zipcode.toString()
      town.province = this.apiData.address.county
      town.id = hash(town)

      return { town, valid: true }
    } catch (error) {
      this.context.log.error('Error transforming PSLocation to GQL:', error)
      return { town: null, valid: false }
    }
  }

  /**
   * @param {PSLocation} LocationAddress
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {{ town: GQLTown, valid: boolean }}
   */
  static toGQL(LocationAddress, context, options = {}) {
    const transformer = new TownTransformer(LocationAddress, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TownTransformer
