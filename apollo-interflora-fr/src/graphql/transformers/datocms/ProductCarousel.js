const CONST = require('../../../const')
const { GQLProduct } = require('../../models')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<ProductCarouselRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class ProductCarouselRecordTransformer extends Transformer {
  transformToGQL() {
    // For some reasons there seem to be some null values inserted on french octopus so we insert them here as well..
    const component = new GQLComponent()

    switch (this.apiData.display) {
      case CONST.cms.dato.productCarouselDisplay.single:
      case CONST.cms.dato.productCarouselDisplay.double:
      case CONST.cms.dato.productCarouselDisplay.crossSell:
        component.name = CONST.cms.positions.slider_product
        break
      case CONST.cms.dato.productCarouselDisplay.simple:
        component.name = CONST.cms.positions.slider_counter1
        break
    }
    if (!component.name) {
      return
    }

    component.params.push({
      name: 'title',
      value: [new GQLScalarString(this.apiData.title)],
    })

    component.params.push({
      name: 'subtitle',
      value: [new GQLScalarString(this.apiData.subtitle)],
    })

    if (this.apiData.link) {
      const button_label = []
      const button_link = []
      for (const link of this.apiData.link) {
        button_label.push(new GQLScalarString(link.label))
        button_link.push(
          DatoUrlTransformer.toGQL(link, this.context, {
            categoryTree: this.options.categoryTree,
          })
        )
      }
      component.params.push({
        name: 'button_label',
        value: button_label,
      })
      component.params.push({
        name: 'button_link',
        value: button_link,
      })
    }
    const products = []
    for (const product of this.apiData.products) {
      let prod = new GQLProduct()
      prod.code = product.commercetoolsProduct
      products.push(prod)
    }
    component.params.push({
      name: 'products',
      value: products,
    })

    return component
  }

  /**
   * @param {ProductCarouselRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ProductCarouselRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductCarouselRecordTransformer
