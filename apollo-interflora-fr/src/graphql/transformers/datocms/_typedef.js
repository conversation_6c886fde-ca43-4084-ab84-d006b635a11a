/** @typedef {import('../../datasources/datocms/_typedef')} */
/** @typedef {import('../../../helpers/categoryTree').CategoryTreeRoot} CategoryTreeRoot */
/** @typedef {import('../../../helpers/dato/LegalText')}

/**
 * @typedef {{
 *  categoryTree: CategoryTreeRoot
 *  page: (DatoCategoryPage|DatoLandingPage|DatoPage|DatoProduct)
 * }} DatoBlockTransformerOptions
 */

/**
 * @typedef {{
 *  categoryTree: CategoryTreeRoot
 *  obfuscationPageType: ('category'|'funnel'|'home'|'other'|'product')
 * }} DatoNavigationItemTransformerOptions
 */

/**
 * @typedef {{
 *  canonical: string
 *  category: DatoCategoryPage
 *  categoryTree: CategoryTreeRoot
 *  footers: DatoFooterNavigation
 *  headerBanner: DatoHeaderBannerRecord
 *  mainNavigation: DatoMainNavigation
 *  parentCategory: DatoCategoryPage
 *  parentPage: DatoPage
 *  legalText: LegalText
 *  type: ('category'|'cms'|'floristDeparment'|'floristDepartments'|'floristTown'|'product'),
 *  zones: string[]
 * }} DatoPageTransformerOptions
 */
