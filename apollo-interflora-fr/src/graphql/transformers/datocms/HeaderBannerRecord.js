const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')

const { render } = require('datocms-structured-text-to-html-string')
const { stripHTMLRootTag } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoHeaderBannerRecord, GQLComponent, {}>}
 */
class HeaderBannerRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'wysiwyg'
    component.params = [
      {
        name: 'content',
        value: [new GQLScalarString(stripHTMLRootTag(render(this.apiData.content?.value)))],
      },
    ]

    return component
  }

  static toQGL(apiData, context, options = {}) {
    const transformer = new HeaderBannerRecordTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = HeaderBannerRecordTransformer
