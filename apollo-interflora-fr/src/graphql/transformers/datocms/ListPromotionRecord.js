const CONST = require('../../../const')
const config = require('../../../config')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')
const GQLImage = require('../../models/Image')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoListPromotionRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class ListPromotionRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.image1

    if (this.apiData.image) {
      let images = new GQLSourceImages()
      let image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)
      images.sources = [image]
      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    component.params.push({
      name: 'description',
      value: [new GQLScalarString(this.apiData.description)],
    })

    if (this.apiData.link?.[0]) {
      component.params.push({
        name: 'link_label',
        value: [new GQLScalarString(this.apiData.link[0].label)],
      })
      component.params.push({
        name: 'link',
        value: [
          DatoUrlTransformer.toGQL(this.apiData.link[0], this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }

    return component
  }

  /**
   * @param {DatoListPromotionRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ListPromotionRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ListPromotionRecordTransformer
