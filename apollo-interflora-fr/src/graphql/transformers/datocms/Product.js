const CONST = require('../../../const')
const { render } = require('datocms-structured-text-to-html-string')

const GQLBadge = require('../../models/Badge')
const GQLClassification = require('../../models/Classification')
const GQLClassificationFeature = require('../../models/ClassificationFeature')
const GQLProduct = require('../../models/Product')

const Transformer = require('../Transformer')

const DatoImageTransfomer = require('./Image')

/** @typedef {import('./_typedef')} */
/** @typedef {import('../../../helpers/dato/LegalText')} */

const { stripHTMLRootTag } = require('../../../helpers/string')
const config = require('../../../config')

/**
 * @typedef {{
 *  isAccessory: boolean
 *  seaKey: string
 * }} DatoProductTransformerOptions
 */

/**
 * @class DatoProductTransformer
 * @extends {Transformer<DatoProduct, GQLProduct, DatoProductTransformerOptions>}
 */
class DatoProductTransformer extends Transformer {
  transformToGQL() {
    const product = new GQLProduct()

    if (this.options.isAccessory) {
      product.__typename = 'Accessory'
      // NOTE: this fields are set by specific resolvers for accessories
      product.useQuantitySelector = undefined
    } else {
      product.useQuantitySelector = this.apiData.useQuantitySelector || false
    }
    product._datoDescription = this.apiData.body?.value ? stripHTMLRootTag(render(this.apiData.body.value)) : ''

    product.code = this.apiData.commercetoolsProduct
    product.name = this.apiData.title
    product.mainCategoryId = this.apiData.defaultCategory?.commercetoolsCategory
    product.seaKey = this.options.seaKey
    product._datoProductType = this.apiData.productType

    for (const reinsurance of this.apiData.reinsurance || []) {
      product.reinsurances.push({
        icon: reinsurance.icon,
        label: reinsurance.title,
      })
    }
    product.highlightText = this.apiData.highlightedText
    product.slugUrl = this.apiData.slug
    // restore the sea slug suffix
    if (this.options.seaKey) {
      const slugSuffix = config.sea[this.options.seaKey].slugSuffix
      if (!product.slugUrl.endsWith(slugSuffix)) {
        product.slugUrl += slugSuffix
      }
    }

    if (this.apiData.additionalInformations) {
      product.additionalInfo = {
        content: this.apiData.additionalInformations,
        type: 'INFO',
      }
    }

    if (this.apiData.careDifficulty) {
      const classification = new GQLClassification()
      classification.code = CONST.product.classifications.MAINTENANCE_LEVEL.code
      classification.name = classification.code // @todo

      const feature = new GQLClassificationFeature()
      feature.code = CONST.product.classifications.MAINTENANCE_LEVEL.featureCode
      feature.values = [
        {
          icon: this.apiData.careDifficulty,
          value: this.apiData.careDifficulty, // @todo
        },
      ]

      classification.features = [feature]
      product.classifications.push(classification)
    }

    if (this.apiData.badge) {
      const { id, key, title } = this.apiData.badge
      const badge = new GQLBadge()
      badge.id = id
      badge.key = key
      badge.title = title
      product.badge = badge
    }

    if (this.apiData.vimeoVideo) {
      product.addImage(
        DatoImageTransfomer.toGQL(undefined, this.context, {
          alt: product.name,
          format: CONST.images.formats.vimeo,
          order: 0,
          type: 'VIDEO',
          url: this.apiData.vimeoVideo.url,
        })
      )

      product.addImage(
        DatoImageTransfomer.toGQL(undefined, this.context, {
          alt: product.name,
          format: CONST.images.formats.mobile,
          order: 0,
          type: 'VIDEO_THUMBNAIL',
          url: this.apiData.vimeoVideo.thumbnailUrl,
        })
      )
    }

    if (this.apiData.richDescription?.[0]) {
      const block = this.apiData.richDescription[0]
      product.descriptionTitle = block.title
      if (block.description) {
        product._datoDescription = stripHTMLRootTag(block.description)
      }

      if (block.image) {
        product.addImage(
          DatoImageTransfomer.toGQL(block.image, this.context, {
            alt: block.image.alt || product.name,
            format: CONST.images.formats.mobile,
            order: 0,
            type: CONST.images.types.descriptionBlock,
          })
        )
      }

      if (block.items.length > 0) {
        const classification = new GQLClassification()
        classification.code = CONST.product.classifications.PLANT_FEATURE.code

        classification.features = block.items.map((teaser) => ({
          code: teaser.cssClass,
          icon: teaser.cssClass,
          name: teaser.title,
          values: [
            {
              value: stripHTMLRootTag(teaser.subtitle),
            },
          ],
        }))
        product.classifications.push(classification)
      }
    }

    product.setLoaded(CONST.dataSources.datocms)

    return product
  }

  /**
   * @param {DatoProduct} apiData
   * @param {CTDatocmsContext} context
   * @param {DatoProductTransformerOptions} options
   * @returns {GQLProduct}
   */
  static toGQL(apiData, context, options) {
    const transformer = new DatoProductTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoProductTransformer
