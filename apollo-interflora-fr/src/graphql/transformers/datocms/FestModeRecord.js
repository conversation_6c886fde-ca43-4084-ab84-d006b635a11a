const Transformer = require('../Transformer')
const GQLFestMode = require('../../models/FestMode')

/**
 * @extends {Transformer<DatoFestMode[], GQLComponent, {}>}
 */
class FestModeTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLFestMode()
    if (this.apiData.length > 0) {
      component.date = new Date(this.apiData[0].date)
      component.id = this.apiData[0].id
      component.label = this.apiData[0].label
    }
    return component
  }

  /**
   * @param {DatoFestMode} festData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLFestMode}
   */
  static toGQL(festData, context, options = {}) {
    const transformer = new FestModeTransformer(festData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = FestModeTransformer
