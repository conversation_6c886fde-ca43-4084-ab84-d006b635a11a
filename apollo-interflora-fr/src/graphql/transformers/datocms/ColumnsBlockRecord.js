const CONST = require('../../../const')
const { GQLScalarBoolean } = require('../../models')
const GQLColumnImage = require('../../models/ColumnImage')
const GQLComponent = require('../../models/Component')
const Transformer = require('../Transformer')
const DatoImageTransformer = require('./Image')
const DatoRouteTransformer = require('./Route')
const TitlePluginTransformer = require('./TitlePlugin')

/**
 * @extends {Transformer<{
 *  column: DatoColumnImageBlockRecord[],
 *  reverse: boolean,
 * }, GQLComponent, {}>}
 */
class ColumnsBlockRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.columns
    component.params.push({
      name: 'columns',
      value: (this.apiData.column || []).map((column) => {
        const gqlColumn = new GQLColumnImage()
        const { cta, description, image, align, title, subtitle } = column
        gqlColumn.cta = this.transformCta(cta)
        gqlColumn.description = description ?? null
        gqlColumn.image = image ? DatoImageTransformer.toGQL(image, this.context) : null
        gqlColumn.title = TitlePluginTransformer.toGQL(title ?? '', this.context)
        gqlColumn.subtitle = subtitle ?? null
        gqlColumn.align = align ?? 'left'

        return gqlColumn
      }),
    })

    component.params.push({
      name: 'reverse',
      value: [new GQLScalarBoolean(this.apiData.reverse ?? false)],
    })

    return component
  }

  /**
   * @param {DatoLinkRecord[]} datoCta
   * @returns {GQLRoute}
   */
  transformCta(datoCta) {
    if (!datoCta.length) {
      return null
    }

    return {
      label: datoCta[0].label,
      target: DatoRouteTransformer.toGQL(datoCta[0], this.context),
    }
  }

  /**
   * @param {{
   *  column: DatoColumnImageBlockRecord[],
   *  reverse: boolean,
   * }} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ColumnsBlockRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ColumnsBlockRecordTransformer
