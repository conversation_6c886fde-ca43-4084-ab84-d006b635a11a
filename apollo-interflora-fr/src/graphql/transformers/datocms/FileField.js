const config = require('../../../config')
const CONST = require('../../../const')

const GQLImage = require('../../models/Image')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

class FileField extends Transformer {
  transformToGQL() {
    const image = new GQLImage()

    image.altText = this.apiData.alt || ''
    image.format = this.options.format || CONST.images.formats.mobile
    image.order = this.options.order || 0
    image.type = this.options.type || CONST.images.types.primary
    image.url = this.apiData.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)
    image.width = this.apiData.width
    image.height = this.apiData.height

    return image
  }

  /**
   * @param {DatoFileField} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  format: string,
   *  order: number,
   *  type: string
   * }} options
   * @returns {GQLImage}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new FileField(apiData, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @param {DatoFileField} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  format: string,
   *  order: number,
   *  type: string
   * }} options
   * @returns {GQLSourceImages}
   */
  static toSourceImages(apiData, context, options = {}) {
    const transformer = new FileField(apiData, context, options)
    const image = transformer.transformToGQL()

    const images = new GQLSourceImages()
    images.sources = [image]

    return images
  }
}

module.exports = FileField
