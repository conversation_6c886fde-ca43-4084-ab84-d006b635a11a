const config = require('../../../config')
const CONST = require('../../../const')
const { getPageSlugForContext } = require('../../../helpers/digitalCatalog')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @typedef {DatoBlockTransformerOptions & {
 *  name: ('moment_first' | 'hero' | 'home_block')
 * }} DatoMomentFirstTransformerOptions
 */

/**
 * @extends {Transformer<DatoMomentFirstRecord, GQLComponent, DatoMomentFirstTransformerOptions>}
 */
class DatoMomentFirstTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    if (
      !this.options.name &&
      this.options.page?.slug === getPageSlugForContext(CONST.cms.dato.pageSlugs.homepage, this.context)
    ) {
      component.name = 'home_block'
    } else {
      component.name = this.options.name || CONST.cms.positions.moment_first
    }

    const links = []
    const link_labels = []

    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    if (this.apiData.description) {
      component.params.push({
        name: 'description',
        value: [new GQLScalarString(this.apiData.description)],
      })
    }

    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    if (this.apiData.link?.[0]) {
      component.params.push({
        name: 'link_label',
        value: [new GQLScalarString(this.apiData.link?.[0].label)],
      })
      component.params.push({
        name: 'link',
        value: [
          DatoUrlTransformer.toGQL(this.apiData.link?.[0], this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    for (const link of this.apiData.dropdownLinks || []) {
      link_labels.push(new GQLScalarString(link.label))
      links.push(
        DatoUrlTransformer.toGQL(link, this.context, {
          categoryTree: this.options.categoryTree,
        })
      )
    }
    component.params.push({
      name: 'link_labels',
      value: link_labels,
    })
    component.params.push({
      name: 'links',
      value: links,
    })

    return component
  }

  /**
   * @param {DatoMomentFirstRecord} block
   * @param {GraphQLContext} context
   * @param {DatoMomentFirstTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoMomentFirstTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoMomentFirstTransformer
