const { GQLProduct } = require('../../models')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<ProductCarouselRecord, GQLComponent, {}>}
 */
class ProductListRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'product_list'

    component.params.push({
      name: 'title',
      value: [new GQLScalarString(this.apiData.title)],
    })

    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    if (this.apiData.link) {
      const button_label = []
      const button_link = []
      for (const link of this.apiData.link) {
        button_label.push(new GQLScalarString(link.label))
        button_link.push(
          DatoUrlTransformer.toGQL(link, this.context, {
            categoryTree: this.options.categoryTree,
          })
        )
      }
      component.params.push({
        name: 'button_label',
        value: button_label,
      })
      component.params.push({
        name: 'button_link',
        value: button_link,
      })
    }

    const products = []
    for (const product of this.apiData.products) {
      let prod = new GQLProduct()
      prod.code = product.commercetoolsProduct
      products.push(prod)
    }
    component.params.push({
      name: 'products',
      value: products,
    })

    return component
  }

  /**
   * @param {ProductListRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ProductListRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductListRecordTransformer
