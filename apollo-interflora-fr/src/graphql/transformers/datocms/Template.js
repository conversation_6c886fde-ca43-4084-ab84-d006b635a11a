const CONST = require('../../../const')

const GQLTemplate = require('../../models/Template')
const Transformer = require('../Transformer')
const BlockTransformer = require('./Block')

/**
 * @typedef {{
 *  categoryTree: CategoryTreeRoot
 *  page: (DatoCategoryPage|DatoLandingPage|DatoPage|DatoProduct)
 * }} TemplateTransformerOptions
 */

/**
 * @extends {Transformer<DatoSection, GQLTemplate, TemplateTransformerOptions>}
 */
class DatoTemplateTransformer extends Transformer {
  transformToGQL() {
    const template = new GQLTemplate()
    template.background = this.apiData.background

    for (const block of this.apiData.blocks) {
      const _block = BlockTransformer.toGQL(block, this.context, { ...this.options })
      if (_block) template.components.push(_block)
    }

    template.name = CONST.cms.defaultTemplateName
    template.wrapper = this.apiData.wrapper

    return template
  }

  /**
   * @param {DatoSectionRecord} section
   * @param {GraphQLContext} context
   * @param {TemplateTransformerOptions} options
   * @returns {GQLTemplate}
   */
  static toGQL(section, context, options = {}) {
    const transformer = new DatoTemplateTransformer(section, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoTemplateTransformer
