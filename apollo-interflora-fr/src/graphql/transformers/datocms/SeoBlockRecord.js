const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoSeoBlockRecord, GQLComponent, {}>}
 */
class SeoBlockTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.seo_block

    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }

    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    if (this.apiData.description) {
      component.params.push({
        name: 'description',
        value: [new GQLScalarString(this.apiData.description)],
      })
    }

    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    return component
  }

  /**
   * @param {DatoSeoBlockRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new SeoBlockTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = SeoBlockTransformer
