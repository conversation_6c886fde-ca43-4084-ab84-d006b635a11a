const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')

/**
 * @typedef {{
 * }} TitleTransformerOptions
 */

/**
 * @extends {Transformer<DatoTitleRecord, GQLComponent, TitleTransformerOptions>}
 */
class TitleTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.title
    component.params = [
      {
        name: 'level',
        value: [new GQLScalarString(this.apiData.level)],
      },
      {
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      },
    ]
    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    return component
  }

  /**
   * @param {DatoTitleRecord} block
   * @param {GraphQLContext} context
   * @param {TitleTransformerOptions} options
   * @return {GQLComponent}
   */
  static toGQL(block, context, options) {
    const transformer = new TitleTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TitleTransformer
