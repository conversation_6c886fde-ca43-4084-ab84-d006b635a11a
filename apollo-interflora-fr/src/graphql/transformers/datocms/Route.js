const GQLRoute = require('../../models/Route')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoLinkRecord, GQLRoute, DatoNavigationItemTransformerOptions>}
 */
class DatoRouteTransformer extends Transformer {
  transformToGQL() {
    const route = new GQLRoute()

    switch (this.apiData.target?.__typename) {
      case 'CategoryPageRecord':
        {
          const parentSlug = this.options.categoryTree?.find(this.apiData.target.slug)?.parent?.slug

          route.name = parentSlug ? 'parent_category' : 'category'
          route.params.push({
            name: 'slugCategory',
            value: this.apiData.target.slug,
          })
          if (parentSlug) {
            route.params.push({
              name: 'slugParentCategory',
              value: parentSlug,
            })
          }
          route.params.push({
            name: 'url',
            value: `/c${parentSlug ? `/${parentSlug}` : ''}/${this.apiData.target.slug}`,
          })
          if (this.apiData.target.id) {
            route.params.push({
              name: 'id',
              value: this.apiData.target.id,
            })
          }
        }
        break
      case 'PageRecord':
        switch (this.apiData.target.slug) {
          case 'floristDepartments':
          case 'index':
          case 'userMyAccount':
            route.name = this.apiData.target.slug
            break
          default:
            route.name = 'cms'
            route.params.push({
              name: 'slug',
              value: this.apiData.target.slug,
            })
            break
        }
        break
      case 'ProductRecord':
        route.name = 'product'
        route.params.push({
          name: 'slugProduct',
          value: this.apiData.target.slug,
        })
        route.params.push({
          name: 'code',
          value: this.apiData.target.commercetoolsProduct,
        })
        break
    }

    return route
  }

  /**
   * @param {DatoLinkRecord} apiData
   * @param {GraphQLContext} context
   * @param {DatoNavigationItemTransformerOptions} options
   * @returns {GQLRoute}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new DatoRouteTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoRouteTransformer
