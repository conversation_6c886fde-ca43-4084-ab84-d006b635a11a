const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoSeoImageBlockRecord, GQLComponent, {}>}
 */
class DatoSeoImageBlockRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.plants_care

    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }
    if (this.apiData.description) {
      component.params.push({
        name: 'descriptions',
        value: [new GQLScalarString(this.apiData.description)],
      })
    }
    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'mainImage',
        value: [images],
      })
    }

    const acc = {
      description: [],
      icon: [],
      subtitle: [],
    }

    for (const teaser of this.apiData.items) {
      for (const key of Object.keys(teaser)) {
        switch (key) {
          case 'subtitle':
            acc.description.push(new GQLScalarString(teaser[key]))
            break
          case 'title':
            acc.subtitle.push(new GQLScalarString(teaser[key]))
            break
          case 'cssClass':
            acc.icon.push(new GQLScalarString(teaser[key]))
            break
        }
      }
    }

    for (const [key, values] of Object.entries(acc)) {
      component.params.push({
        name: `${key}s`,
        value: values,
      })
    }

    return component
  }

  /**
   * @param {DatoSeoImageBlockRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoSeoImageBlockRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoSeoImageBlockRecordTransformer
