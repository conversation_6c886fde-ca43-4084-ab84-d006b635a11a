const Transformer = require('../Transformer')

const AccordionListRecord = require('./AccordionList')
const ArticleRecord = require('./Articles')
const CardBannerRecord = require('./CardBannerRecord')
const ColumnsBlockRecord = require('./ColumnsBlockRecord')
const ContentBlockRecord = require('./ContentBlockRecord')
const DeliveryInformationRecord = require('./DeliveryInformationRecord')
const ExpertBlockRecord = require('./ExpertBlockRecord')
const HeaderPhoneRecord = require('./HeaderPhoneRecord')
const ImageBlockRecord = require('./ImageBlockRecord')
const InterfloraPlusBlockRecord = require('./InterfloraPlusBlockRecord')
const MomentFirstRecord = require('./MomentFirstRecord')
const IconTeaserListRecord = require('./IconTeaserListTransformer')
const ListPromotionRecord = require('./ListPromotionRecord')
const ProductCarouselRecord = require('./ProductCarousel')
const ProductListRecord = require('./ProductListRecord')
const ProductListReassuranceItemRecord = require('./ProductListReassuranceItemRecord')
const QuoteRecord = require('./QuoteRecord')
const RotatingBannerRecord = require('./RotatingBannerRecord')
const ContentBannerRecord = require('./ContentBannerRecord')
const SeoImageBlockRecord = require('./SeoImageBlockRecord')
const SeoBlockRecord = require('./SeoBlockRecord')
const TitleRecord = require('./TitleRecord')
const MainBannerRecord = require('./MainBannerRecord')
const ModularContentBlockRecord = require('./ModularContentBlockRecord')

const Blocks = {
  AccordionListRecord,
  ArticleRecord,
  CardBannerRecord,
  ColumnsBlockRecord,
  ContentBannerRecord,
  ContentBlockRecord,
  DeliveryInformationRecord,
  ExpertBlockRecord,
  HeaderPhoneRecord,
  IconTeaserListRecord,
  ImageBlockRecord,
  InterfloraPlusBlockRecord,
  ListPromotionRecord,
  MainBannerRecord,
  ModularContentBlockRecord,
  MomentFirstRecord,
  ProductCarouselRecord,
  ProductListReassuranceItemRecord,
  ProductListRecord,
  QuoteRecord,
  RotatingBannerRecord,
  SeoBlockRecord,
  SeoImageBlockRecord,
  TitleRecord,
}

/**
 * @typedef {{
 *  categoryTree: CategoryTreeRoot
 *  page: (DatoCategoryPage|DatoLandingPage|DatoPage|DatoProduct)
 * }} BlockTransformerOptions
 */

/**
 * @extends {Transformer<DatoSectionRecord, GQLTemplate, BlockTransformerOptions>}
 */
class BlockTransformer extends Transformer {
  transformToGQL() {
    if (Blocks[this.apiData.__typename]) {
      return Blocks[this.apiData.__typename].toGQL(this.apiData, this.context, this.options)
    }
  }

  /**
   * @param {DatoSectionRecord} datoBlock
   * @param {GraphQLContext} context
   * @param {BlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(datoBlock, context, options = {}) {
    const transformer = new BlockTransformer(datoBlock, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = BlockTransformer
