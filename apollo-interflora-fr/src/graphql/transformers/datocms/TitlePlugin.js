const GQLTitle = require('../../models/Title')
const Transformer = require('../Transformer')

class TitlePluginTransformer extends Transformer {
  transformToGQL() {
    const datoTitle = this.apiData

    if (!datoTitle.length) {
      return null
    }

    const gqlTitle = new GQLTitle()

    try {
      const isTitlePluginStr = datoTitle.charAt(0) === '{' && datoTitle.includes('text')
      if (isTitlePluginStr) {
        const { text, level, color } = JSON.parse(datoTitle)
        gqlTitle.text = text
        gqlTitle.level = level
        gqlTitle.color = color
      } else {
        gqlTitle.text = datoTitle
      }
    } catch (e) {
      gqlTitle.text = datoTitle
    }

    return gqlTitle
  }

  /**
   * @param {string} titleField
   * @param {GraphQLContext} context
   * @param {*} options
   * @return {GQLComponent}
   */
  static toGQL(titleField, context, options) {
    const transformer = new TitlePluginTransformer(titleField, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TitlePluginTransformer
