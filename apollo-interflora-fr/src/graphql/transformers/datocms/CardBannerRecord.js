const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')
const GQLTitle = require('../../models/Title')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')
const { GQLScalarBoolean } = require('../../models')

/**
 * @extends {Transformer<DatoCardBannerRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class DatoCardBannerTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.card_banner

    const title = new GQLTitle()

    try {
      const _title = JSON.parse(this.apiData.title)
      title.text = _title.text
      title.level = _title.level
      title.color = _title.color
    } catch (e) {
      title.text = this.apiData.title
    }
    component.params.push({
      name: 'show_title',
      value: [new GQLScalarBoolean(this.apiData.showTitle ?? false)],
    })
    component.params.push({
      name: 'title',
      value: [title],
    })

    component.params.push({
      name: 'subtitle',
      value: [new GQLScalarString(this.apiData.subtitle)],
    })

    if (this.apiData.description) {
      component.params.push({
        name: 'descriptions',
        value: [new GQLScalarString(this.apiData.description)],
      })
    }

    if (this.apiData.buttonLabel) {
      component.params.push({
        name: 'button_label',
        value: [new GQLScalarString(this.apiData.buttonLabel)],
      })
    }

    if (this.apiData.buttonUrl) {
      component.params.push({
        name: 'button_link',
        value: [
          DatoUrlTransformer.toGQL(this.apiData.buttonUrl, this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    if (this.apiData.links) {
      for (const link of this.apiData.links) {
        component.params.push({
          name: 'link_labels',
          value: [new GQLScalarString(link.label)],
        })
        component.params.push({
          name: 'links',
          value: [
            DatoUrlTransformer.toGQL(link, this.context, {
              categoryTree: this.options.categoryTree,
            }),
          ],
        })
      }
    }

    return component
  }

  /**
   * @param {DatoCardBannerRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoCardBannerTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoCardBannerTransformer
