const { render } = require('datocms-structured-text-to-html-string')

const config = require('../../../config')
const CONST = require('../../../const')

const GQLDepartment = require('../../models/Department')

const Transformer = require('../Transformer')
const FileFieldTransformer = require('./FileField')

/**
 * @extends {Transformer<DatoFloristRegion, GQLDepartment, {}>}
 */
class FloristRegion extends Transformer {
  transformToGQL() {
    const department = new GQLDepartment()

    department.description = render(this.apiData.body?.value)
    department.descriptionBottom = render(this.apiData.afterOtherRegions?.value)

    department.button = {
      href: this.apiData.ctaHref,
      label: this.apiData.ctaLabel,
      show: Boolean(this.apiData.ctaShow),
      title: this.apiData.ctaTitle,
    }

    if (this.apiData.image) {
      department.image = FileFieldTransformer.toGQL(this.apiData.image, this.context, {})
      department.images = [department.image]
    }

    department.indice = this.apiData.code
    department.label = this.apiData.name

    if (this.apiData.mainCity) {
      department.mainTown = {
        label: this.apiData.mainCity.name,
        slugUrl: this.apiData.mainCity.slug,
      }
    }
    if (this.apiData.mainCities?.[0]) {
      const basePath = this.options.customPath || config.apps[this.context.siteId].storeLocator.path

      const doc = {
        document: {
          children: [
            {
              children: [],
              type: 'paragraph',
            },
          ],
          type: 'root',
        },
        schema: 'dast',
      }
      doc.document.children[0].children.push({
        children: this.apiData.mainCities.map((city) => ({
          children: [
            {
              children: [
                {
                  type: 'span',
                  value: city.name,
                },
              ],
              type: 'link',
              url: `${basePath}/${this.apiData.slug}/${city.slug}`,
            },
          ],
          type: 'listItem',
        })),
        style: 'bulleted',
        type: 'list',
      })

      department.mainTowns = render(doc)
    }

    department.others = (this.apiData.otherRegions || []).map((r) => FloristRegion.toGQL(r, this.context, {}))
    department.slugUrl = this.apiData.slug || this.apiData.code
    department.towns = (this.apiData.mainCities || []).map((t) => ({
      label: t.name,
      slugUrl: t.slug,
    }))
    department.type = CONST.geo.departmentTypes.METROPOLITAN
    department.title = this.title
    department.regionCode = this.apiData.regionCode

    return department
  }

  /**
   * @param {DatoFloristRegion} apiData
   * @param {GraphQLContext} context
   * @param {{type: (string | undefined), customPath: (string | undefined)}} options
   * @returns {GQLDepartment}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new FloristRegion(apiData, context, options)
    return transformer.transformToGQL()
  }

  get title() {
    const tpls = config.apps[this.context.siteId].seo[this.options.type] || config.apps[this.context.siteId].seo.default

    return tpls.title.replace(/%s/, this.apiData.name)
  }
}

module.exports = FloristRegion
