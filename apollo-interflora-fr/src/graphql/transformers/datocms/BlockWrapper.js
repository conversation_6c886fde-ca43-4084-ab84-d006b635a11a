const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @typedef {DatoBlockTransformerOptions & {
 *   categoryTree: CategoryTreeRoot
 *   type: ('cross-sell-after'|'cross-sell-before')
 * }} BlockWrapperTransformerOptions
 */

/**
 * @extends {Transformer<DatoProductCarouselRecord, GQLComponent, BlockWrapperTransformerOptions>}
 */
class BlockWrapperTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'block_wrapper'

    if (this.options.type === 'cross-sell-after') {
      if (this.apiData.link?.[0]) {
        component.params.push({
          name: 'button_label',
          value: [new GQLScalarString(this.apiData.link[0].label)],
        })
        component.params.push({
          name: 'button_link',
          value: [
            DatoUrlTransformer.toGQL(this.apiData.link[0], this.context, {
              categoryTree: this.options.categoryTree,
            }),
          ],
        })
      }
    } else if (this.options.type === 'cross-sell-before') {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    for (const k of [
      'title',
      'subtitle',
      'descriptions',
      'icons',
      'images',
      'links',
      'link_labels',
      'subtitles',
      'titles',
    ]) {
      if (!component.params.some((p) => p.name === k)) {
        component.params.push({
          name: k,
          value: [],
        })
      }
    }

    return component
  }

  /**
   * @param {DatoProductCarouselRecord} block
   * @param {GraphqlContext} context
   * @param {BlockWrapperTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options) {
    const transformer = new BlockWrapperTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = BlockWrapperTransformer
