const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')

const { render } = require('datocms-structured-text-to-html-string')

/**
 * @extends {Transformer<DatoAccordionListRecord, GQLComponent, {}>}
 */
class AccordionListTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.accordion_list

    const subtitles = []
    const descriptions = []
    for (const item of this.apiData.items) {
      subtitles.push(new GQLScalarString(item.title))

      // item.text.value is a Hypertext Abstract Syntax Tree format object sent from dato
      descriptions.push(new GQLScalarString(render(item.text?.value)))
    }

    component.params.push({
      name: 'subtitles',
      value: subtitles,
    })

    component.params.push({
      name: 'descriptions',
      value: descriptions,
    })

    return component
  }

  /**
   * @param {DatoAccordionListRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new AccordionListTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = AccordionListTransformer
