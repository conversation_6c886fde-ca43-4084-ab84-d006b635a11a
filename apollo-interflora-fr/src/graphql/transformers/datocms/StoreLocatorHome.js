const GQLBreadcrumb = require('../../models/Breadcrumb')
const GQLPage = require('../../models/Page')
const GQLRoute = require('../../models/Route')
const CONST = require('../../../const')

const PageTransformer = require('../../transformers/datocms/Page')
const { GQLTemplate, GQLScalarString, GQLScalarBoolean } = require('../../models')
const GQLComponent = require('../../models/Component')
const { tagsToSeo } = require('../../../helpers/dato')

/** @typedef {import('../_typedef')} */

/**
 * @extends {PageTransformer<(DatoLandingPage | DatoCategoryPage | DatoPage | DatoCategoryPageTree), GQLPage, { type: string, zones: string[], productCategory: APICategory }>}
 */
class StoreLocatorHomeTransformer extends PageTransformer {
  /**
   *
   * @param {DatoLandingPage | DatoCategoryPage | DatoPage | DatoCategoryPageTree} apiPage
   * @param {GraphQLContext} context
   * @param {{ type: string, zones: string[], productCategory: APICategory }} options
   * @returns {GQLPage}
   */
  static toGQL(apiPage, context, options = {}) {
    const _options = {
      ...options,
      type: 'floristDepartments',
      zones: Array.isArray(options.zones) ? options.zones : undefined,
    }
    return new StoreLocatorHomeTransformer(
      {
        ...apiPage,
        seoMeta: tagsToSeo(apiPage._seoMetaTags),
      },
      context,
      _options
    ).transformToGQL()
  }

  /**
   * @returns {GQLPage}
   */
  transformToGQL() {
    const page = new GQLPage()

    Object.assign(page, this.getNavigation())

    page.breadcrumbs = this._breadcrumbs()

    if (this._includeZone(CONST.cms.zones.before_footer)) {
      if (this.apiData.floristDepartmentsBeforeFooter) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.before_footer, this.apiData.floristDepartmentsBeforeFooter)
        )
      }
    }

    if (this._includeZone(CONST.cms.zones.after_header)) {
      if (this.apiData.floristDepartmentsAfterHeader) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.after_header, this.apiData.floristDepartmentsAfterHeader)
        )
        if (this.apiData.floristDepartmentsHeading) {
          const zone = page.componentZones.at(-1)
          const template = new GQLTemplate()
          template.background = CONST.cms.templateBackground.none
          template.name = CONST.cms.defaultTemplateName
          template.wrapper = CONST.cms.templateWrapper.default

          const component = new GQLComponent()
          component.name = CONST.cms.positions.wysiwyg
          component.params.push({
            name: 'highlight',
            value: [new GQLScalarBoolean(false)],
          })
          component.params.push({
            name: 'content',
            value: [new GQLScalarString(this.apiData.floristDepartmentsHeading)],
          })
          template.components.push(component)

          zone.templates.unshift(template)
        }
      }
    }

    page.meta = this.meta
    page.title = this.apiData.seoMeta?.title || this.apiData.floristDepartmentsTitle
    return page
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const home = new GQLBreadcrumb()
    home.label = 'Home'
    home.link = new GQLRoute()
    home.link.name = 'index'
    home.link.params = [
      {
        name: 'url',
        value: '/',
      },
    ]
    const breadcrumbs = [home]

    const page = new GQLBreadcrumb()
    page.label = this.apiData.floristDepartmentsTitle
    breadcrumbs.push(page)

    return breadcrumbs
  }
}

module.exports = StoreLocatorHomeTransformer
