const GQLLink = require('../../models/Link')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoLinkRecord, GQLLink, {}>}
 */
class DatoLinkTransformer extends Transformer {
  transformToGQL() {
    const link = new GQLLink()

    link.href = this.apiData.url || '#'
    link.targetBlank = Boolean(this.apiData.newWindow)

    return link
  }

  /**
   * @param {DatoExternalLinkRecord} link
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLLink}
   */
  static toGQL(link, context, options = {}) {
    const transformer = new DatoLinkTransformer(link, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoLinkTransformer
