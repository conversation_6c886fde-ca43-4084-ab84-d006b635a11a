const { render } = require('datocms-structured-text-to-html-string')

const GQLTownPage = require('../../models/TownPage')
const GQLTownPageTab = require('../../models/TownPageTab')

const Transformer = require('../Transformer')
const DatoProductTransformer = require('./Product')
const DatoFileFieldTransformer = require('./FileField')
const TemplateTransformer = require('./Template')

const CONST = require('../../../const')

/**
 * @extends {Transformer<DatoFloristPage, GQLTownPage, {}>}
 */
class FloristTownTransformer extends Transformer {
  transformToGQL() {
    const town = new GQLTownPage()

    town.description = this.apiData.subtitle || ''
    town.detail = this.apiData.body?.value ? render(this.apiData.body.value) : ''

    if (this.apiData.image) town.image = DatoFileFieldTransformer.toGQL(this.apiData.image, this.context)

    const tab = new GQLTownPageTab()
    tab.icon = undefined
    tab.label = this.apiData.code

    for (const section of this.apiData.afterHeader || []) {
      const _template = TemplateTransformer.toGQL(section, this.context, {
        categoryTree: this.options.categoryTree,
        page: this.apiData,
      })
      if (_template && _template.components.length > 0) {
        _template.components = _template.components.filter(
          (component) => component.name !== CONST.cms.positions.product_list
        )
        tab.componentsPromo.push(_template)
      }
      for (const block of section.blocks) {
        if (block.__typename === 'ProductListRecord') {
          for (const apiProduct of block.products || []) {
            tab.products.push(DatoProductTransformer.toGQL(apiProduct, this.context))
          }
        }
      }
    }

    for (const section of this.apiData.beforeFooter || []) {
      const _template = TemplateTransformer.toGQL(section, this.context, {
        categoryTree: this.options.categoryTree,
        page: this.apiData,
      })
      if (_template) tab.componentsBottom.push(_template)
    }
    town.tabs = [tab]

    town.title = this.apiData.title

    return town
  }

  static toGQL(apiData, context, options = {}) {
    const transformer = new FloristTownTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = FloristTownTransformer
