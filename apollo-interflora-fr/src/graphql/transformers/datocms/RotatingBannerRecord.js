const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoRotatingBannerRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class DatoRotatingBannerRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name =
      this.options.name ||
      CONST.cms.dato.rotatingBannerPositions[this.apiData.displayType] ||
      CONST.cms.positions.articles

    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }
    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    const acc = {
      description: [],
      icon: [],
      image: [],
      link: [],
      link_label: [],
      subtitle: [],
      title: [],
    }

    for (const teaser of this.apiData.items) {
      for (const key of Object.keys(acc)) {
        switch (key) {
          case 'description':
            acc.description.push(new GQLScalarString(teaser[key]))
            break
          case 'subtitle':
            acc.subtitle.push(new GQLScalarString(teaser[key]))
            break
          case 'icon':
            acc.icon.push(new GQLScalarString(teaser[key]))
            break
          case 'title':
            acc[key].push(teaser[key] ? new GQLScalarString(teaser[key]) : null)
            break
          case 'image':
            if (teaser.image) {
              const images = new GQLSourceImages()

              const image = new GQLImage()
              image.altText = teaser.image.alt || ''
              image.format = CONST.images.formats.mobile
              image.order = 0
              image.type = component.name
              image.url = teaser.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

              images.sources = [image]

              acc.image.push(images)
            } else {
              acc.image.push(null)
            }
            break
          case 'link':
            acc.link.push(
              teaser.link[0]
                ? DatoUrlTransformer.toGQL(teaser.link[0], this.context, {
                    categoryTree: this.options.categoryTree,
                  })
                : null
            )
            break
          case 'link_label':
            acc.link_label.push(teaser.link?.[0]?.label ? new GQLScalarString(teaser.link[0].label) : null)
            break
        }
      }
    }

    for (const [key, values] of Object.entries(acc)) {
      component.params.push({
        name: `${key}s`,
        value: values,
      })
    }

    return component
  }

  /**
   * @param {DatoRotatingBannerRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoRotatingBannerRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoRotatingBannerRecordTransformer
