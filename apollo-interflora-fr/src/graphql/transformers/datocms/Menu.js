/** @typedef {import('./_typedef')} */

const { GQLMenu } = require('../../models')
const Transformer = require('../Transformer')
const DatoLinkTransformer = require('./Link')
const DatoRouteTransformer = require('./Route')

/**
 * @extends {Transformer<DatoNavItem, GQLNavigationItem, DatoNavigationItemTransformerOptions>}
 */
class MenuTransformer extends Transformer {
  transformToGQL() {
    if (Array.isArray(this.apiData)) {
      return this.apiData.map((node) => this._nodeToMenu(node))
    }
    return this._nodeToMenu(this.apiData)
  }

  /**
   * @param {DatoNavItem} node
   * @returns {GQLMenu}
   * @private
   */
  _nodeToMenu(node) {
    const subItems = node.subItems || []
    const menu = new GQLMenu()

    menu.highlightLabel = node.highlightLabel
    menu.highlight = node.highlighted
    menu.icon = node.icon
    menu.clientSide = node.clientSide
    menu.blockType = node.blockType
    menu.promote = node.promote
    menu.obfuscated =
      (node.obfuscated && !node.obfuscationExclusions?.includes(this.options.obfuscationPageType)) ||
      (!node.obfuscated && node.obfuscationExclusions?.includes(this.options.obfuscationPageType))
    menu.label = node.title

    const link = node.link[0]
    if (link) {
      if (link.__typename === 'ExternalLinkRecord') {
        menu.href = DatoLinkTransformer.toGQL(link, this.context, this.options)
      } else if (link.__typename === 'LinkRecord') {
        if (link.target) {
          menu.href = DatoRouteTransformer.toGQL(link, this.context, this.options)
          menu.href.params.push({
            name: 'highlighted',
            value: node.highlighted,
          })
          menu.href.params.push({
            name: 'label',
            value: menu.label,
          })
        }
      }
    }

    menu.children = subItems.map((child) => this._nodeToMenu(child))

    return menu
  }

  /**
   * @param {DatoNavItem[]} apiData
   * @param {GraphQLContext} context
   * @param {DatoNavigationItemTransformerOptions} options
   * @returns {GQLMenu[]}
   */
  static toGQL(apiData, context, options) {
    const transformer = new MenuTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = MenuTransformer
