const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoExpertBlockRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class DatoExpertBlockTransformer extends Transformer {
  transformToGQL() {
    // For some reasons there seem to be some null values inserted on french octopus so we insert them here as well..
    const component = new GQLComponent()

    component.name = CONST.cms.positions.expert_block

    component.params.push({
      name: 'title',
      value: [new GQLScalarString(this.apiData.title)],
    })

    component.params.push({
      name: 'subtitle',
      value: [new GQLScalarString(this.apiData.subtitle)],
    })

    component.params.push({
      name: 'subtitles',
      value: [
        null,
        new GQLScalarString(this.apiData.expertName || ''),
        new GQLScalarString(this.apiData.statement || ''),
      ],
    })

    component.params.push({
      name: 'descriptions',
      value: [
        null,
        new GQLScalarString(this.apiData.expertTitle || ''),
        new GQLScalarString(this.apiData.description || ''),
      ],
    })

    let expertImages = null
    let images = null
    let expertPic = null
    let image = null
    if (this.apiData.profileImage) {
      expertImages = new GQLSourceImages()
      expertPic = new GQLImage()
      expertPic.altText = this.apiData.profileImage.alt || ''
      expertPic.format = CONST.images.formats.mobile
      expertPic.order = 0
      expertPic.type = component.name
      expertPic.url = this.apiData.profileImage.url.replace(
        /^https?:\/\/[^/]+/,
        config.apps[this.context.siteId].mediaBaseUrl
      )
      expertImages.sources = [expertPic]
    }
    if (this.apiData.image) {
      images = new GQLSourceImages()
      image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)
      images.sources = [image]
    }

    component.params.push({
      name: 'images',
      value: [images, expertImages],
    })

    if (this.apiData.links?.[0]) {
      component.params.push({
        name: 'link_labels',
        value: [null, null, new GQLScalarString(this.apiData.links[0].label)],
      })
      component.params.push({
        name: 'links',
        value: [
          null,
          null,
          DatoUrlTransformer.toGQL(this.apiData.links, this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    return component
  }

  /**
   * @param {DatoExpertBlockRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoExpertBlockTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoExpertBlockTransformer
