const DatoFloristPageTransformer = require('./FloristPage')
const DatoFloristRegionTransformer = require('./FloristRegion')
const DatoFloristTownTransformer = require('./FloristTown')
const DatoFooterTransformer = require('./Footer')
const DatoImageTransformer = require('./Image')
const DatoImageBlockRecordTransformer = require('./ImageBlockRecord')
const DatoMenuTransformer = require('./Menu')
const DatoPageTransformer = require('./Page')
const DatoPageProductTranformer = require('./PageProduct')
const DatoProductTransformer = require('./Product')
const DatoStoreLocatorHomeTransformer = require('./StoreLocatorHome')
const DatoTemplateTransformer = require('./Template')
const DatoProductListTransformer = require('./ProductList')

module.exports = {
  DatoFloristPageTransformer,
  DatoFloristRegionTransformer,
  DatoFloristTownTransformer,
  DatoFooterTransformer,
  DatoImageBlockRecordTransformer,
  DatoImageTransformer,
  DatoMenuTransformer,
  DatoPageProductTranformer,
  DatoPageTransformer,
  DatoProductListTransformer,
  DatoProductTransformer,
  DatoStoreLocatorHomeTransformer,
  DatoTemplateTransformer,
}
