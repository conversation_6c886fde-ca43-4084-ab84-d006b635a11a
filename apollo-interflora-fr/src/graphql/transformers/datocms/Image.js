const config = require('../../../config')
const CONST = require('../../../const')

const GQLImage = require('../../models/Image')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoFileField, GQLImage, { alt: string, description: string, format: string, order: number, type: string, url: string }>}
 */
class ImageTransfomer extends Transformer {
  transformToGQL() {
    const image = new GQLImage()
    image.altText = this.options.alt ?? this.apiData.alt ?? ''
    image.format = this.options.format || CONST.images.formats.mobile
    image.order = this.options.order || 0
    image.info = this.options.info
    image.type = this.options.type || CONST.images.types.primary

    if (this.options.url || this.apiData.url) {
      let url = this.options.url || this.apiData.url
      const baseUrl = config.apps[this.context.siteId].mediaBaseUrl

      if (!url.startsWith('http')) {
        url = `${baseUrl}/${url.replace(/^\//, '')}`
      } else {
        url = url.replace(/^https:\/\/[^/]+/, baseUrl)
      }

      image.url = url
    } else {
      image.url = '#'
    }

    return image
  }

  /**
   * @param {DatoFileField} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  alt: string,
   *  description: string,
   *  format: string,
   *  order: number,
   *  type: string,
   *  url: string
   * }} options
   * @returns {GQLImage}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new ImageTransfomer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ImageTransfomer
