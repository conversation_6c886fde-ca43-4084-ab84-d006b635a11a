const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const DatoUrlTransformer = require('./Url')
const Transformer = require('../Transformer')

/**
 * @extends {Transformer<IconTeaserListRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class IconTeaserListTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.icon_text

    const icons = []
    const subtitles = []
    const descriptions = []
    const links = []

    // Without pushing undefined values here, PushReinsurance components aren't shown on front.
    for (const iconTeaser of this.apiData.items) {
      icons.push(new GQLScalarString(iconTeaser.icon))
      subtitles.push(new GQLScalarString(iconTeaser.title))
      if (iconTeaser.description) {
        descriptions.push(new GQLScalarString(iconTeaser.description))
      } else {
        descriptions.push(undefined)
      }
      if (iconTeaser.link) {
        links.push(
          DatoUrlTransformer.toGQL(iconTeaser.link, this.context, {
            categoryTree: this.options.categoryTree,
          })
        )
      } else {
        links.push(undefined)
      }
    }

    component.params.push({
      name: 'icons',
      value: icons,
    })

    component.params.push({
      name: 'descriptions',
      value: descriptions,
    })

    component.params.push({
      name: 'subtitles',
      value: subtitles,
    })

    component.params.push({
      name: 'links',
      value: links,
    })

    return component
  }

  /**
   * @param {IconTeaserListRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new IconTeaserListTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = IconTeaserListTransformer
