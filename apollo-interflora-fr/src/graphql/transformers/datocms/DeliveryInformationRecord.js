const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

const { render } = require('datocms-structured-text-to-html-string')
const { stripHTMLRootTag } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoDeliveryInformationRecord, GQLComponent, {}>}
 */
class DeliveryInformationRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'delivery_block'

    component.params.push({
      name: 'description',
      value: [new GQLScalarString(stripHTMLRootTag(render(this.apiData.description?.value)))],
    })

    if (this.apiData.image) {
      const images = new GQLSourceImages()
      const image = new GQLImage()
      image.altText = this.apiData.image.alt || this.apiData.title
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)
      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    component.params.push({
      name: 'subtitle',
      value: [new GQLScalarString(this.apiData.title)],
    })

    return component
  }

  static toGQL(apiData, context, options = {}) {
    const transformer = new DeliveryInformationRecordTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DeliveryInformationRecordTransformer
