const { render } = require('datocms-structured-text-to-html-string')
const CONST = require('../../../const')
const { GQLPage, GQLTemplate, GQLScalarBoolean, GQLScalarString } = require('../../models')
const GQLComponent = require('../../models/Component')
const GQLComponentZone = require('../../models/ComponentZone')
const PageTransformer = require('../../transformers/datocms/Page')
const { tagsToSeo } = require('../../../helpers/dato')

class FloristPageransformer extends PageTransformer {
  static toGQL(floristPage, context, options = {}) {
    return new FloristPageransformer(
      {
        ...floristPage,
        seoMeta: tagsToSeo(floristPage._seoMetaTags),
      },
      context,
      options
    ).transformToGQL()
  }

  transformToGQL() {
    const page = new GQLPage()

    Object.assign(page, this.getNavigation())

    page.breadcrumbs = this._breadcrumbs()

    if (this._includeZone(CONST.cms.zones.before_footer)) {
      if (this.apiData.FloristDepartmentsBeforeFooter) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.before_footer, this.apiData.FloristDepartmentsBeforeFooter)
        )
      }
      if (this.options.additionalComponents?.beforeFooter) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.before_footer, this.options.additionalComponents.beforeFooter)
        )
      }
    }

    if (this._includeZone(CONST.cms.zones.after_header)) {
      if (this.apiData.floristDepartmentsAfterHeader) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.after_header, this.apiData.floristDepartmentsAfterHeader)
        )
      }
      if (this.options.additionalComponents?.afterHeader) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.after_header, this.options.additionalComponents.afterHeader)
        )
      }
    }

    if (this.apiData.body && this._includeZone(CONST.cms.zones.content)) {
      const component = new GQLComponent()
      component.name = CONST.cms.positions.content
      component.params.push({ name: 'highlight', value: [new GQLScalarBoolean(false)] })
      component.params.push({ name: 'content', value: [new GQLScalarString(render(this.apiData.body.value))] })
      component.params.push({ name: 'subtitle', value: [new GQLScalarString(this.apiData.subtitle)] })

      const template = new GQLTemplate()
      template.name = CONST.cms.defaultTemplateName
      template.components.push(component)

      const zone = new GQLComponentZone()
      zone.name = CONST.cms.zones.content
      zone.templates = [template]

      page.componentZones.push(zone)
    }

    page.meta = this.meta
    page.title = this.apiData.title

    return page
  }
}

module.exports = FloristPageransformer
