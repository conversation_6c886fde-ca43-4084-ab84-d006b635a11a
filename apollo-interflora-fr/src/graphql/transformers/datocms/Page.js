const { render } = require('../../../helpers/dato')

const config = require('../../../config')
const CONST = require('../../../const')

const GQLBreadcrumb = require('../../models/Breadcrumb')
const GQLComponent = require('../../models/Component')
const GQLComponentZone = require('../../models/ComponentZone')
const GQLPage = require('../../models/Page')
const GQLRoute = require('../../models/Route')
const GQLScalarBoolean = require('../../models/ScalarBoolean')
const GQLScalarString = require('../../models/ScalarString')
const GQLTemplate = require('../../models/Template')

const FestModeTransformer = require('./FestModeRecord')
const FooterTransformer = require('./Footer')
const HeaderBannerTransformer = require('./HeaderBannerRecord')
const MenuTransformer = require('./Menu')
const MetaDataTransformer = require('./Metadata')
const Transformer = require('../Transformer')
const TemplateTransformer = require('./Template')

const { GraphQLApolloError } = require('../../errors')
const DatoRouteTransformer = require('./Route')

const { uniqBy } = require('lodash')
const { getCategoryTreeSlugs } = require('../../../helpers/categoryTree')
const { getPageSlugForContext } = require('../../../helpers/digitalCatalog')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<(DatoLandingPage | DatoCategoryPage | DatoPage | DatoCategoryPageTree), GQLPage, DatoPageTransformerOptions}
 */
class PageTransformer extends Transformer {
  /**
   * @returns {object[]}
   */
  get meta() {
    const meta = []

    // title & description
    {
      const tpls = this.context.appConfig.seo[this.options.type] || this.context.appConfig.seo.default

      const metaRef = (ref) =>
        this.apiData.seoMeta?.[ref] || tpls[ref]?.replaceAll(/%s/g, this.apiData.name || this.apiData.title)

      const description = metaRef('description')

      // Get title from datoCMS meta seo component if present.
      // DatoCMS has fallback to fill meta title based on required model name or title if left empty, so we check if it
      // is a fallback and if meta title is same as model, we go with metaRef template function
      const title =
        this.apiData.seoMeta?.title && this.apiData.seoMeta.title !== (this.apiData.name || this.apiData.title)
          ? this.apiData.seoMeta.title
          : metaRef(this.options.metaTitleRef || 'title')

      meta.push({
        __typename: 'Tag',
        attributes: null,
        content: title,
        tag: 'title',
      })
      meta.push({
        __typename: 'Tag',
        attributes: {
          content: description,
          name: 'description',
        },
        content: null,
        tag: 'meta',
      })
    }

    for (const tag of this.apiData._seoMetaTags || []) {
      if (!tag.attributes && !tag.content) continue

      // skip pre-computed values
      if (tag.tag === 'title') continue
      if (tag.tag === 'meta' && tag.attributes?.name === 'description') continue

      meta.push(tag)
    }

    if (this.options.canonical || this.apiData.canonical) {
      meta.push({
        __typename: 'Tag',
        attributes: { content: this.apiData.canonical || this.options.canonical, name: 'canonical' },
        tag: 'link',
      })
    }
    if (this.apiData.seoMetaRobots?.length) {
      meta.push({
        __typename: 'Tag',
        attributes: { content: this.apiData.seoMetaRobots.join(','), name: 'robots' },
        tag: 'meta',
      })
    }

    return meta.map((tag) => MetaDataTransformer.toGQL(tag, this.context, {}))
  }

  /**
   * @returns {GQLPage}
   */
  transformToGQL() {
    if (!this.apiData) {
      throw new GraphQLApolloError('404: Not found', 404)
    }

    const page = new GQLPage()

    page.breadcrumbs = this._breadcrumbs()

    Object.assign(page, this.getNavigation())

    if (this._includeZone(CONST.cms.zones.before_header) && this.options.headerBanner) {
      const template = new GQLTemplate()
      template.background = CONST.cms.templateBackground.none
      template.name = CONST.cms.defaultTemplateName
      template.wrapper = CONST.cms.templateWrapper.default
      template.components = [HeaderBannerTransformer.toQGL(this.options.headerBanner)]

      page.componentZones.push({
        name: CONST.cms.zones.before_header,
        templates: [template],
      })
    }

    if (this._includeZone(CONST.cms.zones.before_footer)) {
      if (this.apiData.beforeFooter) {
        page.componentZones.push(this.generateComponentZone(CONST.cms.zones.before_footer, this.apiData.beforeFooter))
      }
      if (this.options.additionalComponents?.beforeFooter) {
        page.componentZones.push(
          this.generateComponentZone(CONST.cms.zones.before_footer, this.options.additionalComponents.beforeFooter)
        )
      }
    }

    if (this.apiData.beforeProductList && this._includeZone(CONST.cms.zones.before_productlist)) {
      page.componentZones.push(
        this.generateComponentZone(CONST.cms.zones.before_productlist, this.apiData.beforeProductList)
      )
    }

    if (this.apiData.content && this._includeZone(CONST.cms.zones.content)) {
      page.componentZones.push(this.generateComponentZone(CONST.cms.zones.content, this.apiData.content))
    }

    if (
      this.apiData.__typename === 'PageRecord' &&
      this.apiData.body?.value &&
      this._includeZone(CONST.cms.zones.content)
    ) {
      const component = new GQLComponent()
      component.name = 'wysiwyg'
      component.params = [
        {
          name: 'highlight',
          value: [new GQLScalarBoolean(false)],
        },
        {
          name: 'content',
          value: [new GQLScalarString(render(this.apiData.body, config.apps[this.context.siteId].mediaBaseUrl))],
        },
      ]

      const template = new GQLTemplate()
      template.background = CONST.cms.templateBackground.none
      template.components = [component]
      template.name = CONST.cms.defaultTemplateName
      template.wrapper = CONST.cms.templateWrapper.default

      page.componentZones.push({
        name: 'content',
        templates: [template],
      })
    }

    if (this._includeZone(CONST.cms.zones.before_header) && this.options.mainNavigation?.headerPhone) {
      page.componentZones.push(this.generateComponentZone(CONST.cms.zones.header, this.generateHeaderSectionRecord()))
    }

    if (this._includeZone(CONST.cms.zones.after_header)) {
      const zoneId =
        this.options.type === 'floristDepartment' ? CONST.cms.zones.after_info : CONST.cms.zones.after_header
      if (this.apiData.afterHeader) {
        page.componentZones.push(this.generateComponentZone(zoneId, this.apiData.afterHeader))
      }
      if (this.options.additionalComponents?.afterHeader) {
        page.componentZones.push(this.generateComponentZone(zoneId, this.options.additionalComponents.afterHeader))
      }
    }

    if (this.apiData.afterProductList && this._includeZone(CONST.cms.zones.after_productlist)) {
      page.componentZones.push(
        this.generateComponentZone(CONST.cms.zones.after_productlist, this.apiData.afterProductList)
      )
    }

    if (this.apiData.festMode?.[0]) {
      page.festMode = FestModeTransformer.toGQL(this.apiData.festMode, this.context)
    }

    if (this.apiData.firstListItem && this._includeZone(CONST.cms.zones.reinsurance_productlist)) {
      page.componentZones.push(
        this.generateComponentZone(CONST.cms.zones.reinsurance_productlist, [
          {
            background: CONST.cms.templateBackground.none,
            blocks: [this.apiData.firstListItem],
            wrapper: CONST.cms.templateWrapper.default,
          },
        ])
      )
    }

    if (this.apiData.listPromotion && this._includeZone(CONST.cms.zones.inside_productlist)) {
      page.componentZones.push(
        this.generateComponentZone(CONST.cms.zones.inside_productlist, [
          {
            background: CONST.cms.templateBackground.none,
            blocks: [this.apiData.listPromotion],
            wrapper: CONST.cms.templateWrapper.none,
          },
        ])
      )
    }

    page.meta = this.meta
    const metaTitle = PageTransformer.findMetaParam(page.meta, 'title')?.value
    page.title = metaTitle || this.apiData.title || this.apiData.name || ''

    if (this.options.legalText) {
      page.subtitle = this.options.legalText.plpSubtitle
    }

    return page
  }

  /**
   * @param {string} name
   * @param {DatoSection[]} sections
   * @returns {GQLComponentZone}
   */
  generateComponentZone(name, sections) {
    const zone = new GQLComponentZone()
    zone.name = name
    zone.templates = sections.map((section) =>
      TemplateTransformer.toGQL(section, this.context, { categoryTree: this.options.categoryTree, page: this.apiData })
    )
    return zone
  }

  /**
   * @returns {DatoSectionRecord[]}
   */
  generateHeaderSectionRecord() {
    return [
      {
        __typename: 'SectionRecord',
        _seoMetaTags: [],
        background: 'NONE',
        blocks: [
          {
            __typename: 'HeaderPhoneRecord',
            _seoMetaTags: [],
            subtitle: this.options.mainNavigation.headerPhone[0].mention || '',
            title: this.options.mainNavigation.headerPhone[0].phone || '',
          },
        ],
        title: 'Header phone',
        wrapper: 'DEFAULT',
      },
    ]
  }

  /**
   * @returns {{
   *  footers: GQLFooter[],
   *  gellules: GQLGelluleType[],
   *  menus: GQLMenu[],
   * }}
   */
  getNavigation() {
    let obfuscationPageType = 'other'
    if (['category', 'product'].includes(this.options.type)) {
      obfuscationPageType = this.options.type
    } else if (this.options.type === 'cms' && this.apiData.slug === CONST.cms.dato.pageSlugs.homepage) {
      obfuscationPageType = 'home'
    } else if (this.options.type === 'cms' && this.apiData.slug.startsWith('cart')) {
      obfuscationPageType = 'funnel'
    }
    const options = {
      categoryTree: this.options.categoryTree,
      obfuscationPageType,
    }

    const footers = FooterTransformer.toGQL(this.options.footers, this.context, options)
    const gellules = []

    const { menuItems } = this.options.mainNavigation || {}
    const menus = menuItems ? MenuTransformer.toGQL(menuItems, this.context, options) : []

    if (this.options.type === 'category') {
      gellules.push(...this._gellules(menus, options))
    }

    return {
      footers,
      gellules,
      menus,
    }
  }

  /**
   * @param {GQLPage} page
   * @param {GQLComponentZone} zone
   * @returns {GQLComponentZone}
   */
  _addZone(page, zone) {
    const existingZone = page.componentZones.find((z) => z.name === zone.name)

    if (existingZone && existingZone !== zone) {
      existingZone.templates.push(...zone.templates)
      return existingZone
    }

    if (!existingZone) {
      page.componentZones.push(zone)
    }

    return zone
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const home = new GQLBreadcrumb()
    home.label = this.context.appConfig.breadcrumbsHome ?? 'Home'
    home.link = new GQLRoute()
    home.link.name = 'index'
    home.link.params = [
      {
        name: 'url',
        value: '/',
      },
    ]

    const breadcrumbs = [home]

    const isFloristPage = {
      address: ['floristAddressDepartment', 'floristAddressTown'].includes(this.options.type),
      landing: ['floristDepartment', 'floristTown', 'floristTowns'].includes(this.options.type),
    }

    if (this.options.type === 'category') {
      if (this.options.parentCategory) {
        const categoryTreeNode = this.options.categoryTree.find(this.apiData.slug)
        const nestedCategorySlugs = getCategoryTreeSlugs(categoryTreeNode)
        const paramNames = ['slugCategory', 'slugParentCategory', 'slugRootParentCategory']
        const routeNames = ['category', 'parent_category', 'parents_catecory']
        const rangedSlugs = (index) => nestedCategorySlugs.slice(0, index + 1)
        for (const [slugIndex, slug] of nestedCategorySlugs.entries()) {
          if (slugIndex > 2) {
            throw new Error(`not implemented: limited to 3 levels`)
          }
          const categoryTreeNode = this.options.categoryTree.find(slug)
          const parent = new GQLBreadcrumb()
          const params = rangedSlugs(slugIndex)
            .reverse()
            .map((s, i) => ({
              name: paramNames[i],
              value: s,
            }))
          parent.label = categoryTreeNode.title
          if (slug !== this.apiData.slug) {
            parent.link = new GQLRoute()
            parent.link.name = routeNames[slugIndex]
            parent.link.params = [
              ...params,
              {
                name: 'url',
                value: `/c/${rangedSlugs(slugIndex).join('/')}`,
              },
            ]
          }
          breadcrumbs.push(parent)
        }
      } else if (config.cms.breadcrumbs.categories.displayForRootCategories) {
        const breadcrumb = new GQLBreadcrumb()
        breadcrumb.label = this.apiData.name ?? this.apiData.title
        breadcrumbs.push(breadcrumb)
      }
    } else if (
      this.options.type === 'cms' &&
      this.apiData?.slug !== getPageSlugForContext(CONST.cms.dato.pageSlugs.homepage, this.context)
    ) {
      if (this.options.parentPage) {
        const parent = new GQLBreadcrumb()
        parent.label = this.options.parentPage.title

        if (this.options.parentPage.slug.toLowerCase() === config.cms.myAccount.slug.toLowerCase()) {
          parent.link = new GQLRoute()
          parent.link.name = config.cms.myAccount.slug
          parent.link.params = [
            {
              name: 'url',
              value: '/my-account',
            },
          ]
        }

        breadcrumbs.push(parent)
      }

      const page = new GQLBreadcrumb()
      page.label = this.apiData.title

      breadcrumbs.push(page)
    } else if (isFloristPage.address || isFloristPage.landing) {
      const florists = new GQLBreadcrumb()
      florists.label = this.options.storeLocatorHome.floristDepartmentsTitle
      florists.link = new GQLRoute()
      florists.link.name = isFloristPage.landing ? 'floristDepartments' : 'floristAddressDepartments'
      florists.link.params = [
        {
          name: 'url',
          value: config.apps[this.context.siteId].storeLocator.path,
        },
      ]
      breadcrumbs.push(florists)

      if (['floristDeparment', 'floristAddressDepartment'].includes(this.options.type)) {
        const region = new GQLBreadcrumb()
        region.label = this.apiData.name
        breadcrumbs.push(region)
      } else if (['floristTown', 'floristAddressTown'].includes(this.options.type)) {
        const region = new GQLBreadcrumb()
        region.label = this.apiData.region.name
        region.link = new GQLRoute()
        region.link.name = isFloristPage.landing ? 'floristDepartment' : 'floristAddressDepartment'
        region.link.params = [
          {
            name: 'slugUrl',
            value: this.apiData.region.slug,
          },
          {
            name: 'url',
            value: `${config.apps[this.context.siteId].storeLocator.path}/${this.apiData.region.slug}`,
          },
        ]
        breadcrumbs.push(region)

        const town = new GQLBreadcrumb()
        town.label = this.apiData.name
        breadcrumbs.push(town)
      }
      // @todo? floristTowns
    }

    return breadcrumbs
  }

  /**
   * @param {GQLPage} page
   * @param {string} zoneName
   * @returns {GQLComponentZone}
   */
  _getZone(page, zoneName) {
    return (
      page.componentZones.find((z) => z.name === zoneName) ?? {
        name: zoneName,
        templates: [],
      }
    )
  }

  /**
   * @param {string} zoneName
   * @returns {boolean}
   */
  _includeZone(zoneName) {
    if (!this.options.zones) return true

    return this.options.zones.includes(zoneName)
  }

  /**
   * building gellules
   * @param {GQLMenu[]} menus
   * @param {*} options
   * @returns {GQLGelluleType[]}
   */
  _gellules(menus, options) {
    const gellules = []
    const apiGellules = this.apiData.gellules ?? []
    // gellules setted in DatoCms dashboard
    for (const link of apiGellules) {
      if (link.exclude) continue
      const url = DatoRouteTransformer.toGQL(link, this.context, options)
      const linkMenu = menus.find(
        (menu) =>
          menu.href?.name === 'category' &&
          menu.href?.params?.some((param) => param.name === 'slugCategory' && param.value === link.target?.slug)
      )
      if (url.name) {
        gellules.push({
          icon: linkMenu?.icon ?? '',
          label: link.label ?? link.target?.title,
          slug: link.target?.slug,
          url,
        })
      }
    }

    return uniqBy(gellules, 'slug')
  }

  /**
   * @param {DatoLandingPage | DatoCategoryPage | DatoPage | DatoCategoryPageTree} apiPage
   * @param {GraphQLContext} context
   * @param {DatoPageTransformerOptions} options
   * @returns {GQLPage}
   */
  static toGQL(apiPage, context, options = {}) {
    const _options = {
      metaTitleRef: 'title',
      ...options,
      zones: options.zones?.[0] ? options.zones : undefined,
    }
    return new PageTransformer(apiPage, context, _options).transformToGQL()
  }

  /**
   *
   * @param {GQLMeta[]} allMeta
   * @param {string} paramName
   * @returns {GQLMetaParamType|undefined}
   */
  static findMetaParam(allMeta, paramName) {
    return allMeta.reduce((searchParam, meta) => {
      if (searchParam) {
        return searchParam
      }

      const result = meta.params.find((param) => param.name === paramName)
      if (!result) {
        return undefined
      }

      return result
    }, undefined)
  }
}

module.exports = PageTransformer
