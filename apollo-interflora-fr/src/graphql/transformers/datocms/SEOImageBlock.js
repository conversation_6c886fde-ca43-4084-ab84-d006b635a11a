const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoSEOImageBlockRecord, GQLComponent, {}>}
 */
class DatoSEOImageBlockTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    const icons = []
    const images = []
    const links = []
    const link_labels = []
    const descriptions = []
    const subtitles = []
    const titles = []

    switch (this.apiData.display) {
      case CONST.cms.dato.seoImageBlockDisplay.LEFT:
        component.name = CONST.cms.positions.seo_block
        break
      case CONST.cms.dato.seoImageBlockDisplay.RIGHT:
        component.name = CONST.cms.positions.plants_care
        break
    }

    component.params.push({
      name: 'title',
      value: [new GQLScalarString(this.apiData.title)],
    })

    component.params.push({
      name: 'description',
      value: [new GQLScalarString(this.apiData.description)],
    })

    if (this.apiData.image) {
      const images = new GQLSourceImages()
      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'mainImage',
        value: [images],
      })
    }

    for (const iconTeaser of this.apiData.items) {
      descriptions.push(iconTeaser.subtitle ? new GQLScalarString(iconTeaser.subtitle) : null)
      icons.push(iconTeaser.cssClass ? new GQLScalarString(iconTeaser.cssClass) : null)
      images.push(null)
      links.push(null)
      link_labels.push(null)
      subtitles.push(iconTeaser.title ? new GQLScalarString(iconTeaser.title) : null)
      titles.push(null)
    }

    component.params.push({
      name: 'descriptions',
      value: descriptions,
    })

    component.params.push({
      name: 'icons',
      value: icons,
    })

    component.params.push({
      name: 'images',
      value: images,
    })

    component.params.push({
      name: 'links',
      value: links,
    })

    component.params.push({
      name: 'link_labels',
      value: link_labels,
    })

    component.params.push({
      name: 'subtitles',
      value: subtitles,
    })

    component.params.push({
      name: 'titles',
      value: titles,
    })

    return component
  }

  /**
   * @param {DatoSEOImageBlockRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoSEOImageBlockTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoSEOImageBlockTransformer
