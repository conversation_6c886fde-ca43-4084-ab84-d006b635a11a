const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')
const FileFieldTransformer = require('./FileField')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoContentBannerRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class ContentBannerRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name =
      CONST.cms.positions[this.apiData.imageType === CONST.cms.imageTypes.landscape ? 'image3' : 'image1']

    if (this.apiData.image) {
      component.params.push({
        name: 'image',
        value: [FileFieldTransformer.toSourceImages(this.apiData.image, this.context, { type: component.name })],
      })
    }

    component.params.push({
      name: 'description',
      value: [new GQLScalarString(this.apiData.description)],
    })

    if (this.apiData.link?.[0]) {
      component.params.push({
        name: 'link_label',
        value: [new GQLScalarString(this.apiData.link[0].label)],
      })
      component.params.push({
        name: 'link',
        value: [
          DatoUrlTransformer.toGQL(this.apiData.link[0], this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    return component
  }

  /**
   * @param {DatoContentBannerRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ContentBannerRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ContentBannerRecordTransformer
