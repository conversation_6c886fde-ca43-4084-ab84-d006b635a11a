const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<DatoDeliveryInformationRecord, GQLComponent, {}>}
 */
class HeaderPhoneRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'header_phone'

    for (const attr of ['mention', 'phone', 'subtitle', 'title']) {
      if (this.apiData[attr]) {
        let name = attr
        if (attr === 'mention') name = 'subtitle'
        if (attr === 'phone') name = 'title'
        component.params.push({
          name,
          value: [new GQLScalarString(this.apiData[attr])],
        })
      }
    }

    return component
  }

  static toGQL(apiData, context, options = {}) {
    const transformer = new HeaderPhoneRecordTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = HeaderPhoneRecordTransformer
