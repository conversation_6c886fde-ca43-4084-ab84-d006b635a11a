/** @typedef {import('./_typedef')} */

const GQLFooter = require('../../models/Footer')
const Transformer = require('../Transformer')
const DatoRouteTransformer = require('./Route')
const DatoLinkTransformer = require('./Link')

/**
 * @extends {Transformer<DatoFooterNavigation, GQLFooterNavigation, DatoNavigationItemTransformerOptions>}
 */
class FooterTransformer extends Transformer {
  transformToGQL() {
    const { subItems } = this.apiData || {}

    return (subItems || []).map((navItem) => this._nodeToFooter(navItem))
  }

  /**
   * @param {DatoNavItem} node
   * @returns {GQLFooter}
   * @private
   */
  _nodeToFooter(node) {
    const footer = new GQLFooter()
    footer.label = node.title

    const link = node.link[0]

    if (link) {
      footer.label = link.label || footer.label

      if (link.__typename === 'ExternalLinkRecord') {
        footer.href = DatoLinkTransformer.toGQL(link, this.context, this.options)
      } else if (link.__typename === 'LinkRecord') {
        if (link.target) {
          footer.href = DatoRouteTransformer.toGQL(link, this.context, this.options)
        }
      }
    }

    footer.highlight = node.highlighted || false
    footer.mentions = node.mentions ? [node.mentions] : []
    footer.multiple = node.multiple || false

    footer.obfuscated =
      (node.obfuscated && !node.obfuscationExclusions?.includes(this.options.obfuscationPageType)) ||
      (!node.obfuscated && node.obfuscationExclusions?.includes(this.options.obfuscationPageType))

    footer.children = (node.subItems || []).map((child) => this._nodeToFooter(child))

    return footer
  }

  /**
   * @param {DatoFooterNavigation} apiData
   * @param {GraphQLContext} context
   * @param {DatoNavigationItemTransformerOptions} options
   * @returns {GQLFooter[]}
   */
  static toGQL(apiData, context, options) {
    const transformer = new FooterTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = FooterTransformer
