const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')
const GQLScalarBoolean = require('../../models/ScalarBoolean')

const Transformer = require('../Transformer')

const { render } = require('datocms-structured-text-to-html-string')
const { isJSON } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoContentBlockRecord, GQLComponent, {}>}
 */
class ContentBlockTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.content

    component.params.push({
      name: 'highlight',
      value: [new GQLScalarBoolean(Boolean(this.apiData.highlighted || false))],
    })

    component.params.push({
      name: 'description',
      value: [new GQLScalarString(render(this.apiData.content?.value))],
    })

    if (isJSON(this.apiData.title)) {
      const _title = JSON.parse(this.apiData.title)
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(_title?.text ?? '')],
      })
      component.params.push({
        name: 'level',
        value: [new GQLScalarString(_title?.level ?? '')],
      })
    } else {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }

    return component
  }

  /**
   * @param {DatoContentBlockRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ContentBlockTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ContentBlockTransformer
