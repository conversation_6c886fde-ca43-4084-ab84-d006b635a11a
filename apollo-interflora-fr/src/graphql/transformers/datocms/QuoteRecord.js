const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

class DatoQuoteRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'quote'

    if (this.apiData.quote) {
      component.params.push({
        name: 'description',
        value: [new GQLScalarString(this.apiData.quote)],
      })
    }
    if (this.apiData.portrait) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.portrait.alt || `${this.apiData.name}, ${this.apiData.professionelTitle}`
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = 'quote'
      image.url = this.apiData.portrait.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]
      component.params.push({
        name: 'image',
        value: [images],
      })
    }
    if (this.apiData.name || this.apiData.professionelTitle) {
      let subtitle = ''
      if (this.apiData.name) {
        subtitle += `<strong>${this.apiData.name}</strong>`
      }
      if (this.apiData.professionelTitle) {
        subtitle += this.apiData.professionelTitle
      }
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(subtitle)],
      })
    }

    return component
  }

  /**
   * @param {DatoQuoteRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoQuoteRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoQuoteRecordTransformer
