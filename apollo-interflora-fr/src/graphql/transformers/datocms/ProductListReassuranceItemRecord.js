const FileFieldTransformer = require('./FileField')
const IconTeaserListTransformer = require('./IconTeaserListTransformer')

class ProductListReassuranceItemRecordTransformer extends IconTeaserListTransformer {
  transformToGQL() {
    const component = super.transformToGQL()

    if (this.apiData.image) {
      component.params.push({
        name: 'image',
        value: [FileFieldTransformer.toSourceImages(this.apiData.image, this.context, { type: component.name })],
      })
    }

    return component
  }

  /**
   * @param {DatoProductlistReasssuranceItemRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new ProductListReassuranceItemRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductListReassuranceItemRecordTransformer
