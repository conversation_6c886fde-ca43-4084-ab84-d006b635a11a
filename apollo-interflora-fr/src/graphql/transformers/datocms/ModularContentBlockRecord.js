const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')

const Transformer = require('../Transformer')
const DatoCtaAlignedTransformer = require('./CtaAligned')

/**
 * @extends {Transformer<DatoModularContentBlockRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class ModularContentBlockRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.modular_content_block

    this.apiData.content?.forEach((entry) => {
      switch (entry.__typename) {
        case CONST.cms.dato.blockTypes.ContentHtmlRecord:
          component.params.push({
            name: 'content_html',
            value: [new GQLScalarString(entry.html)],
          })
          break
        case CONST.cms.dato.blockTypes.CtaRecord:
          component.params.push({
            name: 'cta_aligned',
            value: [DatoCtaAlignedTransformer.toGQL(entry, this.context, this.options)],
          })
          break
      }
    })

    return component
  }

  /**
   * @param {DatoHelpRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    return new ModularContentBlockRecordTransformer(block, context, options).transformToGQL()
  }
}

module.exports = ModularContentBlockRecordTransformer
