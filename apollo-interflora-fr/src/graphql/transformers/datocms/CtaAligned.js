const GQLCtaAligned = require('../../models/CtaAligned')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoCtaRecord, {}>}
 */
class DatoCtaAlignedTransformer extends Transformer {
  transformToGQL() {
    const cta = new GQLCtaAligned()

    cta.align = this.apiData.align

    if (this.apiData.link?.length) {
      const link = this.apiData.link[0]
      cta.label = link.label
      cta.link = DatoUrlTransformer.toGQL(link, this.context, {
        categoryTree: this.options.categoryTree,
      })
    }
    return cta
  }

  /**
   * @param {DatoCtaRecord} cta
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLCtaAligned}
   */
  static toGQL(cta, context, options = {}) {
    return new DatoCtaAlignedTransformer(cta, context, options).transformToGQL()
  }
}

module.exports = DatoCtaAlignedTransformer
