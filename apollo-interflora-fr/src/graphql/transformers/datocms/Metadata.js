const GQLMeta = require('../../models/MetaData')

const Transformer = require('../Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<DatoTag, GQLMetaData, {}>}
 */
class MetaDataTransformer extends Transformer {
  /**
   * @param {DatoTag} apiData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLMeta}
   */
  static toGQL(apiData, context, options) {
    return new MetaDataTransformer(apiData, context, options).transformToGQL()
  }

  /**
   * @returns {GQLMeta}
   */
  transformToGQL() {
    let tagContent = new GQLMeta()
    tagContent.tag = this.apiData.tag || 'meta'
    if (this.apiData.attributes) {
      tagContent.params = [
        {
          name: this.apiData.attributes.property || this.apiData.attributes.name,
          value: this.apiData.attributes.content,
        },
      ]
    } else if (this.apiData.content) {
      tagContent.params = [
        {
          name: this.apiData.tag,
          value: this.apiData.content,
        },
      ]
    }
    return tagContent
  }
}

module.exports = MetaDataTransformer
