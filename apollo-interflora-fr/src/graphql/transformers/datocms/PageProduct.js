const config = require('../../../config')
const CONST = require('../../../const')

const GQLBreadcrumb = require('../../models/Breadcrumb')
const GQLPage = require('../../models/Page')
const GQLTemplate = require('../../models/Template')

const BlockWrapperTransformer = require('./BlockWrapper')
const DeliveryInformationRecordTransformer = require('./DeliveryInformationRecord')
const PageTransformer = require('./Page')
const RouteTransformer = require('./Route')
const TemplateTransformer = require('./Template')

/**
 * @typedef {{
 *  category: DatoCategoryPage,
 *  type: ('category'|'cms'|'product'),
 *  zones: string[]
 * }} DatoPageTransformerOptions
 */
/**
 * @extends {Transformer<DatoProduct, GQLPage, {}>}
 */
class DatoPageProductTransformer extends PageTransformer {
  transformToGQL() {
    const page = new GQLPage()

    Object.assign(page, this.getNavigation())

    page.breadcrumbs = this._breadcrumbs()

    page.meta = this.meta
    // @todo
    page.pro = false
    page.title =
      this.apiData.seoMeta?.title ||
      PageTransformer.findMetaParam(page.meta, 'title')?.value ||
      this.apiData.title ||
      ''

    // @todo changed to new see ITFFR-4502
    page.version = CONST.cms.templateVersion.new

    for (const [attr, zoneName] of Object.entries(config.datocms.product.zonesMapping)) {
      if (!this.apiData[attr]) continue

      if (this.options.zones && !this.options.zones.includes(zoneName)) continue

      const zone = {
        name: zoneName,
        templates: [],
      }

      for (const section of this.apiData[attr]) {
        // Don't display ITF+ template on the digital catalog PDP
        if ([CONST.siteIds.catalog].includes(this.context.siteId) && section.title === 'pdp_interfloraPlus_template') {
          continue
        }
        zone.templates.push(
          TemplateTransformer.toGQL(section, this.context, {
            categoryTree: this.options.categoryTree,
            page: this.apiData,
          })
        )
      }

      if (this.options.additionalComponents && this.options.additionalComponents[attr]) {
        for (const section of this.options.additionalComponents[attr]?.filter(
          (s) => s.title !== CONST.cms.dato.productCarouselDisplay.crossSell
        ) || []) {
          zone.templates.push(
            TemplateTransformer.toGQL(section, this.context, {
              categoryTree: this.options.categoryTree,
              page: this.apiData,
            })
          )
        }
      }
      page.componentZones.push(zone)
    }

    if (this.options.mainNavigation?.headerPhone) {
      page.componentZones.push(this.generateComponentZone(CONST.cms.zones.header, this.generateHeaderSectionRecord()))
    }

    // after_product zone
    {
      const zone = this._getZone(page, CONST.cms.zones.after_product)

      // Not visible on the digital catalog
      if (this.context.siteId === config.apps.defaultSiteId) {
        if (this.apiData.richDeliveryInformation) {
          const template = new GQLTemplate()
          template.background = CONST.cms.templateBackground.none
          template.name = CONST.cms.defaultTemplateName
          template.wrapper = CONST.cms.templateWrapper.alternate

          template.components.push(
            DeliveryInformationRecordTransformer.toGQL(this.apiData.richDeliveryInformation, this.context)
          )

          zone.templates.unshift(template)
        }
      }

      if (zone.templates.length) {
        this._addZone(page, zone)
      }
    }

    // cross-sells
    if (this.context.siteId === config.apps.defaultSiteId && this.options.crossSellProducts?.length > 0) {
      let buttonLabel = ''
      let subtitle = ''
      let title = ''

      if (this._includeZone(CONST.cms.zones.after_info)) {
        const zone = this._getZone(page, CONST.cms.zones.after_info)

        // retrieve values from default blocks
        for (const section of this.options.additionalComponents?.beforeFooter ?? []) {
          const block = section.blocks.find(
            (b) =>
              b.__typename === CONST.cms.dato.blockTypes.ProductCarousel &&
              b.display === CONST.cms.dato.productCarouselDisplay.crossSell
          )
          if (block) {
            buttonLabel = block.link[0].label
            title = block.title
            subtitle = block.subtitle
            break
          }
        }

        const template = new GQLTemplate()
        template.background = CONST.cms.templateBackground.delight
        template.name = CONST.cms.defaultTemplateName
        template.wrapper = CONST.cms.templateWrapper.default

        const block = {
          link: [
            {
              __typename: 'Link',
              label: buttonLabel,
              target: {
                __typename: 'CategoryPageRecord',
                id: this.apiData.defaultCategory.id,
                slug: this.apiData.defaultCategory.slug,
              },
            },
          ],
        }

        template.components.push(
          BlockWrapperTransformer.toGQL(block, this.context, {
            categoryTree: this.options.categoryTree,
            type: 'cross-sell-after',
          })
        )

        zone.templates.push(template)
        if (zone.templates.length > 0) this._addZone(page, zone)
      }

      // after
      if (this._includeZone(CONST.cms.zones.after_product)) {
        const zone = this._getZone(page, CONST.cms.zones.after_product)
        this._addZone(page, zone)

        const template = new GQLTemplate()
        template.background = CONST.cms.templateBackground.delight
        template.name = CONST.cms.defaultTemplateName
        template.wrapper = CONST.cms.templateWrapper.default

        const block = {
          subtitle,
          title,
        }
        template.components.push(BlockWrapperTransformer.toGQL(block, this.context, { type: 'cross-sell-before' }))
        zone.templates.push(template)
      }
    }

    return page
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const breadcrumbs = super._breadcrumbs()

    if (this.options.category) {
      const breadcrumb = new GQLBreadcrumb()
      breadcrumb.label = this.options.category.name ?? this.options.category.title
      breadcrumb.link = RouteTransformer.toGQL(
        {
          label: this.options.category.name ?? this.options.category.title,
          target: {
            __typename: 'CategoryPageRecord',
            id: this.options.category.id,
            slug: this.options.category.slug,
          },
        },
        this.context,
        {
          categoryTree: this.options.categoryTree,
        }
      )
      breadcrumbs.push(breadcrumb)
    }

    {
      const breadcrumb = new GQLBreadcrumb()
      breadcrumb.label = this.apiData.title
      breadcrumbs.push(breadcrumb)
    }

    return breadcrumbs
  }

  /**
   * @param {DatoProduct} product
   * @param {GraphQLContext} context
   * @param {DatoPageTransformerOptions} options
   * @returns {GQLPage}
   */
  static toGQL(product, context, options = {}) {
    const _options = {
      ...options,
      zones: Array.isArray(options.zones) && options.zones.length ? options.zones : undefined,
    }
    const transformer = new DatoPageProductTransformer(product, context, _options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoPageProductTransformer
