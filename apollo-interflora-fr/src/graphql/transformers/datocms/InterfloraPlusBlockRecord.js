const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')
const DatoUrlTransformer = require('./Url')

/**
 * @extends {Transformer<DatoInterfloraPlusBlockRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class DatoInterfloraPlusTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.interflora_plus

    // @ugly - hard-coded
    component.params.push({
      name: 'icon',
      value: [new GQLScalarString('site-logo-premium')],
    })

    if (this.apiData.description) {
      component.params.push({
        name: 'description',
        value: [new GQLScalarString(this.apiData.description)],
      })
    }
    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }
    if (this.apiData.link?.[0]) {
      if (this.apiData.link[0].label) {
        component.params.push({
          name: 'link_label',
          value: [new GQLScalarString(this.apiData.link[0].label)],
        })
      }

      component.params.push({
        name: 'link',
        value: [
          DatoUrlTransformer.toGQL(this.apiData.link[0], this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }
    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }
    if (this.apiData.legals) {
      component.params.push({
        name: 'legals',
        value: [new GQLScalarString(this.apiData.legals)],
      })
    }

    return component
  }

  /**
   * @param {DatoInterfloraPlusBlockRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoInterfloraPlusTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoInterfloraPlusTransformer
