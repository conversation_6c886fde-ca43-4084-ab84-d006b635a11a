const GQLComponent = require('../../models/Component')
const GQLScalarString = require('../../models/ScalarString')
const CONST = require('../../../const')

const Transformer = require('../Transformer')
const FileFieldTransformer = require('./FileField')
const DatoUrlTransformer = require('./Url')
const { isJSON } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoMainBannerRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class MainBannerRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = 'main-banner'

    if (this.apiData.title) {
      if (isJSON(this.apiData.title)) {
        const _title = JSON.parse(this.apiData.title)
        component.params.push({
          name: 'title',
          value: [new GQLScalarString(_title?.text ?? '')],
        })
        component.params.push({
          name: 'level',
          value: [new GQLScalarString(_title?.level ?? '')],
        })
      } else {
        component.params.push({
          name: 'title',
          value: [new GQLScalarString(this.apiData.title)],
        })
      }
    }

    if (this.apiData.subtitle)
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })

    if (this.apiData.image)
      component.params.push({
        name: 'image',
        value: [
          FileFieldTransformer.toSourceImages(this.apiData.image, this.context, {
            type: CONST.cms.positions.moment_first,
          }),
        ],
      })

    if (this.apiData.link?.length) {
      const link = this.apiData.link[0]
      component.params.push({
        name: 'link_label',
        value: [new GQLScalarString(link.label)],
      })
      component.params.push({
        name: 'link',
        value: [
          DatoUrlTransformer.toGQL(link, this.context, {
            categoryTree: this.options.categoryTree,
          }),
        ],
      })
    }

    return component
  }

  /**
   * @param {DatoMainBannerRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new MainBannerRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = MainBannerRecordTransformer
