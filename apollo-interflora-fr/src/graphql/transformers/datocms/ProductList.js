const config = require('../../../config')

const { render } = require('datocms-structured-text-to-html-string')
const { GQLProductList } = require('../../models')
const Transformer = require('../Transformer')

class DatoProductListTransformer extends Transformer {
  transformToGQL() {
    const {
      // body
      commercetoolsCategory,
      name,
      title,
      slug,
    } = this.apiData

    const productList = new GQLProductList()
    productList.pageSize = config.categories.pageSize

    if (!commercetoolsCategory) {
      productList.total = 0
    }

    productList.bloomreachDisabled = this.apiData.bloomreachDisabled ?? false
    productList.categoryId = commercetoolsCategory
    productList.categoryName = name
    productList.categoryDescription = this.apiData.body?.value ? render(this.apiData.body.value.document) : ''
    productList.categorySlug = slug
    productList.categoryTitle = title

    return productList
  }

  /**
   * @param {DatoProduct} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  categoryDescription,
   *  categoryId: string,
   *  categoryName: string,
   *  categorySlug: string,
   *  categoryTitle: string,
   *  filters: GQLProductFilterType[],
   * }} options
   * @returns {GQLProductList}
   */
  static toGQL(apiData, context, options) {
    const transformer = new DatoProductListTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoProductListTransformer
