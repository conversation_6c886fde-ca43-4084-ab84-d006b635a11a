const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

const DatoUrlTransformer = require('./Url')
const { stripHTMLRootTag } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoArticleRecord, GQLComponent, DatoBlockTransformerOptions>}
 */
class DatoArticleRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.positions.articles

    if (this.apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }
    if (this.apiData.subtitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.subtitle)],
      })
    }

    if (this.apiData.link) {
      if (this.apiData.link.label) {
        component.params.push({
          name: 'button_label',
          value: [new GQLScalarString(this.apiData.link.label || this.apiData.title)],
        })
        component.params.push({
          name: 'button_link',
          value: [
            DatoUrlTransformer.toGQL(this.apiData.link, this.context, {
              categoryTree: this.options.categoryTree,
            }),
          ],
        })
      }
    }

    const acc = {
      description: [],
      icon: [],
      image: [],
      link: [],
      link_label: [],
      subtitle: [],
    }

    for (const teaser of this.apiData.items) {
      let description = { part1: '', part2: '' }
      for (const key of Object.keys(acc)) {
        switch (key) {
          case 'description':
            description.part2 = stripHTMLRootTag(teaser[key])
            break
          case 'subtitle':
            if (config.cms.blocks.teaser.oneLineTeaser) {
              description.part1 = `<strong>${teaser[key]}</strong> `
              acc.subtitle.push(null)
            } else {
              acc.subtitle.push(new GQLScalarString(teaser[key]))
            }
            break
          case 'icon':
            acc.icon.push(new GQLScalarString(teaser[key]))
            break
          case 'image':
            if (teaser.image) {
              const images = new GQLSourceImages()

              const image = new GQLImage()
              image.altText = teaser.image.alt || ''
              image.format = CONST.images.formats.mobile
              image.order = 0
              image.type = component.name
              image.url = teaser.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

              images.sources = [image]

              acc.image.push(images)
            } else {
              acc.image.push(null)
            }
            break
          case 'link':
            acc.link.push(
              teaser.link[0]
                ? DatoUrlTransformer.toGQL(teaser.link[0], this.context, {
                    categoryTree: this.options.categoryTree,
                  })
                : null
            )
            acc.link_label.push(teaser.link?.[0]?.label ? new GQLScalarString(teaser.link[0].label) : null)
            break
        }
      }
      const descriptionString = `${description.part1}${description.part2}`.trim()
      descriptionString.length > 0
        ? acc.description.push(new GQLScalarString(descriptionString))
        : acc.description.push(null)
    }

    for (const [key, values] of Object.entries(acc)) {
      component.params.push({
        name: `${key}s`,
        value: values,
      })
    }

    return component
  }

  /**
   * @param {DatoArticleRecord} block
   * @param {GraphQLContext} context
   * @param {DatoBlockTransformerOptions} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoArticleRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoArticleRecordTransformer
