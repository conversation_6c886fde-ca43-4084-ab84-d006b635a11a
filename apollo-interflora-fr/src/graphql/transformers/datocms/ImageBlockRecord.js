const config = require('../../../config')
const CONST = require('../../../const')

const GQLComponent = require('../../models/Component')
const GQLImage = require('../../models/Image')
const GQLScalarString = require('../../models/ScalarString')
const GQLSourceImages = require('../../models/SourceImages')

const Transformer = require('../Transformer')

const { render } = require('datocms-structured-text-to-html-string')
const { stripHTMLRootTag } = require('../../../helpers/string')

/**
 * @extends {Transformer<DatoImageBlockRecord, GQLComponent, {}>}
 */
class DatoImageBlockRecordTransformer extends Transformer {
  transformToGQL() {
    const component = new GQLComponent()

    component.name = this.options.name || CONST.cms.positions.image2

    if (this.apiData.description) {
      component.params.push({
        name: 'description',
        value: [new GQLScalarString(stripHTMLRootTag(render(this.apiData.description.value)))],
      })
    }
    if (this.apiData.title) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(this.apiData.title)],
      })
    }
    if (this.apiData.image) {
      const images = new GQLSourceImages()

      const image = new GQLImage()
      image.altText = this.apiData.image.alt || ''
      image.format = CONST.images.formats.mobile
      image.order = 0
      image.type = component.name
      image.url = this.apiData.image.url.replace(/^https?:\/\/[^/]+/, config.apps[this.context.siteId].mediaBaseUrl)

      images.sources = [image]

      component.params.push({
        name: 'image',
        value: [images],
      })
    }

    return component
  }

  /**
   * @param {DatoImageBlockRecord} block
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLComponent}
   */
  static toGQL(block, context, options = {}) {
    const transformer = new DatoImageBlockRecordTransformer(block, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoImageBlockRecordTransformer
