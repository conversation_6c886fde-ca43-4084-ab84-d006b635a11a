/** @typedef {import('../../models/Link')} GQLLink */
/** @typedef {import('../../models/Route')} GQLRoute */
/** @typedef {import('./_typedef')} */

const Transformer = require('../Transformer')

const DatoLinkTransformer = require('./Link')
const DatoRouteTransformer = require('./Route')

/**
 * @extends {Transformer<{ __typename: string } & (DatoExternalLinkRecord | DatoLinkRecord), (GQLLink|GQLRoute), DatoNavigationItemTransformerOptions>}
 */
class DatoUrlTransformer extends Transformer {
  transformToGQL() {
    if (this.apiData.__typename === 'ExternalLinkRecord') {
      return DatoLinkTransformer.toGQL(this.apiData, this.context, this.options)
    }

    if (this.apiData.target) {
      return DatoRouteTransformer.toGQL(this.apiData, this.context, this.options)
    }

    return DatoLinkTransformer.toGQL({ url: '#' }, this.context, this.options)
  }

  /**
   * @param {(DatoExternalLinkRecord|DatoLinkRecord)} apiData
   * @param {GraphQLContext} context
   * @param {DatoNavigationItemTransformerOptions} options
   * @returns {(GQLLink|GQLRoute)}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new DatoUrlTransformer(Array.isArray(apiData) ? apiData[0] : apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DatoUrlTransformer
