const { GQL<PERSON>eward, GQLRewardReservation, GQLRewardTransaction } = require('../../models')
const { reward, awardit } = require('../../../const')

const getRewardDetails = (card) => {
  const rewardDetails = new GQLReward()
  if (!card) {
    return rewardDetails
  }

  rewardDetails.id = card.id
  rewardDetails.cardNumber = card.multicode
  rewardDetails.type = reward.services.awardit
  rewardDetails.balance = card.balance
  rewardDetails.startDate = new Date(card.startDate).toISOString()
  rewardDetails.endDate = new Date(card.endDate).toISOString()
  rewardDetails.currency = card.currency
  rewardDetails.status = awardit.cardStatus[card.status] ?? 'Unknown'

  return rewardDetails
}

const getRewardReservation = (reservation) => {
  const rewardReservation = new GQLRewardReservation()

  if (!reservation) {
    return rewardReservation
  }

  rewardReservation.reservationId = reservation.reservationId
  rewardReservation.amount = reservation.amount
  rewardReservation.timestamp = new Date(reservation.timestamp).toISOString()
  rewardReservation.status = reservation.status
  rewardReservation.reservationExpires = new Date(reservation.reservationExpires).toISOString()

  return rewardReservation
}

const getRewardTransaction = (transaction) => {
  const rewardTransaction = new GQLRewardTransaction()
  if (!transaction) {
    return rewardTransaction
  }
  rewardTransaction.transactionId = transaction.transactionId
  rewardTransaction.amount = transaction.amount
  rewardTransaction.currency = transaction.currency
  rewardTransaction.timestamp = new Date(transaction.timestamp).toISOString()
  rewardTransaction.transactionType = transaction.transactionType

  return rewardTransaction
}

module.exports = {
  getRewardDetails,
  getRewardReservation,
  getRewardTransaction,
}
