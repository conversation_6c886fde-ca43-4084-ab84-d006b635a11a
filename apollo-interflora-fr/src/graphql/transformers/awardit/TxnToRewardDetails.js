const Transformer = require('../Transformer')
const { GQLRewardDetails } = require('../../models')
const { getRewardDetails, getRewardTransaction } = require('./helper')

class TxnToRewardDetails extends Transformer {
  transformToGQL() {
    const rewardDetails = new GQLRewardDetails()
    rewardDetails.reward = getRewardDetails(this.apiData.card)

    rewardDetails.transactions = this.apiData.transactions.map((transaction) => {
      return getRewardTransaction(transaction)
    })
    return rewardDetails
  }

  /**
   *
   * @param {{GiftCardData}} giftCardData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLRewardDetails}
   */
  static toGQL(giftCardData, context, options = {}) {
    const transformer = new TxnToRewardDetails(giftCardData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TxnToRewardDetails
