const Transformer = require('../Transformer')
const { GQLRewardEvent } = require('../../models')
const { getRewardDetails, getRewardReservation, getRewardTransaction } = require('./helper')

class TxnToRewardEvent extends Transformer {
  transformToGQL() {
    const rewardDetails = new GQLRewardEvent()
    rewardDetails.reward = getRewardDetails(this.apiData?.card)
    rewardDetails.reservation = getRewardReservation(this.apiData?.reservation)
    rewardDetails.transaction = getRewardTransaction(this.apiData?.transaction)

    return rewardDetails
  }

  /**
   *
   * @param {{GiftCardData}} giftCardData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLRewardEvent}
   */
  static toGQL(giftCardData, context, options = {}) {
    const transformer = new TxnToRewardEvent(giftCardData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TxnToRewardEvent
