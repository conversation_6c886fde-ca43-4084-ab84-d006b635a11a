const CONST = require('../../../const')

const GQLImage = require('../../models/Image')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<GFSImage, GQLImage, {}>}
 */
class ImageTransformer extends Transformer {
  transformToGQL() {
    const image = new GQLImage()

    // [FAM-1260] set default params width and height
    const url = new URL(this.apiData.URL)
    const searchParams = url.searchParams
    if (!searchParams.has('width')) {
      searchParams.set('width', this.apiData.Width ?? '')
    }
    if (!searchParams.has('height')) {
      searchParams.set('height', this.apiData.Height ?? '')
    }
    url.search = searchParams.toString()

    image.altText = this.options.altText || ''
    image.format = this.options.format || CONST.images.formats.mobile
    image.info = undefined
    image.order = this.options.order || 0
    image.type = this.options.type || CONST.images.types.primary
    image.url = url.toString()

    return image
  }

  static toGQL(apiData, context, options) {
    const transformer = new ImageTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ImageTransformer
