const config = require('../../../config')
const { addTZOffsetToDate, formatToAPI, dateRangeIntervals } = require('../../../helpers/dates')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<{ availabilities: {from: Date, to: Date}[], excluded: Date[] }, GQLDateRange[], { days: Number }>}
 */
class DateRangeTransformer extends Transformer {
  transformToGQL() {
    const { availabilities, excluded } = this.apiData
    if (!availabilities?.length) {
      return []
    }
    let ranges = []
    availabilities.forEach((availability) => {
      ranges = [
        ...ranges,
        ...dateRangeIntervals(
          this._toAPI2Hybris(availability.from),
          this._toAPI2Hybris(availability.to),
          (excluded ?? []).map((d) => this._toAPI2Hybris(d))
        ),
      ]
    })

    return ranges
  }

  /**
   * @param {{ availabilities: {from: Date, to: Date}[], excluded: (Date[] | undefined) }} payload
   * @param {GraphQLContext} context
   * @param {{ days: Number }} options
   * @returns {GQLDateRange[]}
   */
  static toGQL(payload, context, options = { days: config.availability.daysToCheck }) {
    return new DateRangeTransformer(payload, context, options).transformToGQL()
  }

  /**
   * @param {Date} d
   * @returns {string}
   */
  _toAPI2Hybris(d) {
    const tz = config.apps[this.context.siteId].timezone
    return formatToAPI(addTZOffsetToDate(d, tz))
  }
}

module.exports = DateRangeTransformer
