const config = require('../../../config')

const GQLPrice = require('../../models/Price')

const Transformer = require('../Transformer')

const { getMarketingFeesAmount } = require('../../../helpers/gfs')

/**
 * @typedef {{
 *  categories: GFSCategory[],
 *  marketingFees: CTProduct[],
 *  product: GFQProduct
 * }} GFSPriceTransformerOptions
 */

/**
 * @extends {Transformer<Number, GQLPrice, GFSPriceTransformerOptions>}
 */
class PriceTransformer extends Transformer {
  transformToGQL() {
    const price = new GQLPrice()

    price.currencyIso = config.apps[this.context.siteId]?.currency || config.apps[config.apps.defaultSiteId].currency
    price.value = Math.round(
      this.apiData * (config.gfs?.fromEurToLocalCurrencyRate || 1) * 100 +
        getMarketingFeesAmount({
          categories: this.options.categories,
          marketingFees: this.options.marketingFees,
          product: this.options.product,
        })
    )
    price.tiers = []
    return price
  }

  /**
   * @param {Number} amount
   * @param {GraphQLContext} context
   * @param {GFSPriceTransformerOptions} options
   * @returns {GQLPrice}
   */
  static toGQL(amount, context, options = {}) {
    const transformer = new PriceTransformer(amount, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = PriceTransformer
