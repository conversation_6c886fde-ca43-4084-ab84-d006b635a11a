const config = require('../../../config')
const CONST = require('../../../const')

const GQLProduct = require('../../models/Product')
const GQLProductVariant = require('../../models/ProductVariant')
const GQLQualifier = require('../../models/Qualifier')
const GQLStock = require('../../models/Stock')

const Transformer = require('../Transformer')
const ImageTransformer = require('./Image')
const PriceTransformer = require('./Price')

const { gfsAvailability } = require('../../../helpers/gfs')

/** @typedef {import('../../datasources/gfs/__typedef')} */

/**
 * @typedef {{
 *  categories: GFSCategory[],
 *  country: GFSCountry,
 *  marketingFees: CTProduct[],
 *  unit: GFSUnit | null
 * }} GFSProductTransformerOptions
 */

/**
 * @extends {Transformer<GFSProduct, GQLProduct, GFSProductTransformerOptions>}
 */
class GFSProductTransformer extends Transformer {
  getCode() {
    if (config.site === CONST.site.fr) {
      return `${this.options.unit?.ClearingCode ?? ''}${this.apiData.CountryCode}${this.apiData.IntercatCode}`
    }

    return `${this.apiData.CountryCode}-${this.apiData.IntercatCode}`
  }

  transformToGQL() {
    const product = new GQLProduct()

    // @hack
    product.countryId = this.apiData.CountryCode
    if (product.countryId === 'UK') product.countryId = 'GB'

    product.accessories = [] // @todo
    product.classification = CONST.product.productClassifications.NONE
    product.code = this.getCode()
    product.description = this.apiData.LongDescription || this.apiData.ShortDescription || ''
    product.hasCustomText = false
    product.images = []
    product.isFromWorkshop = false
    product.name = this.apiData.Name || this.apiData.InternalName || this.apiData.IntercatCode
    product.slugUrl = this.getCode()
    product.subtitle = ''
    product.type = CONST.product.types.interflora
    product.useQuantitySelector = false

    const avail = gfsAvailability(this.options.country, this.apiData)?.apiData
    if (!avail) return null

    for (const def of [
      {
        prefix: 'Min',
        size: {
          code: 1,
          label: 'Small', // @todo
        },
      },
      {
        prefix: 'Mid',
        size: {
          code: 2,
          label: 'Medium', // @todo
        },
      },
      {
        prefix: 'Max',
        size: {
          code: 3,
          label: 'Large', // @todo
        },
      },
    ]) {
      if (!avail[`${def.prefix}Price`]) continue

      const variant = new GQLProductVariant()

      variant.accessories = []
      variant.code = `${product.code}#${def.size.code}`
      variant.countryId = product.countryId

      variant.delivery = {
        // @todo
        available: true,
        express: false,
        price: undefined, // has its own resolver (@todo)
        type: CONST.product.deliveryTypes.florist,
      }

      const apiImages = this.apiData[`${def.prefix}PriceImages`]
      if (apiImages?.length) {
        // add only one PRIMARY for first image
        variant.images.push(
          ...[CONST.images.types.primary, CONST.images.types.primaryThumbnail].map((type) =>
            ImageTransformer.toGQL(apiImages[0], this.context, { order: 0, type })
          )
        )

        variant.images.push(
          ...apiImages.map((apiImage, order) =>
            ImageTransformer.toGQL(apiImage, this.context, { order, type: CONST.images.types.gallery })
          )
        )
      }

      variant.isFromWorkshop = false
      variant.label = def.size.label // @todo
      variant.maxQuantity = 1 // @todo: check if applicable for other countries than FR
      variant.minQuantity = undefined
      variant.parent = product

      variant.price = PriceTransformer.toGQL(avail[`${def.prefix}Price`], this.context, {
        categories: this.options.categories,
        marketingFees: this.options.marketingFees,
        product: this.apiData,
      })

      variant.qualifiers = [
        Object.assign(new GQLQualifier(), {
          label: product.name,
          orderSlug: 0,
          slugUrlPart: product.code,
          type: {
            name: 'slug', // @todo
            value: CONST.product.qualifierTypes.slug,
          },
          value: product.code,
        }),
        Object.assign(new GQLQualifier(), {
          label: def.size.label,
          orderSlug: 1,
          slugUrlPart: def.size.code,
          type: {
            name: 'price',
            value: CONST.product.qualifierTypes.priceVariantType,
          },
          value: `${def.size.code}`,
        }),
        Object.assign(new GQLQualifier(), {
          label: null,
          orderSlug: 2,
          slugUrlPart: CONST.product.zones.INTERNAT,
          type: {
            name: 'zone',
            value: CONST.product.qualifierTypes.zoneGeographique,
          },
          value: CONST.product.zones.INTERNAT,
        }),
      ]

      variant.stock = new GQLStock() // @todo
      variant.stock.status = CONST.product.stockStatuses.unlimited

      variant.variationCode = CONST.product.variationCodes[def.size.code]

      product.variants.push(variant)
    }
    if (product.variants.length === 0) return null

    product._defaultVariants = product.variants
    product._defaultVariant = product.variants[1] || product.variants[0] // @todo

    product._images = product._defaultVariant.images

    return product
  }

  /**
   * @param {GFSProduct} apiData
   * @param {GraphQLContext} context
   * @param {GFSProductTransformerOptions} options
   * @returns {GQLProduct}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new GFSProductTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = GFSProductTransformer
