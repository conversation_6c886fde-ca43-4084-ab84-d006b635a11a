const config = require('../../../config')

const GQLProductList = require('../../models/ProductList')

const Transformer = require('../Transformer')
const ProductTransformer = require('./Product')

/**
 * @typedef {{
 *  categories: GFSCategory[],
 *  category: GFSCategory,
 *  country: string,
 *  offset: number,
 *  marketingFees: CTProduct[],
 *  unit: GFSUnit,
 * }} GFSProductListTransformerOptions
 */

/**
 * @extends {Transformer<GFSProduct[], GQLProductList, GFSProductListTransformerOptions>}
 */
class ProductListTransformer extends Transformer {
  transformToGQL() {
    const list = new GQLProductList()

    const products = this.apiData
      .map((apiProduct) =>
        ProductTransformer.toGQL(apiProduct, this.context, {
          categories: this.options.categories,
          country: this.options.country,
          marketingFees: this.options.marketingFees,
          unit: this.options.unit,
        })
      )
      .filter(Boolean)

    list.filters = []
    list.categoryDescription = this.options.category?.description
    list.categoryId = this.options.category?.id
    list.categoryName = this.options.category?.name
    list.categoryTitle = this.options.category?.title
    list.pageSize = config.categories.pageSize
    list.products = products.slice(this.options.offset, this.options.offset + config.categories.pageSize)
    list.total = products.length

    return list
  }

  /**
   * @param {GFSProduct[]} products
   * @param {GraphQLContext} context
   * @param {GFSProductListTransformerOptions} options
   * @returns {GQLProductList}
   */
  static toGQL(
    products,
    context,
    options = {
      offset: 0,
    }
  ) {
    const transformer = new ProductListTransformer(products, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductListTransformer
