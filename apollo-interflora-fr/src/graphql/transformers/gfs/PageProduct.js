const CONST = require('../../../const')
const { GQLPage, GQLBreadcrumb, GQLRoute } = require('../../models')
const { DatoPageProductTranformer } = require('../datocms')
const PageTransformer = require('./Page')

class PageProductTransformer extends PageTransformer {
  transformToGQL() {
    const product = this.apiData

    /** @type {GQLPage} */
    let page
    if (this.options.cmsPage) {
      page = DatoPageProductTranformer.toGQL(this.options.cmsPage, this.context, {
        canonical: this.options.canonical,
        type: 'product',
        zones: this.options.zones || [],
      })
    } else {
      page = new GQLPage()
      page.title = this.createTitle(product.Name)
      page.meta = this.generateMeta({
        canonical: this.options.canonical,
        description: product.Name,
        title: product.Name,
      })
      // @todo changed to new see ITFFR-4502
      page.version = CONST.cms.templateVersion.new
    }

    page.breadcrumbs = this._breadcrumbs()

    return page
  }

  static toGQL(category, context, options = {}) {
    const transformer = new PageProductTransformer(category, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const breadcrumbs = super._breadcrumbs()
    /** @type {GFSProduct} */
    const product = this.apiData
    const countryCode = product.CountryCode.toLowerCase()
    const category = new GQLBreadcrumb()
    category.label = this.options.categoryTitle || countryCode
    category.link = new GQLRoute()
    category.link.name = 'category'
    category.link.params = [
      {
        name: 'slugCategory',
        value: '0',
      },
      {
        name: 'internationalSymbol',
        value: 'i',
      },
      {
        name: 'countryId',
        value: countryCode,
      },
      {
        name: 'url',
        value: `/i/${countryCode}/c/0`,
      },
    ]

    breadcrumbs.push(category)

    const current = new GQLBreadcrumb()
    current.label = product.Name || ''
    breadcrumbs.push(current)

    return breadcrumbs
  }
}

module.exports = PageProductTransformer
