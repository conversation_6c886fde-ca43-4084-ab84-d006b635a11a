const { GQLPage, GQLBreadcrumb, GQLRoute } = require('../../models')
const PageTransformer = require('./Page')
const config = require('../../../config')
const { sortBy } = require('lodash')

class PageCategoryTransformer extends PageTransformer {
  transformToGQL() {
    /** @type {GFSCountry} */
    const country = this.apiData
    const page = new GQLPage()
    if (this.options.page) {
      page.meta = this.options.page.meta
      page.title = this.options.page.title
      page.breadcrumbs = this.options.page.breadcrumbs
    } else {
      page.meta = this.generateMeta({
        canonical: this.options.canonical,
        description: country.Name,
        title: country.Name,
      })
      page.title = this.createTitle(country.Name)
      page.breadcrumbs = this._breadcrumbs()
    }

    {
      // Set gellules from the categories and the configuration
      const gellules = []

      /** @type {{[key: string]: {ids: string[], name: string, order: number, exclude: boolean|undefined}}} */
      const configCategories = config.gfs.categories
      const configCategoriesEntries = Object.entries(configCategories)

      const categorySlugs = ['all_occasions']
      for (const category of this.options.categories ?? []) {
        const slug = configCategoriesEntries.find(
          ([_slug, { ids, exclude = false }]) => ids.includes(category.Id) && !exclude
        )?.[0]
        if (categorySlugs.includes(slug)) continue
        else if (slug) {
          categorySlugs.push(slug)
        }
      }

      for (const slug of categorySlugs) {
        gellules.push(this._gellules(slug, configCategories[slug].name))
      }
      page.gellules = sortBy(gellules, [(gellule) => configCategories[gellule.slug].order])
    }

    return page
  }

  /**
   * @typedef {{
   *  canonical: *,
   *  categories: GFSCategory[],
   *  page: GQLPage,
   *  type: ('category'|'cms'|'product'),
   *  zones: string[]
   * }} PageCategoryTransformerOptions
   */
  /**
   * @param {GFSCountry} country
   * @param {GraphQLContext} context
   * @param {DatoPageTransformerOptions} options
   * @returns {GQLPage}
   */
  static toGQL(country, context, options = {}) {
    const transformer = new PageCategoryTransformer(country, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const breadcrumbs = super._breadcrumbs()

    const current = new GQLBreadcrumb()
    current.label = this.apiData.Name
    breadcrumbs.push(current)

    return breadcrumbs
  }

  /**
   * @protected
   * @param {string} slug
   * @param {string} label
   * @returns {GQLGellule}
   */
  _gellules(slug, label) {
    const url = new GQLRoute()
    url.name = 'category'
    url.params.push({ name: 'slugCategory', value: slug })
    url.params.push({ name: 'internationalSymbol', value: 'i' })
    url.params.push({ name: 'countryId', value: this.apiData.CountryCode?.toLowerCase() })
    url.params.push({ name: 'rel', value: 'nofollow' })
    return {
      label,
      slug,
      url,
    }
  }
}

module.exports = PageCategoryTransformer
