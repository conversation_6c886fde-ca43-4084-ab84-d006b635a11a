const { isDefaultCountry, getCountryName } = require('../../../helpers/countries')
const config = require('../../../config')
const { GQLCountry } = require('../../models')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<string, GQLCountry, {}>}
 */
class CountryTransformer extends Transformer {
  transformToGQL() {
    const highlightedCountries = config.gfs.highlightedCountries
    const id = this.apiData.toUpperCase()
    const country = new GQLCountry()
    country.default = isDefaultCountry(this.context.siteId, id)
    country.highlighted = highlightedCountries.includes(id)
    country.knownPostalCode = country.default
    country.label = getCountryName(id, this.context.language)
    country.id = id

    return country
  }

  /**
   * @param {string} id
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLCountry}
   */
  static toGQL(id, context, options = {}) {
    const transformer = new CountryTransformer(id, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CountryTransformer
