const config = require('../../../config')
const { GQLBreadcrumb, GQLRoute } = require('../../models')
const MetaDataTransformer = require('../datocms/Metadata')
const Transformer = require('../Transformer')

class PageTransformer extends Transformer {
  get tpls() {
    return config.apps[this.context.siteId].seo[this.options.type] || config.apps[this.context.siteId].seo.default
  }

  createTitle(title) {
    return this.tpls.title.replace(/%s/, title)
  }

  generateMeta(data) {
    const { title, description, canonical } = data
    const meta = []

    // title & description
    {
      meta.push({
        __typename: 'Tag',
        attributes: null,
        content: this.createTitle(title),
        tag: 'title',
      })
      meta.push({
        __typename: 'Tag',
        attributes: {
          content: this.tpls.description.replace(/%s/, description),
          name: 'description',
        },
        content: null,
        tag: 'meta',
      })
    }

    if (canonical) {
      meta.push({
        __typename: 'Tag',
        attributes: { content: canonical, name: 'canonical' },
        tag: 'link',
      })
    }

    return meta.map((tag) => MetaDataTransformer.toGQL(tag, this.context, {}))
  }

  /**
   * @protected
   * @returns {GQLBreadcrumb[]}
   */
  _breadcrumbs() {
    const home = new GQLBreadcrumb()
    home.label = 'Home'
    home.link = new GQLRoute()
    home.link.name = 'index'
    home.link.params = [
      {
        name: 'url',
        value: '/',
      },
    ]

    return [home]
  }
}

module.exports = PageTransformer
