const GQLAccessoryCategory = require('../../models/AccessoryCategory')

const Transformer = require('../Transformer')
/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<CTCategory, GQLAccessoryCategory, {}>}
 */
class AccessoryCategoryTransformer extends Transformer {
  /**
   * @param {CTCategory} apiData
   * @param {Context} context
   * @returns {GQLAccessoryCategory}
   */
  static toGQL(apiData, context) {
    const transformer = new AccessoryCategoryTransformer(apiData, context)
    return transformer.transformToGQL()
  }

  transformToGQL() {
    const accessoryCategory = new GQLAccessoryCategory()

    accessoryCategory.id = this.apiData.id
    accessoryCategory.name = this.apiData.name

    return accessoryCategory
  }
}

module.exports = AccessoryCategoryTransformer
