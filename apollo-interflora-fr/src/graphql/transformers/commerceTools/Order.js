const _ = require('lodash')

const config = require('../../../config')
const CONST = require('../../../const')

const GQLOrder = require('../../models/Order')
const GQLOrderProduct = require('../../models/OrderProduct')

const Transformer = require('../Transformer')
const CTCartTransformer = require('./Cart')
const CTProductVariantTransformer = require('./ProductVariant')

const { addTZOffsetToDate } = require('../../../helpers/dates')
const { localString, variantIds } = require('../../../helpers/commercetools')
const { isOrderArchived } = require('../../../helpers/orders')

/**
 * @typedef {{
 *  distributionChannels: CTChannel[]
 *  skipProductAPI: boolean
 *  useLegacyOrderNumber: boolean
 * }} CTOrderTransformerOptions
 */

/**
 * @extends {Transformer<CTOrder, GQLOrder, {}>}
 */
class CTOrderTransformer extends Transformer {
  transformToGQL() {
    const order = new GQLOrder()

    const cart = CTCartTransformer.toGQL(this.apiData, this.context, this.options)

    order.accessoryItems = cart.accessoryItems
    order.availableEdit = this.apiData.availableEdit || false
    order.billUrl = this.apiData.custom.fields.billUrl || null
    order.createdAt = new Date(this.apiData.createdAt)
    order.countryCode = cart.delivery?.address?.country?.id || this.apiData.country

    order.delivery = cart.delivery
    if (order.delivery) {
      order.delivery.orderId = this.apiData.id
    }

    order.discountCodes = undefined // force resolver
    order.discountCodesPrice = cart.discountCodesPrice
    order.discountedTotal = cart.discountedTotal
    // order.emailHasAccountNoGuest needs a custom resolver
    order.externalReference = null // @todo
    order.id = this.apiData.id
    order.interfloraplus = !!this.apiData.custom?.fields?.interfloraplus
    order.joinMessage = cart.joinMessage
    if (this.options.useLegacyOrderNumber) {
      order.number = this.apiData?.custom?.fields?.legacyOrderNumber || order.id
    } else {
      order.number = this.apiData.orderNumber || order.id
    }
    // order.paymentMode needs a custom resolver
    order.productItems = cart.productItems
    // order.reason needs a custom resolver
    if (this.apiData.custom?.fields?.occasionCode) {
      order.reason = { id: this.apiData.custom.fields.occasionCode }
    }
    order.serviceItems = cart.serviceItems
    // order.reasons needs a custom resolver
    order.signature = cart.joinMessage?.signature // @tbc
    order.total = cart.total
    // order.user needs a custom resolver
    // order.voucher needs a custom resolver
    // moved after setting order.productItems because SEU method uses it
    order.delivery.status = this.evaluateDeliveryStatus(order)

    if (this.options.skipProductAPI) {
      // Order can link to unpublished / deleted products
      // => populate variants / products solely from the order details

      order.accessoryItems = []
      order.productItems = []
      order.serviceItems = []

      for (const items of [cart.accessoryItems, cart.productItems, cart.serviceItems]) {
        for (const item of items) {
          /** @type {GQLProductVariant} */
          let variant

          const { productId, variantId } = variantIds(item.variant.code)
          const lineItem = this.apiData.lineItems.find((l) => l.productId === productId && l.variant.id === variantId)

          if (lineItem) {
            // if we don't, it most likely means the item is matching a custom line item (i.e. GFS)
            // and we won't be able to populate a variant from it
            variant = CTProductVariantTransformer.toGQL(
              {
                product: {
                  id: lineItem.productId,
                },
                variant: lineItem.variant,
              },
              this.context,
              {
                priceChannels: this.options.distributionChannels,
              }
            )

            const parent = new GQLOrderProduct()
            // fake - *could* be computed from variant, but is not used from order
            parent.classification = CONST.product.productClassifications.NONE
            parent.code = lineItem.productId
            parent.key = lineItem.productKey
            parent.name = localString(lineItem.name, this.context) ?? lineItem.name?.en ?? ''
            // fake - we cannot know if the product can be retrieved from dato but the slug is not used anyway
            parent.slugUrl = ''
            // fake
            parent.type = CONST.product.types.interflora
            parent.variants = [variant]
            parent.setLoaded(CONST.dataSources.commerceTools)
            variant.parent = parent
          }

          let arr = order.productItems
          if (items === cart.accessoryItems) {
            arr = order.accessoryItems
          } else if (items === cart.serviceItems) {
            arr = order.serviceItems
          }
          arr.push({
            ...item,
            variant: variant ?? {},
          })
        }
      }

      order.setLoaded(CONST.dataSources.commerceToolsOrderProducts)
    }

    order.archived = isOrderArchived(order.createdAt, order.delivery?.status)

    order.setLoaded(CONST.dataSources.commerceTools)

    return order
  }

  /**
   * @param {CTOrder} apiData
   * @param {GraphQLContext} context
   * @param {CTOrderTransformerOptions} options
   * @returns {GQLOrder}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new CTOrderTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }

  /**
   * @param {CTOrder} order
   * @returns {String}
   */
  evaluateDeliveryStatus(order) {
    var status = new String()

    if (['IT', 'ES', 'PT'].includes(this.context.countryId)) {
      status = this.evaluateDeliveryStatusSEU(order)
    } else {
      status = this.evaluateDeliveryStatusGroup(order)
    }
    return status
  }

  /**
   * @param {CTOrder} order
   * @returns {String}
   */
  evaluateDeliveryStatusGroup(order) {
    var status = new String()
    if (
      [
        CONST.order.ctStatus.ABSENT,
        CONST.order.ctStatus.ACCEPTED,
        CONST.order.ctStatus.ASSIGNATION_NOT_POSSIBLE,
        CONST.order.ctStatus.ASSIGNED,
        CONST.order.ctStatus.EXTERNAL_COURIER,
        CONST.order.ctStatus.INTERNAL_COURIER,
        CONST.order.ctStatus.IN_PROGRESS,
        CONST.order.ctStatus.NEW,
        CONST.order.ctStatus.NEW_ORDER,
        CONST.order.ctStatus.NOT_PRINTED,
        CONST.order.ctStatus.PRINTED,
        CONST.order.ctStatus.SENT,
      ].includes(this.apiData.custom.fields.floristOrderStatus)
    ) {
      status = CONST.order.deliveryStatus.IN_PROGRESS
    } else if (this.apiData.custom.fields.floristOrderStatus === CONST.order.ctStatus.CANCELLED) {
      status = CONST.order.deliveryStatus.CANCELED
    } else if (this.apiData.custom.fields.floristOrderStatus === CONST.order.ctStatus.DELIVERED) {
      status = CONST.order.deliveryStatus.DELIVERED
    } else {
      // mock status based on delivery date @todo: remove
      status = CONST.order.deliveryStatus[11]
      {
        const tz = (config.apps[this.context?.siteId] ?? config.apps[config.apps.defaultSiteId]).timezone
        const date = addTZOffsetToDate(new Date(`${order.delivery.date}T20:00:00Z`), tz)
        if (new Date() > date) {
          status = CONST.order.deliveryStatus.FAC
        }
      }
    }
    return status
  }

  /**
   * @param {CTOrder} order
   * @returns {String}
   */
  evaluateDeliveryStatusSEU(order) {
    var status = new String()
    if (
      [CONST.order.ctStatus.ASSIGNED, CONST.order.ctStatus.REFUSED].includes(
        this.apiData.custom.fields.floristOrderStatus
      )
    ) {
      status = CONST.order.deliveryStatus.ASSIGNED
    } else if (this.apiData.custom.fields.floristOrderStatus === CONST.order.ctStatus.ACCEPTED) {
      // check if the order has any product delivered with a courier
      const atLeastOneCourierProduct =
        order.productItems != null ? order.productItems.some((item) => item.isFromWorkshop) : false
      if (atLeastOneCourierProduct === false) {
        // check if the fake in transit status must be shown
        const { timezone } = this.context.appConfig
        const now = addTZOffsetToDate(new Date(), timezone)
        let deliveryDate = new Date(order.delivery.date)

        if (
          now.getDate() === deliveryDate.getDate() &&
          now.getMonth() === deliveryDate.getMonth() &&
          now.getFullYear() === deliveryDate.getFullYear()
        ) {
          // delivery date is today
          if (typeof order.delivery.time !== 'undefined' && typeof order.delivery.time.hour !== 'undefined') {
            if (now.getHours() > order.delivery.time.hour - 2) {
              // current time is less then 2 hours to the delivery time, start displaying in transit
              status = CONST.order.deliveryStatus.IN_TRANSIT
            }
          } else if (
            typeof order.delivery.rangeHour !== 'undefined' &&
            ['0', '1', '2', '3'].includes(order.delivery.rangeHour.id)
          ) {
            const intervals = [
              { country: 'IT', from: 7, moment: CONST.commerceTools.moment.Morning + '' },
              { country: 'IT', from: 13, moment: CONST.commerceTools.moment.Afternoon + '' },
              { country: 'IT', from: 7, moment: CONST.commerceTools.moment.Wholeday + '' },
              { country: 'ES', from: 8, moment: CONST.commerceTools.moment.Morning + '' },
              { country: 'ES', from: 15, moment: CONST.commerceTools.moment.Afternoon + '' },
              { country: 'ES', from: 8, moment: CONST.commerceTools.moment.Wholeday + '' },
              { country: 'ES', from: 16, moment: CONST.commerceTools.moment.Evening + '' },
              { country: 'PT', from: 8, moment: CONST.commerceTools.moment.Morning + '' },
              { country: 'PT', from: 15, moment: CONST.commerceTools.moment.Afternoon + '' },
              { country: 'PT', from: 8, moment: CONST.commerceTools.moment.Wholeday + '' },
              { country: 'PT', from: 16, moment: CONST.commerceTools.moment.Evening + '' },
            ]
            let hours = now.getHours()
            let interval = intervals.filter(
              (record) => record.country === this.context.countryId && record.moment === order.delivery.rangeHour.id
            )
            if (interval && interval.length >= 1) {
              if (hours >= interval[0].from) {
                // current time passed 'from value' of current configuration, start displaying in transit
                status = CONST.order.deliveryStatus.IN_TRANSIT
              }
            }
          }
        } else if (now > deliveryDate) {
          // after the delivery date, display the in transit
          status = CONST.order.deliveryStatus.IN_TRANSIT
        }

        if (status !== CONST.order.deliveryStatus.IN_TRANSIT) {
          // if the valus of status is not IN_TRANSIT set it to ACCEPTED
          status = CONST.order.deliveryStatus.ACCEPTED
        }
      } else if (
        [
          CONST.order.ctDeliveryStatus.ALMOST_DELIVERING,
          CONST.order.ctDeliveryStatus.ALMOST_PICKING,
          CONST.order.ctDeliveryStatus.PICKING,
          CONST.order.ctDeliveryStatus.DELIVERING,
          CONST.order.ctDeliveryStatus.WAITING_AT_PICKUP,
        ].includes(this.apiData.custom.fields.deliveryStatus)
      ) {
        status = CONST.order.deliveryStatus.IN_TRANSIT
      } else if (this.apiData.custom.fields.deliveryStatus === CONST.order.ctDeliveryStatus.SCHEDULED) {
        status = CONST.order.deliveryStatus.SCHEDULED
      } else if (this.apiData.custom.fields.deliveryStatus === CONST.order.ctDeliveryStatus.DELIVERED) {
        status = CONST.order.deliveryStatus.DELIVERED
      } else {
        status = CONST.order.deliveryStatus.ACCEPTED
      }
    } else if (this.apiData.custom.fields.floristOrderStatus === CONST.order.ctStatus.CANCELLED) {
      status = CONST.order.deliveryStatus.CANCELED
    } else if (this.apiData.custom.fields.floristOrderStatus === CONST.order.ctStatus.DELIVERED) {
      status = CONST.order.deliveryStatus.DELIVERED
    } else {
      status = CONST.order.deliveryStatus.IN_PROGRESS
    }
    return status
  }
}

module.exports = CTOrderTransformer
