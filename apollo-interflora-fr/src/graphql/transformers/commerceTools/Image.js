const config = require('../../../config')
const CONST = require('../../../const')

const { GQLImage } = require('../../models')
const Transformer = require('../Transformer')

const { localString } = require('../../../helpers/commercetools')

/**
 * @extends {Transformer<CTAsset, GQLImage, {}>}
 */
class ImageTransfomer extends Transformer {
  transformToGQL() {
    const image = new GQLImage()

    image.altText = this.options.alt || localString(this.apiData.name || {}, this.context) || ''
    image.format = CONST.images.formats.mobile

    if (this.options.type) {
      image.type = this.options.type
    } else if (this.apiData.tags.includes(CONST.commerceTools.assetTag.PLP)) {
      image.type = CONST.images.types.primaryThumbnail
    } else {
      image.type = CONST.images.types.primary
    }

    image.order = this.options.order || 0
    image.info = this.options.description || localString(this.apiData.description || {}, this.context)
    if (this.apiData.sources[0]?.uri) {
      let url = this.apiData.sources[0].uri
      const baseUrl = config.apps[this.context.siteId].mediaBaseUrl

      if (!url.startsWith('http')) {
        url = `${baseUrl}/${url.replace(/^\//, '')}`
      } else {
        url = url.replace(/^https:\/\/[^/]+/, baseUrl)
      }

      image.url = url
    } else {
      image.url = '#'
    }

    return image
  }

  /**
   * @param {CTAsset} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  alt: string,
   *  description: string,
   *  locale: string,
   *  order: Number
   * }} options
   * @returns {GQLImage}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new ImageTransfomer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ImageTransfomer
