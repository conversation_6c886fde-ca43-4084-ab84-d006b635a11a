const GQLCurrency = require('../../models/Currency')
const Transformer = require('../Transformer')
/**
 * @extends {Transformer<APICurrency, GQLCurrency, {}>}
 */
class CurrencyTransformer extends Transformer {
  /**
   * @param {APICurrency} apiData
   * @returns {GQLCurrency}
   */
  static toGQL(apiData) {
    const transformer = new CurrencyTransformer(apiData)
    return transformer.transformToGQL()
  }
  transformToGQL() {
    const currency = new GQLCurrency()
    currency.active = this.apiData.active
    currency.isocode = this.apiData.isocode
    currency.name = this.apiData.name
    currency.symbol = this.apiData.symbol
    return currency
  }
}
module.exports = CurrencyTransformer
