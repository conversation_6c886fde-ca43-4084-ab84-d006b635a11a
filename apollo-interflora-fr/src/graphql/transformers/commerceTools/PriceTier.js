const Transformer = require('../Transformer')
const CentPrecisitionMoneyTransformer = require('./CentPrecisionMoney')

const GQLPriceTier = require('../../models/PriceTier')
const HighPrecisitionMoneyTransformer = require('./HighPrecisionMoney')

/** @typedef {impor('./_typedef')} */

/**
 * @extends {Transformer<CTPriceTier, GQLTier, CTPriceTransformerOptions>}
 */
class CTTierTransformer extends Transformer {
  transformToGQL() {
    if (this.apiData.value.type === 'centPrecision') {
      const tier = new GQLPriceTier()

      Object.assign(tier, CentPrecisitionMoneyTransformer.toGQL(this.apiData.value, this.context, this.options))
      tier.minimumQuantity = this.apiData.minimumQuantity

      return tier
    } else if (this.apiData.value.type === 'highPrecision') {
      const tier = new GQLPriceTier()

      Object.assign(tier, HighPrecisitionMoneyTransformer.toGQL(this.apiData.value, this.context, this.options))
      tier.minimumQuantity = this.apiData.minimumQuantity

      return tier
    } else {
      throw new Error('not implemented')
    }
  }

  /**
   * @param {CTPriceTier} apiData
   * @param {GraphQLContext} context
   * @param {CTPriceTransformerOptions} options
   * @returns {GQLTier}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new CTTierTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CTTierTransformer
