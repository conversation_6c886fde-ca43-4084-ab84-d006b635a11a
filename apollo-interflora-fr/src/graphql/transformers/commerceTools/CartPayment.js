const { GQLPayment, GQLPrice } = require('../../models')
const Transformer = require('../Transformer')
const config = require('../../../config')

/** @typedef {import('./_typedef')} */

/**
 * @extends {Transformer<CTPayment,GQLPayment>}
 */
class CTCartPaymentTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData) return null
    const payment = new GQLPayment()

    payment.paymentStatus = this.apiData.paymentStatus.interfaceCode
    payment.paymentMode = this.apiData.paymentMethodInfo.method
    payment.custom = {}
    payment.custom.id = this.apiData.custom?.fields?.id ?? null
    payment.custom.reservationId = this.apiData.custom?.fields?.reservationId ?? null
    payment.createdAt = new Date(this.apiData.createdAt)

    const balance = new GQLPrice()
    balance.value = this.apiData.custom?.fields?.balance?.centAmount ?? 0
    balance.currencyIso =
      this.apiData.custom?.fields?.balance?.currencyCode ?? config.apps[this.context.siteId].currency
    balance.tiers = []
    payment.custom.balance = balance

    const price = new GQLPrice()
    price.value = this.apiData.amountPlanned.centAmount
    price.currencyIso = this.apiData.amountPlanned.currencyCode
    price.tiers = []
    payment.amountPlanned = price

    return payment
  }

  static toGQL(apiData, context, options = {}) {
    const transformer = new CTCartPaymentTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CTCartPaymentTransformer
