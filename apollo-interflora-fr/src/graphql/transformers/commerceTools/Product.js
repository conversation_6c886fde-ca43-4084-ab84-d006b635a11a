const _ = require('lodash')

const config = require('../../../config')
const CONST = require('../../../const')

const GQLProduct = require('../../models/Product')
const Transformer = require('../Transformer')
const CTProductVariantTransformer = require('./ProductVariant')
const CTAccessoryCategoryTransformer = require('./AccessoryCategory')

const { localString, computeIsFromWorkshop, variantIds } = require('../../../helpers/commercetools')
const { seaKeyFromPriceChannelKey } = require('../../../helpers/sea')

/**
 * @typedef {{
 *  accessoryTypes: string[]
 *  countryId: string,
 *  filters?: any,
 *  isAccessory: boolean,
 *  isCardMessage: boolean,
 *  priceChannelKey: [string],
 *  linkedProducts: CTProduct[]
 *  mainAssetId: string|null
 *  position: number|null
 * }} ProductTransformerOptions
 */

/**
 * @extends {Transformer<CTProduct, GQLProduct, ProductTransformerOptions>}
 */
class ProductTransformer extends Transformer {
  transformToGQL() {
    const product = new GQLProduct()

    if (this.options.isAccessory) {
      product.__typename = 'Accessory'
      product.position = this.options.position ?? null
      product.accessoryTypes = [...(this.options.accessoryTypes ?? [])]
      product._mainAssetId = this.options.mainAssetId ?? null
      product.useQuantitySelector = undefined
    }

    const computedProductTypeKey = this.getAttribute('product_type')?.key

    product.countryId = this.options.countryId

    // updating the "message card" product informations
    if (
      Boolean(this.options.isCardMessage) ||
      [
        CONST.product.productType.CARD,
        CONST.product.productType.DESIGN_CARD,
        CONST.product.productType.DESIGN_CARD_SAVED,
        CONST.product.productType.DESIGN_CARD_UPLOADED,
      ].includes(computedProductTypeKey)
    ) {
      const accessoryTypes = new Set(...(product.accessories ?? []))
      switch (computedProductTypeKey) {
        case CONST.product.productType.DESIGN_CARD_UPLOADED:
          accessoryTypes.add(CONST.product.accessoryTypes.messageCard)
          accessoryTypes.add(CONST.product.accessoryTypes.customImage)
          break
        case CONST.product.productType.CARD:
        case CONST.product.productType.DESIGN_CARD:
        case CONST.product.productType.DESIGN_CARD_SAVED:
          accessoryTypes.add(CONST.product.accessoryTypes.messageCard)
          break
        default:
          accessoryTypes.add(CONST.product.accessoryTypes.messageCard)
      }
      product.accessoryTypes = [...accessoryTypes]
    }

    product.isFromWorkshop = computeIsFromWorkshop(this.apiData.masterVariant, this.context)

    product.externalUrl =
      this.apiData.masterVariant?.attributes.find((attr) => attr.name === 'external_url')?.value || null
    product.code = this.apiData.id
    product.key = this.apiData.key
    product._ctLegalNotice = this.getAttribute('legal_notice')
    product.name = localString(this.apiData.name, this.context)
    for (const variant of [this.apiData.masterVariant, ...this.apiData.variants]) {
      const gqlVariant = CTProductVariantTransformer.toGQL(
        {
          product: this.apiData,
          variant,
        },
        this.context,
        {
          mainAssetId: product.mainAssetId,
          priceChannelKey: this.options.priceChannelKey,
        }
      )
      gqlVariant.parent = product
      product.variants.push(gqlVariant)
    }

    if (!product.seaKey && this.options.priceChannelKey) {
      product.seaKey = seaKeyFromPriceChannelKey(this.options.priceChannelKey)
    }

    /* 
    Linked products are products which variants are listed and sold as variants of variants of the product its linked to.
    i.e flower with or without pot in France
    */
    if (this.options.linkedProducts) {
      for (const linkedProduct of this.options.linkedProducts) {
        for (const variant of [linkedProduct.masterVariant, ...linkedProduct.variants]) {
          const gqlVariant = CTProductVariantTransformer.toGQL(
            {
              product: linkedProduct,
              variant,
            },
            this.context,
            {
              priceChannelKey: this.options.priceChannelKey,
            }
          )
          gqlVariant.parent = product
          product.variants.push(gqlVariant)
        }
      }
    }

    let detectedZone = false
    // FR only: if filters contains a geo zone or the context is set in a specific zone that is not default, we show this geo zone's variant in PLP
    if (config.site === CONST.site.fr && (this.options.filters?.length > 0 || this.context.zoneGeoId)) {
      /**@type {GQLInputProductFilter | undefined} */
      let geographicZoneFilter = this.options.filters?.find(
        (filter) => filter.filterId === CONST.product.qualifierTypes.zoneGeographique
      )

      if (!geographicZoneFilter) {
        if (
          this.context.zoneGeoId &&
          CONST.product.filterQueries.ZONE_GEO.entries.map((e) => e.zone).includes(this.context.zoneGeoId)
        ) {
          geographicZoneFilter = { values: [this.context.zoneGeoId] }
        }
      }

      if (geographicZoneFilter) {
        const defVariantIndex = product.variants.findIndex((variant) =>
          variant?.qualifiers?.find(
            (q) =>
              q.type.value === CONST.product.qualifierTypes.zoneGeographique &&
              q.value.includes(geographicZoneFilter.values)
          )
        )
        if (defVariantIndex !== -1) {
          const defaultZoneVariant = product.variants[defVariantIndex]
          product.variants[defVariantIndex] = product.variants[0]
          product.variants[0] = defaultZoneVariant
          detectedZone = true
        }
      }
    }

    product._defaultVariants = product.variants
    product._defaultVariant = product.variants[0]
    product._defaultVariantSize = product._defaultVariant.qualifiers.find(
      (q) => q.type.value === CONST.product.qualifierTypes.priceVariantType
    )?.value
    if (!product._defaultVariantSize) {
      this.context.log.warn(`no size qualifier set for ${product._defaultVariant.sku}`)
      product._defaultVariantSize =
        product.variants.length === 1 ? CONST.product.sizes.unique : CONST.product.sizes.medium
    }

    // @warning: must be done **after** variants are set
    product.classification = this.apiData.classification
    product._ctLegalNotice = this.apiData._ctLegalNotice
    product.subtitle = this.apiData.summary

    // sort variants
    {
      const defaultZone = this.context.appConfig.defaultZone
      let i = 0
      // { small: 0, medium: 1, large: 2, unique: 3 }
      const map = CONST.product.sizesSort.reduce((o, size) => {
        o[size] = i++
        return o
      }, {})

      product.variants.sort((a, b) => {
        const sizeA = a.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.priceVariantType)?.value
        const sizeB = b.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.priceVariantType)?.value

        let sizeScoreA = map[sizeA] || 0
        let sizeScoreB = map[sizeB] || 0

        if (sizeScoreA !== sizeScoreB) {
          return sizeScoreA - sizeScoreB
        }

        // If we don't have any specificly asked zone and a default zone is configured (only in FR for now), we get prioritize variant from this default zone.
        if (!this.context.zoneGeoId && defaultZone && !detectedZone) {
          const zoneScoreA =
            a.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.zoneGeographique)?.value ===
            defaultZone
              ? 0
              : 1
          const zoneScoreB =
            b.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.zoneGeographique)?.value ===
            defaultZone
              ? 0
              : 1
          if (zoneScoreA !== zoneScoreB) {
            return zoneScoreA - zoneScoreB
          }
        }

        // same size, same zone > sort by price
        const aPrice = this.getCTVariantFromGQLVariant(a)?.price?.value.centAmount
        const bPrice = this.getCTVariantFromGQLVariant(b)?.price?.value.centAmount
        return aPrice - bPrice
      })
    }

    // FR only + plants covers -> default is with pot cover
    if (config.site === CONST.site.fr && this.options.linkedProducts) {
      const variants = product.variants.filter(
        (v) =>
          v.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.zoneGeographique)?.value ===
          this.context.appConfig.defaultZone
      )
      // only applies to plants having both without and with pot variants
      const hasWithoutPot = variants.some((v) => v.variationCode === CONST.product.variationCodes.SANS_POT)
      if (hasWithoutPot) {
        const withPotVariants = variants.filter((v) =>
          [CONST.product.variationCodes.AVEC_POT_1, CONST.product.variationCodes.AVEC_POT_2].includes(v.variationCode)
        )
        if (withPotVariants.length > 0) {
          // already sorted - the first variant with pot is the smallest one
          product._defaultVariant = withPotVariants[0]
          product._defaultVariantSize = product._defaultVariant.qualifiers.find(
            (q) => q.type.value === CONST.product.qualifierTypes.priceVariantType
          )?.value
        }
      }
    }

    const accessoriesCategory = this.getAttribute('default_accessories_category')
    if (accessoriesCategory)
      product.categories.push(CTAccessoryCategoryTransformer.toGQL(accessoriesCategory, this.context))

    product.bundledProducts = (this.getAttribute('bundled_products') ?? []).map(({ id }) => id)

    product.setLoaded(CONST.dataSources.commerceTools)
    product._skipAvailabilityOn = this.getAttribute('skip_availability_on')?.map((att) => att.key) ?? []

    return product
  }

  /**
   * @param {string} name
   * @returns {*}
   */
  getAttribute(name) {
    const attributes = _.chain(this.apiData.masterVariant?.attributes || [])
      .keyBy('name')
      .mapValues('value')
      .value()
    return attributes[name]
  }

  /**
   * @param {GQLProductVariant} variant
   * @return {CTProductVariant}
   */
  getCTVariantFromGQLVariant(variant) {
    const { productId, variantId } = variantIds(variant.code)

    let ctProduct = this.apiData
    if (productId !== this.apiData.id) {
      ctProduct = (this.options.linkedProducts ?? []).find((p) => p.id === productId)
    }

    return [ctProduct.masterVariant, ...ctProduct.variants].find((v) => v.id === variantId)
  }

  /**
   * @param {CTProduct} apiData
   * @param {Context} context
   * @param {ProductTransformerOptions} options
   * @returns {GQLProduct}
   */
  static toGQL(apiData, context, options) {
    const transformer = new ProductTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductTransformer
