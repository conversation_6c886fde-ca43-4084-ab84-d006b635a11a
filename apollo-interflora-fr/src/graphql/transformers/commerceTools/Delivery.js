const _ = require('lodash')
const config = require('../../../config')
const CONST = require('../../../const')

const { GQLDelivery, GQLContact } = require('../../models')
const PhoneTransformer = require('../Phone')
const Transformer = require('../Transformer')
const AddressTransformer = require('./Address')
const CentPrecisionMoney = require('./CentPrecisionMoney')

const { addTZOffsetToDate } = require('../../../helpers/dates')

/**
 * @extends {Transformer<CTCart, GQLDelivery, {}>}
 */
class DeliveryTransformer extends Transformer {
  transformToGQL() {
    const delivery = new GQLDelivery()

    delivery.parentId = this.apiData.id

    delivery.mode = this.apiData.custom?.fields?.deliveryMode

    // ⚠️ assumes taxes are included in price!
    delivery.discount = CentPrecisionMoney.toGQL(this.apiData.shippingInfo?.discountedPrice?.value, this.context)
    delivery.price = CentPrecisionMoney.toGQL(
      this.apiData.shippingInfo?.price || {
        centAmount: 0,
        currencyCode: 'EUR',
      },
      this.context
    )

    delivery.status = CONST.cart.deliveryStatus.UNKNOWN
    delivery.statusFallbackLabel = '@todo Delivery status' // @todo
    delivery.useFixHour = false // @todo

    if (this.apiData.billingAddress) {
      delivery.sender = AddressTransformer.toGQL(this.apiData.billingAddress, this.context, {
        invoiceRequest: this.apiData.custom?.fields?.invoiceRequest,
        optinEmail: this.apiData.billingAddress?.custom?.fields?.optinEmail,
        optinSMS: this.apiData.billingAddress?.custom?.fields?.optinSMS,
        type: CONST.address.addressType.sender,
      })
    }
    if (this.apiData.shippingAddress) {
      const { date, mode, moment, time } = this.apiData.shippingAddress.custom?.fields || {}
      // by default, consider we deliver late to not block
      let localTimeString = '21:00'

      delivery.date = date

      if (mode) {
        delivery.mode = mode
      }

      if (time) {
        const [hour, minute] = time.substring(0, 5).split(':')
        delivery.time = {
          hour,
          minute,
        }
        localTimeString = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`
      }

      if (moment) {
        delivery.rangeHour = {
          available: true, // @todo!!!
          id: moment,
          label: this.context.appConfig.momentLabels?.[moment],
        }
        const momentTimes = this.context.appConfig.momentTimes?.[moment]
        if (momentTimes && !time) {
          localTimeString = `${`${momentTimes[1]}`.padStart(2, '0')}:00`
        }
      }

      if (date) {
        delivery.status = CONST.cart.deliveryStatus.OK

        // check if delivery date is expired
        const localeDateTime = addTZOffsetToDate(
          new Date(`${date}T${localTimeString}:00.000Z`),
          this.context.appConfig.timezone
        )
        if (localeDateTime < new Date(Date.now() + config.availability.minCutOffDuration)) {
          delivery.status = CONST.cart.deliveryStatus.EXPIRED
        }
      }

      delivery.address = AddressTransformer.toGQL(this.apiData.shippingAddress, this.context, {
        type: CONST.address.addressType.delivery,
      })

      const { contactFirstName, contactLastName, contactPhone, contactTitle } =
        this.apiData.shippingAddress?.custom?.fields ?? {}
      if (contactFirstName && contactLastName) {
        const contact = new GQLContact()
        contact.firstName = contactFirstName
        contact.lastName = contactLastName
        contact.phone = contactPhone
          ? PhoneTransformer.toGQL(contactPhone, {
              countryCode: this.apiData.country || this.apiData.shippingAddress.country || this.context.countryId,
            })
          : undefined
        contact.civility = contactTitle

        delivery.contact = contact
      }
    }

    return delivery
  }

  /**
   * @param {CTCart} apiData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLDelivery}
   */
  static toGQL(apiData, context, options) {
    const transformer = new DeliveryTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = DeliveryTransformer
