const CONST = require('../../../const')
const GQLVoucher = require('../../models/Voucher')
const GQLCurrency = require('./Currency')

const Transformer = require('../Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIVoucher, GQLVoucher, {}>}
 */
class VoucherTransformer extends Transformer {
  /**
   * @param {APIVoucher} apiData
   * @param {GraphqlContext} context
   * @param {Object} options
   * @returns {GQLVoucher}
   */
  static toGQL(apiData, context, options) {
    const transformer = new VoucherTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }

  transformToGQL() {
    const voucher = new GQLVoucher()
    const { cartDiscount, currency, currencyName, currencySymbole } = this.options
    const { type } = cartDiscount.value
    const { name, code, description, validFrom, validUntil, isActive } = this.apiData
    const language = this.context.language

    voucher.code = code
    voucher.name = name[language]
    voucher.description = description[language]
    voucher.startDate = validFrom && validFrom.slice(0, 10)
    voucher.endDate = validUntil && validUntil.slice(0, 10)

    switch (type) {
      case 'relative':
        voucher.type = CONST.cart.discountType.percent
        voucher.value = cartDiscount.value.permyriad
        break

      case 'fixed':
        voucher.type = CONST.cart.discountType.fixed
        voucher.currency = GQLCurrency.toGQL({
          active: isActive,
          isocode: currency,
          name: currencyName,
          symbol: currencySymbole,
        })
        if (currency === cartDiscount.value.money[0].currencyCode) {
          voucher.value = cartDiscount.value.money[0].centAmount
        }
        break

      case 'absolute':
        voucher.type = CONST.cart.discountType.amount
        voucher.currency = GQLCurrency.toGQL({
          active: isActive,
          isocode: currency,
          name: currencyName,
          symbol: currencySymbole,
        })
        if (currency === cartDiscount.value.money[0].currencyCode) {
          voucher.value = cartDiscount.value.money[0].centAmount
        }
        break

      case 'shipping':
        voucher.type = CONST.cart.discountType.freeShipping
        break

      case 'giftLineItem':
        voucher.type = CONST.cart.discountType.gift
        voucher.productCode = `${cartDiscount.value.product.id}#${cartDiscount.value.variantId}`
        break

      default:
    }

    return voucher
  }
}

module.exports = VoucherTransformer
