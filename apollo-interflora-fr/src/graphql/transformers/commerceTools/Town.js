const { hash } = require('../../../helpers/string')
const GQLTown = require('../../models/Town')

const Transformer = require('../Transformer')

/**
 * @typedef {{
 *  city: string,
 *  postalCode: string,
 *  province: string
 * }} TownTransformerApiData
 */

/**
 * @extends {Transformer<TownTransformerApiData, GQLTown, {}>}
 */
class TownTransformer extends Transformer {
  transformToGQL() {
    const town = new GQLTown()

    town.label = this.apiData.city
    town.postalCode = this.apiData.postalCode
    town.province = this.apiData.province
    town.id = hash(town)

    return town
  }

  /**
   * @param {TownTransformerApiData} apiData
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLTown}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new TownTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TownTransformer
