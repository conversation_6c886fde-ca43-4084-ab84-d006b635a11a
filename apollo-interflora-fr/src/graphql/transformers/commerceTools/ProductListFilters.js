const config = require('../../../config')
const CTFacetService = require('../../../helpers/commercetools/facet/FacetService')
const CTSortService = require('../../../helpers/commercetools/SortService')
const Transformer = require('../Transformer')

class CTProductListFiltersTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData) {
      return []
    }

    const facetService = CTFacetService.instance(this.options.locale)
    const sortService = CTSortService.instance(this.options.locale)

    const filters = Object.entries(this.apiData).reduce((allFilters, [fieldName, ctFacet]) => {
      allFilters.push(...facetService.getModelFiltersFromCtFacet(fieldName, ctFacet))

      return allFilters
    }, [])

    filters.push(sortService.getSortFilter())

    if (this.context.isDefaultCountry()) {
      // special delivery date filter
      filters.push({
        id: config.filters.date.id,
        label: config.filters.date.label,
        multiple: false,
        type: config.filters.date.id,
        values: [], // not used by front and requested to be empty
      })
    }

    return filters
  }

  /**
   * @param {*} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  locale: string
   * }} options
   * @returns {GQLProductFilterType[]}
   */
  static toGQL(apiData, context, options) {
    const transformer = new CTProductListFiltersTransformer(apiData, context, options)

    return transformer.transformToGQL()
  }
}

module.exports = CTProductListFiltersTransformer
