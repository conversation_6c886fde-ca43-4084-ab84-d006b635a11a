const CONST = require('../../../const')
const config = require('../../../config')

const GQLProductVariant = require('../../models/ProductVariant')
const GQLQualifier = require('../../models/Qualifier')
const GQLStock = require('../../models/Stock')

const Transformer = require('../Transformer')
const CTImageTransformer = require('./Image')
const CTPriceTransformer = require('./Price')

const { localString, computeIsFromWorkshop } = require('../../../helpers/commercetools')
const { addTZOffsetToDate } = require('../../../helpers/dates')
const { findFirst } = require('../../../helpers/array')
const { seaKeyFromPriceChannelKey } = require('../../../helpers/sea')

/**
 * @typedef {{
 *  mainAssetId: (string|null),
 *  priceChannelKey: string,
 *  priceChannels: CTChannel[]
 * }} ProductVariantOptions
 */

/**
 * @extends {Transformer<{product: CTProduct, variant: CTProductVariant}, GQLProductVariant, ProductVariantOptions>}
 */
class ProductVariantTransformer extends Transformer {
  transformToGQL() {
    const variant = new GQLProductVariant()
    variant.code = `${this.apiData.product.id}#${this.apiData.variant.id}`
    {
      const priceChannelId = this.apiData.variant.price?.channel.id
      const priceChannel = (this.options.priceChannels ?? []).find((c) => c.id === priceChannelId)

      for (const [key, conf] of Object.entries(config.sea)) {
        if (priceChannel?.key === conf.priceChannelKey && conf.priceChannelKey) {
          variant.code = `${this.apiData.product.id}#${key}#${this.apiData.variant.id}`
        }
      }

      let seaKey = this.apiData.product.seaKey
      // if given, the priceChannelKey is the *requested* one. It may differ from
      // the one applied if fall-backing to default channel
      if (
        !seaKey &&
        this.options.priceChannelKey &&
        this.options.priceChannelKey !== config.commerceTools.priceChannelKey
      ) {
        seaKey = seaKeyFromPriceChannelKey(this.options.priceChannelKey)
      }
      if (seaKey) {
        variant.code = [this.apiData.product.id, seaKey, this.apiData.variant.id].join('#')
      }
    }

    variant.id = this.apiData.variant.id
    variant.isFromWorkshop = false
    variant.sku = this.apiData.variant.sku

    variant.countryId = this.apiData.product.countryId

    let productClassification = CONST.product.productClassifications.NONE
    let productClassificationLabel
    let productSize
    let imageInfo
    let sizeLabel

    for (const attr of this.apiData.variant.attributes) {
      switch (attr.name) {
        case 'additional_information':
          variant.additionalInfo = {
            content: localString(attr.value, this.context),
            type: 'INFO',
          }
          break
        case 'alcohol':
          variant.alcohol = Math.round(Number.parseFloat(attr.value) * 10)
          break
        case 'colour':
          {
            const qualifier = new GQLQualifier()
            qualifier.label = localString(attr.value.label, this.context)
            qualifier.orderSlug = 2
            qualifier.slugUrlPart = attr.value.key
            qualifier.type = {
              name: attr.name,
              value: CONST.product.qualifierTypes.color,
            }
            qualifier.value = attr.value.key

            variant.qualifiers.push(qualifier)
          }
          break
        case 'delivery_type':
          variant.delivery = {
            available: true, // @todo
            express: false, // @todo,
            // @nb: price is overriden in resolver
            type: attr.value.key,
          }
          variant.isFromWorkshop = computeIsFromWorkshop(this.apiData.variant, this.context)
          break
        case 'diameter':
          variant.diameter = localString(attr.value, this.context)
          break
        case 'height':
          variant.height = localString(attr.value, this.context)
          break
        case 'image_size':
          imageInfo = localString(attr.value, this.context)
          break
        case 'label':
          variant.label = localString(attr.value, this.context)
          if (variant.label) {
            variant.customLabel = true
          }
          break
        case 'legal_notice':
          // mutate parent product
          this.apiData.product._ctLegalNotice = localString(attr.value, this.context)
          break
        case 'max_quantity':
          variant.maxQuantity = attr.value
          break
        case 'min_quantity':
          variant.minQuantity = attr.value
          break
        case 'product_classification':
          productClassification = attr.value.key
          productClassificationLabel = localString(attr.value.label, this.context)
          break
        /** @deprecated */
        case 'related_accessories':
          for (const val of attr.value) {
            variant.accessories.push({
              code: val.id,
              isAccessory: true,
            })
          }
          break
        case 'size':
          {
            const qualifier = new GQLQualifier()

            qualifier.label = localString(attr.value.label, this.context)
            sizeLabel = qualifier.label
            qualifier.orderSlug = 1
            qualifier.slugUrlPart =
              config.products.qualifiers.size.slug === 'code'
                ? CONST.product.sizeCodes[attr.value.key]
                : this.apiData.variant.id
            qualifier.type = {
              name: 'Niveau prix ?',
              value: CONST.product.qualifierTypes.priceVariantType,
            }
            qualifier.value = attr.value.key
            productSize = attr.value.key

            variant.qualifiers.push(qualifier)
          }
          break
        case 'summary':
          // @dirty: mutates parent api product
          this.apiData.product.summary = localString(attr.value, this.context)
          break
        case 'weight':
          variant.weight = attr.value
          break
        case 'se_only_members_id':
          variant.florists = attr.value
          break
      }
    }

    // marketing fee && variant specific shipping fees
    {
      const currentPrice = this.apiData.variant.price || this.findFirstValidPrice(this.apiData.variant.prices)
      variant.priceChannelId = currentPrice?.channel?.id
      const priceFee = currentPrice?.custom?.fields?.marketing_fee
      const defaultFeeAttr = this.apiData.variant.attributes.find((attr) => attr.name === 'marketing_fee')
      if (priceFee) {
        variant.marketingFee = CTPriceTransformer.toGQL(
          {
            value: currentPrice?.custom?.fields?.marketing_fee,
          },
          this.context
        )
      } else if (defaultFeeAttr) {
        variant.marketingFee = CTPriceTransformer.toGQL(defaultFeeAttr, this.context)
      }

      const shippingFee = currentPrice?.custom?.fields?.shippingFees
      const defaultShippingFee = this.apiData.variant.attributes.find((attr) => attr.name === 'shipping_fees')
      const productDefaultShippingFee = this.apiData.variant.attributes.find(
        (attr) => attr.name === 'product_shipping_fees'
      )
      if (shippingFee) {
        variant.shippingFee = CTPriceTransformer.toGQL(
          {
            value: shippingFee,
          },
          this.context
        )
      } else if (defaultShippingFee) {
        variant.shippingFee = CTPriceTransformer.toGQL(defaultShippingFee, this.context)
      } else if (productDefaultShippingFee) {
        variant.shippingFee = CTPriceTransformer.toGQL(productDefaultShippingFee, this.context)
      }
    }

    switch (this.context.appConfig.country) {
      case 'FR':
        this.handleAssets(variant, imageInfo)
        break
      default:
        this.handleAssetsOriginalTaste(variant, imageInfo)
    }

    {
      // @dirty: mutates parent product
      if (this.apiData.product.key === config.products.interfloraplus.key) {
        this.apiData.product.classification = CONST.product.productClassifications.ITFPLUS
      } else {
        this.apiData.product.classification = productClassification
      }

      const { productClassifications: classifications, sizes, variationCodes } = CONST.product
      variant.variationCode = variationCodes[0]

      switch (productClassification) {
        case classifications.CHOCOLATE:
          if (productSize === sizes.small) variant.variationCode = variationCodes.CHOCOLATE_SIZE_1
          else if (productSize === sizes.medium) variant.variationCode = variationCodes.CHOCOLATE_SIZE_2
          else if (productSize === sizes.large) variant.variationCode = variationCodes.CHOCOLATE_SIZE_3
          break
        case classifications.NONE:
          if (productSize === sizes.small) variant.variationCode = variationCodes[1]
          else if (productSize === sizes.medium) variant.variationCode = variationCodes[2]
          else if (productSize === sizes.large) variant.variationCode = variationCodes[3]
          else if (productSize === sizes.unique) variant.variationCode = variationCodes[0]
          break
        case classifications.PLANT:
          variant.variationCode = variationCodes.SANS_POT
          break
        case classifications.ROSES:
          variant.variationCode = variationCodes.ROSES
          break
        case classifications.WITH_POT:
          if (productSize === sizes.small) variant.variationCode = variationCodes.SANS_POT
          else if (productSize === sizes.medium) variant.variationCode = variationCodes.AVEC_POT_1
          else if (productSize === sizes.large) variant.variationCode = variationCodes.AVEC_POT_2
          else if (productSize === sizes.unique) variant.variationCode = variationCodes.AVEC_POT_1
          break
        case classifications.WITH_VASE:
          if (productSize === sizes.small) variant.variationCode = variationCodes.WITHOUT_VASE
          else if (productSize === sizes.medium) variant.variationCode = variationCodes.WITH_VASE
          else if (productSize === sizes.large) variant.variationCode = variationCodes.WITH_JUG
          break
        case classifications.XMAS_TREE:
          variant.variationCode = variationCodes.XMAS_TREE
          break
        case classifications.XMAS_TREE_DECORATED:
          variant.variationCode = variationCodes.XMAS_TREE_DECORATED
          break
      }

      if (!variant.label) {
        variant.label =
          productClassification === CONST.product.productClassifications.NONE ? sizeLabel : productClassificationLabel
      }
    }

    if (!variant.delivery) {
      // @todo: fetch default shipping method price for the current zone
      //        hard-coded for now to 7€ on demand
      variant.delivery = {
        available: true, // @todo
        express: false, // @todo
        // @nb: price is overriden in resolver
        type: null,
      }
    }

    {
      const stock = new GQLStock()
      stock.status = CONST.product.stockStatuses.unlimited
      variant.stock = stock

      if (this.apiData.variant.availability) {
        let _max = 0
        if (this.apiData.variant.availability.channels) {
          for (const [_id, channel] of Object.entries(this.apiData.variant.availability.channels)) {
            if (channel.isOnStock && channel.availableQuantity > _max) {
              _max = channel.availableQuantity
            }
          }
        } else if (this.apiData.variant.availability.isOnStock && this.apiData.variant.availability.availableQuantity) {
          _max = this.apiData.variant.availability.availableQuantity
        }

        if (_max <= 0) {
          variant.stock.value = 0
          variant.stock.status = CONST.product.stockStatuses.outOfStock
        } else {
          variant.stock.status = CONST.product.stockStatuses.inStock
          variant.stock.value = _max
        }
      }
    }

    if (config.products.qualifiers.zone.enabled) {
      const qualifier = new GQLQualifier()

      let value = this.apiData.variant.sku.split('-').pop()
      if (value !== this.context.appConfig.country && !CONST.product.zones[value]) {
        value = this.context.appConfig.country
      }

      qualifier.orderSlug = 2
      qualifier.slugUrlPart = value
      qualifier.type = {
        name: 'Zone Géographique ?',
        value: CONST.product.qualifierTypes.zoneGeographique,
      }
      qualifier.value = value

      variant.qualifiers.push(qualifier)
    }
    variant.qualifiers.sort((a, b) => a.orderSlug - b.orderSlug)

    return variant
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {string} imageInfo
   */
  handleAssetsOriginalTaste(variant, imageInfo) {
    const mainAssetId = this.options.mainAssetId
    const asset = mainAssetId
      ? this.apiData.variant.assets.find((asset) => asset.id === mainAssetId)
      : this.apiData.variant.assets[0]
    const plpAsset = this.apiData.variant.assets.find((asset) => asset.tags.includes(CONST.commerceTools.assetTag.PLP))

    if (asset) {
      const imageTypes = [CONST.images.types.primary]
      if (!plpAsset) {
        imageTypes.push(CONST.images.types.primaryThumbnail)
      }

      for (const type of imageTypes) {
        variant.images.push(
          CTImageTransformer.toGQL(asset, this.context, {
            description: imageInfo,
            order: 0,
            type,
          })
        )
      }
    }

    if (plpAsset) {
      variant.images.push(
        CTImageTransformer.toGQL(asset, this.context, {
          description: imageInfo,
          order: 0,
        })
      )
    }

    let order = 0
    for (const asset of this.apiData.variant.assets) {
      variant.images.push(
        CTImageTransformer.toGQL(asset, this.context, {
          description: imageInfo,
          order,
          type: CONST.images.types.gallery,
        })
      )
      order++
    }
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {string} imageInfo
   */
  handleAssets(variant, imageInfo) {
    // googleshopping image must not appear on website
    const assets = this.apiData.variant.assets.filter(
      (asset) => !asset.tags.includes(CONST.commerceTools.assetTag.GOOGLE_SHOPPING)
    )

    const primary = assets.find((asset) => asset.tags.includes(CONST.images.types.primary))
    if (primary) {
      variant.images.push(
        CTImageTransformer.toGQL(primary, this.context, {
          description: imageInfo,
          order: 0,
          type: CONST.images.types.primary,
        })
      )
    }

    const primaryThumbnail = findFirst(assets, [
      (asset) => asset.tags.includes(CONST.commerceTools.assetTag.PLP),
      (asset) => asset.tags.includes(CONST.images.types.primaryThumbnail),
    ])

    if (primaryThumbnail) {
      variant.images.push(
        CTImageTransformer.toGQL(primaryThumbnail, this.context, {
          description: imageInfo,
          order: 0,
          type: CONST.images.types.primaryThumbnail,
        })
      )
    }

    const gallery = assets.filter((asset) => asset.tags.includes(CONST.images.types.gallery))
    let order = 0
    for (const asset of gallery) {
      variant.images.push(
        CTImageTransformer.toGQL(asset, this.context, {
          description: imageInfo,
          order,
          type: CONST.images.types.gallery,
        })
      )
      order++
    }
  }

  /**
   * Method used to get first validPrice if CT is not using Price selection: https://docs.commercetools.com/api/projects/products#price-selection
   * @param {CTPrice[]} prices
   * @return {CTPrice}
   */
  findFirstValidPrice(prices) {
    const { timezone } = this.context.appConfig
    const currentDate = addTZOffsetToDate(new Date(), timezone)
    let defaultPrice = null
    let currentPrice = null

    for (const p of prices) {
      let validFrom = null
      let validUntil = null
      if (p.validFrom) {
        validFrom = new Date(p.validFrom).getTime()
      }
      if (p.validUntil) {
        validUntil = new Date(p.validUntil).getTime()
      }
      const currentTime = currentDate.getTime()

      // A price without valid from and valid until is always used as default by CT unless...
      if (!validFrom && !validUntil) {
        if (!defaultPrice) {
          defaultPrice = p
        }
      } else if (
        // ... there is a specific one with at least a valid from or a valid until
        (validFrom && validUntil && validFrom <= currentTime && validUntil >= currentTime) ||
        (validFrom && !validUntil && validFrom <= currentTime) ||
        (!validFrom && validUntil && validUntil >= currentTime)
      ) {
        currentPrice = p
        return currentPrice
      }
    }

    if (!currentPrice && defaultPrice) {
      return defaultPrice
    }

    // If none is found, we return the first price we find (as we used to do)
    return prices[0]
  }

  /**
   * @param {{
   *  product: CTProduct,
   *  variant: CTProductVariant
   * }} apiData
   * @param {GraphQLContext} context
   * @param {ProductVariantOptions} options
   * @returns {GQLProductVariant}
   */
  static toGQL(apiData, context, options) {
    const transformer = new ProductVariantTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = ProductVariantTransformer
