const apm = require('elastic-apm-node')

const config = require('../../../config')

const CONST = require('../../../const')

const { GQLCart, GQLRewardPayment, GQLPrice } = require('../../models')

const Transformer = require('../Transformer')
const CentPrecisitionMoneyTransformer = require('./CentPrecisionMoney')
const DeliveryTransformer = require('./Delivery')
const PriceTransformer = require('./Price')

const { variantIds, computeIsFromWorkshop, variantAttribute } = require('../../../helpers/commercetools')

/**
 * @typedef {{
 *  accessoryProductRelations?: Object<string, { code: string, isCardMessage: boolean }>,
 *  distributionChannels?: CTChannel[],
 *  itfplusDiscountId: string | undefined,
 * }} CartTransformerOptions
 */

/**
 * @extends {Transformer<CTCart, GQLCart, CartTransformerOptions>}
 */
class CartTransformer extends Transformer {
  transformToGQL() {
    const cart = new GQLCart()
    cart.id = this.apiData.id
    cart.number = this.apiData.id

    cart.delivery = DeliveryTransformer.toGQL(this.apiData, this.context, {})

    let productDiscountCents = 0
    let productSubtotalCents = 0
    let productSubtotalDiscountCents = 0
    let subtotalDiscountOnProduct = 0
    const currencyCode = this.context.appConfig.currency

    /**
     * @param {CTLineItem} lineItem
     * @param {GQLPrice} price
     */
    const totalFromLine = (lineItem, price) => {
      const includedInSubtotal = lineItem.productKey !== config.products.interfloraplus.key
      if (includedInSubtotal) {
        productSubtotalCents += lineItem.quantity * price.value
      }

      let discountValue = (lineItem.discountedPricePerQuantity || []).reduce(
        (sum, discount) =>
          sum +
          lineItem.quantity *
            discount.discountedPrice.includedDiscounts
              .filter(({ discount }) => discount.typeId === 'cart-discount')
              .reduce((sum, { discountedAmount }) => sum + discountedAmount.centAmount, 0),
        0
      )

      // add product discount
      if (lineItem.price?.discounted) {
        const discount = lineItem.price.value.centAmount - lineItem.price.discounted.value.centAmount
        subtotalDiscountOnProduct += includedInSubtotal ? discount : 0
      }

      if (lineItem.taxRate && !lineItem.taxRate.includedInPrice)
        discountValue = Math.round(discountValue * (1 + lineItem.taxRate.amount))

      productDiscountCents += discountValue
      if (includedInSubtotal) {
        productSubtotalDiscountCents += discountValue
      }
    }

    // @todo delivery
    const countryId =
      cart.delivery?.address?.country?.id || this.apiData.shippingAddress?.country || this.context.countryId

    // TODO: compute it from the delivery type of the product
    const isFromWorkshop = false

    for (const lineItem of this.apiData.customLineItems || []) {
      if (
        lineItem.custom?.fields?.rewardType &&
        Object.values(CONST.reward.types).includes(lineItem.custom.fields.rewardType)
      ) {
        const rewardPayment = new GQLRewardPayment()
        rewardPayment.id = lineItem.custom.fields.id ?? ''
        rewardPayment.name = lineItem.custom.fields.label ?? ''
        rewardPayment.rewardType = lineItem.custom.fields.rewardType

        const price = new GQLPrice()
        price.value = lineItem.totalPrice.centAmount
        price.currencyIso = lineItem.totalPrice.currencyCode
        price.tiers = []
        rewardPayment.amount = price

        cart.rewardPayments.push(rewardPayment)
        continue
      }

      const price = PriceTransformer.toGQL({ value: lineItem.money })
      totalFromLine(lineItem, price)

      const { productId } = variantIds(lineItem.slug)

      const item = {
        isFromWorkshop,
        price,
        quantity: lineItem.quantity,
        removable: true, // @todo
        total: price.multiply(lineItem.quantity),
        variant: {
          code: lineItem.slug,
          countryId,
          hasCustomText: false, // @todo
          isFromWorkshop,
          parent: {
            code: productId,
            countryId,
          },
          sku: lineItem.sku,
        },
      }
      cart.productItems.push(item)
    }

    const areAllDiscountsAppliedToAllLineItems = this.areAllDiscountsAppliedToAllLineItems()
    for (const lineItem of this.apiData.lineItems) {
      let price = PriceTransformer.toGQL(lineItem.price, this.context)
      if (lineItem.taxRate && !lineItem.taxRate.includedInPrice) price = price.multiply(1 + lineItem.taxRate.amount)

      totalFromLine(lineItem, price)

      let code = `${lineItem.productId}#${lineItem.variant.id}`
      let seaKey

      if (lineItem.custom?.fields.seaChannel?.id) {
        const priceChannelId = lineItem.custom.fields.seaChannel.id
        const priceChannel = (this.options.distributionChannels ?? []).find((channel) => channel.id === priceChannelId)
        if (priceChannel) {
          if (priceChannel.key !== config.commerceTools.priceChannelKey) {
            for (const [key, conf] of Object.entries(config.sea)) {
              if (conf.priceChannelKey === priceChannel.key) {
                seaKey = key
                break
              }
            }

            if (seaKey) {
              code = [lineItem.productId, seaKey, lineItem.variant.id].join('#')
            } else {
              const e = new Error(`[non-blocking] no sea conf found for key ${priceChannel.key}`)
              apm.captureError(e, { custom: e })
            }
          }
        } else {
          const e = new Error(`[non-blocking] no price channel found for id ${priceChannelId}`)
          apm.captureError(e, { custom: e })
        }
      }

      /** @type GQLProductItemCustomizationType[] */
      let customizations = []
      try {
        const rawCustomization = JSON.parse(lineItem.custom?.fields?.customizations ?? '[]')
        if (Array.isArray(rawCustomization)) {
          customizations = rawCustomization
            .map(({ name, value }) => ({
              name,
              value,
            }))
            .filter((item) => Boolean(item.name))
        }
      } catch (e) {
        customizations = []
      }

      const hasCustomImage =
        variantAttribute(lineItem.variant, 'product_type')?.key === CONST.product.productType.DESIGN_CARD_UPLOADED
      const item = {
        customText: lineItem.custom?.fields?.ribbonText,
        customizations,
        imageUrl: hasCustomImage ? lineItem.custom?.fields?.imageUrl ?? null : null,
        isFromWorkshop: computeIsFromWorkshop(lineItem.variant, this.context),
        price,
        quantity: lineItem.quantity,
        removable: true, // @todo
        total: price.multiply(lineItem.quantity),
        variant: {
          code,
          countryId,
          hasCustomText: false, // @todo
          isFromWorkshop,
          parent: {
            code: lineItem.productId,
            countryId,
            seaKey,
          },
          price,
          priceChannelId: lineItem.price?.channel?.id,
          sku: lineItem.variant.sku,
        },
      }

      const cartDiscount = lineItem.discountedPricePerQuantity?.find(
        (discount) => discount.quantity === lineItem.quantity
      )

      if (cartDiscount) {
        const ctPrice = cartDiscount.discountedPrice.value
        if (lineItem.taxRate && !lineItem.taxRate.includedInPrice)
          ctPrice.centAmount = Math.round(ctPrice.centAmount * (1 + lineItem.taxRate.amount))
        const discount = CentPrecisitionMoneyTransformer.toGQL(ctPrice, this.context)
        /* If all discounts are applied to all line items we don't return a discount for each item because it's considered a cart discount.
         * Context of usage: in CT a discount on the cart total also put a discount on the shipping fees.
         * If we only  total price of the items to be discounted we apply the discount on all the cart items instead of the cart.
         * But we still want to display it as a cart discount front side => we don't send a discount in the variant object.
         */
        if (discount?.value !== item.variant.price.value && !areAllDiscountsAppliedToAllLineItems)
          item.variant.discount = discount
      }

      const isAccessoryFor = lineItem.custom?.fields?.isAccessoryFor
      const isService = lineItem.productKey === config.products.interfloraplus.key

      if (isAccessoryFor) {
        item.variant.parent.isAccessory = true
        item.isFor = isAccessoryFor

        const accessoryProductRelations = this.options.accessoryProductRelations
          ? this.options.accessoryProductRelations[lineItem.id]
          : null
        item.isCardMessage = accessoryProductRelations?.isCardMessage ?? null

        // add the custom image to the accessory
        if (hasCustomImage && item.imageUrl) {
          item.accessoryTypes = [CONST.product.accessoryTypes.messageCard, CONST.product.accessoryTypes.customImage]
        } else if (item.isCardMessage) {
          item.accessoryItems = [CONST.product.accessoryTypes.messageCard]
        } else {
          item.accessoryItems = []
        }

        // add the personalized message data
        if (lineItem.custom.fields.messageContent || lineItem.custom.fields.messageSignature) {
          item.joinMessage = {
            message: lineItem.custom?.fields?.messageContent || null,
            signature: lineItem.custom?.fields?.messageSignature || null,
          }
        }

        cart.accessoryItems.push(item)
      } else if (isService) {
        cart.serviceItems.push(item)
      } else {
        cart.productItems.push(item)
      }
    }

    // @todo externalReference

    {
      const fields = this.apiData.custom?.fields || {}

      if (fields.message || fields.signature) {
        cart.joinMessage = {
          message: fields.message,
          signature: fields.signature,
        }
      }
      cart.hasDeliveryMethodSelected = !!fields.hasDeliveryMethodSelected
    }

    {
      // total price in the commerce tools way: final price to pay; includes shipment and discounts
      const totalPrice = CentPrecisitionMoneyTransformer.toGQL(this.apiData.taxedPrice?.totalGross, this.context)

      let totalDiscountCents = productDiscountCents

      if (this.apiData.shippingInfo?.discountedPrice) {
        let discountValue = this.apiData.shippingInfo.discountedPrice.includedDiscounts
          .filter(({ discount }) => this.options.itfplusDiscountId !== discount.id)
          .reduce((sum, discount) => sum + discount.discountedAmount.centAmount, 0)

        if (this.apiData.shippingInfo.taxRate && !this.apiData.shippingInfo.taxRate.includedInPrice)
          discountValue = Math.round(discountValue * (1 + this.apiData.shippingInfo.taxRate.amount))

        totalDiscountCents += discountValue
      }

      if (this.apiData.discountOnTotalPrice) {
        totalDiscountCents += this.apiData.discountOnTotalPrice.discountedGrossAmount.centAmount
      }

      if (totalDiscountCents > 0) {
        cart.discountedTotal = totalPrice
      }

      cart.discountCodesPrice = CentPrecisitionMoneyTransformer.toGQL(
        { centAmount: totalDiscountCents, currencyCode },
        this.context
      )

      cart.subTotal = CentPrecisitionMoneyTransformer.toGQL(
        { centAmount: productSubtotalCents, currencyCode },
        this.context
      )
      if (productDiscountCents + subtotalDiscountOnProduct > 0) {
        cart.discountedSubTotal = CentPrecisitionMoneyTransformer.toGQL(
          { centAmount: productSubtotalCents - productSubtotalDiscountCents - subtotalDiscountOnProduct, currencyCode },
          this.context
        )
      }

      cart.total = CentPrecisitionMoneyTransformer.toGQL(
        { centAmount: totalPrice.value + totalDiscountCents, currencyCode },
        this.context
      )
    }

    return cart
  }

  /**
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @param {CartTransformerOptions} options
   * @returns {GQLCart}
   */
  static toGQL(apiCart, context, options) {
    const transformer = new CartTransformer(apiCart, context, options)
    return transformer.transformToGQL()
  }

  /**
   * Computes if all the discounts applied to the CT cart are applied to all line items.
   * Can only be true for the french website.
   * @returns {boolean}
   */
  areAllDiscountsAppliedToAllLineItems() {
    if (config.site !== CONST.site.fr) return false
    // ITFPLUS and the tomb delivery are services and not product, we exclude them from this logic.
    const items = this.apiData.lineItems.filter(
      (lineItem) =>
        lineItem.productKey !== config.products.interfloraplus.key &&
        lineItem.productKey !== config.products.tombDelivery.key
    )
    const itemsCount = items.length
    const sharedLineItemsDiscountIds = {}
    for (const lineItem of items) {
      if (!lineItem.discountedPrice?.includedDiscounts) {
        // No discount for an item ? No need to compare.
        return false
      }
      for (const includedDiscount of lineItem.discountedPrice?.includedDiscounts ?? []) {
        sharedLineItemsDiscountIds[includedDiscount.discount?.id] =
          (sharedLineItemsDiscountIds[includedDiscount.discount?.id] ?? 0) + 1
      }
    }
    return Object.values(sharedLineItemsDiscountIds).every((x) => x === itemsCount)
  }
}

module.exports = CartTransformer
