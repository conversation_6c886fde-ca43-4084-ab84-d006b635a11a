const { GQLPrice } = require('../../models')
const Transformer = require('../Transformer')

const { getCountryInfo } = require('../../../helpers/countries')

/** @typedef {import('./_typedef')} */

/**
 * @extends {Transformer<CTHighPrecisionMoney, GQLPrice, CTPriceTransformerOptions>}
 */
class HighPrecisionMoneyTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData) return null

    const price = new GQLPrice()

    let amount = this.apiData.preciseAmount

    // apply taxes
    {
      // postalCode & state are not given currently
      let state = this.options.state

      if (this.options.postalCode) {
        const countryInfo = getCountryInfo(this.context.countryId)
        const province = countryInfo.postalCode2Province(this.options.postalCode)
        if (province?.state) state = province.state.key
      }

      const rates =
        this.options.taxCategory?.rates.filter(
          (rate) => rate.country === this.context.countryId && (!rate.state || rate.state === state)
        ) || []

      let rate
      if (rates.length === 1) {
        rate = rates[0]
      } else {
        rate = rates.find((r) => r.state === state)
      }

      if (rate && !rate.includedInPrice) amount *= 1 + rate.amount
    }

    price.currencyIso = this.apiData.currencyCode
    // we transform the price in cents, thats why we remove 2 in the amount fraction digits
    price.value = Math.max(amount / Math.pow(10, this.apiData.fractionDigits - 2), 0)

    return price
  }

  /**
   *
   * @param {CTCentPrecisionMoney} apiData
   * @param {GraphQLContext} context
   * @param {CTPriceTransformerOptions} options
   * @returns {GQLPrice}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new HighPrecisionMoneyTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = HighPrecisionMoneyTransformer
