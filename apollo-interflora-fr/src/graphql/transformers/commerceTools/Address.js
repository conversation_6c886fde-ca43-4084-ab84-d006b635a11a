const config = require('../../../config')
const CONST = require('../../../const')

const { GQLAddress } = require('../../models')

const Transformer = require('../Transformer')

const CountryTransformer = require('./Country')
const PhoneTransformer = require('../Phone')
const TownTransformer = require('./Town')

const { getCountryInfo } = require('../../../helpers/countries')
const { GraphQLApolloError } = require('../../errors')

const strip = (str) => {
  if (!str) return
  if (!config.address.strippedChars) return str
  return str.replace(config.address.strippedChars, '')
}

/**
 * @extends {Transformer<CTAddress, GQLAddress, { fiscalCode?: string, type: GQLAddressTypeEnum }>}
 */
class AddressTransformer extends Transformer {
  transformToGQL() {
    const address = new GQLAddress()

    address.id = this.apiData.key
    address.email = this.apiData.email
    address.address = this.apiData.streetName
    address.address2 = this.apiData.additionalStreetInfo
    address.additionalInformation = this.apiData.additionalAddressInfo
    address.building = this.apiData.building

    const salutations = Object.values(CONST.user.civility)
    if (salutations.includes(`${this.apiData.salutation}`.toUpperCase())) {
      address.civility = this.apiData.salutation.toLocaleUpperCase()
    } else if (this.apiData.salutation === 'mme') {
      address.civility = CONST.user.civility.mme
    } else if (this.apiData.salutation === 'mr') {
      address.civility = CONST.user.civility.mr
    }
    address.company = this.apiData.company
    address.companyNumber = this.apiData.custom?.fields?.companyNumber
    address.country = this.apiData.country ? CountryTransformer.toGQL(this.apiData.country, this.context) : undefined
    address.entityType = this.apiData.custom?.fields?.type
    if (address.entityType === 'COMPANY') address.entityType = CONST.user.userType.b2b
    address.firstName = this.apiData.firstName
    address.fiscalCode = this.apiData.custom?.fields?.fiscalCode
    address.lastName = this.apiData.lastName
    address.pec = this.apiData.custom?.fields?.pec
    address.postalCode = this.apiData.postalCode
    address.province = this.apiData.region

    if (this.apiData.city) {
      address.town = TownTransformer.toGQL(
        { city: this.apiData.city, postalCode: this.apiData.postalCode, province: this.apiData.region },
        this.context
      )
    }

    address.phone = this.apiData.mobile
      ? PhoneTransformer.toGQL(this.apiData.mobile, {
          countryCode: this.apiData.country || address.country?.isocode || this.context.countryId,
        })
      : undefined
    address.phone2 = this.apiData.phone
      ? PhoneTransformer.toGQL(this.apiData.phone, {
          countryCode: this.apiData.country || address.country?.isocode || this.context.countryId,
        })
      : undefined
    address.stored = false // @todo stored
    address.type = this.options.type || CONST.address.addressType.delivery
    address.vatNumber = this.apiData.custom?.fields?.vatNumber

    address.invoiceEmail = this.apiData.custom?.fields?.invoiceEmail
    address.invoiceRequest = this.options.invoiceRequest
    address.invoiceLastName = this.apiData.custom?.fields?.invoiceLastName
    address.invoiceFirstName = this.apiData.custom?.fields?.invoiceFirstName
    address.optinEmail = this.options.optinEmail
    address.optinSMS = this.options.optinSMS
    address.comments = this.apiData.custom?.fields?.comments

    // @todo twitterId
    // @todo isDefault

    return address
  }

  /**
   * @param {GQLInputAddress} address
   * @param {GraphQLContext} context
   * @param {{
   *  delivery: GQLInputCartDelivery
   * }} options
   * @returns {CTAddress}
   */
  static toCommerceTools(address, context, options) {
    const addressType = address === options?.delivery?.sender ? 'billing' : 'shipping'

    let entityType = CONST.address.entityType.individual
    if (address.entityType === CONST.user.userType.b2b) {
      entityType = 'COMPANY'
    } else if (
      Object.values(CONST.address.entityType).includes(address.entityType) &&
      address.entityType !== CONST.user.userType.customerCare
    ) {
      entityType = address.entityType
    }

    // override front-end values for b2b users - currently, user.type is set on CTFR only
    if (context.session?.user?.type === CONST.user.userType.b2b) {
      entityType = CONST.address.entityType.company
    }

    const country = address.country || address.countryId || context.countryId
    const countryInfo = getCountryInfo(country)

    let region
    let state
    if (address.postalCode) {
      const province = countryInfo.postalCode2Province(address.postalCode)
      region = province?.name
      state = province?.state?.key
    }
    if (!state && address.province) {
      const province = countryInfo.searchProvince(address.province)
      region = province?.name
      state = province?.state?.key
    }
    if (!region && address.province) region = address.province

    /** @type {CTAddress} */
    const payload = {
      additionalAddressInfo: address.additionalInformation,
      additionalStreetInfo: strip(address.address2),
      building: address.building,
      city: [CONST.site.se, CONST.site.fr].includes(config.site)
        ? address.townLabel?.replace(`(${address.postalCode})`, '').trim()
        : address.townLabel, // No postal code in parenthesis in CTFR
      company: address.company,
      country,
      custom: {
        fields: {
          comments: address.comments || undefined,
          companyNumber: address.companyNumber || undefined, // custom fields are not nullable
          fiscalCode: address.fiscalCode || undefined,
          invoiceEmail: address.invoiceEmail || undefined,
          invoiceFirstName: address.invoiceFirstName || undefined,
          invoiceLastName: address.invoiceLastName || undefined,
          pec: address.pec || undefined,
          type: entityType,
          vatNumber: address.vatNumber || undefined,
        },
        type: { key: 'address-custom' },
      },
      email: address.email,
      firstName: address.firstName,
      key: address.id,
      lastName: address.lastName,
      mobile: address.phone ? PhoneTransformer.toAPI(address.phone, context) : undefined,
      phone: address.phone2 ? PhoneTransformer.toAPI(address.phone2, context) : undefined,
      postalCode: address.postalCode,
      region,
      salutation: address.civility,
      state,
      streetName: strip(address.address),
    }

    if (addressType === 'shipping') {
      Object.assign(payload.custom.fields, {
        contactFirstName: options?.delivery?.contact?.firstName,
        contactLastName: options?.delivery?.contact?.lastName,
        contactPhone: options?.delivery?.contact?.phone
          ? PhoneTransformer.toAPI(options?.delivery?.contact?.phone, context)
          : undefined,
        contactTitle: options?.delivery?.contact?.civility || undefined,
        date: options?.delivery?.date ? options.delivery.date.toISOString().substring(0, 10) : undefined,
        moment: options?.delivery?.rangeHourId || undefined,
        time: options?.delivery?.time
          ? `${`${options.delivery.time.hour}`.padStart(2, '0')}:${`${options.delivery.time.minute || 0}`.padStart(
              2,
              '0'
            )}:00.000`
          : undefined,
      })
    }

    if (addressType === 'billing') {
      Object.assign(payload.custom.fields, {
        optinEmail: options?.delivery?.sender?.optinEmail,
        optinSMS: options?.delivery?.sender?.optinSMS,
      })
    }

    for (const field of config.address.mandatoryFields[addressType]) {
      if (!address[field]) continue
      if (address[field].trim() !== '' && strip(address[field]).trim() === '') {
        const error = new GraphQLApolloError(`missing mandatory field ${field}`)
        const fieldKey = addressType === 'billing' ? `sender.${field}` : field
        error.addError(`MandatoryFieldMissing`, fieldKey, `* required`)
        throw error
      }
    }

    for (const [key, val] of Object.entries(payload)) {
      if (val === undefined) delete payload[key]
    }

    return payload
  }

  /**
   * @param {CTAddress} apiData
   * @param {GraphQLContext} context
   * @param {{ fiscalCode: string, type: GQLAddressTypeEnum, invoiceRequest: boolean }} options
   * @returns {GQLAddress}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new AddressTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = AddressTransformer
