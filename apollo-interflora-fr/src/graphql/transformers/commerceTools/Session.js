/** @typedef {import('../__typedef')} */

const DAYS = 24 * 3600

class CtSessionTransformer {
  /**
   * @param {{
   *  access_token: number,
   *  expires_in: number,
   *  refresh_token: string,
   *  scope: string,
   * }} response
   * @returns {SessionApiData<{ anonymousId: string }>}
   */
  static fromResponse(response) {
    const { access_token, expires_in, refresh_token, scope } = response
    const now = Date.now()
    let anonymousId = scope.replace(/^(?:.*? )?anonymous_id:([^ ]+)(?: .*)?$/, '$1')
    if (anonymousId === scope) anonymousId = undefined

    return {
      accessToken: {
        expiresAt: now + expires_in * 1000,
        lifetime: expires_in,
        value: access_token,
      },
      anonymousId,
      createdAt: now,
      refreshToken: {
        expiresAt: now + 7 * DAYS * 1000,
        lifetime: 7 * DAYS,
        value: refresh_token,
      },
      scope,
      userId: null,
      username: null,
    }
  }
}

module.exports = CtSessionTransformer
