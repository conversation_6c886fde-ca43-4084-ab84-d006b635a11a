const { randomUUID } = require('crypto')

const CONST = require('../../../const')
const config = require('../../../config')
const { createJwt } = require('../../../helpers')
const GQLToken = require('../../models/Token')

const Transformer = require('../Transformer')

/** @typedef {import('../_typedef')} */
/** @typedef {import('../../Authentication')} AuthenticationService */

/**
 * @extends {Transformer<CTToken, GQLToken, {}>}
 */
class TokenTransformer extends Transformer {
  /**
   * @returns {GQLToken}
   */
  transformToGQL() {
    const token = new GQLToken()

    const payload = {
      ...this.apiData,
      access_token: randomUUID(),
      refresh_token: randomUUID(),
    }

    const sessionId = this.context.auth.sessionId ?? this.context.session?.id

    const { token: accessToken, expiresAt: accessExpiresAt } = createJwt(
      payload,
      CONST.user.tokenType.access,
      sessionId,
      this.context.siteId
    )

    token.value = accessToken
    token.lifetime = accessExpiresAt

    const { token: refreshToken, expiresAt: refreshExpiresAt } = createJwt(
      payload,
      CONST.user.tokenType.refresh,
      sessionId,
      this.context.siteId
    )

    token.refreshToken = refreshToken
    token.refreshLifetime = refreshExpiresAt

    token.state = CONST.user.userRegistrationStates.OK

    token.createdAt = String(Date.now() + this.apiData.expires_in * 1000 - config.interflora.accessTokenLifeTime)

    return token
  }

  /**
   * @param {APIToken} apiData
   * @param {GraphQLContext} context
   * @param {{user: APIUser}} options
   * @returns {GQLToken}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new TokenTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = TokenTransformer
