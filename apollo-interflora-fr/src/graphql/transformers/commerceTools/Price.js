const Transformer = require('../Transformer')
const CentPrecisitionMoneyTransformer = require('./CentPrecisionMoney')
const HighPrecisionMoneyTransformer = require('./HighPrecisionMoney')
const CTTierTransformer = require('./PriceTier')

/** @typedef {import('./_typedef')} */

/**
 * @extends {Transformer<CTPrice, GQLPrice, CTPriceTransformerOptions>}
 */
class CTPriceTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData || !this.apiData.value) return null

    if (this.apiData.value.type === 'centPrecision') {
      const price = CentPrecisitionMoneyTransformer.toGQL(this.apiData.value, this.context, this.options)

      price.tiers = (this.apiData.tiers || []).map((tier) => CTTierTransformer.toGQL(tier, this.context, this.options))

      return price
    } else if (this.apiData.value.type === 'highPrecision') {
      const price = HighPrecisionMoneyTransformer.toGQL(this.apiData.value, this.context, this.options)

      price.tiers = (this.apiData.tiers || []).map((tier) => CTTierTransformer.toGQL(tier, this.context, this.options))

      return price
    } else {
      throw new Error('not implemented')
    }
  }

  /**
   * @param {GQLPrice} price
   * @returns {CTPrice}
   */
  static toCT(price) {
    return {
      centAmount: price.value,
      currencyCode: price.currencyIso,
      fractionDigits: 2,
      type: 'centPrecision',
    }
  }

  /**
   * @param {CTPrice} apiData
   * @param {GraphQLContext} context
   * @param {CTPriceTransformerOptions} options
   * @returns {GQLPrice}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new CTPriceTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CTPriceTransformer
