const config = require('../../../config')
const i18nCountries = require('i18n-iso-countries')
const { GQLCountry } = require('../../models')

const Transformer = require('../Transformer')

/**
 * @extends {Transformer<string, GQLCountry, {}>}
 */
class CountryTransformer extends Transformer {
  transformToGQL() {
    const country = new GQLCountry()

    country.id = this.apiData.toUpperCase()
    country.label = i18nCountries.getName(this.apiData, this.context.language || 'en')
    country.default = country.id === config.apps[this.context.siteId].country
    country.highlighted = false // @todo
    country.knownPostalCode = this.context.isDefaultCountry()
    // @todo highlighted
    // @todo knownPostalCode

    return country
  }

  /**
   * @param {string} id
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLCountry}
   */
  static toGQL(id, context, options = {}) {
    const transformer = new CountryTransformer(id, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CountryTransformer
