const config = require('../../../config')
const CONST = require('../../../const')

const { G<PERSON>User, GQLOrderList } = require('../../models')

const Transformer = require('../Transformer')

/** @typedef {import('../../datasources/commerceTools')} */

/**
 * @typedef {{}} UserTransformerOptions
 */

/**
 * @extends {Transformer<CTCustomer,GQLUser, UserTransformerOptions>}
 */
class UserTransformer extends Transformer {
  /**
   * @returns {GQLUser}
   */
  transformToGQL() {
    const user = new GQLUser()

    // is currently a very basic information for customer care
    // @todo: implement all fields

    user.email = this.apiData.email
    user.firstName = this.apiData.firstName
    user.id = this.apiData.id
    user.lastName = this.apiData.lastName
    user.username = this.apiData.email

    user.type = CONST.user.userType.b2c
    if (this.apiData.customerGroup.obj.key === config.commerceTools.customerCare.customerGroupKey) {
      user.type = CONST.user.userType.customerCare
    }

    user.orders = new GQLOrderList()

    return user
  }

  /**
   * @param {CTCustomer} apiData
   * @param {GraphQLContext} context
   * @param {UserTransformerOptions} options
   * @returns {GQLUser}
   */
  static toGQL(apiData, context, options = {}) {
    const transformer = new UserTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = UserTransformer
