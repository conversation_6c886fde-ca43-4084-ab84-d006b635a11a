const CTProductTransformer = require('./Product')
const Transformer = require('../Transformer')

/**
 * @typedef {{
 *  filters: GQLInputProductFilter[],
 *  linkedProducts: Object<string, CTProduct[]>
 * }} CTProductListTransformerOptions
 */

/**
 * @extends {Transformer<CTProduct[], GQLProductList, CTProductListTransformerOptions>}
 */
class CTProductListTransformer extends Transformer {
  transformToGQL() {
    return (this.apiData || []).map((ctProduct) =>
      CTProductTransformer.toGQL(ctProduct, this.context, {
        filters: this.options?.filters,
        linkedProducts: this.options?.linkedProducts?.[ctProduct.id],
      })
    )
  }

  /**
   * @param {CTProduct[]} apiData
   * @param {CTDatocmsContext} context
   * @param {CTProductListTransformerOptions | undefined } options
   * @returns {GQLProduct[]}
   */
  static toGQL(apiData, context, options) {
    const transformer = new CTProductListTransformer(apiData, context, options)

    return transformer.transformToGQL()
  }
}

module.exports = CTProductListTransformer
