const Transformer = require('../Transformer')
const { GQLUser, GQLTown, GQLPrice } = require('../../models')
const CONST = require('../../../const')
const SEUserPhoneTransformer = require('./Phone')
const countries = require('i18n-iso-countries')
const { isEmpty } = require('lodash')
const config = require('../../../config')

/**
 * @extends {Transformer<OctopusCustomer, GQLUser>}
 */
class SEUserUserTransformer extends Transformer {
  /**
   * @returns {GQLUser}
   */
  transformToGQL() {
    const user = new GQLUser()
    const {
      Address,
      BonusInformation,
      Email,
      FirstName,
      LastName,
      Phone,
      PostalCode,
      Username,
      Country,
      Id,
      City,
      OrganizationId,
      UnitName,
      CO,
      PersonalIdentityNumber,
      hasGlobalDiscountOnProduct,
      hasOrganizationCategory,
      ReceiveOffersEmail,
      ReceiveOffersSms,
    } = this.apiData

    user.civility = null
    user.addressId = null
    user.fiscalCode = null
    user.birthDate = null
    user.pec = null
    user.lastOrder = null
    user.premium = null
    user.vatNumber = null
    user.ssn = PersonalIdentityNumber
    user.username = Username
    user.email = Email
    user.countryId = countries.alpha3ToAlpha2(Country)
    user.id = Id
    user.address = Address
    user.address2 = CO
    user.company = UnitName
    user.companyNumber = OrganizationId
    user.firstName = FirstName !== 'undefined' ? FirstName : null
    user.lastName = LastName !== 'undefined' ? LastName : null
    user.postalCode = PostalCode
    user.optinEmail = ReceiveOffersEmail
    user.optinSMS = ReceiveOffersSms
    user.orders = {
      orders: [],
      total: 0,
    }

    const getPrice = (amount) => {
      const price = new GQLPrice()
      price.currencyIso = config.apps[this.context.siteId].currency
      price.value = Math.round((amount || 0) * 100)
      return price
    }

    const transformBonusItems = (items, rewardType) => {
      if (!items) return []

      // Filter only items with amountType as 'Amount', Percentage is not supported for now
      return items
        .filter((item) => item.amountType.toUpperCase() === CONST.cart.discountType.amount)
        .map((item) => ({
          amount: getPrice(item.amount),
          amountType: item.amountType,
          description: item.description,
          heading: item.heading,
          id: item.id,
          name: item.name,
          rewardType: rewardType,
        }))
    }

    user.bonusInformation = {
      ...BonusInformation,
      bonusItems: transformBonusItems(BonusInformation?.bonusItems, CONST.reward.types.bonus),
      promotions: transformBonusItems(BonusInformation?.promotions, CONST.reward.types.promotion),
    }

    const isUserTypeB2C = isEmpty(OrganizationId.trim())
    if (!isUserTypeB2C) {
      user.type =
        hasGlobalDiscountOnProduct || hasOrganizationCategory
          ? CONST.user.userType.advancedb2b
          : CONST.user.userType.b2b
    } else {
      user.type = CONST.user.userType.b2c
    }

    if (Phone) {
      user.phone = SEUserPhoneTransformer.toGQL(this.apiData, this.context, {
        countryId: user.countryId,
      })
    }

    const townModel = new GQLTown()
    Object.assign(townModel, {
      id: City && PostalCode ? `${City}-${PostalCode}` : '0',
      label: City || '',
      postalCode: PostalCode || '',
    })
    user.town = townModel

    if (City && PostalCode) {
      const townModel = new GQLTown()
      townModel.id = City
      townModel.label = City
      townModel.postalCode = PostalCode
      user.town = townModel
    }

    user.defaultDeliveryAddress = null
    user.defaultSenderAddress = null

    return user
  }

  /**
   * @returns {OctopusRegisterCustomer}
   */
  transformFromGQL() {
    return {
      Email: this.apiData.email,
      FirstName: this.apiData.firstName ?? '',
      LastName: this.apiData.lastName ?? '',
      MobilePhone: `${this.apiData.phone?.prefix ?? ''}${this.apiData.phone?.number ?? ''}`,
      Password: this.apiData.password,
      ReceiveOffersEmail: this.apiData?.optinEmail ?? true,
      ReceiveOffersSms: this.apiData?.optinSMS ?? true,
      Ssn: this.apiData.ssn,
    }
  }

  /**
   * @returns {OctopusRegisterOrganization}
   */
  transformFromGQLForOrganization() {
    return {
      CostCenter: this.apiData.billingCostCenter,
      Email: this.apiData.email,
      InvoiceAddress: this.apiData.billingAddress,
      InvoiceCity: this.apiData.billingCity,
      InvoicePostalCode: this.apiData.billingPostalCode,
      MobilePhone: `${this.apiData.phone?.prefix}${this.apiData.phone?.number}`,
      OrganizationName: this.apiData.company,
      OrganizationNumber: this.apiData.organizationNumber,
      Password: this.apiData.password,
      ReceiveOffersEmail: this.apiData?.optinEmail ?? false,
      ReceiveOffersSms: this.apiData?.optinSMS ?? false,
      SummaryInvoice: this.apiData.billingSummary,
      UserFirstName: this.apiData.firstName,
      UserLastName: this.apiData.lastName,
    }
  }

  /**
   * @returns {OctopusRegisterCustomer}
   */
  transformFromGQLForUpdate() {
    return {
      Address: this.apiData.address,
      CO: this.apiData.address2 === null ? '' : this.apiData.address2,
      City: this.apiData.townLabel,
      Email: this.apiData.email,
      FirstName: this.apiData.firstName !== 'undefined' ? this.apiData.firstName : null,
      LastName: this.apiData.lastName !== 'undefined' ? this.apiData.lastName : null,
      PersonalIdentityNumber: this.apiData.ssn,
      Phone: `${this.apiData.phone?.prefix}${this.apiData.phone?.number}`,
      PostalCode: this.apiData.postalCode,
      ReceiveOffersEmail: this.apiData?.optinEmail ?? true,
      ReceiveOffersSms: this.apiData?.optinSMS ?? true,
    }
  }

  static toGQL(apiData, context) {
    const transformer = new SEUserUserTransformer(apiData, context)
    return transformer.transformToGQL()
  }

  /**
   * @param {{}} apiData
   * @param {GraphQLContext} context
   * @returns {OctopusRegisterCustomer}
   */
  static fromGQLForCustomer(apiData, context) {
    const transformer = new SEUserUserTransformer(apiData, context)
    return transformer.transformFromGQL()
  }

  /**
   * @param {{}} apiData
   * @param {GraphQLContext} context
   * @returns {OctopusRegisterCustomer}
   */
  static fromGQLForUpdate(apiData, context) {
    const transformer = new SEUserUserTransformer(apiData, context)
    return transformer.transformFromGQLForUpdate()
  }

  /**
   * @param {{}} apiData
   * @param {GraphQLContext} context
   * @returns {OctopusRegisterOrganization}
   */
  static fromGQLForOrganization(apiData, context) {
    const transformer = new SEUserUserTransformer(apiData, context)
    return transformer.transformFromGQLForOrganization()
  }
}

module.exports = SEUserUserTransformer
