const config = require('../../../config')

const { GQLOrder, GQLPrice } = require('../../models')
const Transformer = require('../Transformer')
const SVUserDeliveryTransformer = require('./Delivery')
const { addTZOffsetToDate } = require('../../../helpers/dates')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<string, GQLOrder>}
 */
class SVUserOrderTransformer extends Transformer {
  transformToGQL() {
    const order = new GQLOrder()
    const { OrderNumber, OrderDate, DeliveryDate, Total, Status } = this.apiData
    order.availableEdit = false
    order.createdAt = addTZOffsetToDate(new Date(OrderDate), this.context.appConfig.timezone)
    if (DeliveryDate) {
      order.delivery = SVUserDeliveryTransformer.toGQL({ deliveryDate: DeliveryDate, status: Status }, this.context)
    }
    order.id = OrderNumber
    order.number = OrderNumber

    const price = new GQLPrice()
    price.currencyIso = config.apps[this.context.siteId].currency
    price.value = Math.round((Total || 0) * 100)
    order.total = price

    return order
  }

  /**
   * @param {object} order
   * @param {GraphQLContext} context
   * @returns {GQLOrder}
   */
  static toGQL(order, context) {
    const transformer = new SVUserOrderTransformer(order, context)
    return transformer.transformToGQL()
  }
}

module.exports = SVUserOrderTransformer
