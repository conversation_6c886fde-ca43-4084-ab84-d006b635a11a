const { PhoneNumberUtil } = require('google-libphonenumber')

const Transformer = require('../Transformer')
const PhoneTransformer = require('../Phone')

const phoneUtil = PhoneNumberUtil.getInstance()

class SVUserPhoneTransformer extends Transformer {
  transformToGQL() {
    if (!this.apiData?.Phone) {
      return null
    }
    let countryCode

    if (!this.apiData.prefix) {
      try {
        const countryIds = [this.context.countryId]
        if (this.options.countryId) countryIds.unshift(this.options.countryId)

        for (const id of countryIds) {
          if (phoneUtil.isPossibleNumberString(this.apiData.Phone, id)) {
            countryCode = id
            break
          }
        }
      } catch (e) {
        // do nothing
      }
    }

    return PhoneTransformer.toGQL(`${this.apiData.prefix ?? ''}${this.apiData.Phone}`, this.context, {
      countryCode: countryCode ?? this.options.countryId,
    })
  }

  static toGQL(apiData, context, options) {
    const transformer = new SVUserPhoneTransformer(apiData, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = SVUserPhoneTransformer
