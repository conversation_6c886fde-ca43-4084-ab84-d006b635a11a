/** @typedef {import('./_typedef')} */

const Transformer = require('../Transformer')
const GQLCustomization = require('../../models/Customization')

/**
 * @extends {Transformer<CadeauxCustomizationFormField, GQLCustomization, BlockTransformerOptions>}
 */
class CustomizationTransformer extends Transformer {
  /**
   * @returns {GQLCustomization}
   */
  transformToGQL() {
    const customization = new GQLCustomization()
    customization.label = this.apiData.label
    customization.secondaryLabel = this.apiData.secondaryLabel
    customization.name = this.apiData.name
    customization.ref = this.apiData.ref
    customization.type = this.apiData.type
    customization.placeholder = this.apiData.placeholder
    customization.required = this.apiData.required
    customization.maxLength = this.apiData.maxLength ? Number.parseInt(this.apiData.maxLength) : null
    customization.choices = this.apiData.choices
    customization.width = this.apiData.width
    customization.height = this.apiData.height
    customization.allowedCharacters = this.apiData.allowedCharacters
    customization.blacklist = this.apiData.blacklist
    customization.mask = this.apiData.mask
    customization.textCase = this.apiData.textCase
    return customization
  }

  /**
   * @param {CadeauxCustomizationFormField} field
   * @param {GraphQLContext} context
   * @param {{}} options
   * @returns {GQLCustomization}
   */
  static toGQL(field, context, options = {}) {
    const transformer = new CustomizationTransformer(field, context, options)
    return transformer.transformToGQL()
  }
}

module.exports = CustomizationTransformer
