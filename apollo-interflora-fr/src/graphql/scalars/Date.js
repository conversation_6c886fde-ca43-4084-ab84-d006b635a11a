const { GraphQLScalarType, Kind } = require('graphql')
const { formatISO, isValid } = require('date-fns')

/** @typedef {import('graphql').ValueNode} ValueNode */

const DateScalar = new GraphQLScalarType({
  description: 'ISO 8601 Date representation',
  name: 'Date',
  /**
   * NOTE: ast value is always in string format
   * @param {ValueNode} ast
   * @returns {Date|null}
   */
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      if (!/^(19|20)\d{2}-(0[0-9]|1[0-2])-\d{2}$/.test(ast.value)) {
        throw new TypeError(`${ast.value} must be in YYYY-MM-DD format`)
      }

      return this.parseValue(ast.value)
    }

    return null
  },
  /**
   * Parse value from the client.
   * @param {string} value String timestamp
   * @returns {Date}
   */
  parseValue(value) {
    // dates are sent as the one for the current time, UTC
    const d = new Date(new Date().toISOString().replace(/^[\d-]{10}/, value))

    if (isNaN(d)) throw new TypeError('Invalid date format.')

    return d
  },
  /**
   * Value sent to the client
   * @param {Date|string} value
   * @returns {string|null}
   */
  serialize(value) {
    /** May be an ISO date string */
    if (typeof value === 'string') {
      // dates are now sent UTC, to avoid tz issues
      const date = new Date(value.replace(/Z?$/, 'Z'))

      if (!isValid(date)) return null

      return formatISO(date, { representation: 'date' })
    }

    if (isValid(value)) {
      return formatISO(value, { representation: 'date' })
    }

    return null
  },
})

module.exports = DateScalar
