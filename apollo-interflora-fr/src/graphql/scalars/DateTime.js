const { GraphQLScalarType, Kind } = require('graphql')

/** @typedef {import('graphql').ValueNode} ValueNode */

const DateTimeScalar = new GraphQLScalarType({
  description: 'UTC Timestamp representation (String timestamp)',
  name: 'DateTime',
  /**
   * NOTE: ast value is always in string format
   * @param {ValueNode} ast
   * @returns {Date|null}
   */
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING) {
      return this.parseValue(ast.value)
    }

    return null
  },
  /**
   * Parse value from the client.
   * @param {string} value String timestamp
   * @returns {Date}
   */
  parseValue(value) {
    // dates are now sent UTC, to avoid tz issues
    const d = new Date(value.replace(/Z?$/, 'Z'))

    if (isNaN(d)) throw new TypeError('Invalid date format.')

    return d
  },
  /**
   * Value sent to the client
   * @param {Date} value
   * @returns {String|null}
   */
  serialize(value) {
    return value instanceof Date ? `${value.getTime()}` : null
  },
})

module.exports = DateTimeScalar
