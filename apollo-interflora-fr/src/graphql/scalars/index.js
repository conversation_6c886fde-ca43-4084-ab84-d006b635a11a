const { gql } = require('graphql-tag')
const { JSONObjectResolver } = require('graphql-scalars')

const DateTimeScalar = require('./DateTime')
const DateScalar = require('./Date')

const typeDefs = gql`
  scalar Date
  scalar DateTime
  scalar JSONObject
`

const resolvers = {
  Date: DateScalar,
  DateTime: DateTimeScalar,
  JSONObject: JSONObjectResolver,
}

module.exports = { resolvers, typeDefs }
