/**
 * @class GQLCustomizationChoice
 */
class GQLCustomizationChoice {
  constructor() {
    /**
     * @type {string}
     * @readonly
     */
    this.__typename = 'Choice'

    /** @type {string} */
    this.value

    /** @type {string} */
    this.label

    /** @type {boolean} */
    this.selected

    /** @type {string} */
    this.dataToggle
  }
}

/* Sample
{
  "value": "1",
  "label": "Un élève",
  "selected": true,
  "dataToggle": "perso_1"
},
*/

module.exports = GQLCustomizationChoice
