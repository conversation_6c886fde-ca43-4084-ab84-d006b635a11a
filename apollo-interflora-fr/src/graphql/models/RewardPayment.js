/** @typedef {import('../_typedef')} */

/**
 * @class GQLRewardPayment
 */

class GQLRewardPayment {
  constructor() {
    /**
     * The id of the reward
     * @type {string}
     */
    this.id = null

    /**
     * The name of the reward
     * @type {string}
     */
    this.name = null

    /**
     * The amount
     * @type {GQLPrice}
     */
    this.amount = null

    /**
     * The type of the reward
     * GIFT_CARD, BONUS
     * @type {("GIFT_CARD"|"BONUS")}
     */
    this.rewardType = null
  }
}

module.exports = GQLRewardPayment
