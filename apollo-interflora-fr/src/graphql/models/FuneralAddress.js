/**
 * @class GQLFuneralAddress - Used only for Swedish funerals
 */

class GQLFuneralAddress {
  constructor() {
    /** @type {string} */
    this.address

    /** @type {string} */
    this.churchName

    /** @type {string} */
    this.city

    /** @type {string} */
    this.funeralDateUtc

    /** @type {string} */
    this.funeralId

    /** @type {string} */
    this.funeralTime

    /** @type {string} */
    this.memberId

    /** @type {string} */
    this.nameOfDeceased

    /** @type {string} */
    this.postalCode
  }
}

module.exports = GQLFuneralAddress
