/** @typedef {import('../_typedef')} */

/**
 * @class GQLProductList
 */
class GQLProductList {
  constructor() {
    /** @type {boolean} */
    this.bloomreachDisabled = false

    /** @type {string} */
    this.categoryId

    /** @type {string} */
    this.categoryDescription

    /** @type {string} */
    this.categoryName

    /** @type {string} */
    this.categorySlug

    /** @type {string} */
    this.categoryTitle

    /** @type {GQLProductFilterType[]} */
    this.filters = []

    /** @type {number} */
    this.pageSize = 0

    /** @type {GQLProduct[]} */
    this.products = []

    /** @type {number} */
    this.total = 0

    /** @type {string} */
    this.redirectUrl
  }

  /**
   * @returns {boolean}
   */
  get bloomreachEnabled() {
    return !this.bloomreachDisabled
  }
}

module.exports = GQLProductList
