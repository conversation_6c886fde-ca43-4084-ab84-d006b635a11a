/**
 * @class GQLCustomization
 */
class GQLCustomization {
  constructor() {
    /**
     * @type {string}
     * @readonly
     */
    this.__typename = 'Customization'

    /** @type {string} */
    this.label

    /** @type {string} */
    this.secondaryLabel

    /** @type {string} */
    this.name

    /** @type {string} */
    this.ref

    /** @type {string} */
    this.type

    /** @type {string} */
    this.placeholder

    /** @type {boolean} */
    this.required

    /** @type {number} */
    this.maxLength

    /** @type {GQLCustomizationChoice[]} */
    this.choices = []

    /** @type {string} */
    this.width

    /** @type {string} */
    this.height

    /** @type {string} */
    this.allowedCharacters

    /** @type {string} */
    this.blacklist

    /** @type {string} */
    this.mask

    /** @type {string} */
    this.textCase

    /* Sample
    "label": "Ligne 2 :",
    "secondaryLabel": "",
    "name": "customization[ligne2]",
    "ref": "ligne2",
    "type": "text",
    "placeholder": "Merci pour tout",
    "required": false,
    "maxLength": "25",
    "choices": [],
    "width": null,
    "height": null,
    "allowedCharacters": null,
    "blacklist": "^((?![^a-zA-Z0-9\\!\\&\\+\\.\\?\\'\\=\\-ÀÁÂÃÄÅàáâãäåÔÕÖôöÈÉÊËèéêëÇçÎÏîïÙÛÜùûü ]).)*$",
    "mask": null,
    "textCase": null
    */
  }
}

module.exports = GQLCustomization
