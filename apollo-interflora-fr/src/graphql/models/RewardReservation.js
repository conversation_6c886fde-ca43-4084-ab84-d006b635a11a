/** @typedef {import('../_typedef')} */

/**
 * @class GQLRewardReservation
 */

class GQLRewardReservation {
  constructor() {
    /**
     * The unique Id
     * @type {string}
     */
    this.reservationId = null

    /**
     * The amount
     * @type {number}
     */
    this.amount = null

    /**
     * The time stamp of when the reservation was made
     * @type {DateTime}
     */
    this.timestamp = null

    /**
     * The time when the reservation expires
     * @type {DateTime}
     */
    this.reservationExpires = null

    /**
     * The status of the reservation
     * activated, redeemed, canceled, expired
     * @type {("activated"|"redeemed"|"canceled"|"expired")}
     */
    this.status = null
  }
}

module.exports = GQLRewardReservation
