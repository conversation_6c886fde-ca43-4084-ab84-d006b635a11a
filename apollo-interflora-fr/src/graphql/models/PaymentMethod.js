const CONST = require('../../const')

/**
 * @class GQLPaymentMethod
 */
class GQLPaymentMethod {
  constructor() {
    /** @type {boolean} */
    this.availableForCart = true
    /** @type {string} */
    this.encrypted = null
    /** @type {GQLPaymentModeEnum} */
    this.mode
    /** @type {GQLPaymentProvider} */
    this.provider
    /** @type {string[]} */
    this.excludedDeliveryModes = []
    /** @type {boolean} if true, mobile app users will be able to pay using this payment method. */
    this.allowedForMobileApp = false
  }

  /**
   * @param {APICart} apiCart
   * @returns {boolean}
   */
  matchesApiCart(apiCart) {
    if (this.mode === CONST.payment.paymentMode.ALMA && apiCart?.totalPrice?.value < 50) return false
    return true
  }
}

module.exports = GQLPaymentMethod
