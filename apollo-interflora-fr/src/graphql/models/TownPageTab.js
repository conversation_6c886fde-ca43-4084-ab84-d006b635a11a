/** @typedef {import('../_typedef')} */

/**
 * @class GQLTownPageTab
 */
class GQLTownPageTab {
  constructor() {
    /** @type {GQLTemplate[]} */
    this.componentsPromo = []

    /** @type {GQLTemplate[]} */
    this.componentsBottom = []

    /** @type {string} */
    this.icon

    /** @type {string} */
    this.label

    /** @type {GQLUrlUnion} */
    this.link

    /** @type {GQLProduct[]} */
    this.products = []
  }
}

module.exports = GQLTownPageTab
