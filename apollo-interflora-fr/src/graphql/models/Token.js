/**
 * @class GQLToken
 */
class GQLToken {
  constructor() {
    /**
     * access token - to be supplied in X-jwt (preferred) or Authorization header.
     * @type {string}
     */
    this.value

    /** @type {string} */
    this.refreshToken

    /**
     * access token expiration (in seconds).
     * @type {number}
     */
    this.lifetime

    /** @type {number} */
    this.refreshLifetime

    /**
     * unix timestamp (in seconds).
     * @type {string}
     */
    this.createdAt

    /** @type {string} */
    this.state

    /** @type {GQLUser} */
    this.user
  }
}

module.exports = GQLToken
