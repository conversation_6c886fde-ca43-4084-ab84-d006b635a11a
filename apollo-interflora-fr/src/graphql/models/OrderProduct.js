const CONST = require('../../const')

const GQLProduct = require('./Product')

class GQLOrderProduct extends GQLProduct {
  constructor(...args) {
    super(...args)

    // prevent loading from datocms
    this.setLoaded(CONST.dataSources.datocms)
  }

  get defaultVariant() {
    return this.variants[0]
  }

  get defaultVariantLowCost() {
    return this.variants[0]
  }

  /**
   * Returns the list of images matching the required formats/types
   *
   * @param {GQLImageFormat[]} formats
   * @param {GQLImageType[]} types
   * @returns {GQLImage[]}
   */
  getImages(formats = [], types = []) {
    return this.defaultVariant.images.filter(
      (image) => (!formats[0] || formats.includes(image.format)) && (!types[0] || types.includes(image.type))
    )
  }
}

module.exports = GQLOrderProduct
