const GQLInputAddress = require('./InputAddress')

class GQLInputDelivery {
  constructor() {
    /** @type {GQLInputAddress} */
    this.address
    /** @type {string} */
    this.comments
    /** @type {GQLContact} */
    this.contact
    /** @type {Date} */
    this.date
    /** @type {Date} */
    this.dateSmsNotification
    /** @type {GQLDeliveryModeEnum} */
    this.mode
    /** @type {string} */
    this.rangeHourId
    /** @type {GQLInputAddress} */
    this.sender
    /** @type {GQLInputTime} */
    this.time
  }

  /**
   * @param {GQLDelivery} delivery
   * @returns {GQLInputDelivery}
   */
  static fromGQLDelivery(delivery) {
    if (!delivery) {
      return
    }

    const inputDelivery = new GQLInputDelivery()

    inputDelivery.address = GQLInputAddress.fromGQLAddress(delivery.address)
    inputDelivery.contact = delivery.contact
    if (delivery.date) {
      inputDelivery.date = new Date(new Date().toISOString().replace(/^[\d-]{10}/, delivery.date))
    }
    if (delivery.dateSmsNotification) {
      inputDelivery.dateSmsNotification = new Date(
        new Date().toISOString().replace(/^[\d-]{10}/, delivery.dateSmsNotification)
      )
    }
    inputDelivery.mode = delivery.mode
    inputDelivery.rangeHourId = delivery.rangeHour?.id
    inputDelivery.sender = GQLInputAddress.fromGQLAddress(delivery.sender)
    inputDelivery.time = delivery.time

    return inputDelivery
  }
}

module.exports = GQLInputDelivery
