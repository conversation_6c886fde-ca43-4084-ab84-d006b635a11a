/** @typedef {import('./_typedef')} */

const GQLAdditionalInfo = require('./AdditionalInfo')
const GQLAddress = require('./Address')
const GQLAvailabilityDateRanges = require('./AvailabilityDateRanges')
const GQLBreadcrumb = require('./Breadcrumb')
const GQLCart = require('./Cart')
const GQLClassification = require('./Classification')
const GQLClassificationFeature = require('./ClassificationFeature')
const GQLConsent = require('./Consent')
const GQLContact = require('./Contact')
const GQLContactSubject = require('./ContactSubject')
const GQLCountry = require('./Country')
const GQLCtaAligned = require('./CtaAligned')
const GQLDateRange = require('./DateRange')
const GQLDelivery = require('./Delivery')
const GQLDeliveryFeasibility = require('./DeliveryFeasibility')
const GQLDeliveryTimeRange = require('./DeliveryTimeRange')
const GQLDeliveryTimeRangesResult = require('./DeliveryTimeRangesResult')
const GQLFuneralAddress = require('./FuneralAddress')
const GQLGellule = require('./Gellule')
const GQLImage = require('./Image')
const GQLInputAddress = require('./InputAddress')
const GQLInputCart = require('./InputCart')
const GQLInputDelivery = require('./InputDelivery')
const GQLInvalidDestination = require('./InvalidDestination')
const GQLLink = require('./Link')
const GQLLinkItem = require('./LinkItem')
const GQLMenu = require('./Menu')
const GQLMetaData = require('./MetaData')
const GQLOrder = require('./Order')
const GQLOrderList = require('./OrderList')
const GQLOrderProduct = require('./OrderProduct')
const GQLOrderReason = require('./OrderReason')
const GQLOrderResult = require('./OrderResult')
const GQLPage = require('./Page')
const GQLPageAccessoryCategory = require('./PageAccessoryCategory')
const GQLPayment = require('./Payment')
const GQLPaymentCustom = require('./PaymentCustom')
const GQLPaymentMethod = require('./PaymentMethod')
const GQLPhone = require('./Phone')
const GQLPrice = require('./Price')
const GQLPriceTier = require('./PriceTier')
const GQLProduct = require('./Product')
const GQLProductCustomizationPreview = require('./ProductCustomizationPreview')
const GQLProductList = require('./ProductList')
const GQLProductVariant = require('./ProductVariant')
const GQLQualifier = require('./Qualifier')
const GQLReward = require('./Reward')
const GQLRewardDetails = require('./RewardDetails')
const GQLRewardEvent = require('./RewardEvent')
const GQLRewardPayment = require('./RewardPayment')
const GQLRewardReservation = require('./RewardReservation')
const GQLRewardTransaction = require('./RewardTransaction')
const GQLRoute = require('./Route')
const GQLScalarBoolean = require('./ScalarBoolean')
const GQLScalarFloat = require('./ScalarFloat')
const GQLScalarInt = require('./ScalarInt')
const GQLScalarString = require('./ScalarString')
const GQLSolrProduct = require('./SolrProduct')
const GQLSearchSuggestions = require('./SearchSuggestions')
const GQLSolrProductVariant = require('./SolrProductVariant')
const GQLStock = require('./Stock')
const GQLTemplate = require('./Template')
const GQLToken = require('./Token')
const GQLTown = require('./Town')
const GQLTracking = require('./Tracking')
const GQLUser = require('./User')
const GQLVoucher = require('./Voucher')

module.exports = {
  GQLAdditionalInfo,
  GQLAddress,
  GQLAvailabilityDateRanges,
  GQLBreadcrumb,
  GQLCart,
  GQLClassification,
  GQLClassificationFeature,
  GQLConsent,
  GQLContact,
  GQLContactSubject,
  GQLCountry,
  GQLCtaAligned,
  GQLDateRange,
  GQLDelivery,
  GQLDeliveryFeasibility,
  GQLDeliveryTimeRange,
  GQLDeliveryTimeRangesResult,
  GQLFuneralAddress,
  GQLGellule,
  GQLImage,
  GQLInputAddress,
  GQLInputCart,
  GQLInputDelivery,
  GQLInvalidDestination,
  GQLLink,
  GQLLinkItem,
  GQLMenu,
  GQLMetaData,
  GQLOrder,
  GQLOrderList,
  GQLOrderProduct,
  GQLOrderReason,
  GQLOrderResult,
  GQLPage,
  GQLPageAccessoryCategory,
  GQLPayment,
  GQLPaymentCustom,
  GQLPaymentMethod,
  GQLPhone,
  GQLPrice,
  GQLPriceTier,
  GQLProduct,
  GQLProductCustomizationPreview,
  GQLProductList,
  GQLProductVariant,
  GQLQualifier,
  GQLReward,
  GQLRewardDetails,
  GQLRewardEvent,
  GQLRewardPayment,
  GQLRewardReservation,
  GQLRewardTransaction,
  GQLRoute,
  GQLScalarBoolean,
  GQLScalarFloat,
  GQLScalarInt,
  GQLScalarString,
  GQLSearchSuggestions,
  GQLSolrProduct,
  GQLSolrProductVariant,
  GQLStock,
  GQLTemplate,
  GQLToken,
  GQLTown,
  GQLTracking,
  GQLUser,
  GQLVoucher,
}
