/** @typedef {import('./AccessoryCategory')} GQLAccessoryCategory */
/** @typedef {import('./AdditionalInfo')} GQLAdditionalInfo */
/** @typedef {import('./Authentication')} GQLAuthentication */
/** @typedef {import('./AvailabilityDateRanges')} GQLAvailabilityDateRanges */
/** @typedef {import('./Address')} GQLAddress */
/** @typedef {import('./Badge')} GQLBadge */
/** @typedef {import('./Breadcrumb')} GQLBreadcrumb */
/** @typedef {import('./Cart')} GQLCart */
/** @typedef {import('./Component')} GQLComponent */
/** @typedef {import('./Contact')} GQLContact */
/** @typedef {import('./ContactSubject')} GQLContactSubject */
/** @typedef {import('./Country')} GQLCountry */
/** @typedef {import('./CtaAligned')} GQLCtaAligned */
/** @typedef {import('./Customization')} GQLCustomization */
/** @typedef {import('./CustomizationChoice')} GQLCustomizationChoice */
/** @typedef {import('./DateRange')} GQLDateRange */
/** @typedef {import('./Delivery')} GQLDelivery */
/** @typedef {import('./DeliveryFeasibility')} GQLDeliveryFeasibility */
/** @typedef {import('./Department')} GQLDepartment */
/** @typedef {import('./FuneralAddress')} GQLFuneralAddress */
/** @typedef {import('./FestMode')} GQLFestMode */
/** @typedef {import('./Footer')} GQLFooter */
/** @typedef {import('./Image')} GQLImage */
/** @typedef {import('./InvalidDestination')} GQLInvalidDestination */
/** @typedef {import('./Link')} GQLLink */
/** @typedef {import('./Menu')} GQLMenu */
/** @typedef {import('./MetaData')} GQLMeta */
/** @typedef {import('./Order')} GQLOrder */
/** @typedef {import('./OrderList')} GQLOrderList */
/** @typedef {import('./OrderReason')} GQLOrderReason */
/** @typedef {import('./OrderResult')} GQLOrderResult */
/** @typedef {import('./Page')} GQLPage */
/** @typedef {import('./PaymentMethod')} GQLPaymentMethod */
/** @typedef {import('./Phone')} GQLPhone */
/** @typedef {import('./Price')} GQLPrice */
/** @typedef {import('./Tier')} GQLTier */
/** @typedef {import('./Product')} GQLProduct */
/** @typedef {import('./ProductClassification')} GQLProductClassification */
/** @typedef {import('./ProductClassificationFeature')} GQLProductClassificationFeature */
/** @typedef {import('./ProductCustomizationPreview')} GQLProductCustomizationPreview */
/** @typedef {import('./ProductList')} GQLProductList */
/** @typedef {import('./ProductVariant')} GQLProductVariant */
/** @typedef {import('./Reward')} GQLReward */
/** @typedef {import('./RewardDetails')} GQLRewardDetails */
/** @typedef {import('./RewardEvent')} GQLRewardEvent */
/** @typedef {import('./RewardReservation')} GQLRewardReservation */
/** @typedef {import('./RewardTransaction')} GQLRewardTransaction */
/** @typedef {import('./Route')} GQLRoute */
/** @typedef {import('./ScalarBoolean')} GQLScalarBoolean */
/** @typedef {import('./ScalarFloat')} GQLScalarFloat */
/** @typedef {import('./ScalarInt')} GQLScalarInt */
/** @typedef {import('./ScalarString')} GQLScalarString */
/** @typedef {import('./SolrProduct')} GQLSolrProduct */
/** @typedef {import('./SolrProductVariant')} GQLSolrProductVariant */
/** @typedef {import('./SourceImages')} GQLSourceImages */
/** @typedef {import('./Stock')} GQLStock */
/** @typedef {import('./Template')} GQLTemplate */
/** @typedef {import('./Title')} GQLTitle */
/** @typedef {import('./Token')} GQLToken */
/** @typedef {import('./Town')} GQLTown */
/** @typedef {import('./TownPage')} GQLTownPage */
/** @typedef {import('./TownPageTab')} GQLTownPageTab */
/** @typedef {import('./User')} GQLUser */
/** @typedef {import('./ValidationError')} GQLValidationError */
/** @typedef {import('./ValidationResult')} GQLValidationResult */
/** @typedef {import('./Voucher')} GQLVoucher */

/**
 * @typedef {{
 *  fieldsets: {
 *    fields: string[]
 *    label: string
 *    title: string
 *  }[]
 *  placement: string
 *  step: string
 *  title: string
 *  userCategory: string
 * }} GQLForm
 */
