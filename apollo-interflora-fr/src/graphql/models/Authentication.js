/** @typedef {import('../_typedef')} */

/**
 * @class GQLAuthentication
 */
class GQLAuthentication {
  constructor() {
    /** @type {string|null} */
    this.autoStartToken = null

    /** @type {string|null} */
    this.hintCode = null

    /** @type {string} */
    this.referenceId = ''

    /** @type {string|null} */
    this.status = null

    /** @type {string|null} */
    this.qrData = null

    /** @type {GQLToken} */
    this.token = null
  }
}

module.exports = GQLAuthentication
