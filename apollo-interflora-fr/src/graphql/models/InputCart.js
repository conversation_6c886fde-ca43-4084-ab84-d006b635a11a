/** @typedef {import('./InputDelivery')} GQLInputDelivery */

const GQLInputDelivery = require('./InputDelivery')

/**
 * @param {GQLCartProductItemType[]} items
 * @returns {GQLInputCartEntry[]}
 */
const itemsToEntries = (items) => {
  return items.map((item) => ({
    code: item.variant.code,
    customizations: item.variant.customizations,
    quantity: item.quantity,
  }))
}

class GQLInputCart {
  constructor() {
    /** @type {GQLInputCartEntry[]} */
    this.accessories = []
    /** @type {GQLInputDelivery} */
    this.delivery
    /** @type {string} */
    this.externalReference
    /** @type {GQLInputJoinMessage} */
    this.joinMessage
    /** @type {GQLInputCartEntry[]} */
    this.products = []
    /** @type {GQLInputCartEntry[]} */
    this.services = []
  }

  /**
   * @param {GQLCart} cart
   * @returns {GQLInputCart}
   */
  static fromGQLCart(cart) {
    const inputCart = new GQLInputCart()

    inputCart.accessories = itemsToEntries(cart.accessoryItems ?? [])
    inputCart.delivery = GQLInputDelivery.fromGQLDelivery(cart.delivery)
    inputCart.externalReference = cart.externalReference
    inputCart.joinMessage = cart.joinMessage
    inputCart.products = itemsToEntries(cart.productItems ?? [])
    inputCart.services = itemsToEntries(cart.serviceItems ?? [])

    return inputCart
  }
}

module.exports = GQLInputCart
