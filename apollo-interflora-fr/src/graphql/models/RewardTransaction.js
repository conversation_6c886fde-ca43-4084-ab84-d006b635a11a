/** @typedef {import('../_typedef')} */

/**
 * @class GQLRewardTransaction
 */

class GQLRewardTransaction {
  constructor() {
    /**
     * The unique Id
     * @type {string}
     */
    this.transactionId = null

    /**
     * The amount
     * @type {number}
     */
    this.amount = null

    /**
     * The time stamp of when the transaction was made
     * @type {DateTime}
     */
    this.timestamp = null

    /**
     * The type of the transaction
     * LOAD, REDEEM, REFUND, REPURCHASE, CANCEL
     * @type {("LOAD"|"REDEEM"|"REFUND"|"REPURCHASE"|"CANCEL")}
     */
    this.transactionType = null

    /**
     * The currency
     * @type {string}
     */
    this.currency = null
  }
}

module.exports = GQLRewardTransaction
