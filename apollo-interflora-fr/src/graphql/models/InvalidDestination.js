/** @typedef {import('../_typedef')} */

/**
 * @class GQLInvalidDestination
 */
class GQLInvalidDestination {
  constructor() {
    /** @type {GQLCartAccessoryItemType[]} */
    this.alternativeAccessories = []

    /** @type {GQLRoute} */
    this.alternativeCategoryRoute

    /** @type {GQLCartProductItemType[]} */
    this.alternativeProducts = []

    /** @type {boolean} */
    this.isValid = false

    /** @type {string} */
    this.zoneGeo
  }
}

module.exports = GQLInvalidDestination
