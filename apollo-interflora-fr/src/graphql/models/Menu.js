/** @typedef {import('../_typedef')} */

/**
 * @class GQLMenu
 */
class GQLMenu {
  constructor() {
    /** @type {GQLMenu[]} */
    this.children = []

    /** @type {string} */
    this.categoryCode = null

    /** @type {boolean} */
    this.clientSide

    /** @type {string[]} */
    this.devices = []

    /** @type {boolean} */
    this.highlight = false

    /** @type {string} */
    this.highlightLabel

    /** @type {GQLUrlUnion} */
    this.href

    /** @type {string} */
    this.icon

    /** @type {string} */
    this.label

    /** @type {GQLBlockMenuTypeEnum} */
    this.blockType

    /** @type {boolean} */
    this.promote = false

    /** @type {boolean} */
    this.obfuscated = false
  }
}

module.exports = GQLMenu
