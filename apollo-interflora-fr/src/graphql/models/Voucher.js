/** @typedef {import('../_typedef')} */

/**
 * @class GQLVoucher
 */
class GQLVoucher {
  constructor() {
    /** @type {string} */
    this.code

    /** @type {GQLCurrency} */
    this.currency

    /** @type {string} */
    this.description

    /** @type {string} */
    this.endDate

    /** @type {string} */
    this.name

    /** @type {string} */
    this.productCode

    /** @type {string} */
    this.startDate

    /** @type {GQLDiscountTypeEnum} */
    this.type

    /** @type {number} */
    this.value
  }
}

module.exports = GQLVoucher
