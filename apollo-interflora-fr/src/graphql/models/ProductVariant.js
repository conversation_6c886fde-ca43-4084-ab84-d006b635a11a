const CONST = require('../../const')
const GQLBase = require('./Base')

/** @typedef {import('../_typedef')} */

/**
 * @class GQLProductVariant
 */
class GQLProductVariant extends GQLBase {
  constructor() {
    super()

    /** @type {GQLProduct[]} */
    this.accessories = []

    /** @type {GQLProduct[]} */
    this.cards = []

    /** @type {GQLAdditionalInfo} */
    this.additionalInfo

    /** @type {string} */
    this.code

    // gfs only
    /** @type {string} */
    this.countryId

    /** @type {boolean} */
    this.customLabel = false

    /** @type {GQLDelivery} */
    this.delivery

    /** @type {Number} */
    this.diameter

    /** @type {GQLPrice} */
    this.discount

    /** @type {Number} */
    this.height

    /** @type {GQLImage[]} */
    this.images = []

    /** @type {boolean} */
    this.isFromWorkshop = false

    /** @type {string} */
    this.label

    /** @type {GQLPrice} */
    this.marketingFee

    /** @type {Number} */
    this.maxQuantity

    /** @type {Number} */
    this.minQuantity

    /** @type {GQLProduct} */
    this.parent

    /** @type {GQLPrice} */
    this.price

    /** @type {string} */
    this.priceChannelId

    /** @type {GQLProductVariantQualifierType[]} */
    this.qualifiers = []

    /** @type {GQLProductVariantReinsuranceType[]} */
    this.reinsurances = []

    /** @type {GQLPrice} */
    this.shippingFee

    /** @type {GQLStock} */
    this.stock

    /** @type {string} */
    this.variationCode

    /** @type {Number} */
    this.weight

    /** @type {GQLCustomization[]} */
    this.customizations = []
  }

  /**
   * @return {boolean}
   */
  get searchable() {
    if (
      this.qualifiers.find((q) => q.type.value === CONST.product.qualifierTypes.zoneGeographique)?.value ===
      CONST.product.zones.INTERNAT
    ) {
      return false
    }

    return true
  }
}

module.exports = GQLProductVariant
