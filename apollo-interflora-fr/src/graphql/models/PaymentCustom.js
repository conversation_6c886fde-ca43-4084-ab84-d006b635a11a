/**
 * Custom fields for payment methods including Gift card, Bonus, or Promotion.
 * These fields are used to store payment-specific information for non-standard payment types
 * like reservationId for gift cards or providerResponse
 * These fields are used also in the frontend to display the payment status
 *
 * @class GQLPaymentCustom
 */
class GQLPaymentCustom {
  constructor() {
    /** @type {string} */
    this.id = null

    /** @type {string} */
    this.label = null

    /** @type {string} */
    this.transactionId = null

    /** @type {string} */
    this.reservationId = null

    /** @type {('GIFT_CARD'|'BONUS'|'PROMOTION')} */
    this.rewardType = null

    /** @type {string} */
    this.provider = null

    /** @type {string} */
    this.providerResponse = null

    /** @type {GQLPrice} */
    this.balance = null
  }
}

module.exports = GQLPaymentCustom
