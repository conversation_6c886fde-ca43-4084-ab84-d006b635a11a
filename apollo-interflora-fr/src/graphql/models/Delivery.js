/** @typedef {import('../_typedef')} */

/**
 * @class GQLDelivery
 */
class GQLDelivery {
  constructor() {
    /** @type {GQLAddress} */
    this.address

    /** @type {{max: string, min: string}[]} */
    this.availableDateRanges = []

    /** @type {boolean} */
    this.availableNoAddress

    /** @type {GQLContact} */
    this.contact

    /** @type {string} */
    this.date

    /** @type {string} */
    this.dateSmsNotification

    /** @type {GQLPrice} */
    this.discount

    /** @type {number} */
    this.maxDayDelivery

    /** @type {GQLCartDeliveryModeEnum} */
    this.mode

    /** @type {GQLPrice} */
    this.price

    /** @type {{id: string, label: string, available: boolean}} */
    this.rangeHour

    /** @type {string} */
    this.status

    /** @type {string} */
    this.statusFallbackLabel

    /** @type {GQLAddress} */
    this.sender

    /** @type {GQLTimeType} */
    this.time

    /** @type {string} */
    this.trackingId

    /** @type {string} */
    this.trackingUrl

    /** @type {boolean} */
    this.useFixHour

    /** @type {string|null} */
    this.orderId
  }

  get rangeAvailableDates() {
    return this.availableDateRanges
  }
}

module.exports = GQLDelivery
