/** @typedef {import('../_typedef')} */

/**
 * @class GQLOrderResult
 */
class GQLOrderResult {
  constructor() {
    /** @type {GQLOrder} */
    this.order

    /** @type {string} */
    this.queryPostParams

    /** @type {string} */
    this.redirectHtml

    /** @type {string} */
    this.redirectUrl

    /** @type {boolean} */
    this.usePost

    /** @type {GQLToken} */
    this.userToken

    /** @type {GQLOrderStatusTypeEnum} */
    this.status

    /** @type {string} */
    this.statusCode

    /** @type {string} */
    this.statusMessage
  }
}

module.exports = GQLOrderResult
