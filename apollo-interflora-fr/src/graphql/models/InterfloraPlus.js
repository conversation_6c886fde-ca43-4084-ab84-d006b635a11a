const config = require('../../config')

class InterfloraPlus {
  /**
   * @param {Date|null} expirationDate
   */
  constructor(expirationDate = null) {
    /** @type {Date|null} */
    this.expirationDate = expirationDate
  }

  /**
   * @return {boolean}
   */
  isActive() {
    return Boolean(this.expirationDate) && this.expirationDate >= new Date()
  }

  isAboutToExpire() {
    return (
      !this.expirationDate ||
      this.expirationDate <= new Date(Date.now() + config.products.interfloraplus.subscriptionRenewTimeLimit)
    )
  }
}

module.exports = InterfloraPlus
