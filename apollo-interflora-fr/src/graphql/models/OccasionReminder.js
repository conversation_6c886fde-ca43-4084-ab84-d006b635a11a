/** @typedef {import('../_typedef')} */

/**
 * Represents the GraphQL DTO for an OccasionReminder.
 * @class GQLOccasionReminder
 */
class GQLOccasionReminder {
  constructor() {
    /** @type {string} */
    this.id

    /** @type {string} */
    this.recipientName

    /** @type {string} */
    this.occasionType

    /** @type {string} */
    this.date

    /** @type {string | null} */
    this.relation
  }
}

module.exports = GQLOccasionReminder
