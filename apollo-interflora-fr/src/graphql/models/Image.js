/** @typedef {import('../_typedef')} */

/**
 * @class GQLImage
 */
class GQLImage {
  constructor() {
    /** @type {string} */
    this.altText

    /** @type {GQLImageFormatEnum} */
    this.format

    /** @type {GQLImageTypeEnum} */
    this.type

    /** @type {number} */
    this.order

    /** @type {string} */
    this.info

    /** @type {string} */
    this.url

    /** @type {number} */
    this.width

    /** @type {number} */
    this.height
  }
}

module.exports = GQLImage
