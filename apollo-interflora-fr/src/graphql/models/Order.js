/** @typedef {import('../_typedef')} */

const GQLBase = require('./Base')

/**
 * @class GQLOrder
 */
class GQLOrder extends GQLBase {
  constructor() {
    super()

    /** @type {string} */
    this.id

    /** @type {GQLCartProductItemType[]} */
    this.accessoryItems = []

    /** @type {boolean} */
    this.availableEdit

    /** @type {boolean} */
    this.archived

    /** @type {string} */
    this.billUrl

    /** @type {boolean} */
    this.canAttachOrder

    /** @type {string} */
    this.countryCode

    /** @type {Date} */
    this.createdAt

    /** @type {GQLDelivery} */
    this.delivery

    /** @type {string[]} */
    this.discountCodes = []

    /** @type {GQLPrice} */
    this.discountCodesPrice

    /** @type {GQLPrice} */
    this.discountedTotal

    /** @type {boolean} */
    this.emailHasRegisteredUser

    /** @type {string} */
    this.externalReference

    /** @type {GQLJoinMessageType} */
    this.joinMessage

    /** @type {string} */
    this.number

    /** @type {GQLPaymentModeEnum} */
    this.paymentMode

    /** @type {GQLCartProductItemType[]} */
    this.productItems = []

    /** @type {GQLOrderReason} */
    this.reason

    /** @type {GQLOrderReason[]} */
    this.reasons

    /** @type {GQLCartProductItemType[]} */
    this.serviceItems = []

    /** @type {string} */
    this.signature

    /** @type {GQLPrice} */
    this.subTotal

    /** @type {GQLPrice} */
    this.total

    /** @type {GQLUser} */
    this.user

    /** @type {GQLVoucher} */
    this.voucher

    /** @type {boolean} */
    this.interfloraplus
  }
}

module.exports = GQLOrder
