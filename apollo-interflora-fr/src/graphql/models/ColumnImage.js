/** @typedef {import('../_typedef')} */

/**
 * @class GQLColumnImage
 */
class GQLColumnImage {
  constructor() {
    /**
     * @type {string}
     * @readonly
     */
    this.__typename = 'ColumnImage'

    /** @type {GQLImage} */
    this.image = null

    /** @type {GQLTitle} */
    this.title = null

    /** @type {string} */
    this.subtitle = ''

    /** @type {string} */
    this.description = ''

    /** @type {{label: string, target: GQLRoute}} */
    this.cta = null

    /** @type {string} */
    this.align = 'left'
  }
}

module.exports = GQLColumnImage
