const GQLDateRange = require('./DateRange')

/**
 * @class GQLAvailabilityDateRanges
 */
class GQLAvailabilityDateRanges {
  /**
   * @param {GQLDateRange[]} ranges
   */
  constructor(ranges = []) {
    /** @type {GQLDateRange[]} */
    this.ranges = ranges
  }

  /**
   * @returns {boolean}
   */
  get today() {
    if (!this.ranges.length) {
      return true
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    return this.ranges.some((range) => range.includesDate(today))
  }

  get tomorrow() {
    if (!this.ranges.length) {
      return true
    }

    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)

    return this.ranges.some((range) => range.includesDate(tomorrow))
  }

  removeToday() {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    for (const [i, range] of this.ranges.entries()) {
      if (range.includesDate(today)) {
        /** @type {GQLDateRange} */
        let newRange

        if (range.max.getTime() >= today.getTime() + 24 * 3600 * 1000) {
          newRange = new GQLDateRange()
          newRange.min = new Date(range.min.getTime() + 24 * 3600 * 1000)
          newRange.max = range.max
        }

        this.ranges.splice(i, 1, newRange)
      }
    }
  }
}

module.exports = GQLAvailabilityDateRanges
