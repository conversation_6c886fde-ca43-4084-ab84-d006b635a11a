/** @typedef {import('../_typedef')} */

/**
 * @class GQLUser
 */
class GQLUser {
  constructor() {
    /** @type {string} */
    this.addressId

    /** @type {string} */
    this.address

    /** @type {string} */
    this.address2

    /** @type {Date} */
    this.birthDate

    /** @type {GQLCivilityEnum} */
    this.civility

    /** @type {string} */
    this.company

    /** @type {string} */
    this.companyNumber

    /** @type {string} */
    this.countryId

    /** @type {GQLAddress[]} */
    this.deliveryAddresses = []

    /** @type {string} */
    this.email

    /** @type {string} */
    this.firstName

    /** @type {string} */
    this.fiscaleCode

    /** @type {string} */
    this.id

    /** @type {string} */
    this.lastName

    /** @type {GQLOrder} */
    this.lastOrder

    /** @type {string} */
    this.pec

    /** @type {GQLPhone} */
    this.phone

    /** @type {string} */
    this.postalCode

    /** @type {GQLTown} */
    this.town

    /** @type {Date} */
    this.premium

    /** @type {GQLAddress[]} */
    this.senderAddresses = []

    /** @type {GQLUserTypeEnum} */
    this.type

    /** @type {string} */
    this.username

    /** @type {string} */
    this.vatNumber

    /** @type {boolean} */
    this.optinEmail

    /** @type {boolean} */
    this.optinSMS

    /** @type {GQLOrderList} */
    this.orders
  }
}

module.exports = GQLUser
