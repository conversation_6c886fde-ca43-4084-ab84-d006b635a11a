class GQLInputAddress {
  constructor() {
    /** @type {string} */
    this.additionalInformation
    /** @type {string} */
    this.address
    /** @type {string} */
    this.address2
    /** @type {Date} */
    this.birthDate
    /** @type {string} */
    this.birthPlace
    /** @type {string} */
    this.building
    /** @type {GQLCivilityEnum} */
    this.civility
    /** @type {string} */
    this.comments
    /** @type {string} */
    this.company
    /** @type {string} */
    this.companyNumber
    /** @type {string} */
    this.countryId
    /** @type {string} */
    this.email
    /** @type {GQLUserTypeEnum} */
    this.entityType
    /** @type {string} */
    this.firstName
    /** @type {string} */
    this.fiscalCode
    /** @type {string} */
    this.id
    /** @type {string} */
    this.invoiceEmail
    /** @type {string} */
    this.invoiceFirstName
    /** @type {string} */
    this.invoiceLastName
    /** @type {boolean} */
    this.invoiceRequest
    /** @type {string} */
    this.lastName
    /** @type {boolean} */
    this.markDefault
    /** @type {boolean} */
    this.optinEmail
    /** @type {boolean} */
    this.optinSMS
    /** @type {string} */
    this.pec
    /** @type {GQLPhone} */
    this.phone
    /** @type {string} */
    this.postalCode
    /** @type {string} */
    this.province
    /** @type {boolean} */
    this.store
    /** @type {string} */
    this.townId
    /** @type {string} */
    this.townLabel
    /** @type {string} */
    this.twitterId
    /** @type {GQLAddressTypeEnum} */
    this.type
    /** @type {string} */
    this.vatNumber
  }

  /**
   * @param {GQLAddress} address
   * @return {GQLInputAddress}
   */
  static fromGQLAddress(address) {
    const inputAddress = new GQLInputAddress()

    const { country, default: _default, stored: _stored, town, ...rest } = address

    Object.assign(inputAddress, rest)

    inputAddress.countryId = country?.id
    inputAddress.townId = town?.id
    inputAddress.townLabel = town?.label

    return inputAddress
  }
}

module.exports = GQLInputAddress
