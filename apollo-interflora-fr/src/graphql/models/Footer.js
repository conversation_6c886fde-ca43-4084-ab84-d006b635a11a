/** @typedef {import('../_typedef')} */

/**
 * @class G<PERSON>Footer
 */
class G<PERSON>Footer {
  constructor() {
    /** @type {GQLFooter[]} */
    this.children = []

    /** @type {Boolean} */
    this.highlight

    /** @type {GQLUrlUnion} */
    this.href

    /** @type {string} */
    this.label

    /** @type {string[]} */
    this.mentions

    /** @type {Boolean} */
    this.multiple

    /** @type {Boolean} */
    this.obfuscated
  }
}

module.exports = GQLFooter
