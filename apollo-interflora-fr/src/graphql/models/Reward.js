/** @typedef {import('../_typedef')} */

/**
 * Represents a reward, which can be a gift card, bonus or a promotion and is used in the cart
 * to make a reservation for gift cards, confirm the reservation, or redeem the bonus or promotion
 * It's used in the createReward mutation to create a reservation, in the confirmReward mutation to
 * confirm the reservation or create a bonus or promotion, and in the cancelReward mutation to cancel
 * the reservation or bonus or promotion.
 *
 * @class GQLReward
 */

class GQLReward {
  constructor() {
    /**
     *  @type {string}
     */
    this.id = null

    /**
     * The reward code
     * @type {string}
     */
    this.cardNumber = null

    /**
     * The type of the reward
     * @type {("giftCard" | "bonus")}
     */
    this.type = null
    /**
     * The balance amount
     * @type {number}
     */
    this.balance = null

    /**
     * The start date
     * @type {DateTime}
     */
    this.startDate = null

    /**
     * The expiry date
     * @type {DateTime}
     */
    this.endDate = null

    /**
     * The currency
     * @type {string}
     */
    this.currency = null

    /**
     * Information of card status codes
     * 1 - Created
     * 2 - Queued for sending
     * 3 - Sending error
     * 4 - Sending*
     * 5 - Delivery error
     * 6 - Delivered*
     * 7 - Partly or fully redeemed*
     * 8 - Closed
     * 9 - Canceled
     * 10 - Extern distribution*
     * 11 - Not activated
     * @type {number}
     */
    this.status = null
  }
}

module.exports = GQLReward
