const GQLModel = require('./Base')

/** @typedef {import('../_typedef')} */

/**
 * @class GQLDepartment
 */
class GQLDepartment extends GQLModel {
  constructor() {
    super()

    /** @type {GQLDepartmentButton}*/
    this.button

    /** @type {string} */
    this.slugUrl

    /** @type {string} */
    this.description

    /** @type {string} */
    this.descriptionBottom

    /** @type {GQLImage} */
    this.image

    /** @type {GQLImage[]} */
    this.images = []

    /** @type {string} */
    this.indice

    /** @type {string} */
    this.label

    /** @type {string} */
    this.mainTown

    /** @type {string} */
    this.mainTowns

    /** @type {GQLDepartment[]} */
    this.others = []

    /** @type {string} */
    this.slugUrl

    /** @type {string} */
    this.title

    /** @type {GQLDepartmentTownType[]} */
    this.towns = []

    /** @type {GQLDepartmentTypeEnum} */
    this.type

    /** @type {string} */
    this.regionCode
  }
}

module.exports = GQLDepartment
