/** @typedef {import('../_typedef')} */

/**
 * @class GQLPage
 */
class GQLPage {
  constructor() {
    /**
     * @type {string[]}
     * @internal
     */
    this.accessoryCategoryUids = []

    /** @type {GQLBreadcrumb[]} */
    this.breadcrumbs = []

    /** @type {GQLComponentZoneType[]} */
    this.componentZones = []

    /** @type {GQLFestMode} */
    this.festMode = null

    //Those are categories used for accessories and messageCards
    /** @type {GQLAccessoryCategoryType[]} */
    this.accessoryCategories = []

    /** @type {GQLFooter[]} */
    this.footers = []

    /** @type {GQLGelluleType[]} */
    this.gellules = []

    /**
     * @type {string[]}
     * @internal
     */
    this.gelluleUids = []

    /**
     * @type {string[]}
     * @internal
     */
    this.menuUids = []

    /** @type {GQLMenu[]} */
    this.menus = []

    /** @type {GQLMeta[]} */
    this.meta = []

    /** @type {boolean} */
    this.pro = false

    /** @type {string} */
    this.title

    /** @type {string|null} */
    this.subtitle = null

    /** @type {string} */
    this.version
  }
}

module.exports = GQLPage
