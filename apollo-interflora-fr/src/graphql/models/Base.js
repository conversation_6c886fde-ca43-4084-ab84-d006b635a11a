/**
 * @abstract
 */
class GQLBase {
  constructor() {
    /** @type {Object<string, boolean>} */
    this._loaded = {}
  }

  /**
   * @param {string} dataSource
   * @returns {boolean}
   */
  isLoaded(dataSource) {
    return Boolean(this._loaded[dataSource])
  }

  /**
   * @param {string} dataSource
   */
  setLoaded(dataSource) {
    this._loaded[dataSource] = true
  }
}

module.exports = GQLBase
