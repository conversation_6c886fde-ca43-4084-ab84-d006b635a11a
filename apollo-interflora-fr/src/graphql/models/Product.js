const _ = require('lodash')

const CONST = require('../../const')
const GQLBase = require('./Base')

/** @typedef {import('../_typedef')} */

/**
 * @class GQLProduct
 */
class GQLProduct extends GQLBase {
  constructor() {
    super()

    this.__typename = 'Product'

    /** @type {GQLProduct[]} */
    this.accessories = []

    /** @type {GQLProduct} */
    this.card

    /** @type {GQLProduct[]} */
    this.cards = []

    // gfs only
    /** @type {string} */
    this.countryId

    /** @type {string[]} */
    this.accessoryTypes = []

    /** @type {GQLAdditionalInfo} */
    this.additionalInfo

    /** @type {'AUTO_ADD'|'STANDARD'|'SUGGEST'} */
    this.addToCart

    /** @type {boolean} */
    this.availableNoAddress

    /** @type {GQLBadge} */
    this.badge

    /** @type {string} */
    this.canonical

    /** @type {GQLAccessoryCategory[]} */
    this.categories = []

    /** @type {string} */
    this.classification

    /** @type {GQLProductClassification[]} */
    this.classifications = []

    /** @type {string} */
    this.code

    /** @type {object} */
    this.defaultCategory

    /** @type {GQLDelivery} */
    this.delivery

    /** @type {string} */
    this.deliveryDetail

    /** @type {string} */
    this.description

    /** @type {string} */
    this.descriptionTitle

    /** @type {GQLPrice} */
    this.discount

    /** @type {string} */
    this.externalUrl

    /** @type {boolean} */
    this.hasCustomText

    /** @type {string} */
    this.highlightText

    /** @type {boolean} */
    this._isCardMessage

    /** @type {boolean} */
    this.isFromWorkshop

    /** @type {boolean} */
    this.isOnline = true

    /** @type {boolean} */
    this.isRubanDeuil

    /** @type {string} */
    this.key

    /** @type {string} */
    this.legalNotice

    /** @type {GQLProduct[]} */
    this.linked

    /** @types {string} */
    this.mainCategoryId

    /** @type {Array} */
    this.mentions = []

    /** @type {string} */
    this.name

    /** @type {number|undefined} */
    this.position

    /** @type {GQLImage|undefined} */
    this.mainAsset

    /** @type {GQLJoinMessageType} */
    this.joinMessage

    /** @type {GQLPrice} */
    this.price

    /** @type {GQLProductVariantReinsuranceType[]} */
    this.reinsurances = []

    /** @type {string | undefined} */
    this.seaKey

    /** @type {string} */
    this.slugUrl

    /** @type {string} */
    this._datoDescription

    /** @type {string} */
    this._datoProductType

    /** @type {string} */
    this.subtitle

    /** @type {string} */
    this.pictureText

    /** @type {string} */
    this.deliveryTypeText

    /** @type {string} */
    this.summary

    /** @type {GQLProductTypeEnum} */
    this.type

    /** @type {Boolean} */
    this.useQuantitySelector = false

    /** @type {GQLProductVariant[]} */
    this.variants = []

    /** @type {string|null} */
    this._ctLegalNotice = null

    /** @type {string|null} */
    this._mainAssetId = null

    /** @type {GQLProductVariant} */
    this._defaultVariant

    /** @type {string} */
    this._defaultVariantSize = undefined

    /** @type {string} */
    this._defaultVariantZone = undefined

    /** @type {GQLProductVariant[]} */
    this._defaultVariants = undefined

    /** @type {GQLImage[]} */
    this._images = []

    /** @type {string[]} */
    this.bundledProducts = []

    /**
     * List of countries where the availability check should be skipped
     *
     * @type {string[]} */
    this._skipAvailabilityOn = []

    /**
     * Define whether the product is generic
     *
     * If a product does not need to be checked for availability
     * it is generic, otherwise not generic
     *
     * By default, availability is checked for all products
     * @type {boolean} */
    this.isGenericAccessory
  }

  get isAccessory() {
    return this.__typename === 'Accessory'
  }

  get isCardMessage() {
    return this._isCardMessage || this.accessoryTypes.includes(CONST.product.accessoryTypes.messageCard)
  }

  set isCardMessage(value) {
    this._isCardMessage = value
  }

  get mainAssetId() {
    return this._mainAssetId
  }

  get defaultVariantLowCost() {
    return this.defaultVariants[0]
  }

  get defaultVariant() {
    if (!this._defaultVariant) {
      this._setDefaultVariants()
    }
    return this._defaultVariant
  }

  get defaultVariants() {
    if (!this._defaultVariants) {
      this._setDefaultVariants()
    }
    return this._defaultVariants
  }

  /**
   * Returns true if the product uses different products as variants
   * i.e.: plants
   * @returns {boolean}
   */
  get hasProductsAsVariant() {
    return this.variants.some((variant) => variant.code.split('-')[0] !== this.code.split('-')[0])
  }

  /**
   * @returns {string}
   */
  get productType() {
    return this._datoProductType
  }

  /**
   * @param {GQLImage} image
   */
  addImage(image) {
    this._images.push(image)
  }

  /**
   * Returns the list of images matching the required formats/types
   *
   * @param {GQLImageFormat[]} formats
   * @param {GQLImageType[]} types
   * @returns {GQLImage[]}
   */
  getImages(formats = [], types = []) {
    if (this._images.length === 0) {
      return []
    }

    return this._images.filter(
      (image) => (!formats[0] || formats.includes(image.format)) && (!types[0] || types.includes(image.type))
    )
  }

  /**
   * @param {GQLProduct} product
   */
  merge(product) {
    _.merge(this, product)

    for (const variant of this.variants ?? []) {
      variant.parent = this
    }
  }

  _setDefaultVariants() {
    if (this.__typename === 'Accessory' || (!this._defaultVariantZone && this.variants.length === 1)) {
      this._defaultVariants = this.variants
      this._defaultVariant = this.variants[0]
      return
    }

    const result = []

    for (const variant of this.variants) {
      let isDefaultSize = false
      let isDefaultSlug = false
      let isDefaultZone = false

      for (const { type, value } of variant.qualifiers) {
        isDefaultSize =
          isDefaultSize ||
          (type.value === CONST.product.qualifierTypes.priceVariantType && value === this._defaultVariantSize)
        isDefaultSlug = isDefaultSlug || (type.value === CONST.product.qualifierTypes.slug && value === this.slugUrl)
        isDefaultZone =
          isDefaultZone ||
          (type.value === CONST.product.qualifierTypes.zoneGeographique &&
            value.toLowerCase() === this._defaultVariantZone.toLowerCase())
      }

      if (isDefaultZone) {
        result.push(variant)
      }
      if (isDefaultSize && isDefaultSlug && isDefaultZone) {
        this._defaultVariant = variant
      }
    }

    // sort by price
    result.sort((a, b) => (a?.price?.value || 0) - (b?.price?.value || 0))

    this._defaultVariants = result

    // can happen if a product has variants without any qualifier
    if (this._defaultVariants.length === 0) {
      this._defaultVariants = this.variants
      this._defaultVariant = this.variants[0]
    }

    // edge case where we get variants for the default zone but none matching default slug or price
    if (!this._defaultVariant) {
      this._defaultVariant = this._defaultVariants[0]
    }
  }
}

module.exports = GQLProduct
