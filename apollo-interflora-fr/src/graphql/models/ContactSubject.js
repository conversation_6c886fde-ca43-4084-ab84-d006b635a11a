/** @typedef {import('../_typedef')} */

/**
 * @class GQLContactSubject
 */
class GQLContactSubject {
  constructor() {
    /** @type {boolean} */
    this.displayDeliveryDate

    /** @type {string} */
    this.id

    /** @type {string} */
    this.label

    /** @type {boolean} */
    this.mandatoryDeliveryAddress

    /** @type {boolean} */
    this.mandatoryDeliveryDate

    /** @type {boolean} */
    this.mandatoryFile

    /** @type {boolean} */
    this.mandatoryMessage

    /** @type {boolean} */
    this.mandatoryOrderNumber

    /** @type {GQLContactSubject} */
    this.parent = null

    /** @type {GQLContactSubject[]} */
    this.subSubject = []

    /** @type {string} */
    this.type
  }
}

module.exports = GQLContactSubject
