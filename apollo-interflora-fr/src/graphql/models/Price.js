/**
 * @class GQLPrice
 */
class GQLPrice {
  constructor() {
    /** @type {number} */
    this.value

    /** @type {string} */
    this.currencyIso

    /** @type {GQLTier[]} */
    this.tiers
  }

  /**
   * @param {GQLPrice} price
   * @returns {GQLPrice}
   */
  add(price) {
    const result = new GQLPrice()

    if (this.currencyIso !== price.currencyIso) {
      throw new Error(`Can't add price for different currency.`)
    }

    result.currencyIso = this.currencyIso
    result.value = this.value + price.value

    return result
  }

  /**
   * @param {Number} qty
   * @returns {GQLPrice}
   */
  multiply(qty) {
    const result = new GQLPrice()

    result.currencyIso = this.currencyIso
    result.value = Math.round(qty * this.value)

    return result
  }

  /**
   * @param {GQLPrice} price
   * @returns {GQLPrice}
   */
  substract(price) {
    const result = new GQLPrice()

    if (this.currencyIso !== price.currencyIso) {
      throw new Error(`Can't add price for different currency.`)
    }

    result.currencyIso = this.currencyIso
    result.value = this.value - price.value

    return result
  }
}

module.exports = GQLPrice
