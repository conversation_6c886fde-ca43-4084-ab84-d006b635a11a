/** @typedef {import('../_typedef')} */

/**
 * @class GQLCart
 */
class GQLCart {
  constructor() {
    /** @type {GQLCartProductItemType[]} */
    this.accessoryItems = []

    /** @type {GQLDelivery} */
    this.delivery = null

    /** @type {string[]} */
    this.discountCodes = []

    /** @type {GQLPrice} */
    this.discountCodesPrice

    /** @type {GQLPrice} */
    this.discountedTotal

    /** @type {string} */
    this.externalReference

    /** @type {string} */
    this.id

    /** @type {string} */
    this.number

    /** @type {string} */
    this.navigationCategory

    /** @type {GQLCartProductItemType[]} */
    this.productItems = []

    /** @type {GQLCartProductItemType[]} */
    this.suggestedAccessories = []

    /** @type {GQLPaymentModeEnum[]} */
    this.paymentMethods = []

    /** @type {GQLCartProductItemType[]} */
    this.serviceItems = []

    /** @type {GQLPrice} */
    this.subTotal = null

    /** @type {GQLPrice} */
    this.total = null

    /** @type {boolean} */
    this.emailHasAccountPremium

    /** @type {GQLJoinMessageType} */
    this.joinMessage

    /** @type {string[]} */
    this.celebrationTexts = []

    /** @type {GQLOrder} */
    this.order

    /** @type {GQLRewardPayment[]} */
    this.rewardPayments = []

    /** @type {boolean} */
    this.hasDeliveryMethodSelected = false

    /** @type {GQLPayment[]} */
    this.payments = []
  }

  get allItems() {
    return [...this.productItems, ...this.accessoryItems, ...this.serviceItems]
  }
}

module.exports = GQLCart
