/** @typedef {import('../_typedef')} */

/**
 * @class GQLAddress
 */
class GQLAddress {
  constructor() {
    /** @type {string} */
    this.id

    /** @type {string} */
    this.email

    /** @type {string} */
    this.address

    /** @type {string} */
    this.address2

    /** @type {string} */
    this.additionalInformation

    /** @type {string} */
    this.building

    /** @type {string} */
    this.city

    /** @type {GQLCivilityEnum} */
    this.civility

    /** @type {string} */
    this.comments

    /** @type {string} */
    this.company

    /** @type {string} */
    this.companyNumber

    /** @type {GQLCountry} */
    this.country

    /** @type {boolean} */
    this.default

    /** @type {string} */
    this.invoiceEmail

    /** @type {string} */
    this.invoiceFirstName

    /** @type {string} */
    this.invoiceLastName

    /** @type {string} */
    this.firstName

    /** @type {string} */
    this.lastName

    /** @type {string} */
    this.pec

    /** @type {GQLPhone} */
    this.phone

    /** @type {GQLPhone} */
    this.phone2

    /** @type {string} */
    this.postalCode

    /** @type {boolean} */
    this.stored

    /** @type {GQLTown} */
    this.town

    /** @type {GQLAddressTypeEnum} */
    this.type

    /** @type {string} */
    this.twitterId

    /** @type {string} */
    this.vatNumber

    /** @type {boolean} */
    this.invoiceRequest

    /** @type {boolean} */
    this.optinEmail

    /** @type {boolean} */
    this.optinSMS
  }
}

module.exports = GQLAddress
