const DataLoader = require('dataloader')
const apm = require('elastic-apm-node')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

/**
 * @param {PromiseSettledResult<any>} result
 * @returns {any|null}
 */
const resultOrNullOnError = (result) => {
  if (result.status === 'fulfilled') {
    return result.value
  } else {
    apm.captureError(result.reason, { custom: result.reason })
    return null
  }
}

const loaders = {
  customizations(context) {
    return new RedisDataLoader(
      'cadeaux:customizations',
      new DataLoader(async (options) =>
        (
          await Promise.allSettled(
            options.map((option) => context.dataSources.cadeaux.getCustomization(option.cadeauxVariantId))
          )
        ).map(resultOrNullOnError)
      ),
      {
        cacheKeyFn: (opt) => opt.cadeauxVariantId,
        expire: getDataloaderTtl('cadeauxCustomizations'),
        redis: context.server.redis,
      }
    )
  },

  preview(context) {
    return new RedisDataLoader(
      'cadeaux:preview',
      new DataLoader(async (options) =>
        (
          await Promise.allSettled(
            options.map((option) =>
              context.dataSources.cadeaux.getPreview(option.cadeauxVariantId, option.customization)
            )
          )
        ).map(resultOrNullOnError)
      ),
      {
        expire: getDataloaderTtl('cadeauxPreview'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class CadeauxDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createCadeauxLoaders = (context) =>
  new Proxy(new CadeauxDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
