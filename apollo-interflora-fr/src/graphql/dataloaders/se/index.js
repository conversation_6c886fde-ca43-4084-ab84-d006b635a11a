const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || 30 // default 30 seconds
}

const loaders = {
  delivery(context) {
    return new RedisDataLoader(
      'se:delivery',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(
          keys.map(async (key) => {
            return context.dataSources.seFlorist.delivery(key)
          })
        )

        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('delivery'),
        redis: context.server.redis,
      }
    )
  },
  getFuneralAddresses(context) {
    return new RedisDataLoader(
      'se:funeralAddresses',
      new DataLoader(
        async (options) =>
          await Promise.allSettled(options.map((option) => context.dataSources.bitnet.getFuneralAddresses(option)))
      ),
      {
        cacheKeyFn: (opt) => opt,
        expire: getDataloaderTtl('funeralAddresses'),
        redis: context.server.redis,
      }
    )
  },
  rewards(context) {
    return new RedisDataLoader(
      'se:rewards',
      new DataLoader(async (keys) => {
        return await Promise.all(
          keys.map(async (key) => {
            const data = await context.server.redis.GET(key)
            return data ? JSON.parse(data) : false
          })
        )
      }),
      {
        expire: getDataloaderTtl('rewards'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class SEDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createSEDataLoaders = (context) =>
  new Proxy(new SEDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
