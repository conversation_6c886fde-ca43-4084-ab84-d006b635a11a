const config = require('../../../config')
const CONST = require('../../../const')
const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')

const { hash } = require('../../../helpers/string')
const { isUUID } = require('../../../helpers/string')

/** @typedef {import('../../resolvers/ct-datocms/_typedef')} */

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

const loaders = {
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, string[]>}
   */
  activeProductIds(context) {
    return new RedisDataLoader(
      'ct:active-ids',
      new DataLoader(async () => {
        try {
          const result = await context.dataSources.products.getActiveProductIds(context)
          return [result]
        } catch (e) {
          return [e]
        }
      }),
      {
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, CTProduct[]>}
   */
  allGFSMarketingFees(context) {
    return new RedisDataLoader(
      'ct:gfs-mfees',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(keys.map(() => context.dataSources.products.getGFSMarketingFees()))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, CTTaxCategory[]>}
   */
  allTaxCategories(context) {
    return new RedisDataLoader(
      `ct:all-tax-categories:${config.site}`,
      new DataLoader(async () => {
        try {
          const taxes = await context.dataSources.carts.getTaxCategories()
          for (const tax of taxes) {
            context.loaders.commerceTools.taxCategories.prime(tax.id, tax)
          }
          return [taxes]
        } catch (err) {
          return [err]
        }
      }),
      {
        expire: getDataloaderTtl('taxes'),
        redis: context.server.redis,
      }
    )
  },
  cartDiscounts(context) {
    return new RedisDataLoader(
      'ct:cart-discount',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getCartDiscount(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('discounts'),
        redis: context.server.redis,
      }
    )
  },
  cartShippingMethods(context) {
    return new RedisDataLoader(
      'ct:cart-ship-methods',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getCartShippingMethods(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('carts'),
        redis: context.server.redis,
      }
    )
  },
  carts(context) {
    return new RedisDataLoader(
      'ct:carts',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getCart(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('carts'),
        redis: context.server.redis,
      }
    )
  },
  cartsAsRead(context) {
    return new RedisDataLoader(
      'ct:carts',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getCartAsRead(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('carts'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<string, CTCategory>}
   */
  categories(context) {
    return new RedisDataLoader(
      'ct:cat',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.products.getCategory(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<string, CTChannel>}
   */
  channels(context) {
    return new RedisDataLoader(
      'ct:channel:id',
      new DataLoader(async (ids) => {
        const channels = await context.dataSources.ctProject.getChannels({ ids })
        const result = {}

        for (const channel of channels) {
          result[channel.id] = channel
          context.loaders.commerceTools.channelsByKey.prime(channel.key, channel)
        }
        return Object.values(result)
      }),
      {
        expire: getDataloaderTtl('channels'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<string, CTChannel>}
   */
  channelsByKey(context) {
    return new RedisDataLoader(
      'ct:channel',
      new DataLoader(async (keys) => {
        const channels = await context.dataSources.ctProject.getChannels({ keys })
        const result = {}

        for (const channel of channels) {
          result[channel.key] = channel
          context.loaders.commerceTools.channels.prime(channel.id, channel)
        }
        return Object.values(result)
      }),
      {
        expire: getDataloaderTtl('channels'),
        redis: context.server.redis,
      }
    )
  },
  customerGroups(context) {
    return new RedisDataLoader(
      'ct:customer-group',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.customers.getCustomerGroup(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('customer-groups'),
        redis: context.server.redis,
      }
    )
  },
  customers(context) {
    return new RedisDataLoader(
      'ct:customer',
      new DataLoader(async (emails) => {
        const results = await Promise.allSettled(
          emails.map((email) => context.dataSources.customers.getCustomerByEmail(email))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('customers'),
        redis: context.server.redis,
      }
    )
  },
  defaultShippingMethod(context) {
    return new RedisDataLoader(
      'ct:default-shipping-method',
      new DataLoader(async () => [await context.dataSources.carts.getDefaultShippingMethod()]),
      {
        expire: getDataloaderTtl('shippingMethods'),
        redis: context.server.redis,
      }
    )
  },
  discountCodesByCode(context) {
    return new RedisDataLoader(
      'ct:discount-code',
      new DataLoader(async (codes) => {
        const results = await Promise.allSettled(
          codes.map((code) => context.dataSources.carts.getDiscountCodeByCode(code))
        )
        return results.map(async (r) => {
          if (r.status === 'fulfilled') {
            if (r.value) await context.loaders.commerceTools.discounts.prime(r.value?.id, r.value)
            return r.value
          }

          return r.reason
        })
      }),
      {
        expire: getDataloaderTtl('discounts'),
        redis: context.server.redis,
      }
    )
  },
  discounts(context) {
    return new RedisDataLoader(
      'ct:discount-code-id',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getDiscountCode(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('discounts'),
        redis: context.server.redis,
      }
    )
  },
  orders(context) {
    return new RedisDataLoader(
      'ct:orders',
      new DataLoader(async (ids) => {
        /** @type {{ reason: Error, value: CTOrder }[]} */
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.orders.getOrder(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('orders'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<{
   *  after?: Date,
   *  before?: Date,
   *  customQueryParam?: string
   *  email: string,
   *  limit?: number,
   *  offset?: number,
   *  sort?: string,
   * }, CTPagedQueryResult<CTOrder>}
   */
  ordersByCustomerEmail(context) {
    return new RedisDataLoader(
      'ct:orders-by-cust-email',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.orders.getOrdersByCustomerEmail(opts))
        )
        return results.map((r) => {
          if (r.status === 'fulfilled') {
            // no need to await the cache
            Promise.allSettled(
              r.value.results.map((order) => context.loaders.commerceTools.orders.prime(order.id, order))
            )
            return r.value
          }

          return r.reason
        })
      }),
      {
        expire: getDataloaderTtl('orders'),
        redis: context.server.redis,
        tags: (_order, opts) => {
          const tag = `uol:${hash(opts.email)}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  ordersByLegacyOrderNumber(context) {
    return new RedisDataLoader(
      'ct:orders:legacy',
      new DataLoader(async (numbers) => {
        /** @type {{ reason: Error, value: CTOrder }[]} */
        const results = await Promise.allSettled(
          numbers.map((number) => context.dataSources.orders.getOrderByLegacyOrderNumber(number))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('orders'),
        redis: context.server.redis,
      }
    )
  },
  ordersByOrderNumber(context) {
    return new RedisDataLoader(
      'ct:orders',
      new DataLoader(async (numbers) => {
        /** @type {{ reason: Error, value: CTOrder }[]} */
        const results = await Promise.allSettled(
          numbers.map((number) => context.dataSources.orders.getOrderByNumber(number))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('orders'),
        redis: context.server.redis,
      }
    )
  },
  organizationSubcategories(context) {
    return new RedisDataLoader(
      'ct:organization-subcategories',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map(async ({ categoryIdOrKey, organizationId }) => {
            const id = isUUID(categoryIdOrKey)
              ? categoryIdOrKey
              : (await context.loaders.commerceTools.categories.load(categoryIdOrKey))?.id

            return id ? context.dataSources.categories.getOrganizationSubcategories(id, organizationId) : null
          })
        )

        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  payments(context) {
    return new RedisDataLoader(
      'ct:payments',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getPayment(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('payments'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<{ categoryId: string, options: CTProductListOptions }, { id: string, key: string }[]>}
   */
  productKeysPriceSorted(context) {
    return new RedisDataLoader(
      'ct:pkeysPriceSorted',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(
          keys.map((opts) => context.dataSources.products.getProductKeysPriceSorted(opts.categoryId, opts.options))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
        tags: (results) => {
          const tags = []
          for (const r of results) {
            const tag = `p:${r.id}`
            context.cacheTags.add(tag)
            tags.push(tag)
          }
          return tags
        },
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<string, { facets: CTFacetResults, results: CTProductProjection[], total: number }>}
   */
  productLists(context) {
    return new RedisDataLoader(
      'ct:plp',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(
          keys.map(async (key) => {
            let id = key.id
            if (!isUUID(id)) {
              id = (await context.loaders.commerceTools.categories.load(key.id))?.id
            }
            const subcategories = ((await context.loaders.commerceTools.subcategories.load(key.id)) || []).map(
              (c) => c.id
            )
            return context.dataSources.products.getProductListByCategory(id, {
              ...key.opts,
              subcategoryOrderHints: subcategories,
            })
          })
        )

        return results.map((r) => {
          if (r.status !== 'fulfilled') return r.reason

          for (const product of r.value.results ?? []) {
            context.loaders.commerceTools.products.prime({ code: product.id }, product)
          }
          return r.value
        })
      }),
      {
        cacheKeyFn: (key) => `${key.id}:${hash(key.opts)}`,
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
        tags: (response) => {
          const tags = []

          if (!response?.results?.[0]) return []

          for (const product of response.results) {
            const tag = `p:${product.id}`
            context.cacheTags.add(tag)
            tags.push(tag)
          }

          return tags
        },
      }
    )
  },
  productTypes(context) {
    return new RedisDataLoader(
      'ct:product-type',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.products.getProductType(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('productTypes'),
        redis: context.server.redis,
      }
    )
  },
  productTypesByKey(context) {
    return new RedisDataLoader(
      'ct:product-type-key',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(
          keys.map((key) => context.dataSources.products.getProductTypeByKey(key))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('productTypes'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<{
   *  code: string,
   *  channel: [string]
   * }, CTProduct>}
   */
  products(context) {
    return new RedisDataLoader(
      'ct:product',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((options) => context.dataSources.products.getProduct(options.code, options.channel))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opt) => `${opt.code}:${opt.channel ?? config.commerceTools.priceChannelKey}`,
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
        tags: (_product, key) => {
          const tag = `p:${key.code}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<{ channel: [string], key: string }, CTProduct>}
   */
  productsByKey(context) {
    return new RedisDataLoader(
      'ct:product-key',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.products.getProductByKey(opts.key, opts.channel))
        )

        return results.map(async (r) => {
          if (r.status === 'fulfilled') {
            await context.loaders.commerceTools.products.prime({ code: r.value?.id }, r.value)
            return r.value
          }

          return r.reason
        })
      }),
      {
        cacheKeyFn: (opts) => `${opts.key}:${opts.channel ?? config.commerceTools.priceChannelKey}`,
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<{ channel: [string], key: string }, CTProduct>}
   */
  productsByVariantKey(context) {
    return new RedisDataLoader(
      'ct:product-vkey',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.products.getProductByVariantKey(opts.key, opts.channel))
        )

        return results.map(async (r) => {
          if (r.status === 'fulfilled') {
            await context.loaders.commerceTools.products.prime({ code: r.value?.id }, r.value)
            return r.value
          }

          return r.reason
        })
      }),
      {
        cacheKeyFn: (opts) => `${opts.key}:${opts.channel ?? config.commerceTools.priceChannelKey}`,
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  productsKeysIn(context) {
    return new RedisDataLoader(
      'ct:product-keys-in',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.products.getProductsByKey(opts))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => hash(opts),
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
        tags: (products) => products.map((product) => `p:${product.id}`),
      }
    )
  },
  shippingMethodByKey(context) {
    return new RedisDataLoader(
      'ct:shipping-method-by-key',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.carts.getShippingMethodByKey(opts))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('shippingMethods'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, CTStore>}
   */
  stores(context) {
    return new RedisDataLoader(
      'ct:stores',
      new DataLoader(async () => {
        const results = await Promise.allSettled([context.dataSources.ctProject.getStore()])
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('stores'),
        redis: context.server.redis,
      }
    )
  },
  subcategories(context) {
    return new RedisDataLoader(
      'ct:subcategories',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(
          ids.map(async (idOrKey) => {
            const id = isUUID(idOrKey) ? idOrKey : (await context.loaders.commerceTools.categories.load(idOrKey))?.id

            return id ? context.dataSources.categories.getSubcategories(id) : null
          })
        )

        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  taxCategories(context) {
    return new RedisDataLoader(
      'ct:tax-cat',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getTaxCategory(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('taxCategory'),
        redis: context.server.redis,
      }
    )
  },
  taxCategoriesByKey(context) {
    return new RedisDataLoader(
      'ct:tax-cat-key',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(keys.map((key) => context.dataSources.carts.getTaxCategoryByKey(key)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('taxCategoryKey'),
        redis: context.server.redis,
      }
    )
  },
  tokens(context) {
    return new RedisDataLoader(
      'ct:token',
      new DataLoader(async (types) => {
        const results = await Promise.allSettled(
          types.map((type) => {
            if (type === CONST.commerceTools.tokenType.admin) {
              return context.dataSources.ctAuth.adminLogin()
            } else if (type === CONST.commerceTools.tokenType.read) {
              return context.dataSources.ctAuth.readLogin()
            }
            throw new Error(`invalid token type ${type}`)
          })
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('tokens'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, CTZone>}
   */
  zones(context) {
    return new RedisDataLoader(
      'ct:zone',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.carts.getZone(id)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('shippingMethods'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class CTDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createCTLoaders = (context) =>
  new Proxy(new CTDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
