// eslint-disable-next-line no-unused-vars
const RedisDataLoader = require('../RedisDataLoader')

/**
 * @typedef {{
 *   allGFSMarketingFees: RedisDataLoader<boolean, CTProduct[]>
 *   allTaxCategories: RedisDataLoader<boolean, CTTaxCategory[]>
 *   cartDiscounts: RedisDataLoader<string, CTCartDiscount>
 *   cartShippingMethods: RedisDataLoader<string, CTShippingMethod>
 *   carts: RedisDataLoader<string, CTCart>
 *   cartsAsRead: RedisDataLoader<string, CTCart>
 *   categories: RedisDataLoader<string, CTCategory>
 *   channels: RedisDataLoader<string, CTChannel>
 *   channelsByKey: RedisDataLoader<string, CTChannel>
 *   customerGroups: RedisDataLoader<string, CTCustomerGroup>
 *   customers: RedisDataLoader<string, CTCustomer>
 *   defaultShippingMethod: RedisDataLoader<string, CTShippingMethod>
 *   discountCodesByCode: RedisDataLoader<string, CTDiscountCode>
 *   discounts: RedisDataLoader<string, CTDiscountCode>
 *   orders: RedisDataLoader<string, CTOrder>
 *   ordersByCustomerEmail: RedisDataLoader<{ after?: Date, before?: Date, email: string, limit?: number, offset?: number, sort?: string, customQueryParam?: string }, CTPagedQueryResult<CTOrder>>
 *   ordersByOrderNumber: RedisDataLoader<string, CTOrder>
 *   payments: RedisDataLoader<string, CTPayment>
 *   productLists: RedisDataLoader<string, { facets: CTFacetResults, results: CTProductProjection[], total: number }>
 *   productTypes: RedisDataLoader<string, CTProductType>
 *   productTypesByKey: RedisDataLoader<string, CTProductType>
 *   products: RedisDataLoader<{ channel: [string], code: string }, CTProduct>
 *   productsByKey: RedisDataLoader<string, CTProduct>
 *   productsByVariantKey: RedisDataLoader<string, CTProduct>
 *   productsKeysIn: RedisDataLoader<string, CTProduct[]>
 *   shippingMethodByKey: RedisDataLoader<string, CTShippingMethod>
 *   stores: RedisDataLoader<string, CTStore>
 *   subcategories: RedisDataLoader<string, CTCategory[]>
 *   taxCategories: RedisDataLoader<string, CTTaxCategory>
 *   tokens: RedisDataLoader<string, CTToken>
 *   zones: RedisDataLoader<string, CTZone>
 * }} CommerceToolsDataLoaders
 */
