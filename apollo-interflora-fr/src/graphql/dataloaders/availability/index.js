const { hash } = require('../../../helpers/string')
const config = require('../../../config')
const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

const loaders = {
  moments(context) {
    return new RedisDataLoader(
      'avail:moment',
      new DataLoader(async (keys) => {
        const results = await Promise.allSettled(keys.map((opts) => context.dataSources.availability.getMoments(opts)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => hash(JSON.stringify(opts)),
        expire: getDataloaderTtl('moments'),
        redis: context.server.redis,
      }
    )
  },
  towns(context) {
    return new RedisDataLoader(
      'avail:towns',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(options.map((opts) => context.dataSources.availability.getTowns(opts)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => [opts.country, opts.code, opts.search.trim().toLowerCase()].join(':'),
        expire: getDataloaderTtl('towns'),
        redis: context.server.redis,
      }
    )
  },
  unavailabilities(context) {
    return new RedisDataLoader(
      'avail:unavailabilities',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.availability.getUnavailabilities(opts))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => [opts.code, opts.postalCode || opts.province, hash(opts)].join(':'),
        expire: getDataloaderTtl('availabilities'),
        redis: context.server.redis,
      }
    )
  },
  undeliverableDays(context) {
    return new RedisDataLoader(
      'avail:undeliverableDays',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.availability.getUndeliverableDays(opts))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.country}${opts.days}`,
        expire: getDataloaderTtl('availabilities'),
        redis: context.server.redis,
      }
    )
  },
  validateRegionCart(context) {
    return new RedisDataLoader(
      'avail:validateRegionCart',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.availability.validateRegionCart(opts))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.cartId}${opts.postalCode}`,
        expire: getDataloaderTtl('availabilities'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class AvailabilityDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createAvailabilityLoaders = (context) =>
  new Proxy(new AvailabilityDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
