const RedisDataLoader = require('../RedisDataLoader')
const DataLoader = require('dataloader')
const config = require('../../../config')
const sigBrokerTtl = config.graphql?.dataloaders?.sigBroker?.ttl ?? 3600

const loaderDefinitions = {
  sigBrokerJwks(context) {
    return new RedisDataLoader(
      'dk:sig-broker',
      new DataLoader(async (kids) => {
        const results = await Promise.allSettled(
          kids.map((kid) => context.dataSources.signaturgruppen.fetchSigningKey(kid))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        deserialize: (rawValue) => rawValue,
        expire: sigBrokerTtl,
        redis: context.server.redis,
      }
    )
  },
}
class SignaturgruppenLoaders {
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}
const createSignaturgruppenLoaders = (context) =>
  new Proxy(new SignaturgruppenLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)
      if (!target[prop] && loaderDefinitions[prop]) {
        target.map.set(prop, loaderDefinitions[prop](target.context))
        return target.map.get(prop)
      }
      return Reflect.get(...arguments)
    },
  })

module.exports = {
  createSignaturgruppenLoaders,
  loaderDefinitions,
}
