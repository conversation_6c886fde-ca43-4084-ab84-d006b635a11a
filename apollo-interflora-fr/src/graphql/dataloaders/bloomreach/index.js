const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')

const { hash } = require('../../../helpers/string')

const loaders = {
  // We need to pass the uid when making API calls to BR but we do not want to use it in the cache key
  searchProductKeys(context) {
    return new RedisDataLoader(
      'bloomreach:search',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(options.map((opts) => context.dataSources.bloomreach.search(opts)))
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => {
          // Do not use the bloomreach uid to create the cache key
          const { uid: _uid, ...option } = opts
          return hash(option)
        },
        expire: 300,
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class BloomreachDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createBloomreachLoaders = (context) =>
  new Proxy(new BloomreachDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
