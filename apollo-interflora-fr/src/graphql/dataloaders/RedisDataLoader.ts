import _ from 'lodash'
import DataLoader, { Options as DataLoaderOptions } from 'dataloader'
import { RedisClientType } from '@redis/client'

import config from '../../config'
import { makeTagKey, tagKey } from '../../helpers/cache'
import { GraphQLDataloaderError } from '../errors'

export type RedisDataLoaderDeserialize<TValue> = (_result: string) => TValue | Promise<TValue>
export type RedisDataLoaderSerialize<TValue> = (_va: TValue) => Promise<number | string | Buffer>
export type RedisDataLoaderTags<TKey, TValue> = (_val: TValue, _key: TKey) => string[]

export interface RedisDataLoaderOptions<TKey, TValue, TCacheKey = TKey>
  extends DataLoaderOptions<TKey, TValue, TCacheKey> {
  deserialize?: RedisDataLoaderDeserialize<TValue>
  expire?: number
  redis: RedisClientType
  serialize?: RedisDataLoaderSerialize<TValue>
  tags?: RedisDataLoaderTags<TKey, TValue>
}

const parse = async <TKey, TValue, TCacheKey = TKey>(
  resp: string | null,
  opt: RedisDataLoaderOptions<TKey, TValue, TCacheKey>
): Promise<'' | null | TValue> => {
  if (resp === '' || resp === null) {
    return resp
  }

  if (opt.deserialize) {
    return Promise.resolve(opt.deserialize(resp))
  }

  return JSON.parse(resp)
}

const toString = async <TKey, TValue, TCacheKey = TKey>(
  val: TValue,
  opt: RedisDataLoaderOptions<TKey, TValue, TCacheKey>
): Promise<number | string | Buffer> => {
  if (val === null) {
    return ''
  }

  if (typeof val === 'string') {
    return val
  }
  if (typeof val === 'boolean') {
    return Number(val)
  }

  if (opt.serialize) {
    return Promise.resolve(opt.serialize(val))
  }

  if (_.isObject(val)) {
    return JSON.stringify(val)
  }

  return Promise.reject(new Error(`Must be Object, String, Boolean or Null, got ${val}`))
}

const makeKey = <TKey, TValue, TCacheKey = TKey>(
  keySpace: string,
  key: TKey,
  opt: RedisDataLoaderOptions<TKey, TValue, TCacheKey>
) => `${keySpace ? `${keySpace}:` : ''}${opt.cacheKeyFn!(key)}`

export class RedisDataLoader<TKey, TValue, TCacheKey = TKey> {
  keySpace: string
  loader: DataLoader<TKey, TValue | null, TCacheKey>
  opt: Required<Pick<RedisDataLoaderOptions<TKey, TValue, TCacheKey>, 'cacheKeyFn' | 'expire' | 'redis' | 'tags'>> &
    Pick<RedisDataLoaderOptions<TKey, TValue, TCacheKey>, 'deserialize' | 'serialize'>

  constructor(
    keySpace: string,
    userLoader: DataLoader<TKey, TValue, TCacheKey>,
    opt: RedisDataLoaderOptions<TKey, TValue, TCacheKey>
  ) {
    const customOptions = ['cacheKeyFn', 'deserialize', 'expire', 'redis', 'serialize', 'tags']
    this.opt = {
      ...(_.pick(opt, customOptions) as RedisDataLoaderOptions<TKey, TValue, TCacheKey>),
      cacheKeyFn: opt.cacheKeyFn ?? ((k) => (_.isObject(k) ? JSON.stringify(k) : k) as TCacheKey),
      expire: opt.expire ?? config.redis.defaultTTL,
      tags: opt.tags ?? (() => []),
    }

    this.keySpace = keySpace

    /** @type {DataLoader<K, V, C>} */
    this.loader = new DataLoader(async (keys) => {
      const results = await this.rMGet(keys as TKey[])
      return (
        await Promise.allSettled(
          results.map((v, i) => {
            if (v === '') return null

            if (v === null) {
              return userLoader
                .load(keys[i])
                .then((resp) => this.rSetAndGet(keys[i], resp))
                .then((r) => (r === '' ? null : r))
            }

            return v
          })
        )
      ).map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
    }, _.chain(opt).omit(customOptions).extend({ cacheKeyFn: this.opt.cacheKeyFn }).value())
  }

  async load(key: TKey): Promise<TValue | null> {
    if (!key) {
      throw new TypeError('key parameter is required')
    }

    return this.loader.load(key).catch((e) => {
      const err = new GraphQLDataloaderError(e)
      err.extensions.keySpace = this.keySpace
      err.extensions.key = key
      throw err
    })
  }

  async loadMany(keys: TKey[]): Promise<(TValue | null | Error)[]> {
    if (!Array.isArray(keys)) {
      new TypeError('keys parameter is required')
    }

    return this.loader.loadMany(keys).catch((e) => {
      const err = new GraphQLDataloaderError(e)
      err.extensions.keySpace = this.keySpace
      err.extensions.keys = keys
      throw err
    })
  }

  async prime(key: TKey, val: TValue): Promise<this> {
    if (!key) {
      throw new TypeError('key parameter is required')
    }

    if (val === undefined) {
      throw new TypeError('value parameter is required')
    }

    const r = await this.rSetAndGet(key, val)
    this.loader.clear(key).prime(key, r === '' ? null : r)

    return this
  }

  async clear(key: TKey): Promise<this> {
    if (!key) {
      throw new TypeError('key parameter is required')
    }

    await this.rDel(key)
    this.loader.clear(key)

    return this
  }

  clearAllLocal() {
    this.loader.clearAll()
    return this
  }

  async clearByTag(tag: string) {
    const tagKey = makeTagKey(tag)

    const keys = await this.opt.redis.SMEMBERS(tagKey)
    return Promise.all([...keys.map((key) => this.clear(key as TKey)), this.opt.redis.DEL(tagKey)])
  }

  clearLocal(key: TKey) {
    this.loader.clear(key)
    return this
  }

  async rSetAndGet(key: TKey, rawVal: TValue) {
    const fullKey = makeKey(this.keySpace, key, this.opt)

    let val
    try {
      val = await toString(rawVal, this.opt)
    } catch (e) {
      throw new Error(`[${fullKey}] ${(e as Error).message}`)
    }

    if (config.graphql.useCache) {
      await Promise.all([
        this.opt.redis.SET(fullKey, val, {
          expiration: { type: 'EX', value: this.opt.expire },
        }),
        tagKey(fullKey, this.opt.tags(rawVal, key), this.opt.redis),
      ])
    }

    return rawVal
  }

  async rMGet(keys: TKey[]) {
    const results = await this.opt.redis.MGET(_.map(keys, (k) => makeKey(this.keySpace, k, this.opt)))

    return Promise.all(results.map((r) => parse<TKey, TValue, TCacheKey>(r, this.opt)))
  }

  /**
   * @param {K} key
   * @returns {Promise<number>}
   * @private
   */
  async rDel(key: TKey) {
    return this.opt.redis.DEL(makeKey<TKey, TValue, TCacheKey>(this.keySpace, key, this.opt))
  }
}

module.exports = RedisDataLoader
