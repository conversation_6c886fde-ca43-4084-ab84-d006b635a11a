const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || 300
}

const loaders = {
  /**
   * Load occasion reminders for a user
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader}
   */
  remindersByUser(context) {
    return new RedisDataLoader(
      'occasionreminder:by-user',
      new DataLoader(async (userIds) => {
        const results = await Promise.allSettled(
          userIds.map((userId) => context.dataSources.occasionReminders.getRemindersByUser(userId))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (userId) => userId,
        expire: getDataloaderTtl('remindersByUser'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class OccasionReminderDataLoaders {
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createOccasionReminderDataLoaders = (context) =>
  new Proxy(new OccasionReminderDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
