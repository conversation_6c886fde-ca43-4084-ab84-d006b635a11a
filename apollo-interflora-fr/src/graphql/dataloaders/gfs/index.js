const DataLoader = require('dataloader')
const apm = require('elastic-apm-node')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

/**
 * @param {PromiseSettledResult<any>} result
 * @returns {any|null}
 */
const resultOrNullOnError = (result) => {
  if (result.status === 'fulfilled') {
    return result.value
  } else {
    apm.captureError(result.reason, { custom: result.reason })

    return null
  }
}

const loaders = {
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, GFSCategory[]>}
   */
  allCategories(context) {
    return new RedisDataLoader(
      'gfs:categories',
      new DataLoader(async () => {
        const categories = await context.dataSources.gfs.getCategories()
        categories.forEach((category) => {
          context.loaders.gfs.categories.prime(category.Id, category)
        })

        return [categories]
      }),
      {
        expire: getDataloaderTtl('gfsCategories'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, GFSCountry[]>}
   */
  allCountries(context) {
    return new RedisDataLoader(
      'gfs:countries',
      new DataLoader(() => context.dataSources.gfs.getCountries().then((countries) => [countries])),
      {
        expire: getDataloaderTtl('gfsCountries'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, GFSProduct[]>}
   */
  allProducts(context) {
    return new RedisDataLoader(
      'gfs:products',
      new DataLoader((countryId) => context.dataSources.gfs.getProducts(countryId).then((products) => [products])),
      {
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, GFSCategory>}
   */
  categories(context) {
    return new RedisDataLoader(
      'gfs:category',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.gfs.getCategory(id)))

        return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
      }),
      {
        expire: getDataloaderTtl('gfsCategories'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<boolean, DatoInternationalCategoryPage>}
   */
  categoryPage(context) {
    return new RedisDataLoader(
      'gfs:categoryPage',
      new DataLoader(async (options) =>
        (
          await Promise.allSettled(
            options.map((option) =>
              context.dataSources.datocms.getInternationalCategoryPage(option.locale, option.slug)
            )
          )
        ).map(resultOrNullOnError)
      ),
      {
        expire: getDataloaderTtl('gfsCategories'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {DataLoader<string, GFSCountry>}
   */
  countries(context) {
    const idsMap = {
      GB: 'UK',
    }
    return new DataLoader(async (ids) => {
      const countries = await context.loaders.gfs.allCountries.load(true)

      return ids.map((id) => {
        const countryId = idsMap[id] || id
        const country = countries.find((country) => country.Enabled && country.CountryCode === countryId)
        return country
      })
    })
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {DataLoader<string, GFSHoliday>}
   */
  holidays(context) {
    return new RedisDataLoader(
      'gfs:holidays',
      new DataLoader(async (locales) =>
        (await Promise.allSettled(locales.map((locale) => context.dataSources.gfs.getHolidays(locale)))).map(
          resultOrNullOnError
        )
      ),
      {
        expire: getDataloaderTtl('gfsHolidays'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {DataLoader<string, DatoInternationalProduct>}
   */
  productPage(context) {
    return new RedisDataLoader(
      'gfs:productPage',
      new DataLoader(async (locales) =>
        (
          await Promise.allSettled(locales.map((locale) => context.dataSources.datocms.getInternationalProduct(locale)))
        ).map(resultOrNullOnError)
      ),
      {
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
      }
    )
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {DataLoader<{ code: string, countryId: string }, GFSProduct>}
   */
  products(context) {
    return new DataLoader(async (keys) => {
      // assume all keys have the same countryId (should have)
      const products = await context.loaders.gfs.allProducts.load(keys[0].countryId)

      const codes = keys.map((k) => k.code)
      const map = new Map()
      for (const product of products) {
        if (codes.includes(`${product.IntercatCode}`)) {
          map.set(`${product.IntercatCode}`, product)
          if (map.size === keys.length) break
        }
      }

      return keys.map((key) => map.get(key.code))
    })
  },
  /**
   * @param {CTDatocmsContext} context
   * @returns {RedisDataLoader<number, GFSUnit>}
   */
  units(context) {
    return new RedisDataLoader(
      'gfs:unit',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.gfs.getUnit(id)))
        return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
      }),
      {
        expire: getDataloaderTtl('gfsCountries'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class GFSDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createGFSLoaders = (context) =>
  new Proxy(new GFSDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
