// eslint-disable-next-line no-unused-vars
const RedisDataLoader = require('../RedisDataLoader')

/**
 * @typedef {{
 *  accessoryCategoryDefaultSettings: RedisDataLoader<string, DatoAccessoryCategory>
 *  accessoryInfos: RedisDataLoader<string, DatoAccessoryInfo>
 *  categories: RedisDataLoader<{ locale: string, slug: string }, DatoCategoryPage>
 *  categorySlugsById: RedisDataLoader<{ id: string, locale: string }, string>
 *  categoryTree: RedisDataLoader<string, DatoCategoryPage[]>
 *  configuration: RedisDataLoader<boolean, DatoConfiguration>
 *  defaultBlock: RedisDataLoader<{ locale: string, pageType: string }, DatoDefaultBlock>
 *  floristPage: RedisDataLoader<{ locale: string, slug: string }, DatoFloristPage>
 *  floristRegion: RedisDataLoader<{ locale: string, slug: string }, DatoFloristRegion>
 *  floristRegions: RedisDataLoader<string, DatoFloristRegion[]>
 *  footers: RedisDataLoader<string, DatoFooterNavigation>
 *  landingPages: RedisDataLoader<{ locale: string, slug: string }, DatoLandingPage>
 *  legalText: RedisDataLoader<string, DatoLegalText>
 *  mainNavigation: RedisDataLoader<{ locale: string }, DatoMainNavigation>
 *  messageCards: RedisDataLoader<{ locale: string, warehouseType: string }, DatoMessageCardRecord[]>
 *  pages: RedisDataLoader<{ locale: string, slug: string }, DatoPage>
 *  productCodesByIdOrKey: RedisDataLoader<{ code: string, locale: string }, string>
 *  productCodesBySlug: RedisDataLoader<{ locale: [string], seaKey: [string], slug: string }, string>
 *  products: RedisDataLoader<{ code: string, locale: [string], seaKey: [string] }, DatoProduct>
 *  storeLocatorHome: RedisDataLoader<string, DatoStoreLocatorHome>
 * }} DatocmsDataLoaders
 */
