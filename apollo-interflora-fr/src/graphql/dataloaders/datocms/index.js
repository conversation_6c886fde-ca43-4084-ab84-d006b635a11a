const apm = require('elastic-apm-node')

const config = require('../../../config')

const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')
const { getNavigationTypeForContext } = require('../../../helpers/digitalCatalog')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || 3600
}

const loaders = {
  accessoryCategoryDefaultSettings(context) {
    return new RedisDataLoader(
      'dato:accessoryCategoryDefaults',
      new DataLoader(async (categoryIds) => {
        const results = await Promise.allSettled(
          categoryIds.map((id) => context.dataSources.datocms.getAccessoryCategory(id))
        )

        return results.map((result) => {
          if (result.status === 'fulfilled') {
            return result.value?.defaultSettings ?? []
          }

          return []
        })
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  accessoryInfos(context) {
    return new RedisDataLoader(
      'dato:accessoryInfo',
      new DataLoader(async (productIds) => {
        const results = await Promise.allSettled(
          productIds.map((id) => context.dataSources.datocms.getAccessoryInfo(id, context.language))
        )

        return results.map((result) => {
          if (result.status === 'fulfilled') {
            return result.value
          }

          return null
        })
      }),
      {
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  categories(context) {
    return new RedisDataLoader(
      'dato:category',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getCategoryPageBySlug(opts.slug, opts.locale))
        )

        return Promise.all(
          results.map(async (result, i) => {
            if (result.status === 'fulfilled') {
              if (result.value?.commercetoolsCategory) {
                await context.loaders.datocms.categorySlugsById.prime(
                  {
                    id: result.value.commercetoolsCategory,
                    locale: options[i].locale || context.language,
                  },
                  result.value.slug
                )
                return result.value
              }
              return null
            }
            return result.reason
          })
        )
      }),
      {
        cacheKeyFn: (opts) => `${opts.slug}-${opts.locale}`,
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
        tags: (_category, key) => {
          const tag = `c:${key.slug}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  categorySlugsById(context) {
    return new RedisDataLoader(
      'dato:cat-id2slug',
      new DataLoader(async (opts) => {
        const results = await Promise.allSettled(
          opts.map(async ({ id, locale = context.language }) => {
            let ctCategory
            try {
              ctCategory = await context.loaders.commerceTools.categories.load(id)
            } catch (e) {
              // we do not throw (for now) on missing datocms categories
              apm.captureError(e, { custom: e })
              return null
            }

            let category = await context.dataSources.datocms.getCategoryPageById(id, locale)
            if (!category) {
              category = await context.dataSources.datocms.getCategoryPageById(ctCategory.key, locale)
            }

            if (category) {
              await context.loaders.datocms.categories.prime({ locale, slug: category.slug }, category)
            }

            return category
          })
        )
        return results.map((result) => (result.status === 'fulfilled' ? result.value?.slug ?? null : result.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.id}-${opts.locale}`,
        deserialize: (rawValue) => rawValue,
        expire: getDataloaderTtl('categories'),
        redis: context.server.redis,
      }
    )
  },
  categoryTree(context) {
    return new RedisDataLoader(
      'dato:cat-tree',
      new DataLoader(async (locales) => {
        const results = await Promise.allSettled(
          locales.map((locale) => context.dataSources.datocms.getCategoryTree(locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
      }
    )
  },
  configuration(context) {
    return new RedisDataLoader(
      'dato:configuration',
      new DataLoader(async (locales) => {
        const results = await Promise.allSettled(
          locales.map((locale) => context.dataSources.datocms.getConfiguration(locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: () => 'conf',
        expire: getDataloaderTtl('configuration'),
        redis: context.server.redis,
      }
    )
  },
  defaultBlock(context) {
    return new RedisDataLoader(
      'dato:default-block',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getDefaultBlockByPageType(opts.pageType, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.pageType}-${opts.locale}`,
        expire: getDataloaderTtl('defaultBlocks'),
        redis: context.server.redis,
      }
    )
  },
  floristPage(context) {
    return new RedisDataLoader(
      'dato:florist-page',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getFloristPage(opts.slug, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.slug}-${opts.locale}`,
        expire: getDataloaderTtl('getDepartments'),
        redis: context.server.redis,
      }
    )
  },
  floristRegion(context) {
    return new RedisDataLoader(
      'dato:florist-region',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getFloristRegion(opts.slug, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.slug}-${opts.locale}`,
        expire: getDataloaderTtl('getDepartments'),
        redis: context.server.redis,
      }
    )
  },
  floristRegions(context) {
    return new RedisDataLoader(
      'dato:florist-regions',
      new DataLoader(async (locales) => {
        const results = await Promise.allSettled(
          locales.map((locale) => context.dataSources.datocms.getFloristRegions(locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('getDepartments'),
        redis: context.server.redis,
      }
    )
  },
  footers(context) {
    return new RedisDataLoader(
      'dato:footer',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(
          ids.map((id) => context.dataSources.datocms.getFooterNavigation(context.language, id))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
      }
    )
  },
  landingPages(context) {
    return new RedisDataLoader(
      'dato:landing',
      new DataLoader(async (opts) => {
        const results = await Promise.allSettled(
          opts.map((opts) => context.dataSources.datocms.getLandingPage(opts.slug, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.slug}-${opts.locale}`,
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
        tags: (_page, key) => {
          const tag = `pg:${key.slug}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  legalText(context) {
    return new RedisDataLoader(
      'dato:legal-text',
      new DataLoader(async (productTypes) => {
        const results = await Promise.allSettled(
          productTypes.map((productType) => context.dataSources.datocms.getLegalText(productType, context.language))
        )

        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (type) => `${type}-${context.language}`,
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
      }
    )
  },
  mainNavigation(context) {
    const mainNavigationType = getNavigationTypeForContext(context)
    return new RedisDataLoader(
      `dato:main-navigation:${mainNavigationType}`,
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getMainNavigation(opts.locale, mainNavigationType))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${mainNavigationType}_${opts.locale}`,
        expire: getDataloaderTtl('mainNavigation'),
        redis: context.server.redis,
      }
    )
  },
  messageCards(context) {
    return new RedisDataLoader(
      'dato:message-cards',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) => context.dataSources.datocms.getMessageCards(opts.warehouseType, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.locale}`,
        expire: getDataloaderTtl('messageCards'),
        redis: context.server.redis,
      }
    )
  },
  pages(context) {
    return new RedisDataLoader(
      'dato:page',
      new DataLoader(async (opts) => {
        const results = await Promise.allSettled(
          opts.map((opts) => context.dataSources.datocms.getPage(opts.slug, opts.locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.slug}-${opts.locale}`,
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
        tags: (_page, key) => {
          const tag = `pg:${key.slug}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  productCodesByIdOrKey(context) {
    // given a product id or key, returns the value used in commercetoolsProduct attribute
    /** @type {RedisDataLoader<{ code: string, locale: [string] }, string>} */
    return new RedisDataLoader(
      'dato:product-code-code',
      new DataLoader(async (keys) =>
        keys.map(() => Promise.reject(new Error(`loader should not be called - received ${JSON.stringify(keys)}`)))
      ), // not used - is primed manually
      {
        cacheKeyFn: (key) => `${key.code}:${key.locale || context.language}`,
        deserialize: (rawValue) => rawValue,
        expire: getDataloaderTtl('productCodesBySlugUrls'),
        redis: context.server.redis,
        tags: (code) => {
          const tag = `p:${code}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  productCodesBySlug(context) {
    /** @type {RedisDataLoader<{ locale: [string], seaKey: [string], slug: string }, string>} */
    return new RedisDataLoader(
      'dato:product-code-slug',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((option) =>
            context.dataSources.datocms.getProductBySlug(option.slug, option.locale || context.language, option.seaKey)
          )
        )

        return Promise.all(
          results.map(async (result, i) => {
            if (result.status === 'fulfilled') {
              const key = {
                code: result.value.commercetoolsProduct,
                locale: options[i].locale || context.language,
                seaKey: options[i].seaKey,
              }
              await Promise.all([
                context.loaders.datocms.products.prime(key, result.value),
                context.loaders.datocms.productCodesByIdOrKey.prime(key, result.value.commercetoolsProduct),
              ])
              return result.value.commercetoolsProduct
            }

            return result.reason
          })
        )
      }),
      {
        cacheKeyFn: (key) => `${key.slug}:${key.locale || context.language}`,
        deserialize: (rawValue) => rawValue,
        expire: getDataloaderTtl('productCodesBySlugUrls'),
        redis: context.server.redis,
      }
    )
  },
  products(context) {
    /** @type {RedisDataLoader<{ code: string, locale: [string], seaKey: [string] }, DatoProduct>} */
    return new RedisDataLoader(
      'dato:products',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((opts) =>
            context.dataSources.datocms.getProduct(opts.code, opts.locale || context.language, opts.seaKey)
          )
        )

        return Promise.all(
          results.map(async (result, i) => {
            if (result.status === 'fulfilled') {
              await context.loaders.datocms.productCodesByIdOrKey.prime(
                {
                  code: result.value.commercetoolsProduct,
                  locale: options[i].locale || context.locale,
                  seaKey: options[i].seaKey,
                },
                result.value.commercetoolsProduct
              )
              return result.value
            }
            return result.reason
          })
        )
      }),
      {
        cacheKeyFn: (key) => `${key.code}:${key.locale || context.language}:${key.seaKey}`,
        expire: getDataloaderTtl('products'),
        redis: context.server.redis,
        tags: (product) => {
          if (!product?.commercetoolsProduct) return []
          const tag = `p:${product.commercetoolsProduct}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    )
  },
  storeLocatorHome(context) {
    return new RedisDataLoader(
      'dato:storelocator-config',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((locale) => context.dataSources.datocms.getStoreLocatorHome(locale))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('storeLocatorHome'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class DatoDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createDatoLoaders = (context) =>
  new Proxy(new DatoDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
