const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || 300
}

const loaders = {
  publicSearch(context) {
    return new RedisDataLoader(
      'hitta:publicSearch',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map((option) => {
            return context.dataSources.hitta.publicSearch(option.search, option.searchType)
          })
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opt) => opt,
        expire: getDataloaderTtl('publicSearch'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class HittaDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createHittaDataLoaders = (context) =>
  new Proxy(new HittaDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
