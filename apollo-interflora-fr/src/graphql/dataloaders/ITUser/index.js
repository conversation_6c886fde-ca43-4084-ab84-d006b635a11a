const DataLoader = require('dataloader')
const RedisDataLoader = require('../RedisDataLoader')
const config = require('../../../config')

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

const loaders = {
  addresses(context) {
    return new RedisDataLoader(
      'ituser:addresses',
      new DataLoader(async (opts) => {
        const results = await Promise.allSettled(
          opts.map((opt) => context.dataSources.itUser.getAddresses(opt.userId, opt.type))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        cacheKeyFn: (opts) => `${opts.userId}:${opts.addressType}`,
        expire: getDataloaderTtl('users'),
        redis: context.server.redis,
      }
    )
  },
  userByEmail(context) {
    return new RedisDataLoader(
      'ituser:email',
      new DataLoader(async (emails) => {
        const results = await Promise.allSettled(
          emails.map((email) => context.dataSources.itUser.getUserByEmail(email))
        )

        return Promise.all(
          results.map(async (result) => {
            if (result.status === 'fulfilled' && result.value.id) {
              await context.loaders.itUser.userById.prime(result.value.id, result.value)

              return result.value
            }

            return null
          })
        )
      }),
      {
        expire: getDataloaderTtl('itUserUserByEmail'),
        redis: context.server.redis,
      }
    )
  },
  userById(context) {
    return new RedisDataLoader(
      'ituser',
      new DataLoader(async (ids) => {
        const results = await Promise.allSettled(ids.map((id) => context.dataSources.itUser.getUser(id)))
        return Promise.all(
          results.map(async (result) => {
            if (result.status === 'fulfilled') {
              await context.loaders.itUser.userById.prime(result.value.id, result.value)
              return result.value
            }

            return null
          })
        )
      }),
      {
        expire: getDataloaderTtl('itUserUserById'),
        redis: context.server.redis,
      }
    )
  },
}

exports.loaderDefinitions = loaders

class ITUserDataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context
    this.map = new Map()
  }
}

exports.createITUserLoaders = (context) =>
  new Proxy(new ITUserDataLoaders(context), {
    get(target, prop, _receiver) {
      if (target.map.has(prop)) return target.map.get(prop)

      if (!target[prop] && loaders[prop]) {
        target.map.set(prop, loaders[prop](target.context))
        return target.map.get(prop)
      }

      return Reflect.get(...arguments)
    },
  })
