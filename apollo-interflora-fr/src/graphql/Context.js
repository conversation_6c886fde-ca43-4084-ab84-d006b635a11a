const { Redis: IORedis } = require('ioredis')
const { Redlock, ResourceLockedError } = require('@sesamecare-oss/redlock')

const config = require('../config')
const { createDataSources } = require('./datasources')
const CONST = require('../const')

/** @typedef {import('fastify').FastifyInstance} FastifyInstance */
/** @typedef {import('fastify').FastifyRequest} FastifyRequest */
/** @typedef {import('fastify').FastifyReply} FastifyReply */
/** @typedef {import('../plugins/session/session').Session} SessionInstance */
/** @typedef {import('./datasources/_typedef')} */

/** @type {IORedis} */
let redis
/** @type {Redlock} */
let redlock

const getCartIdFromReq = (req) => {
  const apiCart = req.requestContext.get('session')?.apiCart

  return apiCart?.id ?? apiCart?.guid ?? apiCart?.code
}

class GraphQLContext {
  /**
   * @param {FastifyInstance} fastify
   * @param {FastifyRequest} req
   * @param {FastifyReply} reply
   */
  constructor(fastify, req, reply) {
    this.server = fastify
    this.req = req
    this.reply = reply

    /**
     * @type {{
     *    isAnonymous: boolean
     *    isPro: boolean
     *    isRefreshToken: boolean
     *    login: string
     *    oauthToken: string
     *    scope: string
     *    sessionId: string
     * }}
     */
    this.auth = this.req.requestContext.get('auth')

    this.cacheTags = new Set()

    /** @type {string} */
    this.categoryId

    /** @type {string} */
    this._countryId

    /** @type {string} */
    this._cartCountryId

    /** @type {DataSources} */
    this._dataSources

    /** @type {DataLoaders} */
    this.dataloaders

    /** @type {string} */
    this.device

    /** @type {string?} */
    this.jwt = this.req.requestContext.get('jwt')

    /** @type {string} */
    this.language

    /** @type {string} */
    this.partner

    /** @type {Resolvers} */
    this._resolvers

    /** @type {SessionInstance} */
    this.session = this.req.requestContext.get('session')

    /** @type {string} */
    this.siteId

    /** @type {string} */
    this.varyCache

    /** @type {string} */
    this.zoneGeoId

    this.log = this.server.log.child({
      auth: this.req.requestContext.get('auth') || {
        isAnonymous: true,
        login: 'anonymous',
      },
      cartId: getCartIdFromReq(this.req),
      ip: this.ip,
      method: this.req.method,
      referer: this.req.headers['referer'],
    })
  }

  /**
   * @return {AppEnvironment}
   */
  get appConfig() {
    return config.apps[this.siteId] || config.apps[config.apps.defaultSiteId]
  }

  /**
   * @returns {string}
   */
  get baseSiteId() {
    if (this.isDefaultCountry() && config.apps[this.siteId] && this.siteId !== config.apps.defaultSiteId)
      return this.siteId

    return this.device ? `${this.device.toLowerCase()}-${this.countryId}` : this.countryId
  }

  /**
   * @returns {string | undefined}
   */
  get clientSessionId() {
    return this.req.headers['x-apollo-sessid']
  }

  get countryId() {
    return this._countryId || (config.apps[this.siteId] || config.apps[config.apps.defaultSiteId]).country
  }

  set countryId(value) {
    this._countryId = value
  }

  get cartCountryId() {
    return this.session?.apiCart?.countryId
  }

  set cartCountryId(value) {
    this._cartCountryId = value
  }

  /**
   * @returns {DataSources}
   */
  get dataSources() {
    if (!this._dataSources) {
      this._dataSources = createDataSources({
        cache: this.server.redis,
        context: this,
      })
    }
    return this._dataSources
  }

  /**
   * @returns {string}
   */
  get ip() {
    return this.req.headers['true-client-ip'] || this.req.headers['x-real-ip'] || this.req.ip
  }

  /**
   * @template T
   * @returns {T}
   */
  get loaders() {
    return this.resolvers.dataloaders
  }

  get redlock() {
    if (!redlock) {
      if (!redis) {
        redis = new IORedis(config.redis)
      }
      redlock = new Redlock([redis], {
        retryCount: 60,
        retryDelay: 2000, // 2s
      })
      redlock.on('error', (err) => {
        if (err instanceof ResourceLockedError) {
          return
        }
        this.log.error(`[redlock] ${err.message}`)
      })
    }
    return redlock
  }

  /**
   * @returns {Resolvers}
   */
  get resolvers() {
    if (!this._resolvers) {
      const { createResolvers } = require(`./resolvers/${config.site}`)
      this._resolvers = createResolvers(this)
    }

    return this._resolvers
  }

  /**
   * @returns {boolean}
   */
  isDefaultCountry() {
    const site = config.apps[this.siteId]

    if (!this.countryId || !site) {
      return false
    }

    return this.countryId.toLowerCase() === site.country.toLowerCase()
  }

  /**
   * Check if context's device is the mobile app
   * @returns {boolean}
   */
  isMobileApp() {
    return [
      CONST.devices.appAndroid.toUpperCase(),
      CONST.devices.appIOS.toUpperCase(),
      CONST.devices.deprecatedAppAndroid.toUpperCase(),
      CONST.devices.deprecatedAppIOS.toUpperCase(),
    ].includes(this.device.toUpperCase())
  }

  /**
   * @returns {boolean}
   */
  isSameCountryIdInCart() {
    if (!this.cartCountryId) {
      return this.isDefaultCountry()
    }
    if (this.countryId) {
      return this.cartCountryId.toLowerCase() === this.countryId.toLowerCase()
    } else {
      return this.cartCountryId.toLowerCase() === config.apps[this.siteId]?.country?.toLowerCase()
    }
  }

  /**
   * @param {string} key
   * @param {Function} fn
   * @param {{ ttl: number }} opts
   */
  async lock(key, fn, opts = {}) {
    // Using a long ttl by default to work-around [this bug](https://github.com/mike-marcacci/node-redlock/issues/168)
    const { ttl = this.redlock.settings.retryCount * this.redlock.settings.retryDelay } = opts

    return this.redlock.using([`redlock:${key}`], ttl, async (signal) => {
      const result = await fn()

      if (signal.aborted) throw signal.error
      return result
    })
  }

  /**
   * Creates outside of graphql
   *
   * @param {FastifyInstance} fastity
   * @param {FastifyRequest} request
   * @param {FastifyReply} response
   * @returns {GraphQLContext}
   */
  static async create(fastity, request, response) {
    const context = new GraphQLContext(fastity, request, response)

    context.siteId = config.apps.defaultSiteId

    return context
  }
}

module.exports = GraphQLContext
