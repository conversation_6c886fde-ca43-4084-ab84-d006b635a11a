const _ = require('lodash')
const apm = require('elastic-apm-node')
const soap = require('soap')

const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')

const { languageCodes, uicCodes } = require('./helpers')
const { removeAccents } = require('../../../helpers/string')

/**
 * @typedef {import('./_typedef')}
 * @typedef {import('fastify').FastifyInstance} FastifyInstance
 */

let _client

class Axerve extends RESTDataSource {
  /**
   * @param {string} cartId
   * @returns {Promise<{
   *  bankTransactionId: string,
   *  paymentID: string,
   *  shopTransactionId: string,
   *  transactionResult: ('OK'|'KO'),
   *  transactionType: 'cancel'
   * }>}
   */
  async cancel(cartId) {
    const body = {
      shopLogin: config.axerve.shopLogin,
      shopTransactionID: cartId,
    }
    return this.post('payment/cancel', { body })
  }

  /**
   * @returns {Promise<soap.Client>}
   */
  client() {
    if (!_client) {
      _client = soap.createClientAsync(config.axerve.soap.cryptWSDL)
    }

    return _client
  }

  /**
   * @param {string} encrypted
   * @returns {Promise<{
   *  TransactionType: string,
   *  TransactionResult: string,
   *  ShopTransactionId: string,
   *  BankTransactionID: string,
   *  AuthorizationCode: string,
   *  Currency: string,
   *  Amount: string,
   *  Country: string,
   *  CustomInfo: string,
   *  Buyer: {
   *    BuyerEmail: string,
   *    BuyerName: string
   *  },
   *  TDLevel: string,
   *  ErrorCode: string,
   *  ErrorDescription: string,
   *  AlertCode: string,
   *  AlertDescription: string,
   *  CVVPresent: string,
   *  MaskedPAN: string,
   *  PaymentMethod: string,
   *  TOKEN: string,
   *  ProductType: string,
   *  TokenExpiryMonth: string,
   *  TokenExpiryYear: string,
   *  TransactionKey: string,
   *  VbV: string,
   *  VbVRisp: string,
   *  VbVBuyer: string,
   *  VbVFlag: string,
   *  RiskResponseCode: string,
   *  RiskResponseDescription: string,
   *  ThreeDS: Object
   * }>}
   */
  async decrypt(encrypted) {
    const client = await this.client()
    const opts = {
      CryptedString: encrypted,
      shopLogin: config.axerve.shopLogin,
    }
    if (config.tokens.axerveApiKey && config.tokens.axerveApiKey !== '') {
      opts.apikey = config.tokens.axerveApiKey
    }
    const response = await client.DecryptAsync(opts)

    const result = response?.[0]?.DecryptResult?.GestPayCryptDecrypt
    if (!result) {
      throw new Error(`Error calling axerveDecrypt\n${JSON.stringify(result)}`)
    }

    return result
  }

  /**
   * @param {AxerveEncryptPayload} payload
   * @returns {Promise<AxerveEncryptResponse>}
   */
  async encrypt(payload) {
    const addressesMatch = _.isEqual(payload.billingAddress, payload.shippingAddress)

    const _sanitizeName = (name) => removeAccents(name).replace(/ +/g, '').toLowerCase()

    const sanitizedBillingName = _sanitizeName(`${payload.billingAddress.firstName}${payload.billingAddress.lastName}`)
    const sanitizedShippingName = _sanitizeName(
      `${payload.shippingAddress.firstName}${payload.shippingAddress.lastName}`
    )

    const _payload = {
      amount: payload.amount,
      shopLogin: config.axerve.shopLogin,
      shopTransactionId: payload.id,
      transDetails: {
        threeDsContainer: {
          buyerDetails: {
            acctInfo: {
              chAccAgeInd: '01',
              paymentAccInd: '01',
              shipNameIndicator: sanitizedBillingName === sanitizedShippingName ? '01' : '02', // nb: using billing
            },
            addrMatch: addressesMatch ? 'Y' : 'N',
            billingAddress: payload.billingAddress,
            cardHolder: {
              email: payload.billingAddress.email,
              name: `${payload.billingAddress.firstName} ${payload.billingAddress.lastName}`.trim(),
            },
            merchantRiskIndicator: {
              deliveryTimeframe:
                (payload.deliveryDate || new Date()).getTime() - Date.now() >= 2 * 24 * 3600 * 1000 ? '04' : '02',
              shipIndicator: addressesMatch ? '01' : '03',
            },
            profileDetails: {
              authMethod: '01',
            },
            shippingAddress: payload.shippingAddress,
          },
        },
        type: 'EC',
      },
      uicCode: uicCodes[payload.currency],
    }

    if (Number.parseFloat(payload.amount) <= config.axerve.TRALimit) {
      _payload.transDetails.threeDSAuthResult = {
        authenticationLevel: 'TR',
      }
    }

    if (config.tokens.axerveApiKey && config.tokens.axerveApiKey !== '') {
      _payload.apikey = config.tokens.axerveApiKey
    }

    const client = await this.client()
    const response = await client.EncryptAsync(_payload)

    return response
  }

  /**
   * @param {AxerveEncryptPayload} payload
   * @returns {Promise<AxerveEncryptResponse>}
   */
  async encryptPaypal(payload) {
    const _payload = {
      amount: payload.amount,
      buyerEmail: payload.billingAddress.email,
      buyerName: `${payload.billingAddress.firstName} ${payload.billingAddress.lastName}`,
      languageId: languageCodes[payload.language],
      paymentTypes: [{ paymentType: 'PAYPAL' }],
      ppSellerProtection: '1',
      shippingDetails: {
        shipToCity: payload.shippingAddress.city,
        shipToCountryCode: payload.shippingAddress.country,
        shipToName: `${payload.shippingAddress.firstName} ${payload.shippingAddress.lastName}`,
        shipToState: payload.shippingAddress.state,
        shipToStreet: payload.shippingAddress.line1,
        shipToStreet2: payload.shippingAddress.line2,
        shipToZip: payload.shippingAddress.postCode || payload.shippingAddress.city,
      },
      shopLogin: config.axerve.shopLogin,
      shopTransactionId: payload.id,
      uicCode: uicCodes[payload.currency],
    }

    if (config.tokens.axerveApiKey && config.tokens.axerveApiKey !== '') {
      _payload.apikey = config.tokens.axerveApiKey
    }

    const client = await this.client()
    const response = await client.EncryptAsync(_payload)

    return response
  }

  async getDetails(cartId) {
    return this.post('payment/detail', {
      body: {
        shopLogin: config.axerve.shopLogin,
        shopTransactionID: cartId,
      },
    })
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.axerve.rest.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {Promise}
   */
  async willSendRequest(_path, request) {
    request.headers.authorization = `apikey ${config.tokens.axerveApiKey}`
  }

  /**
   * @param {FastifyInstance} fastify
   * @param {*} _options
   * @param {Function} done
   */
  static fastifyPlugin(fastify, _options, done) {
    fastify.all('/graphql/axerve/s2s', async (request, reply) => {
      const conf = config.apps[config.apps.defaultSiteId]

      // let 3s to prefer human confirmation
      setTimeout(() => {
        // call graphql createOrder - do not wait for response
        fastify
          .inject({
            body: JSON.stringify({
              query: `
            mutation AxerveS2S($context: InputContext!, $query: String!) {
              confirmRedirectOrder(
                context: $context, 
                urlPath: "s2s",
                urlQuery: $query
              ) {
                status
              }
            }
          `,
              variables: {
                context: {
                  countryId: conf.country,
                  language: conf.language,
                  siteId: config.apps.defaultSiteId,
                },
                query: `a=${request.query.a}&b=${request.query.b}`,
              },
            }),
            headers: {
              'Content-Type': 'application/json',
            },
            method: 'POST',
            url: '/graphql',
          })
          .then((response) => {
            const { statusCode, statusMessage } = response
            fastify.log.info('axerve s2s: %O', {
              json: response.json(),
              query: `a=${request.query.a}&b=${request.query.b}`,
              statusCode,
              statusMessage,
            })
          })
          .catch((e) => apm.captureError(e, { custom: e }))
      }, 3000)

      return reply.status(200).send('OK')
    })
    done()
  }
}

module.exports = Axerve
