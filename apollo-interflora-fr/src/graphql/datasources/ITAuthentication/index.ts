import config from '../../../config'
import { RESTDataSource, RESTDataSourceOptions } from '../RESTDataSource'

// export interface ItAuthenticationChangePasswordResponse {}

// export interface ITAuthenticationLoginResponse {}

export class ITAuthenticationDatasource extends RESTDataSource {
  constructor(...args: [RESTDataSourceOptions]) {
    super(...args)
    this.baseURL = config.itAuthentication.baseUrl
  }

  changePassword(Email: string, OldPassword: string, NewPassword: string) {
    return this.post('api/v1/ChangePassword', {
      body: {
        Email,
        NewPassword,
        OldPassword,
      },
    })
  }

  login(username: string, password: string) {
    return this.post('api/v1/Login', {
      body: {
        Email: username,
        Password: password,
      },
    })
  }
}

export { ITAuthenticationDatasource as itAuthentication }
