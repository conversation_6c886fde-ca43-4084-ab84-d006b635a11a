/**
 * @typedef {{
 *  address_line_1: string
 *  address_line_2: string
 *  admin_area_1: string
 *  admin_area_2: string
 *  postal_code: string
 *  country_code: string
 * }} PaypalAddress
 */

/**
 * @typedef {{
 *  create_time: string
 *  id: string
 *  intent: PaypalIntent
 *  links: PaypalLink[]
 *  payer: Paypal<PERSON>erson
 *  payment_source: PaypalPerson
 *  processing_instruction: PaypalProcessingInstruction
 *  purchase_units: PaypalPurchaseUnit[]
 *  status: PaypalCaptureOrderStatus
 *  update_time: string
 * }} PaypalCaptureOrderResponse
 */

/**
 * @typedef {('APPROVED'|'COMPLETED'|'CREATED'|'PAYER_ACTION_REQUIRED'|'SAVED'|'VOIDED')} PaypalCaptureOrderStatus
 */

/**
 * @typedef {{
 *  purchase_units: PaypalPurchaseUnit[]
 *  intent: ('CAPTURE' | 'AUTHORIZE')
 *  payment_source: {
 *    paypal: {
 *      experience_context: {
 *        brand_name: string
 *        shipping_preference: ('GET_FROM_FILE' | 'NO_SHIPPING' | 'SET_PROVIDED_ADDRESS')
 *        landing_page: ('LOGIN' | 'GUEST_CHECKOUT' | 'NO_PREFERENCE')
 *        user_action: ('CONTINUE' | 'PAY_NOW')
 *        payment_methid_preferencce: ('UNRESTRICTED' | 'IMMEDIATE_PAYMENT_REQUIRED')
 *        locale: string
 *        return_url: string
 *        cancel_url: string
 *      }
 *      billing_agreement_id: string
 *      vault_id: string
 *      email_address: string
 *      name: {
 *        given_name: string
 *        surname: string
 *      }
 *      phone: {
 *        phone_type: ('FAX' | 'HOME' | 'MOBILE' | 'OTHER' | 'PAGER')
 *        phone_number: {
 *          national_number: string
 *        }
 *      }
 *      birth_date: string
 *      tax_info: {
 *        tax_id: string
 *        tax_id_type: ('BR_CPF' | 'BRCNPJ')
 *      }
 *      address: PaypalAddress
 *      attributes: {
 *        customer: {
 *          id: string
 *        }
 *        vault: {
 *          store_in_vault: ('ON_SUCCESS')
 *          description: string
 *          usage_pattern: string
 *          usage_type: string
 *          customer_type: string
 *          permit_multiple_payment_tokens: boolean
 *        }
 *      }
 *    }
 *  }
 * }} PaypalCreateOrderRequest
 */

/**
 * @typedef {{
 *  create_time: string
 *  id: string
 *  intent: PaypalIntent
 *  links: PaypalLink[]
 *  payer: PaypalPayer
 *  payment_source: PaypalPaymentSource
 *  processing_instruction: PaypalProcessingInstruction
 *  purchase_units: PaypalPurchaseUnit[]
 *  status: PaypalOrderStatus
 *  update_time: string
 * }} PaypalCreateOrderResponse
 */

/**
 * @typedef {{
 *  source_currency: string
 *  target_currency: string
 *  value: string
 * }} PaypalExchangeRate
 */

/**
 * @typedef {('AUTHORIZE'|'CAPTURE')} PaypalIntent
 */

/**
 * @typedef {{
 *  href: string
 *  metho: ('GET'|'PATCH'|'POST')
 *  rel: string
 * }} PaypalLink
 */

/**
 * @typedef {{
 *  access_token: string
 *  app_id: string
 *  expires_in: number
 *  nonce: string
 *  scope: string
 *  token_type: 'Bearer'
 * }} PaypalOAuthResponse
 */

/**
 * @typedef {{
 *  create_time: string
 *  id: string
 *  intent: PaypalIntent
 *  links: PaypalLink[]
 *  payer: PaypalPerson
 *  payment_source: {
 *    paypal: PaypalPerson
 *  }
 *  purchase_units: PaypalPurchaseUnit[]
 *  status: PaypalOrderStatus
 * }} PaypalOrderDetails
 */

/**
 * @typedef {('APPROVED'|'COMPLETED'|'CREATED'|'PAYER_ACTION_REQUIRED'|'SAVED'|'VOIDED')} PaypalOrderStatus
 */

/**
 * @typedef {{
 *   email_address: string
 *   merchant_id: string
 * }} PaypalPayee
 */

/**
 * @typedef {{
 *  authorizations: PaypalAuthorization[]
 *  captures: PaypalCapture[]
 *  refunds: PaypalRefund[]
 * }} PaypalPayments
 */

/**
 * @typedef {PaypalPaymentRecordBase & {
 *  status: PaypalAuthorizationStatus
 *  status_details: {
 *    reason: 'PENDING_REVIEW'
 *  }
 * }} PaypalAuthorization
 */

/**
 * @typedef {('CAPTURED'|'CREATED'|'DENIED'|'PARTIALLY_CAPTURED'|'PENDING'|'VOIDED')} PaypalAuthorizationStatus
 */

/**
 * @typedef {PaypalPaymentRecordBase & {
 *  disbursement_mode: string
 *  final_capture: boolean
 *  status: PaypalCaptureStatus
 *  status_details: {
 *    reason: ('BUYER_COMPLAINT'|'CHARGEBACK'|'ECHECK'|'INTENATIONAL_WITHDRAWAL'|'OTHER'|'PENDING_REVIEW'|'RECEIVING_PREFERENCE_MANDATES_MANUAL_ACTON'|'REFUNDED'|'TRANSACTION_APPROVED_AWAITING_FUNDING'|'UNILATERAL'|'VERIFICATION_REQUIRED')
 *  }
 *  payee?: PaypalPayee
 * }} PaypalCapture
 */

/**
 * @typedef {('COMPLETED'|'DECLINED'|'FAILED'|'PARTIALLY_REFUNDED'|'PENDING'|'REFUNDED')} PaypalCaptureStatus
 */

/**
 * @typedef {PaypalPaymentRecordBase & {
 *  acquire_reference_number: strig
 *  note_to_payer: string
 *  seller_payable_breakdown: {
 *    gross_amount: PaypalPrice
 *    net_amount: PaypalPrice
 *    net_amount_breakdown: {
 *      converted_amount: PaypalPrice
 *      exchange_rate: PaypalExchangeRate
 *      payable_amount: PaypalPrice
 *    }[]
 *    paypal_fee: PaypalPrice
 *    paypal_fee_in_receivable_currency: PaypalPrice
 *    platform_fees: {
 *      amount: PaypalPrice
 *      payee: PaypalPayee
 *    }[]
 *    total_refunded_amount: PaypalPrice
 *  }
 *  status: PaypalRefundStatus
 *  status_details: {
 *    reason: 'ECHECK'
 *  }
 * }} PaypalRefund
 */

/**
 * @typedef {('CANCELLED'|'COMPLETED'|'FAILED'|'PENDING')} PaypalRefundStatus
 */

/**
 * @typedef {{
 *  amount?: PaypalPrice
 *  create_time: strng
 *  custom_id: string
 *  expiration_time: strng
 *  id: string
 *  invoice_id: string
 *  network_transaction_reference: PaypalNetworkTransactionReference
 *  processor_response: PaypalProcessorResponse
 *  seller_protection: PaypalSellerProtection
 *  update_time: string
 * }} PaypalPaymentRecordBase
 */

/**
 * @typedef {{
 *  account_id: string
 *  email_address: string
 *  name: {
 *    given_name: string
 *    surname: string
 *  }
 * }} PaypalPerson
 */

/**
 * @typedef {{
 *  currency_code: string
 *  value: string
 * }} PaypalPrice
 */

/**
 * @typedef {('NO_INSTRUCTION'|'ORDER_COMPLETE_ON_PAYMENT_APPROVAL')} PaypalProcessingInstruction
 */

/**
 * @typedef {{
 *  name: string
 *  quantity: string
 *  description: string
 *  sku: string
 *  category: ('DIGITAL_GOODS' | 'PHYSICIAL_GOODS' | 'DONATION')
 *  unit_amount: PaypalPrice
 *  tax: PaypalPrice
 *  number: string
 * }} PaypalPurchaseItems
 */

/**
 * @typedef {{
 *  reference_id: string
 *  description: string
 *  custom_id: string
 *  invoice_id: string
 *  soft_descriptor: string
 *  items: PaypalPurchaseItems[]
 *  amount: {
 *    currency_code: string
 *    value: string
 *    breakdown: {
 *      item_total: PaypalPrice
 *      shipping: PaypalPrice
 *      handling: PaypalPrice
 *      tax_total: PaypalPrice
 *      insurance: PaypalPrice
 *      shipping_discount: PaypalPrice
 *      discount: PaypalPrice
 *    }
 *  },
 *  payee: PaypalPayee,
 *  payment_instruction: {
 *    platform_fees: {
 *      amount: PaypalPrice
 *      payee: PaypalPayee
 *    }[]
 *    payee_pricing_tier_id: string
 *    payee_receivable_fx_rate_id: string
 *    disbursement_mode: ('INSTANT' | 'DELAYED')
 *  }[]
 *  payments: PaypalPayments
 *  shipping: {
 *    type: ('SHIPPING' | 'PICKUP_IN_PERSON')
 *    name: {
 *      given_name: string
 *      surname: string
 *    },
 *    address: PaypalAddress
 *  }
 *  supplementary_data: {
 *    card: {
 *      level_2: {
 *        invoice_id: string,
 *        tax_total: PaypalPrice
 *      },
 *      level_3: {
 *        ships_from_postal_code: string
 *        line_items: {
 *          name: string
 *          quantity: string
 *          description: string
 *          sku: string
 *          category: ('DIGITAL_GOODS' | 'PHYSICIAL_GOODS' | 'DONATION')
 *          unit_amount: PaypalPrice
 *          tax: PaypalPrice
 *          commodity_code: string
 *          unit_of_mesure: string
 *          discount_amount: PaypalPrice
 *          total_amount: PaypalPrice
 *        }[]
 *        shipping_amount: PaypalPrice
 *        duty_amount: PaypalPrice
 *        discount_amount: PaypalPrice
 *        shipping_address: PaypalAddress
 *      }
 *    }
 *  }
 * }} PaypalPurchaseUnit
 */

/**
 * @typedef {{
 *  auth_algo: string
 *  cert_url: string
 *  transmission_id: string
 *  transmission_sig: string
 *  transmission_time: string
 *  webhook_id: string
 *  webhook_event: {
 *    event_version: string
 *    resource_version: string
 *  }
 * }} PaypalVerifyWebhookSigatureRequest
 */

/**
 * @see https://developer.paypal.com/api/rest/webhooks/event-names/
 * @typedef {{
 *  create_time: string
 *  event_type: ('PAYMENT.CAPTURE.COMPLETED')
 *  event_version: '1.0'
 *  id: string
 *  resource_type: string
 *  summary: string
 * }} PaypalWebhookEnvelope
 */

/**
 * @typedef {PaypalWebhookEnvelope & {
 *  resource: PaypalCapture & {
 *    supplementary_data: {
 *      related_ids: {
 *        order_id: string
 *      }
 *    }
 *  }
 * }} PaypalWebhookPaymentCaptureCompleted
 */
