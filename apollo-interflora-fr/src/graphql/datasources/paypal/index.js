const apm = require('elastic-apm-node')
const { randomUUID } = require('node:crypto')

const config = require('../../../config')
const CONST = require('../../../const')

const { RESTDataSource } = require('../RESTDataSource')

const { createJwt } = require('../../../helpers/auth')
const Session = require('../../../plugins/session/session')
const { GraphQLApolloError } = require('../../errors')
const toBase64 = (o) => Buffer.from(JSON.stringify(o)).toString('base64url')

/** @typedef {import('fastify').FastifyRequest} FastifyRequest */

/** @typedef {import('./_typedef')} */

class PaypalDataSource extends RESTDataSource {
  constructor() {
    super()

    this.token
  }

  get baseUrl() {
    return this.context.appConfig.baseUrl
  }

  get centsCurrencies() {
    return ['EUR', 'SEK']
  }

  /**
   * @param {CTCart} cart
   * @returns {PaypalCreateOrderRequest}
   */
  cartToPaypalCreateOrderPayload(cart) {
    if (!cart.taxedPrice) {
      const error = new Error('cart2Paypal: no taxed price in cart')
      throw error
    }

    const taxCurrencyCode = cart.taxedPrice.totalTax.currencyCode
    const taxPrice = this._formatToPaypalAmount(cart.taxedPrice.totalTax.centAmount, taxCurrencyCode)
    const taxedPriceCurrencyCode = cart.taxedPrice.totalNet.currencyCode
    const taxedPrice = this._formatToPaypalAmount(cart.taxedPrice.totalNet.centAmount, taxedPriceCurrencyCode)

    let postalCode = cart.shippingAddress.postalCode
    if (!postalCode) {
      this.context.log.info(`[paypal] adding fake postal code`)
      postalCode = '12345'
    }

    /** @type {PaypalCreateOrderRequest} */
    const payload = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          amount: {
            breakdown: {
              item_total: {
                currency_code: taxedPriceCurrencyCode,
                value: taxedPrice,
              },
              tax_total: {
                currency_code: taxCurrencyCode,
                value: taxPrice,
              },
            },
            currency_code: cart.taxedPrice.totalGross.currencyCode,
            value: this._formatToPaypalAmount(
              cart.taxedPrice.totalGross.centAmount,
              cart.taxedPrice.totalGross.currencyCode
            ),
          },
          custom_id: [config.environment, config.site, cart.id].join('#'), // nb: 127 chars max!
          reference_id: cart.id,
          shipping: {
            address: {
              address_line_1: cart.shippingAddress.streetName,
              address_line_2: cart.shippingAddress.additionalStreetInfo,
              admin_area_2: cart.shippingAddress.city,
              country_code: cart.shippingAddress.country,
              postal_code: postalCode,
            },
            name: {
              full_name: `${cart.shippingAddress.firstName || ''} ${cart.shippingAddress.lastName || ''}`,
            },
            type: 'SHIPPING',
          },
          supplementary_data: {
            card: {
              level_2: {
                tax_total: {
                  currency_code: taxCurrencyCode,
                  value: taxPrice,
                },
              },
              level_3: {
                line_items: [
                  {
                    name: cart.id,
                    quantity: 1,
                    unit_amount: {
                      currency_code: taxedPriceCurrencyCode,
                      value: taxedPrice,
                    },
                  },
                ],
              },
            },
          },
        },
      ],
    }

    // The mobile app needs an url to detect to go back from their webview.
    if (
      [
        CONST.devices.appIOS,
        CONST.devices.appAndroid,
        CONST.devices.deprecatedAppAndroid,
        CONST.devices.deprecatedAppIOS,
      ].includes(cart.custom.fields.device)
    ) {
      payload.payment_source = {
        paypal: {
          experience_context: {
            cancel_url: `${config.apps[config.apps.defaultSiteId].baseUrl}/checkout/paiements/paypal/failure`,
            return_url: `${config.apps[config.apps.defaultSiteId].baseUrl}/checkout/paiements/paypal/success`,
          },
        },
      }
    }
    return payload
  }

  /**
   * @returns {Promise<string>}
   */
  async getAccessToken() {
    if (!this.token) {
      const auth = Buffer.from(config.tokens.paypalClientId + ':' + config.tokens.paypalAppSecret).toString('base64')
      /** @type {PaypalOAuthResponse} */
      const response = await this.post('v1/oauth2/token', {
        body: 'grant_type=client_credentials',
        headers: {
          Authorization: `Basic ${auth}`,
        },
      })
      this.token = response.access_token

      // delete 10s before expiration
      setTimeout(() => {
        this.token = undefined
      }, (response.expires_in - 10) * 1000)
    }
    return this.token
  }

  /**
   * @param {CTCart} cart
   * @returns {Promise<PaypalCreateOrderResponse>}
   */
  createOrder(cart) {
    return this.post('v2/checkout/orders', { body: this.cartToPaypalCreateOrderPayload(cart) })
  }

  /**
   * @param {string} orderId
   * @returns {Promise<PaypalCaptureOrderResponse>}
   */
  captureOrder(orderId) {
    return this.post(`v2/checkout/orders/${orderId}/capture`, {
      headers: {
        'PayPal-Request-Id': orderId,
      },
    })
  }

  /**
   * @param {string} id
   * @returns {Promise<PaypalOrderDetails>}
   */
  getOrder(id) {
    return this.get(`v2/checkout/orders/${id}`)
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.paypal.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {FastifyRequest} request
   * @returns {Promise<void>}
   * @throws {GraphQLApolloError}
   */
  async verifyWebhookSignature(request) {
    const payload = {
      auth_algo: request.headers['paypal-auth-algo'],
      cert_url: request.headers['paypal-cert-url'],
      transmission_id: request.headers['paypal-transmission-id'],
      transmission_sig: request.headers['paypal-transmission-sig'],
      transmission_time: request.headers['paypal-transmission-time'],
      webhook_event: request.body,
      webhook_id: config.paypal.webhookId,
    }

    const response = await this.post('v1/notifications/verify-webhook-signature', payload)
    if (response?.verification_status !== 'SUCCESS') {
      throw new GraphQLApolloError(`[paypal] failed to verify webhook signature: ${JSON.stringify(response)}`)
    }
  }

  /**
   * @param {string} path
   * @param {AugmentedRequest} request
   */
  async willSendRequest(path, request) {
    if (/^\/?v1\/oauth/.test(path)) return

    const token = await this.getAccessToken()
    request.headers.authorization = `Bearer ${token}`
    request.headers['content-type'] = 'application/json'
  }

  /**
   * @param {FastifyInstance} fastify
   * @param {*} _options
   * @param {Function} done
   */
  static fastifyPlugin(fastify, _options, done) {
    fastify.post('/graphql/paypal/webhook', async (request, reply) => {
      const conf = config.apps[config.apps.defaultSiteId]

      const eventType = request.body.event_type
      if (!['PAYMENT.CAPTURE.COMPLETED'].includes(eventType)) {
        reply.send({ noop: true, reason: `unhandled event type "${eventType}"` })
        return
      }

      /** @type {PaypalWebhookPaymentCaptureCompleted} */
      const body = request.body

      const paypalOrderId = body.resource.supplementary_data.related_ids.order_id

      const paypal = new PaypalDataSource()
      paypal.initialize({
        context: {
          get appConfig() {
            return conf
          },
        },
      })

      const paypalOrder = await paypal.getOrder(paypalOrderId)
      const [environment, site, cartId] = paypalOrder.purchase_units[0].custom_id.split('#')
      if (environment !== config.environment || site !== config.site) {
        apm.log.info(
          `[paypal] ${site}-${environment} webhook received on ${config.site}-${config.environment} -- skipping`
        )
        return reply.status(200).send('OK')
      }

      await paypal.verifyWebhookSignature(request)

      const session = new Session(fastify.redis, randomUUID())
      session.apiCart = { id: cartId }
      await session.save()

      const jwt = createJwt(
        {
          access_token: randomUUID(),
          expires_in: 15 * 60 * 1000,
          scope: 'scope',
          token_type: CONST.user.tokenType.access,
        },
        undefined,
        session.id
      )

      // let time to prefer human confirmation
      setTimeout(() => {
        fastify
          .inject({
            body: JSON.stringify({
              query: `
            mutation PaypalNotification($context: InputContext!, $paypalOrderId: String!) {
              createOrder(
                context: $context, 
                orderInfo: {
                  mode: ${CONST.payment.paymentMode.PAYPAL}
                  paymentToken: $paypalOrderId
                  provider: ${CONST.payment.provider.PAYPAL}
                }
              ) {
                status
              }
            }
          `,
              variables: {
                context: {
                  countryId: conf.country,
                  language: conf.language,
                  siteId: config.apps.defaultSiteId,
                },
                paypalOrderId,
              },
            }),
            headers: {
              'Content-Type': 'application/json',
              'x-jwt': jwt.token,
            },
            method: 'POST',
            url: '/graphql',
          })
          .then((response) => {
            const { statusCode, statusMessage } = response
            fastify.log.info('paypal s2s: %O', {
              json: response.json(),
              paypalOrderId,
              statusCode,
              statusMessage,
            })
          })
          .catch((e) => apm.captureError(e, { custom: e }))
      }, 15000)

      return reply.status(200).send('OK')
    })
    done()
  }

  /**
   * @param {string} captureId
   * @return {Promise<PaypalCapture>}
   */
  async getCapture(captureId) {
    return this.get(`v2/payments/captures/${captureId}`)
  }

  /**
   * @param {string} captureId
   * @param {number} amount
   * @return {Promise<PaypalRefund>}
   */
  async refund(captureId, amount = null) {
    const capture = await this.getCapture(captureId)
    const authAssertion = this._getAuthAssertion(capture?.payee?.merchant_id)

    let body = null

    if (amount) {
      body = {
        amount: {
          currency_code: capture.amount['currency_code'],
          value: this._formatToPaypalAmount(amount, capture.amount['currency_code']), // Paypal expect currencies accepting decimals (like EUR, SEK) to be sent with decimals
        },
      }
    }

    const refundResponse = await this.post(`v2/payments/captures/${captureId}/refund`, {
      body,
      headers: {
        'PayPal-Auth-Assertion': authAssertion,
      },
    })

    return refundResponse
  }

  /**
   * @param {string} refundId
   * @return {Promise<PaypalRefund>}
   */
  async getRefundDetails(refundId) {
    return this.get(`v2/payments/refunds/${refundId}`)
  }

  /**
   * Returns the correct amount format for Paypal queries: decimals are not allowed for some currencies
   * @param {number} amount
   * @param {string} currencyCode
   * @returns {string}
   */
  _formatToPaypalAmount(amount, currencyCode) {
    if (this.centsCurrencies.includes(currencyCode)) {
      return (amount / 100).toFixed(2)
    } else {
      return `${amount}`
    }
  }

  /**
   * Transform a paypal amount to an integer amount representing the smallest units possibles: paypal can return non-decimal amounts for some currencies
   * @param {string} amount
   * @param {string} currencyCode
   * @returns {number}
   */
  _formatFromPaypalAmount(amount, currencyCode) {
    if (this.centsCurrencies.includes(currencyCode)) {
      return parseFloat(amount) * 100
    } else {
      return parseInt(amount)
    }
  }

  /**
   * JWT assertion that identifies the merchant
   * @param {string} payerId
   * @return {string}
   * @private
   */
  _getAuthAssertion(payerId) {
    const header = toBase64({
      alg: 'none',
    })
    const payload = toBase64({
      iss: config.tokens.paypalClientId,
      payer_id: payerId,
    })

    return `${header}.${payload}.`
  }
}

module.exports = {
  paypal: PaypalDataSource,
}
