/**
 * @typedef {{
 *  LastName: string|null
 *  Country: {}
 *  CompanyName: string|null
 *  PostalCode: string|null
 *  Title: string|null
 *  TitleCode: string|null
 *  Batiment: string|null
 *  TwitterId: string|null
 *  FormattedAddress: string|null
 *  VisibleInAddressBook: boolean
 *  AdditionalInfo: string|null
 *  Porte: string|null
 *  Id: string|null
 *  Line2: string|null
 *  Line1: string|null
 *  Email: string|null
 *  Lieu: string|null
 *  NatureAddress: string|null
 *  TypeCeremonie: string|null
 *  Town: string|null
 *  CodeLocalite: string|null
 *  FirstName: string|null
 *  Phone: string|null
 *  Etage: string|null
 *  ShippingAddress: string|null
 *  Digicode: string|null
 *  Region: {}
 *  DefaultAddress: boolean
 * }} SelfcareAddress
 */

/**
 * @typedef {{
 *   Signature: string|null
 *   Message: string|null
 *   ReferenceCommande: string|null
 *   DeliveryMinute: string|null
 *   Moment: string|null
 *   DeliveryDate: string|null
 *   DeliveryAddress: SelfcareAddress|null
 *   DeliveryHour: string|null
 *   MessageRubanDeuil: string|null
 *   DeliveryContact: SelfcareAddress|null
 * }} SelfcareUpdateRequest
 */

/**
 * @typedef {{
 *   Id: string|null
 *   Version: number
 *   CreatedAt: string
 *   LastModifiedAt: string
 *   OrderNumber: string|null
 *   PurchaseOrderNumber: string
 *   CustomerId: string
 *   CustomerEmail: string
 *   CustomerGroup: SelfcareICustomerGroupReference
 *   BusinessUnit: SelfcareIReference
 *   Store: SelfcareIReference
 *   LineItems: SelfcareILineItem[]
 *   CustomLineItems: {
 *     Id: string
 *     Key: string
 *     Name: string
 *     Money: SelfcareITypedMoney
 *     TaxedPrice: SelfcareITaxedItemPrice
 *       PerMethodTaxRate: {
 *       ShippingMethodKey: string
 *       TaxRate: SelfcareITaxRate
 *     }[]
 *     DiscountedPricePerQuantity: {
 *       Quantity: number
 *       DiscountedPrice: {
 *         Value: SelfcareITypedMoney
 *         IncludedDiscounts: {
 *           Discount: SelfcareIReference
 *           DiscountedAmount: SelfcareITypedMoney
 *         }[]
 *       }
 *     }[]
 *     Custom: {
 *       Type: SelfcareIReference
 *       Fields: any
 *     }
 *     ShippingDetails: {
 *       Tatgets: any[]
 *       Valid: boolean
 *     }
 *     PriceMode: string
 *   }[]
 *   TotalPrice: SelfcareITypedMoney
 *   TaxedPrice: SelfcareITaxedItemPrice
 *   TaxedShippingPrice: SelfcareITaxedItemPrice
 *   DiscountOnTotalPrice: {
 *     DiscountedAmount: SelfcareITypedMoney
 *     IncludedDiscounts: {
 *       Discount: SelfcareIReference
 *       DiscountedAmount: SelfcareITypedMoney
 *     }[]
 *     DiscountedNetAmount: SelfcareITypedMoney
 *     DiscountedGrossAmount: SelfcareITypedMoney
 *   }
 *   TaxMode: string
 *   TaxRoundingMode: string
 *   TaxCalculationMode: string
 *   InventoryMode: string
 *   BillingAddress: SelfcareAddress
 *   ShippingAddress: SelfcareAddress
 *   ShippingMode: string
 *   ShippingKey: string
 *   ShippingInfo: {
 *     ShippingMethodName: string
 *     Price: SelfcareITypedMoney
 *     ShippingRate: {
 *       Price: SelfcareITypedMoney
 *       FreeAbove: SelfcareITypedMoney
 *       IsMatching: boolean
 *       Tiers: string[]
 *       TaxedPrice: SelfcareITaxedItemPrice
 *       TaxRate: SelfcareITaxRate
 *       TaxCategory: SelfcareIReference
 *       ShippingMethod: SelfcareIReference
 *       Deliveries: any[]
 *       DiscountedPrice:  {
 *         Value: SelfcareITypedMoney
 *         IncludedDiscounts: {
 *           Discount: SelfcareIReference
 *           DiscountedAmount: SelfcareITypedMoney
 *         }[]
 *       }
 *       ShippingMehtodState: string
 *     }
 *   }
 *   ShippingRateInput: string
 *   ShippingCustomFields: {
 *     Type: SelfcareIReference
 *     Fields: any
 *   }
 *   Shipping: any
 *   ItemShippingAddress: SelfcareAddress
 *   DiscountCodes: {
 *     DiscountCode: SelfcareIReference
 *     State: string
 *   }[]
 *   DirectDiscounts: {
 *     Id: string
 *     Value: string
 *     Target: string
 *   }[]
 *   RefusedGifts: SelfcareIReference[]
 *   PaymentInfo: {
 *     Payments: SelfcareIReference[]
 *   }
 *   Country: string
 *   Locale: string
 *   Origin: string
 *   Cart: SelfcareIReference
 *   Qoute: SelfcareIReference
 *   OrderState: string
 *   ShipmentState: string
 *   PaymentState: string
 *   State: SelfcareIReference
 *   SyncInfo: {
 *     Channel: SelfcareIReference[]
 *     ExternalId: string
 *     SyncedAt: string
 *   }
 *   ReturnInfo: {
 *     Items: any[]
 *     ReturnIrackingId: string
 *     ReturnDate: string
 *   }[]
 *   Custom: {
 *     Type: SelfcareIReference
 *     Fields: any
 *   }
 *   CompletedAt: string
 *   LastModifiedBy: SelfcareIReference
 *   CreatedBy: SelfcareIReference
 * }} SelfcareIOrder
 */

/**
 * @typedef {{
 *   TypeId: string
 *   Id: string
 *   Obj: any
 * }} SelfcareIReference
 */

/**
 * @typedef {{
 *   Id: string
 *   Key: string
 *   ProductId: string
 *   Name: string
 *   ProductSlug: string
 *   ProductType: SelfcareIReference
 *   Variant: {
 *     Id: string
 *     Sku: string
 *     Key: string
 *     Prices: SelfcareIPrice[]
 *     Attributes: {
 *       Name: string
 *       Value: any
 *     }[]
 *     Price: SelfcareIPrice
 *     Images: {
 *       Url: string
 *       Dimensions: {
 *         W: number
 *         H: number
 *       }
 *       Label: string
 *     }[]
 *     Assets: {
 *       Id: string
 *       Sources: SelfcareIAssetSource[]
 *     }
 *     Availability: {
 *       Channels: {
 *         IsOnStock: boolean
 *         RestockableInDays: number
 *         AvailableQuantity: number
 *         Id: string
 *         Version: number
 *       }[]
 *       IsOnStock: boolean
 *       RestockableInDays: number
 *       AvailableQuantity: number
 *       Id: string
 *       Version: number
 *     }
 *     IsMatchingVariant: boolean
 *     ScopedPrice: {
 *       Id: string
 *       Value: SelfcareITypedMoney
 *       CurrentValue: SelfcareITypedMoney
 *       Country: string
 *       CustomerGroup: SelfcareIReference
 *       Channel: SelfcareIReference
 *       ValidFrom: string
 *       ValidUntil: string
 *       Discounted: {
 *         Value: SelfcareITypedMoney
 *         Discount: SelfcareIReference
 *       }
 *       Custom: {
 *         Type: SelfcareIReference
 *         Fields: any
 *       }
 *     }
 *     ScopedPriceDiscounted: boolean
 *   }
 *   Price: SelfcareIPrice
 *   Quantity: number
 *   TotalPrice: SelfcareITypedMoney
 *   DiscountedPricePerQuantity: {
 *     Quantity: number
 *     DiscountedPrice: {
 *       Value: SelfcareITypedMoney
 *       IncludedDiscounts: {
 *         Discount: SelfcareIReference
 *         DiscountedAmount: SelfcareITypedMoney
 *       }[]
 *     }
 *   }[]
 *   TaxedPrice: SelfcareITaxedItemPrice
 *   TaxedPricePortions: {
 *     ShippingMethodKey: string
 *     TaxedPrice: SelfcareITaxedItemPrice
 *   } []
 *   State: {
 *     Quantity: number
 *     State: SelfcareIReference
 *   }
 *   TaxRate: SelfcareITaxRate[]
 *   PerMethodTaxRate: {
 *     ShippingMethodKey: string
 *     TaxRate: SelfcareITaxRate
 *   }[]
 *   SupplyChannel: SelfcareIReference
 *   DistributionChannel: SelfcareIReference
 *   PriceMode: string
 *   LineItemMode: string
 *   InventoryMode: string
 *   ShippingDetails: {
 *     Tatgets: any[]
 *     Valid: boolean
 *   }
 *   Custom: {
 *     Type: SelfcareIReference
 *     Fields: any
 *   }
 *   AddedAt: string
 *   LastModifiedAt: string
 * }} SelfcareILineItem
 */

/**
 * @typedef {{
 *   Id: string,
 *   Key: string
 *   Value: SelfcareITypedMoney
 *   Country: string
 *   CustomerGroup: SelfcareIReference
 *   Channel: SelfcareIReference
 *   ValidFrom: string
 *   ValidUntil: string
 *   Discounted: {
 *     Value: SelfcareITypedMoney
 *     Discount: SelfcareIReference
 *   }
 *   Tiers: {
 *     MinimumQuantity: number
 *     Value: SelfcareITypedMoney
 *   }[]
 *   Custom: {
 *     Type: SelfcareIReference,
 *     Fields: any
 *   }
 * }} SelfcareIPrice
 */

/**
 * @typedef {{
 *   CentAmount: number
 *   CurrencyCode: string
 *   Type: any
 *   FractionDigits: number
 * }} SelfcareITypedMoney
 */

/**
 * @typedef {{
 *   Uri: string
 *   Key: string
 *   Dimensions: {
 *     W: number
 *     H: number
 *   }
 *   ContentType: string
 * }} SelfcareIAssetSource
 */

/**
 * @typedef {{
 *   TotalNet: SelfcareITypedMoney
 *   TotalGross: SelfcareITypedMoney
 *   TaxPortions: {
 *     Name: string
 *     Rate: number
 *     Amount: SelfcareITypedMoney
 *   }[]
 *   TotalTax: SelfcareITypedMoney
 * }} SelfcareITaxedItemPrice
 */

/**
 * @typedef {{
 *   Id: string
 *   Key: string
 *   Name: string
 *   Amount: number
 *   IncludedInPrice: boolean
 *   Country: string
 *   State: string
 *   SubRates: {
 *     Name: string
 *     Amount: number
 *   }} SelfcareITaxRate
 */
