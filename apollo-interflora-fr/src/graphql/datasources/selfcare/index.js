const { RESTDataSource } = require('../RESTDataSource')
const config = require('../../../config')

/** @typedef {import('./_typedef')} */

class SelfCareDataSource extends RESTDataSource {
  /**
   * @param {string} orderNumber
   * @return {Promise<boolean>}
   */
  async isEditableOrder(orderNumber) {
    const res = await this.get('IsEligible', { params: { orderNumber } })
    if (!res) {
      return false
    }

    return res.Eligible || false
  }

  /**
   * @param {SelfcareUpdateRequest} updateRequest
   * @return {Promise<SelfcareIOrder>}
   */
  async updateOrder(updateRequest) {
    return this.post('UpdateSelfcareOrder', { body: updateRequest })
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.selfCare.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.selfCare.headers) {
      for (const [name, value] of Object.entries(config.selfCare.headers)) {
        request.headers[name] = value
      }
    }
  }
}

module.exports = {
  selfCare: SelfCareDataSource,
}
