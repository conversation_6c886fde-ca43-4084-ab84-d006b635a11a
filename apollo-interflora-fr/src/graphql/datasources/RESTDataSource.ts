import { DataSourceConfig, RESTDataSource as ApolloRESTDataSource } from '@apollo/datasource-rest'

import config from '../../config'
import { ValueOrPromise } from '@apollo/datasource-rest/dist/RESTDataSource'

export type RESTDataSourceOptions = DataSourceConfig & {
  timeout?: number
}

export class RESTDataSource extends ApolloRESTDataSource {
  cache?: KeyValueCache
  context?: GraphQLContext

  constructor(opts: RESTDataSourceOptions) {
    const { timeout = config.datasources.timeout, ...options } = opts ?? {}

    const origFetch = opts?.fetch ?? fetch

    const dataSourceConfig: DataSourceConfig = {
      ...options,
      fetch: (url, init) =>
        origFetch(url, {
          ...(init ?? {}),
          signal: init?.signal ?? AbortSignal.timeout(timeout),
        }),
    }

    super(dataSourceConfig)
  }

  initialize({ cache, context }: { cache: KeyValueCache; context: GraphQLContext }) {
    this.cache = cache
    this.context = context
  }

  // parent method overrides the baseURL path if an absolute path is given
  protected resolveURL(path: string): ValueOrPromise<URL> {
    let baseURL = this.baseURL
    let newPath = path

    if (baseURL) {
      if (!baseURL.endsWith('/')) {
        baseURL += '/'
      }
      if (path.startsWith('/')) {
        newPath = path.slice(1)
      }
    }

    return new URL(newPath, baseURL)
  }
}
