const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')

/** @typedef {import('./__typedef')} */

class Cadeaux extends RESTDataSource {
  /**
   * @param {string | number} variantId
   * @returns {Promise<Object>}
   */
  getCustomization(variantId) {
    return this.get(`ajax/customization/${variantId}`)
  }

  /**
   * Body should be sent as form-data
   *
   * @param {string | number} variantId
   * @param {{name: string, value: string}[]} customization
   * @returns {Promise<{ type: string, images: { label: string, image: string }[]}>}
   */
  getPreview(variantId, customization) {
    const boundary = '------' + new Date().getTime().toString()
    const bodyBoundary = '--' + boundary
    const newLine = '\r\n'
    let body = bodyBoundary

    customization.forEach((element) => {
      body += newLine
      body += `Content-Disposition: form-data; name="customization[${element.name}]"${newLine}${newLine}${element.value}`
      body += newLine
      body += bodyBoundary
    })

    return this.post(`ajax/customization/${variantId}`, {
      body,
      headers: { 'Content-Type': 'multipart/form-data; boundary=' + boundary },
    })
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.cadeaux.baseUrl
    }

    return super.resolveURL(request)
  }
}

module.exports = {
  cadeaux: Cadeaux,
}
