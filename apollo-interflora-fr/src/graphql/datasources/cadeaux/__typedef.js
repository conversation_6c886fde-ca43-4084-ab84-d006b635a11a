/** @typedef {{
 * label: string
 * secondaryLabel: string
 * name: string
 * ref: string
 * type: string
 * required: boolean
 * placeholder: string
 * maxLength: string
 * choices: object[]
 * width: null
 * height: null
 * allowedCharacters: null
 * blacklist: string
 * mask: null
 * textCase: null
 * allowedBases: null
 * minDate: null
 * exclusions: null
 * }} CadeauxCustomizationFormField
 */

/** @typedef {{
 * picto: null
 * dataToggle: null
 * field: CadeauxCustomizationFormField
 * }} CadeauxCustomizationForm
 */

/** @typedef {object} CadeauxCustomization
 * @property {number} variantId
 * @property {string} selectType
 * @property {null} modalId
 * @property {string} variantPrice
 * @property {null} variantPromoPrice
 * @property {boolean} available
 * @property {object[]} form
 * @property {null} form.picto
 * @property {object} form.field
 * @property {string} form.field.label
 * @property {string} form.field.secondaryLabel
 * @property {string} form.field.name
 * @property {string} form.field.ref
 * @property {string} form.field.type
 * @property {string} form.field.placeholder
 * @property {boolean} form.field.required
 * @property {string} form.field.maxLength
 * @property {} form.field.choices
 * @property {null} form.field.width
 * @property {null} form.field.height
 * @property {null} form.field.allowedCharacters
 * @property {string} form.field.blacklist
 * @property {null} form.field.mask
 * @property {null} form.field.textCase
 * @property {null} form.dataToggle
 * @property {} variants
 * @property {null} promoLabel
 * @property {object} dataLayers
 * @property {string} dataLayers.date
 * @property {string} dataLayers.websiteId
 * @property {string} dataLayers.country
 * @property {null} dataLayers.customerId
 * @property {boolean} dataLayers.isLogged
 * @property {null} dataLayers.deliveryType
 * @property {string} dataLayers.deliveryPostCode
 * @property {null} dataLayers.deliveryDate
 * @property {null} dataLayers.deliveryHours
 * @property {null} dataLayers.deliveryDelay
 * @property {string} dataLayers.navigationCategory
 * @property {null} dataLayers.cartId
 * @property {number} dataLayers.cartCreationUtc
 * @property {null} dataLayers.transactionId
 * @property {string} dataLayers.transactionTotal
 * @property {string} dataLayers.productsPrice
 * @property {string} dataLayers.transactionShipping
 * @property {string} dataLayers.transactionTax
 * @property {null} dataLayers.promoTypeTransaction
 * @property {null} dataLayers.promoCode
 * @property {null} dataLayers.promoName
 * @property {null} dataLayers.transactionCountry
 * @property {number} dataLayers.uniqueProduct
 * @property {object[]} dataLayers.products
 * @property {string} dataLayers.products.name
 * @property {number} dataLayers.products.productId
 * @property {number} dataLayers.products.sku
 * @property {string} dataLayers.products.price
 * @property {string} dataLayers.products.size
 * @property {string} dataLayers.products.type
 * @property {string} dataLayers.products.productType
 * @property {number} dataLayers.products.quantity
 * @property {null} dataLayers.products.productCategory
 * @property {number} dataLayers.products.productDiscount
 * @property {} dataLayers.productsRemoved
 * @property {} dataLayers.productsAdded
 * @property {null} dataLayers.event
 * @property {null} dataLayers.em
 * @property {null} dataLayers.ph
 * @property {string} dataLayers.eventTime
 * @property {boolean} dataLayers.isNewCart
 * @property {boolean} sameForm
 * @property {boolean} showPreview
 * @property {boolean} customizable
 * @property {object[]} accessories
 * @property {number} accessories.id
 * @property {number} accessories.productId
 * @property {number} accessories.variantId
 * @property {string} accessories.link
 * @property {string} accessories.thumbnail
 * @property {string} accessories.title
 * @property {string} accessories.name
 * @property {string} accessories.price
 * @property {null} accessories.promoPrice
 * @property {object[]} accessories.dataLayers
 * @property {string} accessories.dataLayers.name
 * @property {number} accessories.dataLayers.productId
 * @property {number} accessories.dataLayers.sku
 * @property {string} accessories.dataLayers.price
 * @property {string} accessories.dataLayers.size
 * @property {string} accessories.dataLayers.type
 * @property {string} accessories.dataLayers.productType
 * @property {number} accessories.dataLayers.quantity
 * @property {null} accessories.dataLayers.productCategory
 * @property {number} accessories.dataLayers.productDiscount
 * @property {null} accessories.promoLabel
 * @property {null} accessories.promoLabelBackgroundColor
 * @property {boolean} accessories.isPromoted
 * @property {boolean} accessories.withAlternatePromoteStyle
 * @property {string} accessories.promoteLabel
 * @property {boolean} accessories.available
 * @property {number} accessories.averageRating
 * @property {number} accessories.reviewsCount
 * @property {null} accessories.plpTag
 * @property {string} accessories.description
 * @property {string} accessories.characteristic
 * @property {object} accessories.images
 * @property {string} accessories.images.0-44245
 * @property {number} accessories.rawPrice
 * @property {number} accessories.discount
 * @property {} selectedAccessories
 * @property {null} bgRemoveType
 */
