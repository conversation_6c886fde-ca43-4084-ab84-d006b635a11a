const { RESTDataSource } = require('../RESTDataSource')
const apm = require('elastic-apm-node')

const config = require('../../../config')
const CONST = require('../../../const')

class DiscountCode extends RESTDataSource {
  /**
   * @param {string} cartId
   * @returns {Promise<{
   *   Generated: boolean,
   *   DiscountCode: (string|null)
   * }>}
   */
  generateOrderVoucher(cartId) {
    return this.get('GenerateOrderVoucher', {
      params: { cartId },
    })
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.discountCodeMs?.headers) {
      for (const [name, value] of Object.entries(config.discountCodeMs.headers)) {
        request.headers[name] = value
      }
    }
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      if (!config.discountCodeMs?.baseUrl) {
        throw new Error('Discount Code MS url not configured')
      }

      this.baseURL = config.discountCodeMs.baseUrl
    }

    return super.resolveURL(request)
  }
}

module.exports = {
  discountCode: DiscountCode,
}
