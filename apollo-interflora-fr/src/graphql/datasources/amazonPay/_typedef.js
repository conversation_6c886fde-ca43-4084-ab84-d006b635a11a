/**
 * @typedef {{
 *  addressLine1: string
 *  addressLine2: string
 *  addressLine3: string
 *  city: string
 *  countryCode: string
 *  districtOrCountry: string
 *  name: string
 *  phoneNumber: string
 *  postalCode: string
 *  stateOrRegion: string
 * }} AZAddressDetail
 */

/**
 * @typedef {{
 *  addressDetails: AZAddressDetail
 *  chargePermissionType: ('OneTime'|'Recurring')
 *  merchantMetadata: AZMerchantMetaData
 *  paymentDetails: AZPaymentDetails
 *  scopes: ('billingAddress'|'name'|'email'|'phoneNumber')[]
 *  storeId: string
 *  webCheckoutDetails: AZWebCheckoutDetails
 * }} AZCheckoutPayload
 */

/**
 * @typedef {{
 *   checkoutSessionId: string
 *   webCheckoutDetails: null
 *   chargePermissionType: 'OneTime'
 *   recurringMetadata: null
 *   productType: null
 *   paymentDetails: null
 *   merchantMetadata: null
 *   supplementaryData:null
 *   buyer: null
 *   billingAddress: null
 *   paymentPreferences: [ null ]
 *   statusDetails: {
 *     state: ('Completed'|'Pending'|'Declined'|'Canceled'|'Closed')
 *     reasonCode: null
 *     reasonDescription: null
 *     lastUpdatedTimestamp: string
 *   }
 *   shippingAddress: null
 *   platformId:null
 *   chargePermissionId: string
 *   chargeId: string
 *   constraints: [ null]
 *   creationTimestamp: string
 *   expirationTimestamp: null
 *   storeId: null
 *   deliverySpecifications: null
 *   providerMetadata: null
 *   releaseEnvironment: null
 * }} AZCheckoutSession
 */

/**
 * @typedef {{
 *  amazonPayRedirectUrl: string
 *  checkoutMode: 'ProcessOrder'
 *  checkoutReviewReturnUrl: string
 *  checkoutResultReturnUrl: string
 *  checkoutCancelUrl: string
 * }} AZWebCheckoutDetails
 */

/**
 * @typedef {{
 *  customInformation: string
 *  merchantReferenceId: string
 *  merchantStoreName: string
 *  noteToBuyer: string
 * }} AZMerchantMetaData
 */

/**
 * @typedef {{
 *  allowOvercharge: boolean
 *  canHandlePengingAuthorization: boolean
 *  chargeAmount: AZPrice
 *  extendExpiration: boolean
 *  paymentItent: ('Authorize'|'AuthorizeWithCapture'|'Confirm')
 *  presentmentCurrency: string
 *  softDescriptor: string
 *  totalOrderAmount: AZPrice
 * }} AZPaymentDetails
 */

/**
 * @typedef {{
 *  amount: Number
 *  currencyCode: string
 * }} AZPrice
 */
