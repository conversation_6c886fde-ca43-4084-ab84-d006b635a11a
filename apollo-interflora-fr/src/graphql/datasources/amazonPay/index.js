const config = require('../../../config')

const Client = require('@amazonpay/amazon-pay-api-sdk-nodejs')

const { GraphQLApolloError } = require('../../errors')

/** @typedef {import('./_typedef')} */

let client

class AmazonPay {
  /**
   * @returns {Client.WebStoreClient}
   */
  get client() {
    if (!client) {
      client = new Client.WebStoreClient({
        privateKey: Buffer.from(config.tokens.amazonPayPrivateKey, 'base64').toString(),
        publicKeyId: config.amazonPay.publicKey,
        region: 'eu',
      })
    }
    return client
  }

  /**
   * @param {string} sessionId
   * @param {Number} amount
   * @param {string} currencyCode
   * @returns {Promise<AZCheckoutSession>}
   */
  async completeCheckoutSession(sessionId, amount, currencyCode) {
    const payload = {
      chargeAmount: {
        amount,
        currencyCode,
      },
    }

    return this.execute(() => this.client.completeCheckoutSession(sessionId, payload))
  }

  /**
   * @param {string} sessionId
   * @returns {Promise<AZCheckoutSession>}
   */
  async getCheckoutSession(sessionId) {
    return this.execute(() => this.client.getCheckoutSession(sessionId))
  }

  /**
   * @param {DataSourceConfig} config
   */
  async initialize(config) {
    this.cache = config.cache
    this.context = config.context
  }

  async renderButton(id, amount, currencyCode) {
    /** @type {AZCheckoutPayload} */
    const payload = {
      merchantMetadata: {
        merchantReferenceId: id,
        merchantStoreName: 'Merchant store name',
        noteToBuyer: 'Note to buyer',
      },
      paymentDetails: {
        chargeAmount: {
          amount,
          currencyCode,
        },
        paymentIntent: 'AuthorizeWithCapture',
      },
      scopes: ['name'],
      storeId: config.amazonPay.storeId,
      webCheckoutDetails: {
        checkoutCancelUrl: `${config.apps[this.context.siteId].baseUrl}/checkout/payment/amazonPay/cancel`,
        checkoutMode: 'ProcessOrder',
        checkoutResultReturnUrl: `${config.apps[this.context.siteId].baseUrl}/checkout/payment/amazonPay/response`,
      },
    }
    const signature = this.client.generateButtonSignature(payload)

    return {
      buttonColor: config.amazonPay.buttonColor,
      checkoutLanguage: config.i18n.lang2Locale[this.context.language]?.replace('-', '_') || 'en_US',
      createCheckoutSessionConfig: {
        payloadJSON: JSON.stringify(payload),
        signature,
      },
      ledgerCurrency: config.apps[this.context.siteId].currency,
      merchantId: config.amazonPay.merchantId,
      placement: 'Cart',
      productType: 'PayOnly',
      publicKeyId: config.amazonPay.publicKey,
    }
  }

  async updateCheckoutSession(sessionId, payload) {
    const signature = this.client.generateButtonSignature(payload)
    const session = await this.execute(() => this.client.updateCheckoutSession(sessionId, payload))

    return {
      session,
      signature,
    }
  }

  /**
   * @param {Function} fn
   * @returns {Promise<*>}
   */
  async execute(fn) {
    try {
      const response = await fn()
      return response.data
    } catch (e) {
      const error = new GraphQLApolloError(e.response?.data?.reasonCode, e.statusCode)
      error.addError('amazonPay', e.response?.data?.reasonCode || e.message, e.response?.data?.message)
      error.originalError = e
      throw error
    }
  }
}

module.exports = {
  amazonPay: AmazonPay,
}
