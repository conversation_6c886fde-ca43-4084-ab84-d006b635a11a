const _ = require('lodash')
const gql = require('graphql-tag')

const config = require('../../../config')
const CONST = require('../../../const')

const DatoCMS = require('./DatoCMS')

const getConfigurationRecord = require('./gql/ConfigurationRecord')
const getPageRecord = require('./gql/PageRecord')
const CategoryPageRecord = require('./gql/CategoryPageRecord')
const DefaultBlockRecord = require('./gql/DefaultBlockRecord')
const FloristRegion = require('./gql/FloristRegion')
const FloristPage = require('./gql/FloristPage')
const FooterRecord = require('./gql/FooterRecord')
const FormDefinitionRecord = require('./gql/FormDefinitionRecord')
const InternationalCategoryPageRecord = require('./gql/InternationalCategoryPageRecord')
const InternationalProductRecord = require('./gql/InternationalProductRecord')
const LandingPageRecord = require('./gql/LandingPageRecord')
const MainNavigationRecord = require('./gql/MainNavigationRecord')
const ProductRecord = require('./gql/ProductRecord')
const StoreLocatorHomeRecord = require('./gql/StoreLocatorHomeRecord')
const MessageCardRecord = require('./gql/MessageCardRecord')
const LegalTextRecord = require('./gql/LegalTextRecord')

/** @typedef {import('./_typedef')} */

/**
 * A Data Source service to interact with Dato Cms endpoints.
 *
 * @class Cms
 * @extends DatoCMS
 * @example
 *  // In a GraphQL resolver
 *  const pages = await context.dataSources.cms.getPages()
 */
class Cms extends DatoCMS {
  /**
   * @param {string} productId
   * @param {string} locale
   * @returns {Promise<DatoAccessoryInfo>}
   */
  async getAccessoryInfo(productId, locale) {
    const { accessoryInfo } = await this.query({
      query: gql`
        query AccessoryInfoQuery($productId: String, $locale: SiteLocale) {
          accessoryInfo(filter: { commercetoolsProduct: { eq: $productId } }, locale: $locale) {
            commercetoolsProduct
            legalNotice
            maxQuantity
            hasCustomText
          }
        }
      `,
      variables: {
        locale,
        productId,
      },
    })

    return accessoryInfo
  }

  /**
   *
   * @param {string} categoryId
   * @returns {Promise<DatoAccessoryCategory>}
   */
  async getAccessoryCategory(categoryId) {
    const { accessoryCategory } = await this.query({
      query: gql`
        query AccessoryCategoryQuery($categoryId: String) {
          accessoryCategory(filter: { commercetoolsCategory: { eq: $categoryId } }) {
            commercetoolsCategory
            defaultSettings
          }
        }
      `,
      variables: {
        categoryId,
      },
    })

    return accessoryCategory
  }

  /**
   * @param {string} locale
   * @returns {Promise<DatoCategoryPage[]>}
   */
  async getCategoryTree(locale) {
    const getPage = async (skip = 0) => {
      const {
        _allCategoryPagesMeta: { count: total },
        allCategoryPages: categories,
      } = await this.query({
        query: gql`
          query Q {
            _allCategoryPagesMeta {
              count
            }
            allCategoryPages(first: 50, skip: ${skip}) {
              parent {
                slug
                title(locale: ${locale})
              }
              slug
              title(locale: ${locale})
            }
          }
        `,
      })

      return {
        categories,
        total,
      }
    }

    let { categories, total } = await getPage()
    while (categories.length < total) {
      const { categories: next } = await getPage(categories.length)
      categories = categories.concat(next)
    }

    return categories
  }

  /**
   * @param {string} locale
   * @returns {Promise<DatoConfiguration>}
   */
  async getConfiguration(locale) {
    const fragment = await getConfigurationRecord(this)
    const hasRoundUpProducts = await this.typeHasField('ConfigurationRecord', 'roundUpProducts')

    const { configuration } = await this.query({
      query: gql`
        query Q${hasRoundUpProducts ? `($locale: SiteLocale)` : ''} {
          configuration {
            ...fieldsOnConfigurationRecord
          }
        }
        ${fragment}
      `,
      variables: { locale },
    })

    return configuration
  }

  /**
   * @param {string} warehouseType
   * @param {string} locale
   * @returns {Promise<DatoMessageCardRecord|null>}
   */
  async getMessageCards(warehouseType, locale) {
    const { messageCard } = await this.query({
      query: gql`
        query getMessageCards($warehouseType: String!, $locale: SiteLocale) {
          messageCard(filter: { warehouseType: { eq: $warehouseType } }, locale: $locale) {
            ...fieldsOnMessageCardRecordFragment
          }
        }
        ${MessageCardRecord}
      `,
      variables: {
        locale,
        warehouseType,
      },
    })

    return messageCard
  }

  /**
   * @param {string} id
   * @param {string} locale
   * @param {string} [seaKey]
   * @returns {Promise<DatoProduct>}
   */
  async getProduct(id, locale, seaKey) {
    const { product } = await this.query(
      {
        query: gql`
          query getProduct($id: String!, $locale: SiteLocale) {
            product(filter: { commercetoolsProduct: { eq: $id } }, locale: $locale) {
              ...fieldsOnProductRecord
            }
          }

          ${ProductRecord}
        `,
        variables: {
          id,
          locale,
        },
      },
      seaKey
    )

    if (!product) {
      throw new Error('404 not found')
    }

    return product
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @param {string} [seaKey]
   * @returns {Promise<DatoProduct>}
   */
  async getProductBySlug(slug, locale, seaKey) {
    const { product } = await this.query(
      {
        query: gql`
          query getProductBySlug($locale: SiteLocale, $slug: String!) {
            product(filter: { slug: { eq: $slug } }, locale: $locale) {
              ...fieldsOnProductRecord
            }
          }

          ${ProductRecord}
        `,
        variables: {
          locale,
          slug: slug.toLowerCase(),
        },
      },
      seaKey
    )

    if (!product) {
      throw new Error('404 not found')
    }

    return product
  }

  /**
   * @param {string} id
   * @param {string} locale
   * @returns {Promise<DatoCategoryPage>}
   */
  getCategoryPageById(id, locale) {
    return this._getCategoryPage({ commercetoolsCategory: { eq: id } }, locale)
  }

  /**
   * @param {string} locale
   * @returns {Promise<DatoStoreLocatorHome>}
   */
  async getStoreLocatorHome(locale) {
    const { storeLocatorHome } = await this.query({
      query: gql`
        query getStoreLocatorHome($locale: SiteLocale) {
          storeLocatorHome(locale: $locale) {
            ...fieldsOnStorLocatorHome
          }
        }

        ${StoreLocatorHomeRecord}
      `,
      variables: {
        locale,
      },
    })

    return storeLocatorHome
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @returns {Promise<DatoCategoryPage>}
   */
  async getCategoryPageBySlug(slug, locale) {
    return this._getCategoryPage({ slug: { eq: slug.toLowerCase() } }, locale)
  }

  /**
   * @param {string} pageType
   * @param {string} locale
   * @returns {Promise<DatoDefaultBlock>}
   */
  async getDefaultBlockByPageType(pageType, locale) {
    return this._getDefaultBlock({ pageType: { eq: pageType } }, locale)
  }

  /**
   * Returns list of floristRegions.
   * @param {string} locale
   * @param {Number} first
   * @param {Number} skip
   * @returns {Promise<DatoFloristRegion[]>}
   */
  async getFloristRegions(locale, first = config.storeLocator.regions.pageSize, skip = 0) {
    const { allFloristRegions, _allFloristRegionsMeta } = await this.query({
      query: gql`
        query getFloristRegions($locale: SiteLocale, $first: IntType, $skip: IntType) {
          allFloristRegions(locale: $locale, first: $first, skip: $skip) {
            label: name
            mainTown: mainCity {
              label: name
              slugUrl: slug
            }
            slugUrl: slug
            code
            type: regionType
            regionCode
          }
          _allFloristRegionsMeta {
            count
          }
        }
      `,
      variables: {
        first,
        locale,
        skip,
      },
    })

    if (_allFloristRegionsMeta.count < skip) {
      return []
    } else {
      return allFloristRegions.concat(await this.getFloristRegions(locale, first, skip + first))
    }
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @returns {Promise<DatoFloristRegion>}
   */
  async getFloristRegion(slug, locale) {
    const { floristRegion } = await this.query({
      query: gql`
        query getFloristRegion($slug: String, $locale: SiteLocale) {
          floristRegion(filter: { slug: { eq: $slug } }, locale: $locale) {
            ...fieldsOnFloristRegionRecord
          }
        }

        ${FloristRegion}
      `,
      variables: {
        locale,
        slug: slug.toLowerCase(),
      },
    })

    return floristRegion
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @returns {Promise<DatoFloristRegion>}
   */
  async getFloristPage(slug, locale) {
    const { floristPage } = await this.query({
      query: gql`
        query getFloristPage($slug: String, $locale: SiteLocale) {
          floristPage(filter: { slug: { eq: $slug } }, locale: $locale) {
            ...fieldsOnFloristPageRecord
          }
        }

        ${FloristPage}
      `,
      variables: {
        locale,
        slug: slug.toLowerCase(),
      },
    })

    return floristPage
  }

  /**
   * @param {{
   *  placement: string,
   *  step: string,
   *  userCategory: string
   * }} opts
   * @param {*} locale
   * @returns {Promise<DatoFormDefinition>}
   */
  async getFormDefinition(opts, locale) {
    const filter = {
      placement: { eq: opts.placement },
      step: { eq: opts.step },
      userCategory: { eq: opts.userCategory },
    }

    const { formDefinition } = await this.query({
      query: gql`
        query getFormDefinition($filter: FormDefinitionModelFilter, $locale: SiteLocale) {
          formDefinition(filter: $filter, locale: $locale) {
            ...fieldsOnFormDefinitionRecord
          }
        }

        ${FormDefinitionRecord}
      `,
      variables: {
        filter,
        locale,
      },
    })

    return formDefinition
  }

  /**
   * @param {string} locale
   * @param {string} slug
   * @returns {Promise<DatoInternationalCategoryPage>}
   */
  async getInternationalCategoryPage(locale, slug) {
    const { internationalCategoryPage } = await this.query({
      query: gql`
        query getInternationalCategoryPage($locale: SiteLocale, $slug: String) {
          internationalCategoryPage(filter: { slug: { eq: $slug } }, locale: $locale) {
            ...fieldsOnInternationalCategoryPageRecord
          }
        }

        ${InternationalCategoryPageRecord}
      `,
      variables: {
        locale,
        slug: slug.toLowerCase(),
      },
    })

    return internationalCategoryPage
  }

  /**
   * @param {string} locale
   * @returns {Promise<DatoInternationalProduct>}
   */
  async getInternationalProduct(locale) {
    const { internationalProduct } = await this.query({
      query: gql`
        query getInternationalProduct($locale: SiteLocale) {
          internationalProduct(locale: $locale) {
            ...fieldsOnInternationalProductRecord
          }
        }

        ${InternationalProductRecord}
      `,
      variables: {
        locale,
      },
    })

    return internationalProduct
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @returns {Promise<DatoLandingPage>}
   */
  async getLandingPage(slug, locale) {
    const { landingPage } = await this.query({
      query: gql`
        query getLandingPageBySlug($locale: SiteLocale, $slug: String!) {
          landingPage(filter: { slug: { eq: $slug } }, locale: $locale) {
            ...fieldsOnLandingPageRecord
          }
        }

        ${LandingPageRecord}
      `,
      variables: {
        locale,
        slug: slug.toLowerCase(),
      },
    })
    return landingPage
  }

  /**
   * @param {string} slug
   * @param {string} locale
   * @returns {Promise<DatoLandingPage>}
   */
  async getPage(slug, locale) {
    const fragment = await getPageRecord(this)

    const { page } = await this.query({
      query: gql`
        query getPageBySlug($locale: SiteLocale, $slug: String!) {
          page(filter: { slug: { eq: $slug } }, locale: $locale) {
            ...fieldsOnPageRecord
          }
        }

        ${fragment}
      `,
      variables: {
        locale,
        slug: slug.toLowerCase(),
      },
    })

    return page
  }

  /**
   * @param {string} productType
   * @param {string} locale
   * @returns {Promise<DatoLegalText>}
   */
  async getLegalText(productType, locale) {
    const { legalText } = await this.query({
      query: gql`
        query LegalText($locale: SiteLocale, $productType: String!) {
          legalText(locale: $locale, filter: { productType: { eq: $productType } }) {
            ...fieldsOnLegalText
          }
        }

        ${LegalTextRecord}
      `,
      variables: {
        locale,
        productType,
      },
    })

    return legalText
  }

  /**
   * @param {string} locale
   * @param {string} type - DEFAULT, GFS or CATALOG
   * @return {Promise<*>}
   */
  async getMainNavigation(locale, type = CONST.cms.dato.mainNavigationType.default) {
    const { mainNavigation } = await this.query({
      query: gql`
        query mainNavigation($type: String, $locale: SiteLocale) {
          mainNavigation(filter: { typeNavigation: { eq: $type } }, locale: $locale) {
            ...fieldsOnMainNavigationRecord
          }
        }

        ${MainNavigationRecord}
      `,
      variables: {
        locale,
        type,
      },
    })

    return mainNavigation
  }

  async getFooterNavigation(locale, footerNumber = CONST.cms.footerNumber.ONE) {
    const { footer } = await this.query({
      query: gql`
        query getFooterNavigation($locale: SiteLocale, $footerNumber: String) {
          footer(locale: $locale, filter: { footerNumber: { eq: $footerNumber } }) {
            ...fieldsOnFooterRecord
          }
        }

        ${FooterRecord}
      `,
      variables: {
        footerNumber,
        locale,
      },
    })

    return footer
  }

  /**
   * @param {Object} filter
   * @param {string} locale
   * @returns {Promise<DatoCategoryPage>}
   */
  async _getCategoryPage(filter, locale) {
    const { categoryPage } = await this.query({
      query: gql`
        query getCategoryPageBySlug($filter: CategoryPageModelFilter, $locale: SiteLocale) {
          categoryPage(filter: $filter, locale: $locale) {
            ...fieldsOnCategoryPageRecord
          }
        }

        ${CategoryPageRecord}
      `,
      variables: {
        filter,
        locale,
      },
    })

    return categoryPage
  }

  /**
   * @param {Object} filter
   * @param {string} locale
   * @returns {Promise<DatoDefaultBlock>}
   */
  async _getDefaultBlock(filter, locale) {
    const { defaultBlock } = await this.query({
      query: gql`
        query getDefaultBlock($filter: DefaultBlockModelFilter, $locale: SiteLocale) {
          defaultBlock(filter: $filter, locale: $locale) {
            ...fieldsOnDefaultBlockRecord
          }
        }

        ${DefaultBlockRecord}
      `,
      variables: {
        filter,
        locale,
      },
    })

    return defaultBlock
  }
}

module.exports = Cms
