const { ApolloClient, ApolloLink, HttpLink, InMemoryCache, concat, gql } = require('@apollo/client/core')
const apm = require('elastic-apm-node')

/** @typedef {import('@apollo/client').ApolloError} ApolloError */

const config = require('../../../config')
const fetch = require('../../../helpers/fetch')
const fs = require('fs')
const possibleTypes = require('./possibleTypes.json')

/** @type {Map<string | undefined, InMemoryCache>} - cache by channel */
const caches = new Map()
/** @type {Map<string | undefined, ApolloClient>} - client by channel */
const clients = new Map()

class DatoCMS {
  /**
   * @private
   * @param {string} [seaKey]
   * @returns {ApolloClient}
   */
  getClient(seaKey) {
    if (!clients.has(seaKey)) {
      const httpLink = new HttpLink({
        fetch,
        fetchOptions: { timeout: config.datasources.timeout },
        headers: {
          Referer: `https://apollo.${config.environment}.${config.site}/`,
        },
        uri: config.datocms.endpointUrl,
      })

      let datoEnvironment = config.datocms.environment
      if (seaKey) {
        if (!config.sea[seaKey]) {
          throw new Error(`invalid sea config key given: ${seaKey}`)
        }
        datoEnvironment = config.sea[seaKey].datocmsEnv
      }

      const authMiddleware = new ApolloLink((operation, forward) => {
        operation.setContext(({ headers = {} }) => ({
          headers: {
            ...headers,
            ...(datoEnvironment ? { 'X-Environment': datoEnvironment } : undefined),
            authorization: `Bearer ${config.datocms.token}`,
          },
        }))
        return forward(operation)
      })

      if (!caches.has(seaKey)) {
        caches.set(seaKey, new InMemoryCache({ possibleTypes }))
      }

      const client = new ApolloClient({
        cache: caches.get(seaKey),
        defaultOptions: {
          query: {
            fetchPolicy: 'network-only',
          },
          watchQuery: {
            fetchPolicy: 'cache-and-network',
          },
        },
        link: concat(authMiddleware, httpLink),
      })
      clients.set(seaKey, client)
    }
    return clients.get(seaKey)
  }

  /**
   * Given a structured text field name, returns its possible inline block types
   *
   * @param {string} name
   * @returns {string[]}
   */
  async fieldInlineBlocks(name) {
    const schema = await this.schema()

    const type = schema.types.find((t) => t.name === name)
    if (!type) return []
    const blocks = type.fields.find((f) => f.name === 'blocks')
    if (!blocks) return []

    const blockType = blocks.type.ofType.ofType.ofType

    switch (blockType.kind) {
      case 'SCALAR':
        return []
      case 'OBJECT':
        return [blockType.name]
      case 'UNION':
        return schema.types.find((t) => t.name === blockType.name).possibleTypes.map((t) => t.name)
    }
  }

  /**
   * Generates possibleTypes.json file, used by apollo client to match fragments
   * ⚠️ Needs to be regenerated each time the schema is updated in this area!!
   *
   * usage:
   * ```
   *   docker compose exec backend ash
   *   SITE=it node
   *   Dato = require('./src/graphql/datasources/datocms/Cms')
   *   d = new Dato()
   *   d.generateFragments()
   * ```
   */
  async generateFragments() {
    const {
      data: {
        __schema: { types },
      },
    } = await this.client().query({
      query: gql`
        {
          __schema {
            types {
              kind
              name
              possibleTypes {
                name
              }
            }
          }
        }
      `,
    })
    const possibleTypes = {}

    for (const supertype of types) {
      if (supertype.possibleTypes) {
        possibleTypes[supertype.name] = supertype.possibleTypes.map((subtype) => subtype.name)
      }
    }

    fs.writeFileSync(`${__dirname}/possibleTypes.json`, JSON.stringify(possibleTypes))
  }

  /**
   * @param {QueryOptions<any>} opt
   * @param {string} [seaKey]
   * @param {number} attempt
   * @returns {Promise<any>}
   */
  query(opt, seaKey, attempt = 0) {
    return new Promise((resolve, reject) => {
      let isfirst = true

      const queryName = opt.query.definitions?.find((def) => def.operation === 'query')?.name.value || 'datocms-query'

      const span = apm.startSpan(queryName, 'datocms')
      span?.addLabels({
        ...(opt.variables ?? {}),
        attempt,
      })

      const sub = this.getClient(seaKey)
        .watchQuery({
          ...opt,
          fetchPolicy: 'cache-and-network',
        })
        .subscribe({
          error: (e) => {
            sub.unsubscribe()
            reject(e)
            span?.setOutcome('failure')
            span?.end()
          },
          next: ({ data }) => {
            if (!isfirst) sub.unsubscribe()
            isfirst = false
            resolve(data)
            span?.setOutcome('success')
            span?.end()
          },
        })
    }).catch(
      /**
       * @param {ApolloError} e
       * @returns {Promise<any>}
       */
      (e) => {
        if (e.networkError?.statusCode === 429 && attempt < 5) {
          // randomize the retry delay somewhere btw 800 and 2000 ms to try to minimize remaining 429
          const delay = Math.floor(800 + Math.random() * 1200)
          return new Promise((resolve) => setTimeout(resolve, delay)).then(() => this.query(opt, seaKey, attempt + 1))
        }
        throw e
      }
    )
  }

  async schema() {
    if (!this._schema) {
      const response = await this.getClient().query({
        query: gql`
          query IntrospectionQuery {
            __schema {
              queryType {
                name
              }
              mutationType {
                name
              }
              subscriptionType {
                name
              }
              types {
                ...FullType
              }
              directives {
                name
                description

                locations
                args {
                  ...InputValue
                }
              }
            }
          }

          fragment FullType on __Type {
            kind
            name
            description

            fields(includeDeprecated: true) {
              name
              description
              args {
                ...InputValue
              }
              type {
                ...TypeRef
              }
              isDeprecated
              deprecationReason
            }
            inputFields {
              ...InputValue
            }
            interfaces {
              ...TypeRef
            }
            enumValues(includeDeprecated: true) {
              name
              description
              isDeprecated
              deprecationReason
            }
            possibleTypes {
              ...TypeRef
            }
          }

          fragment InputValue on __InputValue {
            name
            description
            type {
              ...TypeRef
            }
            defaultValue
          }

          fragment TypeRef on __Type {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                  ofType {
                    kind
                    name
                    ofType {
                      kind
                      name
                      ofType {
                        kind
                        name
                        ofType {
                          kind
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        `,
      })

      this._schema = response.data.__schema
    }

    return this._schema
  }

  /**
   * @param {string} typeName
   * @param {field} fieldName
   * @returns {Promise<boolean>}
   */
  async typeHasField(typeName, fieldName) {
    const schema = await this.schema()

    const type = schema.types.find((t) => t.name === typeName)
    if (!type) return false
    return type.fields.some((f) => f.name === fieldName)
  }
}

module.exports = DatoCMS
