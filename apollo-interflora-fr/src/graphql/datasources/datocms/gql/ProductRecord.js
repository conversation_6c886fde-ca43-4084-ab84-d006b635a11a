const { gql } = require('@apollo/client/core')

const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const LinkRecord = require('./LinkRecord')
const Reinsurances = require('./ReinsuranceItemRecord')
const SectionRecord = require('./SectionRecord')
const SeoImageBlockRecord = require('./SeoImageBlockRecord')
const Tag = require('./Tag')
const VideoField = require('./VideoField')

module.exports = gql`
  fragment fieldsOnProductRecord on ProductRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    additionalInformations
    afterHeader {
      ...fieldsOnSectionRecord
    }
    afterInfo {
      ...fieldsOnSectionRecord
    }
    backOfficeReady
    badge {
      id
      key
      title
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    body {
      blocks
      links
      value
    }
    canonical
    careDifficulty
    commercetoolsProduct
    commercetoolsAccessories
    defaultCategory {
      commercetoolsCategory
      id
      parent {
        slug
        title
      }
      slug
      title
    }
    highlightedText
    id
    productType
    reinsurance {
      ...fieldsOnReinsuranceItemRecord
    }
    richDeliveryInformation {
      description {
        value
      }
      image {
        ...fieldsOnFileField
      }
      title
    }
    richDescription {
      ...fieldsOnSeoImageBlockRecord
    }
    seoMeta {
      description
      image {
        ...fieldsOnFileField
      }
      title
      twitterCard
    }
    seoMetaRobots
    slug
    title(locale: $locale)
    useQuantitySelector
    vimeoVideo {
      ...fieldsOnVideoField
    }
  }

  ${Tag}
  ${ExternalLinkRecord}
  ${FileField}
  ${LinkRecord}
  ${Reinsurances}
  ${SectionRecord}
  ${SeoImageBlockRecord}
  ${VideoField}
`
