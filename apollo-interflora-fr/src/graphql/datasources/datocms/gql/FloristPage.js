const { gql } = require('@apollo/client/core')

const FileField = require('./FileField')
const FloristPageModelBodyField = require('./FloristPageModelBodyField')
const SectionRecord = require('./SectionRecord')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnFloristPageRecord on FloristPageRecord {
    __typename
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    afterHeader {
      ...fieldsOnSectionRecord
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    body(locale: $locale) {
      ...fieldsOnFloristPageModelBodyField
    }
    canonical
    code
    id
    image {
      ...fieldsOnFileField
    }
    locality
    name
    postalCode
    region {
      code
      name
      slug
    }
    seoMetaRobots
    slug
    subtitle(locale: $locale)
    title(locale: $locale)
  }

  ${FileField}
  ${FloristPageModelBodyField}
  ${SectionRecord}
  ${Tag}
`
