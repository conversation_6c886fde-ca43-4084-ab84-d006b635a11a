const { gql } = require('@apollo/client/core')

const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const LinkRecord = require('./LinkRecord')
const Reinsurances = require('./ReinsuranceItemRecord')
const SeoImageBlockRecord = require('./SeoImageBlockRecord')
const VideoField = require('./VideoField')

module.exports = gql`
  fragment fieldsOnProductRecordNoSection on ProductRecord {
    additionalInformations
    backOfficeReady
    badge {
      id
      key
      title
    }
    body {
      blocks
      links
      value
    }
    careDifficulty
    commercetoolsProduct
    defaultCategory {
      id
      parent {
        slug
        title
      }
      slug
      title
      commercetoolsCategory
    }
    highlightedText
    id
    productType
    reinsurance {
      ...fieldsOnReinsuranceItemRecord
    }
    richDeliveryInformation {
      description {
        value
      }
      image {
        ...fieldsOnFileField
      }
      title
    }
    richDescription {
      ...fieldsOnSeoImageBlockRecord
    }
    seoMeta {
      description
      image {
        ...fieldsOnFileField
      }
      title
      twitterCard
    }
    slug
    title(locale: $locale)
    useQuantitySelector
    vimeoVideo {
      ...fieldsOnVideoField
    }
  }

  ${ExternalLinkRecord}
  ${FileField}
  ${LinkRecord}
  ${Reinsurances}
  ${SeoImageBlockRecord}
  ${VideoField}
`
