const { gql } = require('@apollo/client/core')

const LinkRecord = require('./LinkRecord')
const ExternalLinkRecord = require('./ExternalLinkRecord')

module.exports = gql`
  fragment fieldsOnNavItemRecord on NavItemRecord {
    blockType
    clientSide
    highlightLabel
    highlighted
    icon
    id
    link {
      ...fieldsOnLinkRecord
      ...fieldsOnExternalLinkRecord
    }
    mentions
    obfuscated
    obfuscationExclusions
    promote
    targetBlank
    title
  }

  ${LinkRecord}
  ${ExternalLinkRecord}
`
