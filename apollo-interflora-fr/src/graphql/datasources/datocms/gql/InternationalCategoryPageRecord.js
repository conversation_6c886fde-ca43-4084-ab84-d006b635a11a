const { gql } = require('@apollo/client/core')

const InternationalCategoryPageModelBodyField = require('./InternationalCategoryPageModelBodyField')
const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const IconTeaserRecord = require('./IconTeaserRecord')
const LinkRecord = require('./LinkRecord')
const SectionRecord = require('./SectionRecord')
const SeoField = require('./SeoField')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnInternationalCategoryPageRecord on InternationalCategoryPageRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    afterHeader {
      ...fieldsOnSectionRecord
    }
    afterProductList {
      ...fieldsOnSectionRecord
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    beforeProductList {
      ...fieldsOnSectionRecord
    }
    body {
      ...fieldsOnInternationalCategoryPageModelBodyField
    }
    canonical
    firstListItem {
      image {
        ...fieldsOnFileField
      }
      items {
        ...fieldsOnIconTeaserRecord
      }
    }
    listPromotion {
      description
      image {
        ...fieldsOnFileField
      }
      link {
        ...fieldsOnLinkRecord
        ...fieldsOnExternalLinkRecord
      }
      title
    }
    name(locale: $locale)
    seoMeta {
      ...fieldsOnSeoField
    }
    seoMetaRobots
    slug
    title(locale: $locale)
  }

  ${InternationalCategoryPageModelBodyField}
  ${ExternalLinkRecord}
  ${FileField}
  ${IconTeaserRecord}
  ${LinkRecord}
  ${SectionRecord}
  ${SeoField}
  ${Tag}
`
