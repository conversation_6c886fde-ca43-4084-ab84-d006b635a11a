const { gql } = require('@apollo/client/core')

const ExternalLinkRecord = require('./ExternalLinkRecord')
const LinkRecord = require('./LinkRecord')
const Reinsurances = require('./ReinsuranceItemRecord')
const SectionRecord = require('./SectionRecord')
const SeoImageBlockRecord = require('./SeoImageBlockRecord')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnInternationalProductRecord on InternationalProductRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    afterHeader {
      ...fieldsOnSectionRecord
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    id
    reinsurance {
      ...fieldsOnReinsuranceItemRecord
    }
    seoMeta {
      description
      image {
        ...fieldsOnFileField
      }
      title
      twitterCard
    }
    slug
    title(locale: $locale)
  }

  ${Tag}
  ${ExternalLinkRecord}
  ${LinkRecord}
  ${Reinsurances}
  ${SectionRecord}
  ${SeoImageBlockRecord}
`
