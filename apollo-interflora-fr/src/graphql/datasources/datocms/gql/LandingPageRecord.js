const SectionRecord = require('./SectionRecord')
const Tag = require('./Tag')
const FestMode = require('./FestModeRecord')
const SeoField = require('./SeoField')
const ExternalLinkRecord = require('./ExternalLinkRecord')
const LinkRecord = require('./LinkRecord')
const { gql } = require('@apollo/client/core')

module.exports = gql`
  fragment fieldsOnLandingPageRecord on LandingPageRecord {
    __typename
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    afterHeader {
      ...fieldsOnSectionRecord
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    canonical
    content {
      ...fieldsOnSectionRecord
    }
    festMode {
      ...fieldsOnFestModeRecord
    }
    seoMeta {
      ...fieldsOnSeoField
    }
    seoMetaRobots
    slug
    title(locale: $locale)
  }

  ${Tag}
  ${SectionRecord}
  ${ExternalLinkRecord}
  ${LinkRecord}
  ${FestMode}
  ${SeoField}
`
