const { gql } = require('@apollo/client/core')

const FileField = require('./FileField')
const FloristPage = require('./FloristPage')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnFloristRegionRecord on FloristRegionRecord {
    ...fieldsOnFloristRegionRecordWithoutOthers
    otherRegions {
      ...fieldsOnFloristRegionRecordWithoutOthers
    }
  }

  fragment fieldsOnFloristRegionRecordWithoutOthers on FloristRegionRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    body {
      blocks
      links
      value
    }
    canonical
    code
    ctaHref
    ctaLabel
    ctaShow
    ctaTitle
    image {
      ...fieldsOnFileField
    }
    mainCities {
      ...fieldsOnFloristPageRecord
    }
    mainCity {
      ...fieldsOnFloristPageRecord
    }
    name
    regionCode
    regionName
    slug
    seoMetaRobots
    afterOtherRegions {
      blocks
      links
      value
    }
    type: regionType
  }

  ${FileField}
  ${FloristPage}
  ${Tag}
`
