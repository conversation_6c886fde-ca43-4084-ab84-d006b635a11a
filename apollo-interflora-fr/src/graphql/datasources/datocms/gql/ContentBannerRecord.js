const { gql } = require('@apollo/client/core')

const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const LinkRecord = require('./LinkRecord')

module.exports = gql`
  fragment fieldsOnContentBannerRecord on ContentBannerRecord {
    description
    image {
      ...fieldsOnFileField
    }
    link {
      ...fieldsOnLinkRecord
      ...fieldsOnExternalLinkRecord
    }
    imageType
  }

  ${ExternalLinkRecord}
  ${FileField}
  ${LinkRecord}
`
