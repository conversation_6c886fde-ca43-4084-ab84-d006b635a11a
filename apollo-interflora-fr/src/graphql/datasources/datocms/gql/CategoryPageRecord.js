const { gql } = require('@apollo/client/core')

const CategoryPageModelBodyField = require('./CategoryPageModelBodyField')
const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const IconTeaserRecord = require('./IconTeaserRecord')
const LinkRecord = require('./LinkRecord')
const SectionRecord = require('./SectionRecord')
const SeoField = require('./SeoField')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnCategoryPageRecord on CategoryPageRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    afterHeader {
      ...fieldsOnSectionRecord
    }
    afterProductList {
      ...fieldsOnSectionRecord
    }
    beforeFooter {
      ...fieldsOnSectionRecord
    }
    beforeProductList {
      ...fieldsOnSectionRecord
    }
    body {
      ...fieldsOnCategoryPageModelBodyField
    }
    bloomreachDisabled
    canonical
    cardSuggestions {
      message
    }
    commercetoolsCategory
    firstListItem {
      image {
        ...fieldsOnFileField
      }
      items {
        ...fieldsOnIconTeaserRecord
      }
    }
    gellules {
      exclude
      label
      target {
        __typename
        ... on CategoryPageRecord {
          id
          parent {
            slug
            title
          }
          slug
          title
        }
      }
    }
    listPromotion {
      description
      image {
        ...fieldsOnFileField
      }
      link {
        ...fieldsOnLinkRecord
        ...fieldsOnExternalLinkRecord
      }
      title
    }
    name(locale: $locale)
    seoMeta {
      ...fieldsOnSeoField
    }
    seoMetaRobots
    slug
    title(locale: $locale)
    legalTextProductType
  }

  ${CategoryPageModelBodyField}
  ${ExternalLinkRecord}
  ${FileField}
  ${IconTeaserRecord}
  ${LinkRecord}
  ${SectionRecord}
  ${SeoField}
  ${Tag}
`
