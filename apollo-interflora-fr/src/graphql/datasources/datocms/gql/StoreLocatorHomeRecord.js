const { gql } = require('@apollo/client/core')
const SeoFieldRecord = require('./SeoField')
const SectionRecord = require('./SectionRecord')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnStorLocatorHome on StoreLocatorHomeRecord {
    title: floristDepartmentsTitle
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    canonical
    floristDepartmentsMetaSeo {
      ...fieldsOnSeoField
    }
    floristDepartmentsTitle
    floristDepartmentsBreadcrumb
    floristDepartmentsBeforeFooter {
      ...fieldsOnSectionRecord
    }
    floristDepartmentsAfterHeader {
      ...fieldsOnSectionRecord
    }
    floristDepartmentsHeading
    seoMetaRobots
  }

  ${Tag}
  ${SectionRecord}
  ${SeoFieldRecord}
`
