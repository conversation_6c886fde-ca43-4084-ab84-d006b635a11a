const { gql } = require('@apollo/client/core')

const FileField = require('./FileField')
const LinkRecord = require('./LinkRecord')
const ExternalLinkRecord = require('./ExternalLinkRecord')

module.exports = gql`
  fragment fieldsOnMainBannerRecord on MainBannerRecord {
    title
    subtitle
    image {
      ...fieldsOnFileField
    }
    link {
      ...fieldsOnLinkRecord
      ...fieldsOnExternalLinkRecord
    }
  }

  ${FileField}
  ${LinkRecord}
  ${ExternalLinkRecord}
`
