const { gql } = require('@apollo/client/core')

const HeaderBannerRecord = require('./HeaderBannerRecord')
const ProductRecord = require('./ProductRecord')

/** @typedef {import('../DatoCMS')} DatoCMS */

/**
 * @param {DatoCMS} datocms
 * @returns {DocumentNode}
 */
module.exports = async (datocms) => {
  const hasHeaderBanner = await datocms.typeHasField('ConfigurationRecord', 'headerBanners')
  const hasRoundUpProducts = await datocms.typeHasField('ConfigurationRecord', 'roundUpProducts')

  return gql`
    fragment fieldsOnConfigurationRecord on ConfigurationRecord {
      ${
        hasHeaderBanner
          ? `
      headerBanners {
        ...fieldsOnHeaderBannerRecord
      } `
          : ''
      }
      ${
        hasRoundUpProducts
          ? `roundUpProducts(locale: $locale) {
              ...fieldsOnProductRecord
            }`
          : ''
      }
      
    }

    ${hasHeaderBanner ? HeaderBannerRecord : ''}
    ${hasRoundUpProducts ? ProductRecord : ''}
  `
}
