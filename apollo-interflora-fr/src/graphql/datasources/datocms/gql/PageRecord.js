const ExternalLinkRecord = require('./ExternalLinkRecord')
const SectionRecord = require('./SectionRecord')
const SeoField = require('./SeoField')
const Tag = require('./Tag')
const { gql } = require('@apollo/client/core')
const bodyBlocks = require('../../../../helpers/dato/blocks')

/** @typedef {import('../DatoCMS')} DatoCMS */

/**
 * @param {DatoCMS} datocms
 * @returns {DocumentNode}
 */
module.exports = async (datocms) => {
  const bodyBlockTypes = await datocms.fieldInlineBlocks('PageModelBodyField')
  const pageBlocks = bodyBlocks.filter((bodyBlock) => bodyBlockTypes.includes(bodyBlock.type))

  const blockFragments = pageBlocks.map(
    (block) => `... on ${block.type} {
      ${block.gqlFragment}
    }`
  )

  const includes = pageBlocks.filter((block) => Boolean(block.gqlInclude)).map((block) => block.gqlInclude)

  const blocksGql = pageBlocks.length
    ? `{
    ${blockFragments.join('\n')}
  }`
    : ''

  let pageFragmentGql = gql`
    fragment fieldsOnPageRecord on PageRecord {
      __typename
      _seoMetaTags(locale: $locale) {
        ...fieldsOnTag
      }
      afterHeader {
        ...fieldsOnSectionRecord
      }
      beforeFooter {
        ...fieldsOnSectionRecord
      }
      canonical
      slug
      title
      body {
        value
        blocks ${blocksGql}
        links
      }
      seoMeta {
        ...fieldsOnSeoField
      }
      seoMetaRobots
    }

    ${ExternalLinkRecord}
    ${SectionRecord}
    ${SeoField}
    ${Tag}
  `

  for (const include of includes) {
    pageFragmentGql = gql`
      ${pageFragmentGql}
      ${include}
    `
  }

  return pageFragmentGql
}
