const { gql } = require('@apollo/client/core')

const ExternalLinkRecord = require('./ExternalLinkRecord')
const FileField = require('./FileField')
const IconTeaserRecord = require('./IconTeaserRecord')
const LinkRecord = require('./LinkRecord')
const Product = require('./ProductRecordNoSection')
const Tag = require('./Tag')

module.exports = gql`
  fragment fieldsOnSectionRecord on SectionRecord {
    _seoMetaTags(locale: $locale) {
      ...fieldsOnTag
    }
    background
    blocks {
      ... on AccordionListRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        id
        items {
          title
          text {
            blocks
            links
            value
          }
        }
      }
      ... on ArticleRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        items {
          description(markdown: true)
          icon
          image {
            ...fieldsOnFileField
          }
          link {
            __typename
            ... on ExternalLinkRecord {
              ...fieldsOnExternalLinkRecord
            }
            ... on LinkRecord {
              ...fieldsOnLinkRecord
            }
          }
          subtitle
        }
        subtitle
        title
      }
      ... on ColumnsBlockRecord {
        reverse
        column {
          image {
            ...fieldsOnFileField
          }
          title
          subtitle
          description
          align
          cta {
            ...fieldsOnLinkRecord
          }
        }
      }
      ... on ContentBannerRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description
        image {
          ...fieldsOnFileField
        }
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        imageType
      }
      ... on ContentBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        title
        highlighted
        content {
          value
        }
      }
      ... on ExpertBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description
        expertName
        expertTitle
        image {
          ...fieldsOnFileField
        }
        links {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        profileImage {
          ...fieldsOnFileField
        }
        statement
        subtitle
        title
      }
      ... on HeaderPhoneRecord {
        mention
        phone
      }
      ... on CardBannerRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        buttonLabel
        buttonUrl
        description
        image {
          ...fieldsOnFileField
        }
        links {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        subtitle
        title
        showTitle
      }
      ... on IconTeaserListRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        items {
          ...fieldsOnIconTeaserRecord
        }
      }
      ... on ImageBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description {
          blocks
          links
          value
        }
        image {
          ...fieldsOnFileField
        }
        title
      }
      ... on InterfloraPlusBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description(markdown: true)
        image {
          ...fieldsOnFileField
        }
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        title
        legals
      }
      ... on MainBannerRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        title
        subtitle
        image {
          ...fieldsOnFileField
        }
        link {
          ...fieldsOnLinkRecord
          ...fieldsOnExternalLinkRecord
        }
      }
      ... on ModularContentBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        content {
          __typename
          ... on ContentHtmlRecord {
            html
          }
          ... on CtaRecord {
            align
            link {
              __typename
              ... on ExternalLinkRecord {
                ...fieldsOnExternalLinkRecord
              }
              ... on LinkRecord {
                ...fieldsOnLinkRecord
              }
            }
          }
        }
      }
      ... on MomentFirstRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description
        image {
          ...fieldsOnFileField
        }
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        dropdownLinks {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        subtitle
      }
      ... on ProductCarouselRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        display
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        products {
          ...fieldsOnProductRecordNoSection
        }
        subtitle
        title
      }
      ... on ProductListRecord {
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        products {
          ...fieldsOnProductRecordNoSection
        }
        subtitle
        title
      }
      ... on QuoteRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        name
        portrait {
          ...fieldsOnFileField
        }
        professionelTitle
        quote
      }
      ... on RotatingBannerRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        displayType
        items {
          description
          icon
          image {
            ...fieldsOnFileField
          }
          link {
            __typename
            ... on ExternalLinkRecord {
              ...fieldsOnExternalLinkRecord
            }
            ... on LinkRecord {
              ...fieldsOnLinkRecord
            }
          }
          subtitle
        }
        subtitle
        title
      }
      ... on SeoBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        description(markdown: true)
        image {
          ...fieldsOnFileField
        }
        subtitle
        title
      }
      ... on SeoImageBlockRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        image {
          ...fieldsOnFileField
        }
        items {
          cssClass
          subtitle
          title
        }
        title
        description
      }
      ... on TeaserListRecord {
        _seoMetaTags(locale: $locale) {
          ...fieldsOnTag
        }
        items {
          description
          image {
            ...fieldsOnFileField
          }
          link {
            __typename
            ... on ExternalLinkRecord {
              ...fieldsOnExternalLinkRecord
            }
            ... on LinkRecord {
              ...fieldsOnLinkRecord
            }
          }
          subtitle
        }
        link {
          __typename
          ... on ExternalLinkRecord {
            ...fieldsOnExternalLinkRecord
          }
          ... on LinkRecord {
            ...fieldsOnLinkRecord
          }
        }
        subtitle
        title
      }
      ... on TitleRecord {
        level
        subtitle
        title
      }
    }
    title
    wrapper
  }

  ${ExternalLinkRecord}
  ${FileField}
  ${IconTeaserRecord}
  ${LinkRecord}
  ${Product}
  ${Tag}
`
