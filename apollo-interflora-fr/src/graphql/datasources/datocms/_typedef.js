/**
 * @typedef {{
 *  commercetoolsProduct: string
 *  legalNotice: string
 *  maxQuantity: number
 *  hasCustomText: boolean
 * }} DatoAccessoryInfo
 */

/**
 * @typedef {{
 *  commercetoolsCategory: string,
 *  defaultSettings: DatoAccessorySettings[]
 * }} DatoAccessoryCategory
 */

/**
 * @typedef {{
 *  id: string,
 *  position: number,
 *  assetId: string|null
 *  useDefaultTypes: boolean|undefined
 *  types: string[]
 * }} DatoAccessorySettings
 */

/**
 * @typedef {{
 *   defaultOrder: boolean
 *   accessories: DatoAccessorySettings[]
 * }} DatoVariantAccessoryList
 */

/**
 * @typedef {{
 *  blocks: string[]
 *  links: string[]
 *  value: Object
 * }} DatoAdditionalInformationBody
 */

/**
 * @typedef {(DatoCategoryPage|DatoCategoryPageTree)} DatoProductCategory
 */

/**
 * @typedef {{
 *  headerBanners: {
 *    content: DatoAdditionalInformationBody,
 *    from: Date,
 *    to: Date,
 *    where: ('category'|'funnel'|'home'|'other'|'product')[]
 *  }[]
 * roundUpProducts: DatoProduct[]
 * }} DatoConfiguration
 */

/**
 * @typedef {{ message: string }} DatoCardSuggestion
 */

/**
 * @typedef {{
 *  alt: string
 *  author: string
 *  basename: string
 *  blurUpThumb: string
 *  blurhash: string
 *  colors: {
 *    alpha: number
 *    blue: number
 *    green: number
 *    hex: string
 *    red: number
 *  }[]
 *  copyright: string
 *  customData: string
 *  exitInfo: string
 *  filename: string
 *  folcalPoint: {
 *    x: number
 *    y: number
 *  }
 *  format: string
 *  height: number
 *  id: string
 *  md5: string
 *  mimeType: string
 *  nodes: string
 *  responsiveImage: {
 *    alt: string
 *    aspectRatio: number
 *    base64: string
 *    bgColor: string
 *    height: number
 *    sizes: string
 *    src: string
 *    srcSet: string
 *    title: string
 *    webpSrcSet: string
 *    width: number
 *  }
 *  size: number
 *  smartTags: string[]
 *  tags: string[]
 *  title: string
 *  url: string
 *  video: DatoUploadVideoField
 *  width: number
 * }} DatoFileField
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  __typename: string
 *  afterHeader: DatoSection[]
 *  afterProductList: DatoSection[]
 *  beforeFooter: DatoSection[]
 *  beforeProductList: DatoSection[]
 *  body: DatoCategoryBody
 *  firstListItem: {
 *   image: DatoFileField
 *   items: DatoIconTeaserRecord[]
 *  }
 *  name: string
 *  seoMeta: DatoSeo
 *  slug: string
 *  title: string
 * }} DatoInternationalCategoryPage
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  afterHeader: DatoSection[]
 *  seoMeta: DatoSeo
 *  slug: string
 *  title: string
 * }} DatoInternationalProduct
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  __typename: string
 *  afterHeader: DatoSection[]
 *  afterProductList: DatoSection[]
 *  beforeFooter: DatoSection[]
 *  beforeProductList: DatoSection[]
 *  body: DatoCategoryBody
 *  cardSuggestions: DatoCartSuggestion[]
 *  commercetoolsCategory: string
 *  firstListItem: {
 *   image: DatoFileField
 *   items: DatoIconTeaserRecord[]
 *  }
 *  id: string
 *  insideProductList: DatoContentBlockRecord[]
 *  name: string
 *  parent: {
 *   slug: string
 *   title: string
 *  }
 *  seoMeta: DatoSeo
 *  slug: string
 *  startOfProductList: DatoProductListReassurance[]
 *  title: string
 * }} DatoCategoryPage
 */

/**
 * @typedef {{
 *  cssClass: string
 *  id: string
 *  subtitle: string
 *  title: string
 * }} DatoCssIconTeaser
 */

/**
 * @typedef {{
 *  body: {
 *    value: Object
 *  }
 *  code: string
 *  ctaLabel: string
 *  ctaTitle: string
 *  ctaHref: string
 *  ctaShow: string
 *  image: DatoFileField
 *  mainCities: DatoFloristPage[]
 *  mainCity: DatoFloristPage
 *  otherRegions: DatoFloristRegion[]
 *  regionCode: string
 *  regionName: string
 *  afterOtherRegions: {
 *    value: Object
 *  }
 * }} DatoFloristRegion
 */

/**
 * @typedef {{
 *  footerNumber: string
 *  id: string
 *  label: string
 *  url: string
 *  obfuscated: boolean
 *  subItems: DatoNavItem[]
 * }} DatoFooterNavigation
 */

/**
 * @typedef {{
 *  id: string
 *  label: string
 *  newWindow: boolean
 *  url: string
 * }} DatoExternalLinkRecord
 */

/**
 * @typedef {{
 *  description: string
 *  image: DatoFileField
 *  link: DatoLinkRef
 *  title: string
 *  legals: string
 * }} DatoInterfloraPlusBlockRecord
 */

/**
 * @typedef {{
 *  label: string
 *  target: (
 *    {
 *      __typename: 'CategoryPageRecord'
 *      id: string
 *      slug: string
 *    }|
 *    {
 *      __typename: 'LandingPageRecord'
 *      id: string
 *      slug: string
 *    }|
 *    {
 *      __typename: 'PageRecord'
 *      id: string
 *      slug: string
 *    }|
 *    {
 *      __typename: 'ProductRecord'
 *      commercetoolsProduct: string
 *      slug: string
 *    }
 *  )
 * }} DatoLinkRecord
 */

/**
 * @typedef { ({ __typename: string } & (DatoExternalLinkRecord | DatoLinkRecord))[] } DatoLinkRef
 */

/**
 * @typedef {{
 *  id: string
 *  menuItems: DatoNavItem[]
 * }} DatoMainNavigation
 */

/**
 * @typedef {{
 *  gellules: DatoMessageCardBlockRecord[]
 *  id: string
 *  warehouseType: string
 * }} DatoMessageCardRecord
 */

/**
 * @typedef {{
 * exclude: boolean
 * icon: string
 * id: string
 * label: string
 * products: DatoProduct[]
 * }} DatoMessageCardBlockRecord
 */

/**
 * @typedef {{
 *  blockType: string
 *  clientSide: Boolean
 *  highlightLabel: string
 *  highlighted: boolean
 *  obfuscated: boolean
 *  icon: string
 *  id: string
 *  link: DatoLinkRef
 *  promote: boolean
 *  subItems: DatoNavItem[]
 *  targetBlank: boolean
 *  title: string
 *  obfuscationExclusions : String[]
 * }} DatoNavItem
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  additionalInformations: string
 *  afterHeader: DatoSection[]
 *  afterInfo: DatoSection[]
 *  badge: {
 *    id: string
 *    key: string
 *    title: string
 *  }
 *  body: DatoProductBody
 *  careDifficulty: ('level-1'|'level-2'|'level-3')
 *  commercetoolsProduct: string
 *  commercetoolsAccessories: Record<string, DatoVariantAccessoryList>
 *  defaultCategory: {
 *   parent: {
 *    slug: string
 *    title: string
 *   }
 *   slug: string
 *   title: string
 *  }
 *  deliveryDetails: DatoProductDeliveryDetails
 *  highlightedText: string
 *  id: string
 *  productCategories: DatoProductCategory[]
 *  richDeliveryInformation: DatoDeliveryInformationRecord
 *  richDescription: DatoSeoImageBlock[]
 *  seoMeta: DatoSeo
 *  slug: string
 *  summary: string
 *  title: string
 *  useQuantitySelector: boolean
 *  vimeoVideo: DatoVideo
 * }} DatoProduct
 */

/**
 * @typedef {{
 *  blocks: string[]
 *  links: string[]
 *  value: Object
 * }} DatoProductBody
 */

/**
 * @typedef {{
 *  blocks: string[]
 *  links: string[]
 *  value: Object
 * }} DatoProductDeliveryDetails
 */

/**
 * @typedef {{
 *  displayType: ('CATEGORY'|'HIGHLIGHTED'|'REASSURANCE')
 *  id: string
 *  items: {
 *    description: string
 *    icon: string
 *    image: DatoFileField
 *    link: (DatoExternalLinkRecord | DatoLinkRecord)[]
 *    subtitle: string
 *  }[]
 *  subtitle: string
 * }} DatoRotatingBannerRecord
 */

/**
 * @typedef {{
 *  description: {
 *    value: Object
 *  }
 *  image: DatoFileField
 *  title: string
 * }} DatoDeliveryInformationRecord
 */

/**
 * @typedef {{
 * attributes: {
 *  property: string
 *  name: string
 *  content: string
 * }
 * content: string
 * tag: string
 * }} DatoTag
 */

/**
 * @typedef {{
 *  description: string
 *  display: string
 *  id: string
 *  image: DatoFileField
 *  items: DatoCssIconTeaser[]
 * }} DatoSeoImageBlock
 */

/**
 * @typedef {{
 *  buttonLabel: string
 *  buttonUrl: string
 *  description: string
 *  image: DatoFileField
 *  links: DatoExternalLinkRecord[]
 *  subtitle: string
 *  title: string
 *  showTitle: boolean
 * }} DatoCardBannerRecord
 */

/**
 * @typedef {{
 *  description: string
 *  image: DatoFileField
 *  link: DatoExternalLinkRecord | DatoLinkRecord
 *  dropdownLinks: (DatoExternalLinkRecord | DatoLinkRecord)[]
 *  subtitle: string
 * }} DatoMomentFirstRecord
 */

/**
 * @typedef {{
 *  description: string
 *  expertName: string
 *  expertTitle: string
 *  image: DatoFileField
 *  links: (DatoExternalLinkRecord | DatoLinkRecord)[]
 *  profileImage: DatoFileField
 *  subtitle: string
 *  statement: string
 *  title: string
 * }} DatoExpertBlockRecord
 */

/**
 * @typedef {{
 *  background: string
 *  blocks: DatoSectionRecord[]
 *  title: string
 *  wrapper: string
 * }} DatoSection
 */

/**
 * @typedef {(DatoModularContentBlockRecord|DatoRotatingBannerRecord|DatoCardBannerRecord|DatoMomentFirstRecord|DatoExpertBlockRecord)} DatoSectionRecord
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  __typename: string
 *  afterHeader: DatoSection[]
 *  beforeFooter: DatoSection[]
 *  content: DatoSection[]
 *  festMode: DatoFestMode[]
 *  seoMeta: DatoSeo
 *  slug: string
 *  title: string
 * }} DatoLandingPage
 */

/**
 * @typedef {{
 *   productType: string,
 *   plpSubtitle: DatoStructuredText,
 *   plpDeliveryPrice: DatoStructuredText,
 *   pdpPictureDescription: DatoStructuredText,
 *   pdpReinsurance: DatoStructuredText
 *   cartDeliveryPriceOption: string
 * }} DatoLegalText
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  __typename: string
 *  afterHeader: DatoSection[]
 *  beforeFooter: DatoSection[]
 *  body: DatoCategoryBody
 *  slug: string
 *  title: string
 * }} DatoPage
 */

/**
 * @typedef {{
 *  _seoMetaTags: DatoSeoMetaTag[]
 *  __typename: string
 *  items: DatoAccordionItemRecord[]
 * }} DatoAccordionListRecord
 */

/**
 * @typedef {{
 *  text: DatoStructuredText
 *  title: string
 * }} DatoAccordionItemRecord
 */

/**
 * @typedef {{
 *  blocks: string[]
 *  links: string[]
 *  value: Object
 * }} DatoStructuredText
 */

/**
 * @typedef {{
 *  content: DatoStructuredText
 *  highlighted: boolean
 *  title: string
 *  level: string
 * }} DatoContentBlockRecord
 */

/**
 * @typedef {{
 *  image: DatoFileField
 *  title: string[]
 *  description: string
 *  align: string
 *  cta: DatoLinkRecord[]
 * }} DatoColumnImageBlockRecord
 */

/**
 * @typedef {{
 *  description: string
 *  icon: string
 *  title: string
 *  link: (DatoExternalLinkRecord | DatoLinkRecord)
 * }} DatoIconTeaserRecord
 */

/**
 * @typedef {{
 *  items: DatoIconTeaserRecord[]
 * }} DatoIconTeaserListRecord
 */

/**
 * @typedef {{
 *  display: string
 *  link: (DatoExternalLinkRecord | DatoLinkRecord)[]
 *  products: {commercetoolsProduct: string}[]
 *  subtitle: string
 *  title: string
 * }} DatoProductCarouselRecord
 */

/**
 * @typedef {{
 *  description: string
 *  image: DatoFileField
 *  link: (DatoExternalLinkRecord | DatoLinkRecord)
 *  imageType: string
 * }} DatoContentBannerRecord
 */

/**
 * @typedef {{
 *  title: string
 *  description: string
 *  image: DatoFileField
 *  link: (DatoExternalLinkRecord | DatoLinkRecord)
 * }} DatoListPromotionRecord
 */

/**
 * @typedef {{
 *  date: Date
 *  id: string
 *  label: string
 * }} DatoFestMode
 */

/**
 * @typedef {{
 *  id: string
 *  description: string
 *  display: string
 *  image: DatoFileField
 *  items: {
 *    cssClass: string
 *    subtitle: string
 *    title: string
 *  }[]
 *  title: string
 * }} DatoSeoImageBlockRecord
 */

/**
 * @typedef {{
 *  id: string
 *  description: string
 *  image: DatoFileField
 *  items: {
 *    cssClass: string
 *    subtitle: string
 *    title: string
 *  }[]
 *  subtitle: string
 *  title: string
 * }} DatoSeoBlockRecord
 */

/**
 * @typedef {{
 *  duration: number
 *  framerate: number
 *  mp4Url: string
 *  muxAssetId: string
 *  muxPlaybackId: string
 *  streamingUrl: string
 *  thumbnailUrl: string
 * }} DatoUploadVideoField
 */

/**
 * @typedef {{
 *  align: string
 *  link: (DatoExternalLinkRecord[] | DatoLinkRecord[])
 * }} DatoCtaRecord
 */

/**
 * @typedef {{
 *  title: string
 *  level: string
 *  subtitle: string
 *  image: DatoFileField
 *  link: (DatoExternalLinkRecord[] | DatoLinkRecord[])
 * }} DatoMainBannerRecord
 */

/**
 * @typedef {{
 *  title: string
 *  subtitle: string
 *  level: string
 *  color: string
 * }} DatoTitleBlockRecord
 */

/**
 * @typedef {{
 *  html: string
 * }} DatoContentHtmlRecord
 */

/**
 * @typedef {{
 *  content: (DatoCtaRecord[] | DatoContentHtmlRecord[])
 * }} DatoModularContentBlockRecord
 */
