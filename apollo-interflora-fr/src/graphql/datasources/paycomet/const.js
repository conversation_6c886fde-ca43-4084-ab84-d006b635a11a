const CONST = require('../../../const')
const { createEnum } = require('../../../helpers/enum')

const PaycometArticleType = {
  Digital: 8,
  Discount: 4,
  GiftCard: 9,
  Physical: 5,
  SalesTax: 7,
  ShippingFee: 6,
  StoreCredit: 10,
  Surcharge: 11,
}

const PaycometAccountNumberType = {
  Account: 2,
  Card: 1,
  Cash: 3,
  Other: 0,
}

const PaycometPaymentMethod = createEnum(
  {
    Bancontact: 19,
    Beeline: 27,
    Bizum: 11,
    Cards: 1,
    EPS: 20,
    Giropay: 14,
    GooglePay: 35,
    InstantCredit: 33,
    KlarnaPayments: 34,
    KlarnaPaynow: 13,
    MultibancoSIBS: 16,
    MyBank: 15,
    NuaPay: 40,
    PayPal: 10,
    Paysafecard: 28,
    Paysera: 22,
    PostFinance: 23,
    Przelewy24: 18,
    QIWIWallet: 24,
    Skrill: 29,
    Trustly: 17,
    WebMoney: 30,
    <PERSON><PERSON>xMoney: 25,
    iDEAL: 12,
  },
  {
    gql: {
      Cards: CONST.payment.paymentMode.CARD,
      PayPal: CONST.payment.paymentMode.PAYPAL,
      __default__: CONST.payment.paymentMode.NONE,
    },
  }
)

const PaycometOperationState = createEnum(
  {
    Error: 0,
    Pending: 2,
    Success: 1,
  },
  {
    ct: {
      Error: CONST.order.statuses.error,
      Pending: CONST.order.statuses.redirect,
      Success: CONST.order.statuses.success,
    },
  }
)

module.exports = {
  PaycometAccountNumberType,
  PaycometArticleType,
  PaycometOperationState,
  PaycometPaymentMethod,
}
