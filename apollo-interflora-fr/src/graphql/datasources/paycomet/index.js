const apm = require('elastic-apm-node')
const config = require('../../../config')
const CONST = require('../../../const')
const { isUUID } = require('../../../helpers/string')
const { PaycometPaymentMethod } = require('./const')
const { RESTDataSource } = require('../RESTDataSource')

/** @typedef {import('@apollo/server').AugmentedRequest} AugmentedRequest */

class PaycometDataSource extends RESTDataSource {
  constructor() {
    super()
    this.baseURL = config.paycomet.baseUrl
  }

  /**
   * @param {Promise<PaycometExecutePurchaseRequest>} opts
   */
  async executePurchase(opts) {
    return this.post('payments', { body: opts })
  }

  /**
   * @param {string} order
   * @returns {Promise<PaycometOperationInfo>}
   */
  async operationInfo(order) {
    if (!isUUID(order)) {
      throw new Error('Invalid order ID')
    }

    try {
      return await this.post(`payments/${order}/info`, {
        body: {
          payment: {
            terminal: config.tokens.paycometTerminal,
          },
        },
      })
    } catch (error) {
      apm.captureError(error, {
        custom: {
          terminal: config.tokens.paycometTerminal,
        },
      })

      throw error
    }
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   */
  willSendRequest(_path, request) {
    request.headers['PAYCOMET-API-TOKEN'] = config.tokens.paycometApiKey
  }

  static fastifyPlugin(fastify, _options, done) {
    const conf = config.apps[config.apps.defaultSiteId]

    /**
     * @param {FastifyRequest} _request
     * @param {FastifyReply} _reply
     * @param {FastifyError} error
     * @param {HookHandlerDoneFunction} done
     */
    const onError = function (_request, _reply, error, done) {
      apm.captureError(
        error,
        {
          custom: error.custom,
        },
        done
      )
    }

    fastify.route({
      handler: function (request, reply) {
        const { paycometId, Order, MethodId, TransactionType } = request.body
        // execute_purchase
        if (TransactionType === '1') {
          fastify
            .inject({
              body: JSON.stringify({
                query: `
mutation CreateOrder($context: InputContext!, $orderInfo: InputOrderInfo!) {
  createOrder(
    context: $context,
    orderInfo: $orderInfo
  ) {
    statusCode
    statusMessage
  }
}`,
                variables: {
                  context: {
                    cartId: Order,
                    countryId: conf.country,
                    language: conf.language,
                    siteId: config.apps.defaultSiteId,
                  },
                  orderInfo: {
                    mode: PaycometPaymentMethod.translateValue('gql', MethodId),
                    paymentToken: `${paycometId}`,
                    provider: CONST.payment.provider.PAYCOMET,
                  },
                },
              }),
              headers: {
                'Content-Type': 'application/json',
              },
              method: 'POST',
              url: '/graphql',
            })
            .then((response) => {
              const { statusCode, statusMessage } = response
              fastify.log.info('paycomet notification - create order: %0', {
                json: response.json(),
                paymentToken: `${paycometId}`,
                statusCode,
                statusMessage,
              })
            })
            .catch((e) => {
              apm.captureError(e, { custom: e })
              fastify.log.error('[paycomet webhook] error: %0', e)
            })

          return reply.send('OK')
        } else {
          return reply.status(400).send('Not expected')
        }
      },
      method: ['POST'],
      onError,
      url: '/notification/paycomet',
    })

    done()
  }
}

module.exports = {
  paycomet: PaycometDataSource,
}
