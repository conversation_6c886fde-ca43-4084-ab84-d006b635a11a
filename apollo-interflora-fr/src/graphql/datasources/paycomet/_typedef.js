/**
 * @typedef {'si' | 'no'} FirstBuyEnum
 */

/**
 * 0 - failed
 * 1 - correct
 * 2 - pending
 * @typedef {0 | 1 | 2} OperationInfoStateEnum
 */

/**
 * @typedef {{
 *  cc: number
 *  subscriber: number
 * }} PaycometPhone
 */

/**
 * @typedef {{
 *  id: string
 *  name: string
 *  surname: string
 *  email: string
 *  homePhone: PaycometPhone
 *  mobilePhone: PaycometPhone
 *  workPhone: PaycometPhone
 *  firstBuy: FirstBuyEnum
 * }} PaycometCustomer
 */

/**
 * @typedef {{
 *  shipAddrCity: string
 *  shipAddrCountry: string
 *  shipAddrLine1: string
 *  shipAddrLine2: string
 *  shipAddrLine3: string
 *  shipAddrPostCode: string
 *  shipAddrState: string
 * }} PaycometShipping
 */

/**
 * @typedef {{
 *  billAddrCity: string
 *  billAddrCountry: string
 *  billAddrLine1: string
 *  billAddrLine2: string
 *  billAddrLine3: string
 *  billAddrPostCode: string
 *  billAddrState: string
 * }} PaycometBilling
 */

/**
 * @typedef {{
 *  chAccAgeInd: string
 *  chAccChange: string
 *  chAccChangeInd: string
 *  chAccDate: string
 *  chAccPwChange: string
 *  chAccPwChangeInd: string
 *  nbPurchaseAccount: number
 *  provisionAttemptsDay: number
 *  txnActivityDay: string
 *  txnActivityYear: string
 *  paymentAccAge: string
 *  paymentAccInd: string
 *  shipAddressUsage: string
 *  shipAddressUasageInd: string
 *  shipNameIndicator: string
 *  suspiciousAccActivity: string
 * }} PaycometAcctInfo
 */

/**
 * @typedef {{
 *  deliveryEmailAddress: string
 *  deliveryTimeframe: string
 *  giftCardAmount: string
 *  giftCardCount: number
 *  giftCardCurr: string
 *  preOrderDate: string
 *  preOrderPurchaseInd: string
 *  reorderItemsInd: string
 *  shipIndicator: string
 * }} PaycometMerchantRiskIndicator
 */

/**
 * @typedef {{
 *  sku: string
 *  quantity: number
 *  unitPrice: number
 *  name: string
 *  category: string
 *  articleType: number
 * }} PaycometCartItem
 */

/**
 * @typedef {{
 *  firstName: string
 *  lastName: string
 *  country: string
 *  address: string
 *  city: string
 *  accountNumber: string
 *  accountNumberType: number
 *  utr: string
 *  bai: string
 *  senderName: string
 * }} PaycometAft
 */

/**
 * @typedef {{
 *  customer: PaycometCustomer
 *  shipping: PaycometShipping
 *  billing: PaycometBilling
 *  acctID: string
 *  acctInfo: PaycometAcctInfo
 *  merchantRiskIndicator: PaycometMerchantRiskIndicator
 *  threeDSRequestorAuthenticationInfo: {
 *    threeDSReqAuthData: string
 *    threeDSReqAuthMethod: string
 *    threeDSReqAuthTimestamp: string
 *  }
 *  shoppingCart: PaycometCartItem[]
 *  addrMatch: string
 *  purchaseInstalData: number
 *  recurringExpiry: string
 *  recurringFrequency: string
 *  aft: {}
 * }} PaycometMerchantData
 */

/**
 * @typedef {{
 *  payment: {
 *    terminal?: number
 *    order: string
 *    amount: string
 *    currency: string
 *    methodId: string
 *    originalIp: string
 *    secure: number
 *    idUser: number
 *    tokenUser: string
 *    scoring?: string
 *    productDescription?: string
 *    merchantDescription?: string
 *    userInteraction?: number
 *    trxType?: string
 *    scaException?: string
 *    urlOk?: string
 *    urlKo?: string
 *    notifyDirectPayment?: number
 *    tokenize?: number
 *    merchantData: PaycometMerchantData
 *  }
 * }} PaycometExecutePurchaseRequest
 */

/**
 * @typedef {{
 *  errorCode: number
 *  amount: string
 *  currency: string
 *  methodId: number
 *  order: string
 *  authCode: string
 *  challengeUrl: string
 *  idUser: number
 *  tokenUser: number
 *  cardCountry: string
 *  methodData: any[]
 * }} PaycometExecutePurchaseResponseOk
 */

/**
 * @typedef {{
 *  paycometId: integer
 *  operationId: integer
 *  methodId: integer
 *  timestamp: string
 *  settlementDate: string
 *  order: string
 *  operationType: integer
 *  operationName: string
 *  state: OperationInfoStateEnum
 *  stateName: string
 *  response: 'OK' | 'KO' | 'UF'
 *  terminal: integer
 *  terminalName: string
 *  user: string
 *  amount: string
 *  currency: string
 *  amountDisplay: string
 *  errorCode: integer
 *  errorDescription: string
 *  feeEuro: number
 *  feePercent: number
 *  originalIp: string
 *  pan: string
 *  scoring: string
 *  merchantBank: string
 *  bicCode: string
 *  productDescription: string
 *  cardType: string
 *  cardCategory: string
 *  cardBrand: string
 *  cardCountry: string
 *  secure: string
 *  splitId: integer
 *  issuerBank: string
 *  authCode: string
 * }} PaycometOperation
 */

/**
 * @typedef {PaycometOperation & {
 *  amountEur: string
 *  terminalCurrency: string
 *  amountEurDisplay: string
 *  merchantData: PaycometMerchantData
 *  history: PaycometOperation[]
 * }} PaycometOperationInfo
 */

/**
 * @typedef {{
 *  errorCode: number
 *  error: {
 *    message: string
 *    detail: any[]
 *  }
 * }} PaycometExecutePurchaseResponseUnprocessableEntity
 */

/**
 * @typedef {{
 *  errorCode: number
 *  payment: PaycometOperationInfo
 * }} PaycometOperationInfoResponse
 */
