const config = require('../../../config')

const apm = require('elastic-apm-node')
const debug = require('debug')('itf:checkout')
const { merge } = require('lodash')

const { Checkout } = require('checkout-sdk-node')

const { CheckoutError } = require('../../../helpers/payment/checkout')

/** @typedef {import('./_typedef')} */

class CheckoutDataSource {
  get baseUrl() {
    return config.apps[this.context.siteId].baseUrl
  }

  /**
   * @param {string} id
   * @param {string} idempotencyKey
   * @returns {Promise<CheckoutCaptureResponse>}
   */
  capturePayment(id, idempotencyKey) {
    return this.client.payments.capture(id, undefined, idempotencyKey).catch((e) => {
      apm.captureError(e, { custom: { ...e, payload: JSON.stringify(id) }, message: JSON.stringify(e) })
      const error = new Error(e.message)
      error.source = e
      throw error
    })
  }

  /**
   * @param {string} id
   * @returns {Promise<CheckoutPaymentDetails>}
   */
  getPaymentDetails(id) {
    return this.client.payments.get(id).catch((e) => {
      apm.captureError(e, { custom: { ...e, payload: JSON.stringify(id) }, message: JSON.stringify(e) })
    })
  }

  /**
   * @param {DataSourceConfig} opts
   */
  async initialize(opts) {
    this.cache = opts.cache
    this.context = opts.context
    this.client = new Checkout(config.tokens.checkoutAuthSk)
  }

  /**
   * @see https://api-reference.checkout.com/#operation/requestAPaymentOrPayout
   *
   * @param {CheckoutPaymentRequest} payload
   * @param {Checkout3DSOptions} threeDSecureOptions
   * @param {string} redirectUrl
   * @returns {Object}
   */
  async requestPayment(payload, threeDSecureOptions, redirectUrl) {
    /** @type {CheckoutPaymentRequest} */
    const conf = config.apps[this.context.siteId]
    let failureUrl = `${redirectUrl}/failure`
    let successUrl = `${redirectUrl}/success`

    /**
     * [GGT-830]
     * Checkout.com does not provide the correct payment ID in the return url
     * to compensate until it provides the correct we add cart ID
     * to retrieve the order when redirecting Alma => our website
     */
    if (payload.source?.type === 'alma' && payload.metadata?.cart_id) {
      const almaQuery = `?type=alma&cartId=${payload.metadata.cart_id}`
      failureUrl += almaQuery
      successUrl += almaQuery
    }

    const _payload = merge(
      {
        failure_url: failureUrl,
        metadata: {
          session_id: this.context.auth?.sessionId,
          system: config.site,
        },
        payment_ip: this.context.ip,
        success_url: successUrl,
      },
      payload
    )

    // validation constraints
    // @see https://api-reference.checkout.com/#operation/requestAPaymentOrPayout
    for (const address of [_payload.source?.billing_address, _payload.shipping?.address]) {
      if (!address) continue

      if (address.address_line1 && Buffer.from(address.address_line1).byteLength > 200) {
        const buff = Buffer.from(address.address_line1)
        address.address_line1 = buff.subarray(0, 200).toString()
        if (!address.address_line2) address.address_line2 = ''
        address.address_line2 += ` ${buff.subarray(201).toString()}`
      }

      if (address.address_line2 && Buffer.from(address.address_line2).byteLength > 200) {
        this.context.log.warn({ address }, `[checkout] truncating address line 2 to max length`)
        address.address_line2 = Buffer.from(address.address_line2).subarray(0, 200).toString()
      }

      if (address.city && Buffer.from(address.city).byteLength > 50) {
        this.context.log.warn({ address }, `[checkout] truncating city to max length`)
        address.city = Buffer.from(address.city).subarray(0, 50).toString()
      }

      if (address.state && Buffer.from(address.state).byteLength > 2) {
        this.context.log.warn({ address }, `[checkout] deleting invalid long state`)
        address.state = undefined
      }

      if (address.zip && Buffer.from(address.zip).byteLength > 50) {
        this.context.log.warn({ address }, `[checkout] truncating zip to max length`)
        address.zip = Buffer.from(address.zip).subarray(0, 50).toString()
      }

      // remove empty strings
      for (const [key, val] of Object.entries(address)) {
        if (typeof val === 'string' && val.trim() === '') delete address[key]
      }
      if (!address.zip) {
        this.context.log.warn({ address }, `[checkout] feeding fake zip code`)
        address.zip = 'unknown'
      }
    }

    const processingChannelId = config.tokens?.checkoutProcessingChannelId ?? conf?.checkout?.processingChannelId

    if (!processingChannelId) {
      throw new CheckoutError(
        {
          error_codes: [500],
          error_type: 'Undefined processing channel',
        },
        'Undefined processing channel'
      )
    }
    _payload['processing_channel_id'] = processingChannelId

    if (_payload.source.type === 'token' || threeDSecureOptions?.enabled) {
      _payload['3ds'] = threeDSecureOptions ? threeDSecureOptions : { enabled: true }
    }

    debug('checkout payload: %O', _payload)

    try {
      return await this.client.payments.request(_payload)
    } catch (e) {
      apm.captureError(e, { custom: { ...e, payload: JSON.stringify(_payload) }, message: JSON.stringify(e) })
      throw e
    }
  }

  /**
   *
   * @param {string} paymentId
   * @param {string} reference
   * @param {number | null} amount
   * @param {Object.<string, any> | null} metadata
   * @returns {Promise<CheckoutRefundResponse>}
   * @throws {CheckoutError}
   */
  async refundPayment(paymentId, reference, amount = null, metadata = null) {
    let payload = {}
    if (amount) payload.amount = amount
    if (reference) payload.reference = reference
    if (metadata) payload.metadata = metadata

    return this.client.payments.refund(paymentId, payload)
  }

  static fastifyPlugin(fastify, _options, done) {
    const conf = config.apps[config.apps.defaultSiteId]
    //This is an endpoint for async order transformation (for example if client is leaving just after 3ds)
    fastify.post('/graphql/checkout/webhook', async (request, reply) => {
      /*
        https://www.checkout.com/docs/four/workflows
        https://www.checkout.com/docs/four/workflows/view-events#Get_event_types
        https://www.checkout.com/docs/four/workflows/webhook-event-types/payment_pending
      */
      // Webhooks are notifying the configured endpoint for every entity payments, but we have multiple systems per entity so we also check the metadata to filter payments.
      if (
        request.headers.authorization !== config.tokens.checkoutAuthWebhook ||
        request.body.data?.metadata?.system !== config.site
      ) {
        return reply.status(403).send(`Failed to authenticate request.`)
      }
      if (request.body.type === 'payment_captured') {
        // let 3s to prefer human confirmation
        setTimeout(() => {
          // call graphql createOrder - do not wait for response
          fastify
            .inject({
              body: JSON.stringify({
                query: `
              mutation CheckoutS2S($context: InputContext!, $query: String!) {
                confirmRedirectOrder(
                  context: $context,
                  urlPath: "s2s",
                  urlQuery: $query
                ) {
                  statusCode
                  statusMessage
                }
              }
            `,
                variables: {
                  context: {
                    countryId: conf.country,
                    language: conf.language,
                    siteId: config.apps.defaultSiteId,
                  },
                  query: `cko-session-id=${request.body.data.id}`,
                },
              }),
              headers: {
                'Content-Type': 'application/json',
              },
              method: 'POST',
              url: '/graphql',
            })
            .then((response) => {
              debug('webhook confirmRedirectORder: %O', response)
              const { statusCode, statusMessage } = response
              fastify.log.info('checkout s2s: %O', {
                json: response.json(),
                query: `cko-session-id=${request.body.data.id}`,
                statusCode,
                statusMessage,
              })
            })
            .catch((e) => {
              apm.captureError(e, { custom: e })
              fastify.log.error('[checkout webhook] error: %O', e)
            })
        }, 3000)
        debug('webhook payload: %O', request.body)

        return reply.status(200).send('OK')
      } else {
        //No other webhook events expected for now
        return reply.status(400).send('Not expected')
      }
    })
    done()
  }
}

module.exports = {
  checkout: CheckoutDataSource,
}
