/**
 * @typedef {{
 *  address_line1: string
 *  address_line2: string
 *  city: string
 *  country: string
 *  state: string
 *  zip: string
 * }} CheckoutAddress
 */

/**
 * @typedef {{
 *  country_code: string
 *  number: string
 * }} CheckoutPhone
 */

/**
 * @typedef {{
 *  email: string
 *  name: string
 * }} CheckoutCustomerInfo
 */

/**
 * @typedef {{
 *  name: string
 *  quantity: number
 *  unit_price: number
 * }} CheckoutItem
 */

/**
 * @typedef {{
 *  3ds: {
 *    attempt_n3d: boolean
 *    authentication_amount: number
 *    authentication_date: string
 *    challenge_cancel_reason: string
 *    challenge_indicator: ('challenge_requested'|'challenge_requested_mandate'|'no_challenge_requested'|'no_preference')
 *    cryptogram:string
 *    cryptogram_algorithm: string
 *    eci: string
 *    exemption: ('3ds_outage'|'low_risk_program'|'low_value'|'other'|'out_of_sca_scope'|'secure_corporate_payment'|'transaction_risk_assessment'|'trusted_listing')
 *    enabled: boolean
 *    flow_type: ('challenged'|'frictionless'|'frictionless_delegated')
 *    score: string
 *    status: string
 *    status_reason_code: string
 *    version: string
 *    xid: string
 *  }
 *  amount: number
 *  authorization_type: ('Estimated'|'Final')
 *  billing_descriptor: {
 *    city: string
 *    name: string
 *    reference: string
 *  }
 *  capture: boolean
 *  capture_on: string
 *  currency: string
 *  customer: {
 *    email: string
 *    id: string
 *    name: string
 *    phone: {
 *      country_code: string
 *      number: string
 *    }
 *    tax_number: string
 *  }
 *  description: string
 *  failure_url: string
 *  items: {
 *    commodity_code: string
 *    discount_amount: number
 *    name: string
 *    quantity: number
 *    reference: string
 *    tax_amount: number
 *    total_amount: number
 *    unit_of_measure: string
 *    unit_price: number
 *    wspay_goods_id: string
 *  }[]
 *  marketplace: {
 *    sub_entity_id: string
 *    sub_entities: {
 *      amount: number
 *      commission: {
 *        amount: number
 *        percentage: number
 *      }
 *      id: string
 *      reference: string
 *    }[]
 *  }
 *  merchant_initiated: boolean
 *  metadata: object
 *  payment_ip: string
 *  payment_type: ('Installment'|'MOTO'|'Recurring'|'Regular')
 *  previous_payment_id: string
 *  processing: {
 *    aft: boolean
 *    airline_data: {
 *      flight_leg_details: {
 *        arrival_airport: string
 *        carrier_code: string
 *        departure_airport: strin,g
 *        departure_date: string
 *        departure_time: string
 *        fare_basis_code
 *        flight_number: number
 *        service_class: string
 *        stopover_code: string
 *      }[]
 *      passenger: {
 *        country_code: string
 *        date_of_birth: string
 *        name: {
 *          full_name: string
 *        }
 *      }
 *      ticket: {
 *        issue_date: string
 *        issuing_carrier_code: string
 *        number: string
 *        travel_agency_code: string
 *        travel_agency_name: string
 *      }
 *    }[]
 *    brand_name: string
 *    discount_amount: number
 *    duty_amount: number
 *    invoice_id: string
 *    locale: string
 *    merchant_initiated_reason: string
 *    open_id: string
 *    order_id: string
 *    original_order_amount: number
 *    os_type: ('ANDROID'|'IOS')
 *    preferred_scheme: ('cartes_bancaires'|'mastercard'|'visa')
 *    product_type: ('In-App'|'Mini Program'|'Official Account'|'QR Code')
 *    receipt_id: string
 *    set_transaction_context: {
 *      key: string
 *      value: string
 *    }[]
 *    shipping_amount: number
 *    shipping_preference: string
 *    shipping_tax_amount: number
 *    tax_amount: number
 *    terminal_type: ('APP'|'WAP'|'WEB')
 *    user_actoun: ('CONTINUE'|'PAY_NOW')
 *  }
 *  processing_channel_id: string
 *  recipient: {
 *    account_number: string
 *    dob: string
 *    last_name: string
 *    zip: string
 *  }
 *  reference: string
 *  risk: {
 *    enabled: boolean
 *  }
 *  sender: {
 *    address: CheckoutAddress
 *    first_name: string
 *    identification: string
 *    last_name: string
 *    reference: string
 *    type: ('corporate'|'individual'|'instrument')
 *  }
 *  shipping: {
 *    address: {
 *      address_line1: string
 *      address_line2: string
 *      city: string
 *      country: string
 *      state: string
 *      zip: string
 *    }
 *    from_address_zip: string
 *    name?: string
 *    phone: {
 *      country_code: string
 *      number: string
 *    }
 *  }
 *  source: {
 *    billing_address: {
 *      address_line1: string
 *      address_line2: string
 *      city: string
 *      country: string
 *      state: string
 *      zip: string
 *    }
 *    phone: CheckoutPhone
 *    recipient_name?: string
 *    token: string
 *    type: ('paypal'|'token'|'alma')
 *  }
 *  success_url: string
 * }} CheckoutPaymentRequest
 */

/**
 * @typedef {{
 *  id: string
 *  type: 'Authorization'|'Void'|'Card Verification'|'Capture'|'Refund'
 *  response_code: string
 *  response_summary?: string
 * }} CheckoutPaymentActionSummary
 */

/**
 * @see https://api-reference.checkout.com/#operation/getPaymentDetails
 * @typedef {{
 *  actions?: CheckoutPaymentActionSummary[]
 *  amount: number
 *  approved: boolean
 *  currency: string
 *  id: string
 *  metadata: {
 *    session_id: string
 *    cart_id: string
 *    method: GQLPaymentModeEnum
 *  }
 *  reference: string
 *  requested_on: string
 *  source: CheckoutSource
 *  status: (
 *    'Authorized' |
 *    'Cancelled' |
 *    'Captured' |
 *    'Card Verified' |
 *    'Declined' |
 *    'Expired' |
 *    'Paid' |
 *    'Partially Captured' |
 *    'Partially Refunded' |
 *    'Pending' |
 *    'Refunded' |
 *    'Voided'
 *  )
 * }} CheckoutPaymentDetails
 */

/**
 * @see https://api-reference.checkout.com/#operation/requestAPaymentOrPayout
 * @typedef {{
 *  3ds: {
 *    donwgraded: boolean
 *    enrolled: ('Y'|'N'|'U')
 *    upgrade_reason: string
 *  }
 *  action_id: string
 *  amount: number
 *  approved: boolean
 *  auth_code: string
 *  balances: {
 *    available_to_capture: number
 *    available_to_refund: number
 *    available_to_void: number
 *    total_authorized: number
 *    total_captured: number
 *    total_refunded: number
 *    total_voided: number
 *  }
 *  currency: string
 *  customer: {
 *    id: string
 *    email: string
 *    name: string
 *    phone: CheckoutPhone
 *  }
 *  eci: string
 *  expires_on: string
 *  id: string
 *  processed_on: string
 *  processing {
 *    acquirer_transaction_id: string
 *    partner_authorizaton_code: string
 *    partner_authorization_response_code: string
 *    partner_client_token: string
 *    partner_error_codes: string[]
 *    partner_error_message: string
 *    partner_order_id: string
 *    partner_payment_id: string
 *    partner_session_id: string
 *    partner_status: string
 *    partner_transaction_id: string
 *    recommendation_code: string
 *    retrieval_reference_number: string
 *  }
 *  reference: string
 *  response_code: string
 *  response_summary: string
 *  risk: {
 *    flagged: boolean
 *    score: number
 *  }
 *  scheme_id: string
 *  source: CheckoutSource
 *  status: ('Authorized'|'Pending'|'Card Verified'|'Declined')
 * }} CheckoutPaymentRequestResponse
 */

/**
 * @typedef {{
 *   attempt_n3d: boolean
 *   authentication_amount: number
 *   authentication_date: string
 *   challenge_cancel_reason: string
 *   challenge_indicator: ('challenge_requested'|'challenge_requested_mandate'|'no_challenge_requested'|'no_preference')
 *   cryptogram:string
 *   cryptogram_algorithm: string
 *   eci: string
 *   exemption: ('3ds_outage'|'low_risk_program'|'low_value'|'other'|'out_of_sca_scope'|'secure_corporate_payment'|'transaction_risk_assessment'|'trusted_listing')
 *   enabled: boolean
 *   flow_type: ('challenged'|'frictionless'|'frictionless_delegated')
 *   score: string
 *   status: string
 *   status_reason_code: string
 *   version: string
 *   xid: string
 * }} Checkout3DSOptions
 */

/**
 * @typedef {{
 *    avs_check: string
 *    billing_address: {
 *      address_line1: string
 *      address_line2: string
 *      city: string
 *      country: string
 *      state: string
 *      zip: string
 *    }
 *    bin: string
 *    card_category: ('Consumer'|'Commercial')
 *    card_type: ('Credit'|'Debit'|'Prepaid'|'Charge'|'Deferred Debit')
 *    cvv_check: string
 *    expiry_month: number
 *    expiry_year: number
 *    fingerprint: string
 *    id: string
 *    issuer: string
 *    issuer_country: string
 *    last4: string
 *    name: string
 *    payment_account_reference: string
 *    phone: CheckoutPhone
 *    product_id: string
 *    product_type: string
 *    scheme: string
 *    scheme_local: string
 *    token: string
 *    type: ('card'|'paypal'|'token'|'alma')
 * }} CheckoutSource
 */

/**
 * @typedef {{
 *  body: Object
 *  http_code: number
 *  name: string
 * }} CheckoutError
 */

/**
 * @typedef {{
 *  action_id: string
 *  reference?: string
 *  _links?: {
 *   payment: {
 *    href: string
 *   }
 *  }
 * }} CheckoutRefundResponse
 */
