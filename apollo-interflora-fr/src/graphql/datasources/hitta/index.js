const config = require('../../../config')
const { RESTDataSource } = require('../RESTDataSource')
const crypto = require('crypto')
const CONST = require('../../../const')

/** @typedef {import('./__typedef')} */

/**
 * https://hitta.github.io/public/http-api/authentication.html
 * https://hitta.github.io/public/http-api/search/combined.html
 *
 * @extends {RESTDataSource}
 */
class HittaDataSources extends RESTDataSource {
  constructor(...args) {
    super(...args)
    this.baseURL = config.hitta.baseUrl
    this.SEARCH_TYPES = CONST.hitta.searchTypes

    if (!config.hitta.callerId || !config.tokens?.hittaApiKey) {
      throw new Error('Hitta configuration is missing: callerId or hittaApiKey')
    }
  }

  setHeaders() {
    const callerId = config.hitta.callerId
    const key = config.tokens?.hittaApiKey
    const time = Math.floor(Date.now() / 1000).toString()
    const randomString = crypto.randomBytes(8).toString('hex')
    const stringToHash = callerId + time + key + randomString
    const hashedString = crypto.createHash('sha256').update(stringToHash).digest('hex')

    this.asUserHeaders = {
      'hitta-callerid': callerId,
      'hitta-hash': hashedString,
      'hitta-random': randomString,
      'hitta-time': time,
    }
  }

  /**
   * @param {string} query
   * @param {SEARCH_TYPES} searchType
   * @param {number} pageNumber
   * @param {number} pageSize
   * @returns {PublicSearchResponse}
   */
  async publicSearch(query, searchType = this.SEARCH_TYPES.LOCATION, pageNumber = 1, pageSize = 200) {
    this.setHeaders()
    const params = {
      'location.type': 'ADDRESS',
      'page.number': pageNumber,
      'page.size': pageSize,
    }
    let queryParam = `/${query.trim()}`

    if (searchType !== this.SEARCH_TYPES.LOCATION) {
      params['what'] = query
      queryParam = ''
    }

    const searchParams = new URLSearchParams(params)

    const response = await this.get(`publicsearch/v1/${searchType}${queryParam}`, {
      headers: this.asUserHeaders,
      params: searchParams,
    })

    switch (searchType) {
      case this.SEARCH_TYPES.PERSON:
        return response.result.persons?.person || []
      case this.SEARCH_TYPES.COMPANIES:
        return response.result.companies?.company || []
      case this.SEARCH_TYPES.LOCATION:
        return response.result.locations?.location || []
      default:
        return []
    }
  }
}

module.exports = {
  hitta: HittaDataSources,
}
