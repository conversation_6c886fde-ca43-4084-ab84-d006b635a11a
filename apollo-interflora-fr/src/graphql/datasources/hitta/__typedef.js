/**
 * @typedef {{
 *    total: number
 *    included: number
 *    attribute: PSAttribute[]
 * }} PublicSearchResponse
 */

/**
 * @typedef {{
 *  id: string
 *  displayName: string
 *  metadata: {
 *    score: number
 *  }
 *  address: PSAddress[]
 * }} SearchResponse
 */

/**
 * @typedef {{
 *  type: string
 *  usageCode: string
 *  isWorkAddress: boolean
 *  city: string
 *  cityPreposition: string
 *  zipcode: string
 *  street: string
 *  number: string
 *  entrance: string
 *  district: string
 *  community: string
 *  county: string
 *  lkf: string
 *  shape: PSShape
 *  coordinate: PSCoordinate
 *  routeCoordinate: {
 *    type: string
 *    coordinates: [number, number]
 *  }
 *    attribute: PSAttribute[]
 * }} PSAddress
 */

/**
 * @typedef {{
 *   type: string
 *   coordinates: [number, number]
 * }} PSShape
 */

/**
 * @typedef {{
 *  north: number
 *  east: number
 *  system: string
 * }} PSCoordinate
 */

/**
 * @typedef {{
 *  name: string
 *  value: string
 * }} PSAttribute
 */
