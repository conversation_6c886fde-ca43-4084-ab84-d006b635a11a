const { RESTDataSource } = require('../RESTDataSource')
const debug = require('debug')('itf:solr')

const config = require('../../../config')

/** @typedef {import('./_typedef')} */

/**
 * @class
 * @extends {RESTDataSource<GraphQLContext>}
 */
class SolrDataSource extends RESTDataSource {
  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.solr.baseUrl
    }

    return super.resolveURL(request)
  }

  get isVisibleFilters() {
    const filters = [
      'currentStep_string:COMMERCIALISE',
      'inStock_boolean:true',
      'minPriceAfterDiscount_double: [1 TO *]',
    ]

    if (this.context.countryId?.toLowerCase() === 'fr') {
      filters.push(`dateVisibility_date:[* TO ${this.convertToSolrDate(new Date())}]`)
    }

    return filters
  }

  /**
   * Solr options.
   * @returns {Object}
   */
  get options() {
    return {
      defType: 'edismax',
      'facet.mincount': 1,
      'facet.sort': 'index',
      fl: '*',
      indent: true,
      q: '*:*',
      spellcheck: true,
      'spellcheck.collate': true,
      'spellcheck.dictionary': 'fr', // @todo if we need to adjust
      wt: 'json',
    }
  }

  didReceiveResponse(response, request) {
    debug('%s received in %dms', request.headers['x-id'], Date.now() - Number.parseInt(request.headers['x-start']))
    return super.didReceiveResponse(response, request)
  }

  /**
   * Set Authorization header to perform Solr requests.
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    request.headers.accept = 'application/json'
    request.headers['content-type'] = 'application/x-www-form-urlencoded'
  }

  /**
   * @param {BodyInit|Object} data
   * @returns {Promise<SolrResult>}
   */
  search(data) {
    const _data = Object.assign({}, this.options, data)
    const pathPrefix = this._getScope()

    debug('%s %O', pathPrefix, _data)

    const body = Object.entries(_data)
      .reduce(
        (acc, [key, values]) =>
          acc.concat((Array.isArray(values) ? values : [values]).map((value) => `${key}=${value}`)),
        []
      )
      .join('&')

    const searchParams = new URLSearchParams(body)

    return this.post(pathPrefix, { body: searchParams.toString() })
  }

  /**
   * Solr responds with text/plain content type, so we need
   * to parse the body to get a json object.
   * @param {Response} response
   * @returns {Object}
   */
  async parseBody(response) {
    const contentType = response.headers['content-type']

    if (contentType === 'application/json') {
      return response.json()
    }

    return JSON.parse(await response.text())
  }

  /**
   * Get Solr catalog name.
   * @param {'desktop' | 'mobile'} type
   * @returns {string|undefined}
   */
  getCatalog(type = 'mobile') {
    const { countryId } = this.context

    // NB: countryId is not defined when in "real" anonymous mode
    const scope = countryId?.toLowerCase() || 'fr'
    const catalog = config.solr.catalogs?.[scope]?.[type] || config.solr.catalogs?.ww?.[type]

    return catalog ? catalog.replace('{country}', countryId?.toUpperCase()) : undefined
  }

  /**
   * Transforms a javascript Date into a valid Solr date string
   * representation (just strips the ms).
   * @param {Date} date
   * @returns {string}
   */
  convertToSolrDate(date) {
    return date.toISOString().replace(/\.\d+Z$/, 'Z')
  }

  /**
   * Get Solr scoped URL based on countryId from the request context.
   * @private
   * @returns {string}
   */
  _getScope() {
    // NB: countryId is not defined when in "real" anonymous mode
    const countryId = this.context.countryId?.toLowerCase() || 'fr'

    if (countryId in config.solr.cores) {
      return `/${config.solr.cores[countryId]}/select`
    }

    return `/${config.solr.cores.ww}/select`
  }
}

module.exports = SolrDataSource
