/** @typedef {import('./Solr')} SolrDataSource */

/**
 * @typedef {{
 *  solr: SolrDataSource
 * }} SolrDataSources
 */

/**
 * @typedef {{
 *  facet_counts: {
 *      facet_dates: Object<string, string[]>,
 *      facet_fields: Object<string, string[]>,
 *      facet_queries: Object<string, string[]>,
 *      facet_ranges: Object<string, string[]>
 *  },
 *  response: {
 *      numFound: Number,
 *      start: Number,
 *      docs: Object[]
 *  },
 *  responseHeader: {
 *      status: Number,
 *      QTime: Number,
 *      params: Object<string, string|string[]>
 *  },
 *  stats?: {
 *      stats_fields?: {
 *          minPriceAfterDiscount_double?: {
 *              min: Number,
 *              max: Number,
 *              [key: string]: any
 *          }
 *      }
 * }
 * }} SolrResult
 */
