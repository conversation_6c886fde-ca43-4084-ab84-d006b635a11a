/**
 * @typedef {(
 * 'AddPaymentInfo' |
 * 'AddToCart' |
 * 'AddToWishlist' |
 * 'CompleteRegistration' |
 * 'CustomizeProduct' |
 * 'Donate' |
 * 'FindLocation' |
 * 'InitiateCheckout' |
 * 'Lead' |
 * 'Purchase' |
 * 'Schedule' |
 * 'Search' |
 * 'StartTrial' |
 * 'SubmitApplication' |
 * 'Subscribe' |
 * 'ViewContent'
 * )} MetaStandardEvent
 */

/**
 * @typedef {{
 *  id: string,
 *  quantity: number
 * }} MetaContentObject
 */

/**
 * @typedef {{
 *  contentCategory: string?
 *  content_ids: string[] | number[] | null
 *  content_name: string?
 *  content_type: 'product' | 'product_group' | null
 *  contents: MetaContentObject[] | null
 *  currency: string?
 *  num_items: number?
 *  predicted_ltv: number?
 *  search_string: string?
 *  status: boolean?
 *  value: number?
 * }} MetaCustomEventData
 */

/**
 * @typedef {{
 *
 * }} BloomreachAttributeSuggestion
 */
