const config = require('../../../config')

const fbSdk = require('facebook-nodejs-business-sdk')
const CONST = require('../../../const')
const { getConsent, getMetaTracking } = require('../../../helpers/tracking')

class MetaDataSource {
  constructor() {
    try {
      if (config.tokens.meta && config.tracking.meta.pixelId) {
        this.client = fbSdk.FacebookAdsApi.init(config.tokens.meta)
        this.pixelId = config.tracking.meta.pixelId
      } else {
        this.client = undefined
      }
    } catch (e) {
      this.client = undefined
      const error = new Error(e.message)
      error.source = e
      throw error
    }
  }

  /**
   * @param {GraphQLContext} context
   * @param {Date} eventDate
   * @param {MetaStandardEvent} eventName
   * @param {fbSdk.CustomData} eventData
   * @param {string | null} url
   * @param {string | null} source
   * @param {fbSdk.UserData | null} userData
   */
  async registerTrackingEvent(context, eventDate, eventName, eventData, url, source, userData) {
    if (!this.client) {
      return
    }

    // If the user consented to being tracked as per the RGPD and e-privacy Directive stipulates
    const consent = await getConsent(context, 'meta')
    if (!consent) {
      return
    }

    const event = new fbSdk.ServerEvent()
    // sent in UTC time as asked in docs: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/server-event#event-time
    event.setEventTime(Math.floor(eventDate.getTime() / 1000))
    event.setEventName(eventName)
    event.setCustomData(eventData)
    event.setUserData(userData)
    event.setEventSourceUrl(url)
    event.setActionSource(source)

    const eventRequest = new fbSdk.EventRequest(config.tokens.meta, this.pixelId, [event])
    // Uncomment the line below and change the test code to conduct tests and visualize the event on the meta dashboard.
    //eventRequest.setTestEventCode('TEST4280')
    await eventRequest.execute()
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {GQLProductVariant} product
   * @param {number} quantity
   * @param {boolean} isAccessory
   * @param {boolean} isService
   */
  async registerAddCart(context, date, product, quantity, isAccessory, isService) {
    const userData = await this.generateUserData(context)
    const productContent = await this.generateProductContent(
      context,
      product,
      quantity,
      this.getProductBrand(product, isAccessory, isService)
    )
    const productData = new fbSdk.CustomData()
    productData.setValue(this.formatPrice(product.price?.value))
    productData.setCurrency(this.getCurrency(product.price?.currencyIso?.toLowerCase()))
    productData.setContents([productContent])
    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.AddToCart,
      productData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {GQLProductVariant} product
   */
  async registerProductPageView(context, date, product) {
    const userData = await this.generateUserData(context)
    const productContent = await this.generateProductContent(
      context,
      product,
      1,
      this.getProductBrand(product, false, false)
    )
    const productData = new fbSdk.CustomData()
    productData.setContents([productContent])
    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.ViewContent,
      productData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {DatoCategoryPage} category
   */
  async registerCategoryPageView(context, date, category) {
    const userData = await this.generateUserData(context)
    const categoryContent = new fbSdk.Content()
    categoryContent.setCategory('category')
    categoryContent.setTitle(category?.slug || 'empty')
    categoryContent.setId(category?.slug || 'empty')
    const categoryData = new fbSdk.CustomData()
    categoryData.setContents([categoryContent])
    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.ViewContent,
      categoryData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {string} type
   * @param {string} slug
   */
  async registerGenericPageView(context, date, type, slug) {
    const userData = await this.generateUserData(context)
    const pageContent = new fbSdk.Content()
    pageContent.setCategory(type || 'unknown')
    pageContent.setTitle(slug || 'empty')
    pageContent.setId(slug || 'empty')
    const pageData = new fbSdk.CustomData()
    pageData.setContents([pageContent])
    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.ViewContent,
      pageData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {GQLOrder} order
   * @param {CTCart} cart
   */
  async registerOrderPaid(context, date, order, cart) {
    const userData = await this.generateUserData(
      context,
      cart?.billingAddress?.email || cart?.customerEmail || order?.user?.email || null,
      cart?.billingAddress?.mobile || cart?.billingAddress?.custom?.fields?.contactPhone || order?.user?.phone || null
    )

    const productContent = []
    for (const item of order.productItems) {
      productContent.push(
        await this.generateProductContent(
          context,
          item.variant,
          item.quantity,
          this.getProductBrand(item.variant, false, false)
        )
      )
    }

    for (const item of order.accessoryItems) {
      productContent.push(
        await this.generateProductContent(
          context,
          item.variant,
          item.quantity,
          this.getProductBrand(item.variant, true, false)
        )
      )
    }

    for (const item of order.serviceItems) {
      productContent.push(
        await this.generateProductContent(
          context,
          item.variant,
          item.quantity,
          this.getProductBrand(item.variant, false, true)
        )
      )
    }

    const orderData = new fbSdk.CustomData()
    orderData.setOrderId(order.id)
    orderData.setValue(this.formatPrice(order.discountedTotal?.value || order.total?.value))
    orderData.setCurrency(this.getCurrency(order.discountedTotal?.currencyIso?.toLowerCase()))
    orderData.setContents(productContent)

    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.Purchase,
      orderData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {Date} date
   * @param {CTCart} cart
   */
  async registerInitiateCheckout(context, date, cart) {
    const userData = await this.generateUserData(
      context,
      cart?.billingAddress?.email || cart?.customerEmail || null,
      cart?.billingAddress?.mobile || cart?.billingAddress?.custom?.fields?.contactPhone || null
    )

    const productContent = []
    for (const item of cart.lineItems) {
      let product = new fbSdk.Content()
      product.setId(item.productKey)
      product.setQuantity(item.quantity)
      product.setItemPrice(this.formatPrice(item.taxedPrice?.totalNet?.centAmount))
      product.setDeliveryCategory(fbSdk.DeliveryCategory.HOME_DELIVERY)
      product.setTitle(item.name[config.apps.apps.language])
      productContent.push(product)
    }

    const orderData = new fbSdk.CustomData()
    orderData.setValue(this.formatPrice(cart.taxedPrice?.totalNet?.centAmount || cart.totalPrice?.centAmount))
    orderData.setCurrency(this.getCurrency(cart.taxedPrice?.totalNet?.currencyCode?.toLowerCase()))
    orderData.setContents(productContent)

    await this.registerTrackingEvent(
      context,
      date,
      CONST.tracking.meta.events.InitiateCheckout,
      orderData,
      context.req.headers[CONST.tracking.urlHeaderName] || 'unknown-url',
      CONST.tracking.meta.action_sources.website,
      userData
    )
  }

  /**
   *
   * @param {GQLProductVariant} product
   * @param {boolean} isAccessory
   * @param {boolean} isService
   * @returns {string}
   */
  getProductBrand(product, isAccessory, isService) {
    if (isAccessory) {
      return CONST.tracking.meta.brands.accessory
    } else if (isService) {
      return CONST.tracking.meta.brands.service
    } else if (product.isFromWorkshop) {
      return CONST.tracking.meta.brands.workshop
    } else {
      return CONST.tracking.meta.brands.florist
    }
  }

  /**
   * @param {GraphQLContext} context
   * @param {GQLProductVariant} product
   * @param {number} quantity
   * @param {string} type
   * @returns {fbSdk.Content}
   */
  async generateProductContent(context, product, quantity, type) {
    const productContent = new fbSdk.Content()
    if (context.categoryId && type !== CONST.tracking.meta.brands.service) {
      /** @type {CTCategory} */
      const category = await context.loaders.commerceTools.categories.load(context.categoryId)
      productContent.setCategory(category.name?.[config.apps.apps.language])
    }
    productContent.setId(product.sku)
    productContent.setQuantity(quantity)
    productContent.setItemPrice(this.formatPrice(product.price?.value))
    productContent.setBrand(type)
    productContent.setDescription(product.parent?.subtitle)
    productContent.setDeliveryCategory(fbSdk.DeliveryCategory.HOME_DELIVERY)
    productContent.setTitle(product.parent?.name)
    return productContent
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {string} email
   * @param {string} phone
   * @returns {fbSdk.UserData}
   */
  async generateUserData(context, email = null, phone = null) {
    if (this.client) {
      const { fbc, fbp } = await getMetaTracking(context)
      const userData = new fbSdk.UserData()
      userData.setFbp(fbp)
      userData.setFbc(fbc)
      userData.setClientUserAgent(context.req?.headers?.['user-agent'])
      /* No need to hash those informations ourselves as the business SDK from meta is doing it for us:
      https://developers.facebook.com/docs/marketing-api/conversions-api/using-the-api#hachage */
      userData.setClientIpAddress(context.req?.ip)
      if (email) {
        userData.setEmail(email)
      }

      if (phone) {
        userData.setPhone(phone)
      }

      return userData
    }
  }

  /**
   *
   * @param {number} value
   * @returns {number | undefined}
   */
  formatPrice(value) {
    if (value && !Number.isNaN(value)) {
      return Number((value / 100).toFixed(2))
    } else {
      return undefined
    }
  }

  /**
   *
   * @param {string} curr
   * @returns {string}
   */
  getCurrency(curr) {
    // Change when nordic countries join
    if (!curr || curr === undefined || curr !== 'eur') {
      return config.apps.apps.currency?.toLowerCase() || 'eur'
    } else {
      return curr
    }
  }
}

module.exports = {
  meta: MetaDataSource,
}
