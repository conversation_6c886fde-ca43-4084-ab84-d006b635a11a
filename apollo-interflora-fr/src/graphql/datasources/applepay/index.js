const apm = require('elastic-apm-node')
const fetch = require('../../../helpers/fetch')
const https = require('https')

const config = require('../../../config')

/** @typedef {import('./_typedef')} */

class ApplePay {
  /**
   * @param {DataSourceConfig} config
   */
  async initialize(config) {
    this.context = config.context
  }
  /**
   * @param {GraphQLContext} context
   * @returns {Promise<Object>} // Sadly Apple doesn't give the structure of the object it returns.
   */
  async getPaymentSetup() {
    try {
      if (!config.tokens.applePayKey || !config.tokens.applePayPem) {
        throw new Error('Apple pay keys are not configurated')
      }
      const key = Buffer.from(config.tokens.applePayKey, 'base64').toString()
      const pem = Buffer.from(config.tokens.applePayPem, 'base64').toString()
      const sslConfiguredAgent = new https.Agent({
        cert: pem,
        key: key,
        rejectUnauthorized: false,
      })

      const response = await fetch(config.applePay.gatewayUrl, {
        agent: sslConfiguredAgent,
        body: JSON.stringify({
          displayName: 'Interflora',
          initiative: 'web',
          initiativeContext: this.context.appConfig.baseUrl.replace(/(^\w+:|^)\/\//, ''), // just get domain
          merchantIdentifier: config.tokens.applePayMerchantId,
        }),
        headers: { 'Content-Type': 'application/json' },
        method: 'post',
      })

      return await response.json()
    } catch (e) {
      apm.captureError(e, { custom: e })
    }
  }
}

module.exports = {
  applePay: ApplePay,
}
