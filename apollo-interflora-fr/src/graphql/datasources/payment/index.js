const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import('@apollo/datasource-rest').AugmentedRequest} AugmentedRequest */

/**
 * Microservice linked to payment, currently only used in CT FR to check if B2B user can pay by invoice.
 * @extends {RESTDataSource<GraphQLContext>}
 */
class PaymentDatasource extends RESTDataSource {
  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * Check if an user can pay by invoice, FR only
   * @param {string} customerUid
   * @param {number} totalPrice
   * @returns {Promise<{IsCustomerExists: boolean, IsAuthorizedToPayByInvoice: boolean, IsBlocked: boolean}>}
   */
  async isCustomerAuthorizedToPayByInvoice(customerUid, totalPrice) {
    return this.post(`IsCustomerAuthorizedToPayByInvoice`, {
      body: { CustomerUid: customerUid, TotalPrice: totalPrice },
    })
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.paymentMS?.headers) {
      for (const [name, value] of Object.entries(config.paymentMS.headers)) {
        request.headers[name] = value
      }
    }
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.paymentMS.baseUrl
    }

    return super.resolveURL(request)
  }
}

module.exports = {
  payment: PaymentDatasource,
}
