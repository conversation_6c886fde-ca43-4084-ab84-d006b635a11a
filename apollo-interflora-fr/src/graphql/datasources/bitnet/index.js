const { RESTDataSource } = require('../RESTDataSource')
const { XMLParser } = require('fast-xml-parser')

const config = require('../../../config')
const { GraphQLApolloError } = require('../../errors')

/** @typedef {import("./_typedef")}  */

/**
 * Source of funeral addresses from Bitnet API for Sweden
 * @extends {RESTDataSource}
 */
class BitnetDataSource extends RESTDataSource {
  constructor() {
    super()

    this.asUserHeaders = {
      Authorization: `Basic ${config.tokens.funeralBasic}`,
    }
  }

  /**
   * @param {string} name
   * @returns {Promise<FuneralAddressesResponse[]>}
   */
  async getFuneralAddresses(name) {
    try {
      const response = await this.post('partnerApi/EF/GetFunerals', {
        body: { DeceasedName: name },
        headers: this.asUserHeaders,
      })

      const decodedResponse = this.xmlToJson(response)
      const funeralActs = decodedResponse?.Result?.FuneralAct

      if (Array.isArray(funeralActs)) {
        return funeralActs
      } else if (funeralActs) {
        return [funeralActs]
      } else {
        return []
      }
    } catch (error) {
      throw new GraphQLApolloError(`[bitnet] failed to get funeral addresses: ${error.message}`)
    }
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.bitnet.baseUrl
    }

    return super.resolveURL(request)
  }

  xmlToJson(data) {
    const parser = new XMLParser({
      htmlEntities: true,
    })
    return parser.parse(data)
  }
}

module.exports = {
  bitnet: BitnetDataSource,
}
