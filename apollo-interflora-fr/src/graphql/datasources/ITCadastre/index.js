const createTree = require('functional-red-black-tree')

let _cities // are shared between all instances of this class

class ITCadastre {
  get cities() {
    if (!_cities) {
      _cities = createTree((a, b) => a.localeCompare(b))

      const data = require(`${__dirname}/codes.json`)

      for (const [province, city, code] of data) {
        _cities = _cities.insert(city, {
          city,
          code,
          province,
        })
      }
    }

    return _cities
  }

  /**
   * @param {string} search
   * @param {string} province
   * @param {Number} limit
   * @returns {{ city: string, code: string, province: string }[]}
   */
  autocompleteCities(search, province, limit = 20) {
    let i = 0
    const results = []

    const from = this.cities.ge(search)

    for (const city of this._nodeGenerator(from)) {
      if (province && province.trim().toUpperCase() !== city.province) continue

      results.push(city)
      i++

      if (i >= limit) break
    }

    return results
  }

  /**
   * @param {string} city
   * @param {string} province
   * @returns {{
   *  city: string,
   *  code: string,
   *  province: string
   * }}
   */
  getCity(city, province) {
    for (const c of this.autocompleteCities(city, province)) {
      if (c.city === city) return c
    }
    return null
  }

  /**
   * @param {DataSourceConfig} config
   */
  initialize(config) {
    this.cache = config.cache
    this.context = config.context
  }

  /**
   * Red/black tree node iterators are not Javascript iterators
   *
   * @param {RBNode} node
   * @returns {Generator<RBNode>}
   */
  *_nodeGenerator(node) {
    while (node.hasNext) {
      yield node.value
      node.next()
    }
    return node.value
  }
}

module.exports = {
  itCadastre: ITCadastre,
}
