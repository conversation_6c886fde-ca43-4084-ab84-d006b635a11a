const AuthDataSource = require('./Auth')
const CartDataSources = require('./Cart')
const CategoriesDataSource = require('./Categories')
const CustomersDataSource = require('./Customers')
const CustomObjectsDataSource = require('./CustomObjects')
const Inventories = require('./Inventories')
const OrderDataSource = require('./Order')
const ProductDataSource = require('./Products')
const ProjectDataSource = require('./Project')

module.exports = {
  carts: CartDataSources,
  categories: CategoriesDataSource,
  ctAuth: AuthDataSource,
  ctProject: ProjectDataSource,
  customObjects: CustomObjectsDataSource,
  customers: CustomersDataSource,
  inventories: Inventories,
  orders: OrderDataSource,
  products: ProductDataSource,
}
