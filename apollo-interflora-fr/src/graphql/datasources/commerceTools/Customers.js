const config = require('../../../config')

const CommerceTools = require('./CommerceTools')

/** @typedef {import('./_typedef')} */

class Customers extends CommerceTools {
  /**
   * @param {CTCustomerDraft} payload
   * @returns {Promise<CTCustomerSignInResult>}
   */
  async createCustomer(payload) {
    const req = this.api.customers().post({
      body: payload,
    })

    const response = await this.execAdmin(req)

    return response?.body
  }

  /**
   * @param {string} email
   * @returns {Promise<CTCustomer>}
   */
  async getCustomerByEmail(email) {
    const req = this.api.customers().get({
      queryArgs: {
        expand: ['customerGroup'],
        where: `lowercaseEmail="${email.toLowerCase().replace(/"/g, '')}"`,
      },
    })

    const response = await this.execRead(req)
    return response?.body.results[0] || null
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCustomerGroup>}
   */
  async getCustomerGroup(id) {
    const req = this.api.customerGroups().withId({ ID: id }).get()

    return this.api.execRead(req)
  }

  /**
   *
   * @param {string} id
   * @param {{
   *  actions: CTAction[],
   *  version: number
   * }} payload
   * @param {number} attempt
   * @returns {Promise<CTCustomer>}
   */
  async updateCustomer(id, payload, attempt = 0) {
    const req = this.api
      .customers()
      .withId({ ID: id })
      .post({
        body: payload,
        queryArgs: {
          expand: ['customerGroup'],
        },
      })

    let response
    try {
      response = await this.execAdmin(req)
    } catch (e) {
      if (e.statusCode === 409 && attempt < config.commerceTools.maxAttempts) {
        const version = e.originalError?.body?.errors?.[0]?.currentVersion || payload.version + 1
        this.context.log.info(`[ds updateCustomer] ${id} version retry ${version} #${attempt + 1}`)
        return this.updateCustomer(
          id,
          {
            ...payload,
            version,
          },
          attempt + 1
        )
      }
      throw e
    }

    return response?.body
  }
}

module.exports = Customers
