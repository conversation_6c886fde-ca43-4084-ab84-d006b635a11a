const config = require('../../../config')

const CommerceTools = require('./CommerceTools')

class Carts extends CommerceTools {
  /**
   * @param {CTCartDraft} draft
   * @returns {Promise<CTCart>}
   */
  async createCart(draft) {
    const req = this.api.me().carts().post({
      body: draft,
    })

    const { body: cart } = await this.exec(req)
    return cart
  }

  /**
   * @param {CTOrderFromCartDraft} draft
   * @param {number} attempt
   * @returns {Promise<CTOrder>}
   */
  async createOrder(draft, attempt = 0) {
    const req = this.api.orders().post({
      body: draft,
    })

    let order
    try {
      const response = await this.execAdmin(req)
      order = response.body
    } catch (e) {
      if (e.statusCode === 409 && attempt < config.commerceTools.maxAttempts) {
        const version = e.originalError?.body?.errors?.[0]?.currentVersion || draft.version + 1
        this.context.log.info(`[ds createOrder] ${draft.id} version retry ${version} #${attempt + 1}`)

        return this.createOrder(
          {
            ...draft,
            version,
          },
          attempt + 1
        )
      }
      throw e
    }

    return order
  }

  /**
   * @param {CTPaymentDraft} draft
   * @returns {Promise<CTPayment>}
   */
  async createPayment(draft) {
    const req = this.api.payments().post({
      body: draft,
    })

    const { body: payment } = await this.execAdmin(req)
    return payment
  }

  /**
   * @param {string} id
   * @param {number} version
   * @returns {Promise<void>}
   */
  async deleteCart(id, version) {
    // @see https://docs.commercetools.com/api/projects/carts#delete-cart-in-store-by-id
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .carts()
      .withId({ ID: id })
      .delete({
        queryArgs: { version },
      })

    return this.execAdmin(req)
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCart>}
   */
  async getCart(id) {
    // @see https://github.com/commercetools/commercetools-sdk-typescript/blob/master/packages/platform-sdk/src/generated/client/carts/by-project-key-carts-request-builder.ts
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .me()
      .carts()
      .withId({ ID: id })
      .get()

    const { body: cart } = await this.exec(req)
    return cart
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCart>}
   */
  async getCartAsRead(id) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .carts()
      .withId({ ID: id })
      .get()

    const { body: cart } = await this.execRead(req)

    return cart
  }

  /**
   * @param {string} userId
   * @returns {Promise<CTCart>}
   */
  async getCartByUserId(userId) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .carts()
      .get({
        queryArgs: {
          limit: 1,
          where: `cartState="Active" and custom(fields(userId="${userId.replace(/"/g, '')}"))`,
        },
        sort: `lastModifiedAt desc`,
      })

    const response = await this.execRead(req)

    return response?.body?.results?.[0]
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCartDiscount>}
   */
  async getCartDiscount(id) {
    const req = this.api.cartDiscounts().withId({ ID: id }).get()

    const response = await this.execRead(req)
    return response?.body
  }

  /**
   * @param {string} id
   * @returns {Promise<CTPagedQueryResult<CTShippingMethod>>}
   */
  async getCartShippingMethods(id) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .shippingMethods()
      .matchingCart()
      .get({
        queryArgs: {
          cartId: id,
        },
      })

    const response = await this.exec(req)
    return response?.body?.results
  }

  /**
   * @param {string} key
   * @return {Promise<CTShippingMethod>}
   */
  async getShippingMethodByKey(key) {
    const req = this.api.shippingMethods().withKey({ key }).get()

    const response = await this.exec(req)
    return response?.body
  }

  /**
   * @returns {Promise<CTShippingMethod>}
   */
  async getDefaultShippingMethod() {
    const req = this.api.shippingMethods().get({
      queryArgs: {
        where: `isDefault = true`,
      },
    })

    const response = await this.exec(req)
    return response?.body?.results[0]
  }

  /**
   * @param {string} id
   * @returns {Promise<CTDiscountCode>}
   */
  async getDiscountCode(id) {
    const req = this.api.discountCodes().withId({ ID: id }).get()

    const { body: code } = await this.execRead(req)
    return code
  }

  /**
   * @param {string} code
   * @returns {Promise<CTDiscountCode>}
   */
  async getDiscountCodeByCode(code) {
    const req = this.api.discountCodes().get({
      queryArgs: {
        where: `code = "${code.replace(/"/g, '')}"`,
      },
    })

    const response = await this.execRead(req)
    return response?.body?.results[0] || null
  }

  /**
   * @param {string} id
   * @returns {Promise<CTPayment>}
   */
  async getPayment(id) {
    const req = this.api.payments().withId({ ID: id }).get()

    const response = await this.execRead(req)
    return response.body
  }

  /**
   * @param {string} interfaceId
   * @param {string} paymentInterface
   * @return {Promise<CTPayment>}
   */
  async getPaymentByInterfaceId(interfaceId, paymentInterface) {
    const req = this.api.payments().get({
      queryArgs: {
        where: `interfaceId = "${interfaceId}" and paymentMethodInfo(paymentInterface = "${paymentInterface}")`,
      },
    })

    const response = await this.execRead(req)
    return response.body?.results?.[0]
  }

  /**
   * @param {string} id
   * @returns {Promise<CTTaxCategory>}
   */
  async getTaxCategory(id) {
    const req = this.api.taxCategories().withId({ ID: id }).get()
    const response = await this.execRead(req)
    return response?.body
  }

  /**
   * @param {string} key
   * @returns {Promise<CTTaxCategory>}
   */
  async getTaxCategoryByKey(key) {
    const req = this.api.taxCategories().withKey({ key }).get()
    const response = await this.execRead(req)
    return response?.body
  }

  /**
   * Get all tax categories defined for the current site
   * @returns {Promise<CTTaxCategory[]>}
   */
  async getTaxCategories() {
    const req = this.api.taxCategories().get({
      queryArgs: {
        where: `rates(country="${config.apps[config.apps.defaultSiteId].country}")`,
      },
    })
    const response = await this.execRead(req)

    return response?.body?.results
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCart>}
   */
  async replicateCart(id) {
    const req = this.api
      .carts()
      .replicate()
      .post({
        body: {
          reference: {
            id,
            typeId: 'cart',
          },
        },
      })
    const response = await this.execAdmin(req)
    return response?.body
  }

  /**
   * @param {string} id
   * @param {{
   *  actions: CTUpdateAction[],
   *  version: Number
   * }} payload
   * @param {Number} attempt
   * @returns {Promise<CTCart>}
   */
  async updateCart(id, payload, attempt = 0) {
    // @see https://github.com/commercetools/commercetools-sdk-typescript/blob/master/packages/sdk-client/src/types/sdk.d.ts#L95
    const req = this.api.inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey }).me().carts().withId({ ID: id }).post({
      body: payload,
      uri: id,
    })

    let response = {}
    try {
      response = await this.exec(req)
    } catch (e) {
      if (e.statusCode === 409 && attempt < config.commerceTools.maxAttempts) {
        const version = e.originalError?.body?.errors?.[0]?.currentVersion || payload.version + 1
        this.context.log.info(`[ds updateCart] ${id} version retry ${version} #${attempt + 1}`)
        return this.updateCart(
          id,
          {
            ...payload,
            version,
          },
          attempt + 1
        )
      }
      throw e
    }

    const { body: cart } = response
    return cart
  }

  /**
   * @param {string} id
   * @param {{
   *  actions: CTUpdateAction[],
   *  version: Number
   * }} payload
   * @param {Number} attempt
   * @returns {Promise<CTCart>}
   */
  async updateCartAsAdmin(id, payload, attempt = 0) {
    const req = this.api.carts().withId({ ID: id }).post({
      body: payload,
    })

    let response = {}
    try {
      response = await this.execAdmin(req)
    } catch (e) {
      if (e.statusCode === 409 && attempt < config.commerceTools.maxAttempts) {
        const version = e.originalError?.body?.errors?.[0]?.currentVersion || payload.version + 1
        this.context.log.info(`[ds updateCart] ${id} version retry ${version} #${attempt + 1}`)
        return this.updateCartAsAdmin(
          id,
          {
            ...payload,
            version,
          },
          attempt + 1
        )
      }
      throw e
    }

    return response?.body
  }

  /**
   * @param {string} id
   * @returns {Promise<CTZone>}
   */
  async getZone(id) {
    const req = this.api.zones().withId({ ID: id }).get()

    const { body: zone } = await this.execRead(req)
    return zone
  }
}

module.exports = Carts
