const config = require('../../../config')

const CommerceTools = require('./CommerceTools')

/** @typedef {import('./_typedef')} */

class CustomObjects extends CommerceTools {
  /**
   * @param {string} key
   * @returns {Promise<object>}
   */
  async getCustomObject(key) {
    const req = this.api
      .customObjects()
      .withContainerAndKey({
        container: 'apollo',
        key,
      })
      .get()

    return this.execRead(req)
  }

  /**
   * @param {{
   *  key: string,
   *  value: object,
   *  version: number
   * }} payload
   * @param {number} attempt
   * @returns {Promise<object>}
   */
  async updateCustomObject(payload, attempt = 0) {
    const req = this.api.customObjects().post({
      body: { container: 'apollo', ...payload },
    })

    let response
    try {
      response = await this.execAdmin(req)
    } catch (e) {
      if (e.statusCode === 409 && attempt < config.commerceTools.maxAttempts) {
        const version = e.originalError?.body?.errors?.[0]?.currentVersion || payload.version + 1
        this.context.log.info(`[ds updateCustomObject] ${payload.key} version retry ${version} #${attempt + 1}`)
        return this.updateCustomObject(
          {
            ...payload,
            version,
          },
          attempt + 1
        )
      }
      throw e
    }

    return response?.body
  }
}

module.exports = CustomObjects
