const CommerceTools = require('./CommerceTools')

/** @typedef {import('./_typedef')} */

class Project extends CommerceTools {
  /**
   * @param {{
   *  ids: string[],
   *  keys: string[]
   * }} opts
   * @returns {Promise<CTChannel[]>}
   */
  async getChannels(opts) {
    const where = []
    if (opts.ids) {
      where.push(`id in ("${opts.ids.map((id) => id.replace(/"/g, '')).join('", "')}")`)
    }
    if (opts.keys) {
      where.push(`key in ("${opts.keys.map((key) => key.replace(/"/g, '')).join('", "')}")`)
    }

    const req = this.api.channels().get({
      queryArgs: { where },
    })
    const response = await this.execRead(req)

    return response?.body?.results
  }

  /**
   * @returns {Promise<CTStore>}
   */
  async getStore() {
    const req = this.api
      .stores()
      .withKey({ key: this.storeKey })
      .get({
        queryArgs: {
          expand: ['distributionChannels[*]'],
        },
      })
    const response = await this.execRead(req)

    return response?.body
  }
}

module.exports = Project
