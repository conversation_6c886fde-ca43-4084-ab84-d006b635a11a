/** @typedef {import('@commercetools/platform-sdk/dist/declarations/src/generated/client/by-project-key-request-builder').ByProjectKeyRequestBuilder} CTApi */
/** @typedef {import('@commercetools/sdk-client-v2').Client} CTClient */
/** @typedef {import('@commercetools/platform-sdk').ResourceIdentifier} CartResourceIdentifier */
/** @typedef {import('@commercetools/platform-sdk/dist/declarations/src/generated/shared/utils/requests-utils').ApiRequest} CTApiRequest */
/** @typedef {import('@commercetools/platform-sdk/dist/declarations/src/generated/client/product-projections/by-project-key-product-projections-request-builder').ByProjectKeyProductProjectionsRequestBuilder} CTApiProductProjection */

// business types
// ----------------------------------------------------------------------------

/**
 * @typedef {{ action: string }} CTAction
 */

/**
 * @typedef {{
 *  additionAddressInfo: string
 *  additionalStreetInfo: string
 *  apartment: string
 *  building: string
 *  city: string
 *  company: string
 *  country: string
 *  custom: {
 *    fields: {
 *      comments?: string
 *      invoiceEmail: string
 *      invoiceFirstName: string
 *      invoiceLastName: string
 *      vatNumber: string
 *      pec: string
 *      companyNumber: string
 *      fiscalCode: string
 *      date: Date
 *      time: string
 *      contactPhone: string
 *      contactLastName: string
 *      contactFirstName: string
 *      contactTitle: CTAddressContactTitleEnum
 *      optinEmail: boolean
 *      optinSMS: boolean
 *      type: CTAddressTypeEnum
 *      moment: CTAddressMomentEnum
 *      momentDescription?: string
 *      mode: CTAddressModeEnum
 *    },
 *    type: string
 *  }
 *  department: string
 *  email: string
 *  externalId: string
 *  fax: string
 *  firstName: string
 *  id: string
 *  key: string
 *  lastName: string
 *  mobile: string
 *  phone: string
 *  pOBox: string
 *  postalCode: string
 *  region: string
 *  salutation: string
 *  state: string
 *  streetName: string
 *  streetNumber: string
 *  title: string
 * }} CTAddress
 */

/**
 * @typedef {('MergeWithExistingCustomerCart'|'UseAsNewActiveCustomerCart')} CTAnonymousCartSignInMode
 */

/**
 * @typedef {{
 *  custom: CTCustomFields
 *  description: CTLocalizedString
 *  id: string
 *  key: string
 *  name: CTLocalizedString
 *  sources: CTAssetSource[]
 *  tags: string[]
 * }} CTAsset
 */

/**
 * @typedef {{
 *  h: Number
 *  w: Number
 * }} CTAssetDimensions
 */

/**
 * @typedef {{
 *  contentType: string
 *  dimensions: CTAssetDimensions
 *  key: string
 *  uri: string
 * }} CTAssetSource
 */

/**
 * @template {CTLocalizedString|CTMoney|CTReference|string|string[]} T
 * @typedef {{
 *  name: string
 *  value: T
 * }} CTAttributeGeneric<T>
 */

/**
 * @typedef {CTAttributeGeneric<any>} CTAttribute
 */

/**
 * @typedef {(CTHighPrecisionMoney|CTMoney)} CTBaseMoney
 */

/**
 * @see https://docs.commercetools.com/api/projects/carts#cart
 * @typedef {{
 *  anonymousId: string
 *  billingAddress: CTAddress
 *  cartState: CTCartState
 *  country: string
 *  createdAt: Date
 *  createdBy: CTCreatedBy
 *  custom: {
 *    fields: {
 *      additional_data: string
 *      deliveryStatus: string
 *      floristOrderStatus: string
 *      message: string
 *      signature: string
 *      deliveryMode?: CTDeliveryMode
 *      invoiceRequest: boolean
 *      hasDeliveryMethodSelected: boolean
 *      legacyOrderNumber: string
 *    },
 *    type: string
 *  }
 *  customerEmail: string
 *  customerGroup: CTReference<CTCustomerGroup>
 *  customerId: string
 *  customLineItems: CTCustomLineItem[]
 *  deleteDaysAfterLastModification: Number
 *  discountCodes: CTDiscountCodeInfo[]
 *  id: string
 *  inventoryMode: CTInventoryMode
 *  itemShippingAddresses: CTAddress[]
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: CTLastModifiedBy
 *  lineItems: CTLineItem[]
 *  locale: string
 *  origin: CTCartOrigin
 *  paymentInfo: CTPaymentInfo
 *  refusedGifts: CTReference<CTCartDiscount>[]
 *  shippingAddress: CTAddress
 *  shippingInfo: CTShippingInfo
 *  shippingRateInput: CTShippingRateInput
 *  store: CTKeyReference<CTStore>
 *  taxCalculationMode: CTTaxCalculationMode
 *  taxedPrice: CTTaxedPrice
 *  taxMode: CTTaxMode
 *  taxRoundingMode: CTRoundingMode
 *  totalLineItemQuantity: Number
 *  totalPrice: CTCentPrecisionMoney
 *  version: Number
 * }} CTCart
 */

/**
 * @see https://docs.commercetools.com/api/projects/carts#cartdraft
 * @typedef {{
 *  anonymousId: string
 *  billingAddress: CTAddressDraft
 *  currency: 'EUR'
 *  custom: CTCustomFieldsDraft
 *  customerId: string
 *  customerEmail: string
 *  customerGroup: string
 *  customLineItems: CTCustomLineItemDraft[]
 *  country: string
 *  deleteDaysAfterLastModification: Number
 *  discountCodes: string[]
 *  externalTaxRateForShippingMethod: CTExternalTaxRateDraft
 *  inventoryMode: CTInventoryMode
 *  itemShippingAddresses: CTAddressDraft[]
 *  key: string
 *  lineItems: CTLineItemDraft[]
 *  locale: string
 *  origin: CTCartOrigin
 *  shippingAddress: CTAddressDraft
 *  shippingMethod: CartResourceIdentifier
 *  shippingRateInput: CTShippingRateInputDraft
 *  store: CartResourceIdentifier
 *  taxCalculationMode: CTTaxCalculationMode
 *  taxMode: CTTaxMode
 *  taxRoundingMode: CTRoundingMode
 * }} CTCartDraft
 */

/**
 * @typedef {{
 *  ancestors: CTReference<'category'>[]
 *  createdAt: Date
 *  createdBy: string
 *  custom?: {
 *    fields?: {
 *      organizationId?: string,
 *      zone?: 'DOMTOM' | 'CORSE',
 *    }
 *  }
 *  description: string
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: string
 *  name: CTLString
 *  slug: CTLString
 *  version: Number
 * }} CTCategory
 */

/**
 * @typedef {Object<string, Number>} CTCategoryOrderHints
 */

/**
 * @typedef {{
 *  type: 'centPrecision'
 *  currencyCode: string
 *  centAmount: Number
 *  fractionDigits: Number
 * }} CTCentPrecisionMoney
 */

/**
 * @typedef {{
 *  custom: CTCustomFields<>
 *  discountedPricePerQuantity: CTDiscountedLineItemPriceForQuantity[]
 *  id: string
 *  key: string
 *  money: CTTypedMoney
 *  name: CTLocalizedString
 *  perMethodTaxRate: CTMethodTaxRate[]
 *  priceMode: CTCustomLineItemPriceMode
 *  quantity: number
 *  taxedPrice: CTTaxedItemPrice
 *  taxedPricePortions: CTMethodTaxedPrice[]
 *  totalPrice: CTCentPrecisionMoney
 *  slug: string
 *  state: CTItemState[]
 *  taxCategory: CTReference<"tax-category", CTTaxCategory>
 *  taxRate: CTTaxRate
 *  shippingDetails: CTItemShippingDetails
 * }} CTCustomLineItem
 */

/**
 * @typedef {{
 *  type: 'highPrecision'
 *  currencyCode: string
 *  centAmount: Number
 *  fractionDigits: Number
 *  preciseAmount: Number
 * }} CTHighPrecisionMoney
 */

/**
 * @typedef {{
 *  address: CTAddress
 *  createdAt: CTDateTime
 *  createdBy: CTByUser
 *  custom: CTCustomFields
 *  description: CTLocalizedString
 *  geoLocation: CTGeoJson
 *  id: string
 *  key: string
 *  lastModifiedAt: CTDateTime
 *  lastModifiedBy: CTByUser
 *  name: CTLocalizedString
 *  reviewRatingStatistics: CTReviewRatingStatistics
 *  roles: ChannelRoleEnum[]
 *  version: number
 * }} CTChannel
 */

/**
 * @typedef {{
 *  addresses: CTAddress[]
 *  authenticationMode: CTAuthenticationMode
 *  billinAddressIds: string[]
 *  companyName: string
 *  createdAt: Date
 *  createdBy: CTByUser
 *  custom: {
 *    fields: {
 *      interfloraplusExpirationDateES: Date
 *      interfloraplusExpirationDatePT: Date
 *    }
 *    type: CTTypeReference
 *  }
 *  customerGroup: CTReference<CTCustomerGroup>
 *  customerNumber: string
 *  dateOfBirth: Date
 *  defaultBillingAddressId: string
 *  defaultShippingAddressId: string
 *  email: string
 *  externalId: string
 *  firstName: string
 *  id: string
 *  isEmailVerified: boolean
 *  lastModifiedAt: Date
 *  lastModifiedBy: CTByUser
 *  lastName: string
 *  locale: string
 *  middleName: string
 *  password: string
 *  key: string
 *  salutation: string
 *  shippingAddressIds: string[]
 *  stores: CTReference<CTStoreKeyReference>[]
 *  title: string
 *  vatId: string
 *  version: number
 * }} CTCustomer
 */

/**
 * @typedef {{
 *  addresses: CTAddress[]
 *  anonymousCart: CTCartResourceIdentifier
 *  anonymousId: string
 *  authenticationMode: CTAuthenticationMode
 *  billingAddresses: CTAddress[]
 *  companyName: string
 *  custom: {
 *    fields: {
 *      interfloraplusExpirationDateES: Date
 *      interfloraplusExpirationDatePT: Date
 *    }
 *    type: CTTypeReference
 *  }
 *  customerGroup: CTReference<'customer-group'>
 *  customerNumber: string
 *  dateOfBirth: string
 *  defaultBillingAddress: CTAddress
 *  email: string
 *  externalId: string
 *  firstName: string
 *  isEmailVerified: boolean
 *  key: string
 *  lastName: string
 *  locale: string
 *  middleName: string
 *  password: string
 *  salutation: string
 *  shippingAddresses: CTAddress[]
 *  stores: CTStoreResourceIdentifier[]
 *  title: string
 *  vatId: string
 * }} CTCustomerDraft
 */

/**
 * @typedef {{
 *  createdAt: Date
 *  createdBy: CTByUser
 *  custom: CTCustomFields
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: CTByUser
 *  name: string
 *  version: number
 * }} CTCustomerGroup
 */

/**
 * @typedef {{
 *  anonymousCart: CTReference<'cart'>
 *  anonymousCartSignInMode: ('MergeWithExistingCustomerCart'|'UseAsNewActiveCustomerCart')
 *  anonymousId: string
 *  email: string
 *  password: string
 *  updateProductData: boolean
 * }} CTCustomerSignin
 */

/**
 * @typedef {{
 *  cart: CTCart
 *  customer: CTCustomer
 * }} CTCustomerSignInResult
 */

/**
 * @typedef {{
 *  fields: CTFieldContainer
 *  type: CTReference<'type'>
 * }} CTCustomFields
 */

/**
 * @typedef {{
 *  applicationVersion: number
 *  cartDiscounts: CTReference<'cart-discount'>[]
 *  cartPredicate: string
 *  code: string
 *  createdAt: CTDateTime
 *  createdBy: CTByUser
 *  custom: CTCustomFields
 *  description: CTLocalizedString
 *  id: string
 *  isActive: boolean
 *  groups: string[]
 *  lastModifiedAt: CTDateTime
 *  lastModifiedBy: CTByUser
 *  maxApplications: number
 *  maxApplicationsPerCustomer: number
 *  name: CTLocalizedString
 *  references: CTReference[]
 *  validFrom: CTDateTime
 *  validUntil: CTDateTime
 *  version: number
 * }} CTDiscountCode
 */

/**
 * @typedef {{
 *  discountCode: CTReference<'discount-code'>
 *  state: CTDiscountCodeState
 * }} CTDiscountCodeInfo
 */

/**
 * @typedef {(
 *  'ApplicationStoppedByPreviousDiscount'|
 *  'DoesNotMatchCart'|
 *  'MatchesCart'|
 *  'MaxApplicationReached'|
 *  'NotActive'|
 *  'NotValid'
 * )} CTDiscountCodeState
 */

/**
 * @typedef {{
 *  discount: CTReference<CTCartDiscount>
 *  discountedAmount: CTTypedMoney
 * }} CTDiscountedLineItemPortion
 */

/**
 * @typedef {{
 *  includedDiscounts: CTDiscountedLineItemPortion[]
 *  value: CTTypedMoney
 * }} CTDiscountedLineItemPrice
 */

/**
 * @typedef {(
 *  'CEREMONY'|
 *  'NO_ADDRESS'|
 *  'SELF'|
 *  'STANDARD'|
 *  'TOMB'
 * )} CTDeliveryMode
 */

/**
 * @typedef {{
 *  discount: CTReference<'product-discount'>
 *  value: CTMoney
 * }} CTDiscountedPrice
 */

/**
 * @typedef {{
 *  key: string
 *  label: string
 * }} CTEnumValue
 */

/**
 * @typedef {{
 *  amount: number
 *  country: string
 *  includedInPrice: boolean
 *  name: string
 *  state?: string
 * }} CTExternalTaxRateDraft
 */

/**
 * @typedef {CTFilteredFacetResult | CTRangeFacetResult | CTTermFacetResult} CTFacetResult
 */

/**
 * @typedef {Object<string, CTFacetResult>} CTFacetResults
 */

/**
 * @typedef {{
 *  count: number
 *  productCount: number
 *  term: string
 * }} CTFacetTerm
 */

/**
 * @typedef {Object.<string, CTFieldDefinition>} CTFieldContainer
 */

/**
 * @typedef {{
 *  inputHint: CTTextInputHint
 *  label: CTLString
 *  name: string
 *  required: Boolean
 *  type: CTFieldType
 * }} CTFieldDefinition
 */

/**
 * @typedef {{ name: 'Boolean' }} CTBooleanType
 * @typedef {{ name: 'Date' }} CTDateType
 * @typedef {{ name: 'DateTime' }} CTDateTimeType
 * @typedef {{
 *  name: 'Enum'
 *  values: CTEnumValue[]
 * }} CTEnumType
 * @typedef {{
 *  name: 'LocalizedEnum'
 *  values: CTLocalizedEnumValue[]
 * }} CTLocalizedEnumType
 * @typedef {{ name: 'LocalizedString' }} CTLocalizedStringType
 * @typedef {{ name: 'Money' }} CTMoneyType
 * @typedef {{ name: 'Number' }} CTNumberType
 * @typedef {{
 *  name: 'Reference'
 *  referenceTypeId: (
 *    'cart'|
 *    'category'|
 *    'channel'|
 *    'customer'|
 *    'key-value-document'|
 *    'order'|
 *    'product'|
 *    'product-type'|
 *    'review'|
 *    'state'|
 *    'shipping-method'|
 *    'zone'
 *  )
 * }} CTReferenceType
 * @typedef {{
 *  name: 'Set'
 *  elementType: CTFieldType
 * }} CTSetType
 * @typedef {{ name: 'String' }} CTStringType
 * @typedef {{ name: 'Time' }} CTTimeType
 *
 * @typedef {(
 *  CTBooleanType|
 *  CTDateType|
 *  CTDateTimeType|
 *  CTEnumType|
 *  CTLocalizedEnumType|
 *  CTLocalizedStringType|
 *  CTMoneyType|
 *  CTNumberType|
 *  CTReferenceType|
 *  CTSetType|
 *  CTStringType|
 *  CTTimeType
 * )} CTFieldType
 */

/**
 * @typedef {{
 *  count: number
 *  productCount: number
 *  type: 'filter'
 * }} CTFilteredFacetResult
 */

/**
 * @typedef {{
 *  centAmount: Number
 *  currencyCode: 'EUR'
 *  fractionDigits: Number
 *  preciseAmount: Number
 *  type: 'highPrecision'
 * }} CTHighPrecisionMoney
 */

/**
 * @typedef {{
 *  addedAt: Date
 *  custom: CTCustomFields
 *  discountedPrice?: CTDiscountedLineItemPrice
 *  discountedPricePerQuantity: CTDiscountedLineItemPriceForQuantity[]
 *  distributionChannel: CTReference<CTChannel>
 *  id: string
 *  lastModifiedAt: Date
 *  lineItemMode: CTLineItemMode
 *  name: CTLocalizedString
 *  price: CTPrice
 *  priceMode: CTLineItemPriceMode
 *  productId: string
 *  productKey: string
 *  productSlug: CTLocalizedString
 *  productType: CTReference<CTProductType>
 *  quantity: Number
 *  shippingDetails: CTItemShippingDetails
 *  state: CTItemState[]
 *  supplyChannel: CTReference<CTChannel>
 *  taxedPrice: CTTaxedItemPrice
 *  taxRate: CTTaxRate
 *  totalPrice: CTCentPrecisionMoney
 *  variant: CTProductVariant
 * }} CTLineItem
 */

/**
 * @typedef {{
 *  key: string
 *  label: CTLString
 * }} CTLocalizedEnumValue
 */

/**
 * @typedef {Object<string, string>} CTLocalizedString
 */

/**
 * @typedef {{
 *  country: string
 *  state: string
 * }} CTLocation
 */

/**
 * @typedef {{
 *  en: string
 *  en-US: string
 *  it: string
 *  es: string
 *  pt: string
 * }} CTLString
 */

/**
 * @typedef {{
 *  centAmount: Number
 *  currencyCode: 'EUR'
 *  fractionDigits: Number
 *  type: 'centPrecision'
 * }} CTMoney
 */

/**
 * @typedef {{
 *  id: string
 *  version: Number
 * }} CTMyOrderFromCartDraft
 */

/**
 * @typedef {{
 *  amountPlanned: CTCentPrecisionMoney
 *  anonymousId: string
 *  createdAt: string
 *  createdBy: CTByUser
 *  custom: CTCustomFields
 *  customer: CTReference<'customer'>
 *  id: string
 *  interfaceId: string
 *  interfaceInteractions: CTCustomFields[]
 *  key: string
 *  lastModifiedAt: string
 *  lastModifiedBy: CTByUser
 *  paymentMethodInfo: CTPaymentMethodInfo
 *  paymentStatus: CTPaymentStatus
 *  transactions: CTTransaction[]
 *  version: Number
 * }} CTPayment
 */

/**
 * @typedef {{
 *  amountPlanned: Number
 *  custom: CTCustomFields
 *  paymentMethodInfo: CTPaymentMethodInfo
 *  transation: CTMyTransactionDraft
 * }} CTMyPaymentDraft
 */

/**
 * @typedef {{
 *  amount: CTMoney
 *  custom: CTCustomFields
 *  interactionId: string
 *  timestamp: Date
 *  type: CTTransactionType
 * }} CTMyTransactionDraft
 */

/**
 * @typedef {CTCart & {
 *  cart: CTReference<CTCart>
 *  completedAt: Date
 *  orderNumber: string
 *  orderState: CTOrderState
 *  paymentInfo: CTPaymentInfo
 *  paymentState: CTPaymentState
 *  shipmentState: CTShipmentState
 *  shippingInfo: CTShippingInfo
 *  syncInfo: CTSyncInfo
 * }} CTOrder
 */

/**
 * @see https://docs.commercetools.com/api/projects/orders#orderfromcartdraft
 * @typedef {{
 *  cart: {
 *   id: string
 *   key: string
 *   type: 'Cart'
 *  }
 *  custom: CTCustomFields
 *  orderNumber: string
 *  orderState: CTOrderState
 *  paymentState: CTPaymentState
 *  shipmentState: CTShipmentState
 *  state: CTReference<CTState>
 *  version: Number
 * }} CTOrderFromCartDraft
 */

/**
 * @template T
 * @typedef {{
 *  count: Number
 *  facets: CTFacetResult[]
 *  limit: Number
 *  meta: Object
 *  offset: Number
 *  results: T[]
 *  total: Number
 * }} CTPagedQueryResult<T>
 */

/**
 * @see https://docs.commercetools.com/api/projects/payments#paymentdraft
 * @typedef {{
 *  amountPlanned: CTMoney
 *  anonymousId: string
 *  custom: CTCustomFields
 *  customer: CTReference<CTCustomer>
 *  interfaceId: string
 *  interfaceInteractions: CTCustomFieldsDraft[]
 *  key: string
 *  paymentMethodInfo: CTPaymentInfo
 *  paymentStatus: CTPaymentStatus
 *  transactions: CTTransactionDraft[]
 * }} CTPaymentDraft
 */

/**
 * @typedef {{
 *  payments: CTReference<CTPayment>[]
 * }} CTPaymentInfo
 */

/**
 * @typedef {{
 *  method: string
 *  name: CTLocalizedString
 *  paymentInterface: string
 * }} CTPaymentMethodInfo
 */

/**
 * @typedef {{
 *  interfaceCode: string
 *  interfaceText: string
 *  state: CTReference<CTState>
 * }} CTPaymentStatus
 */

/**
 * @typedef {{
 *  channel: CTReference<'channel'>
 *  country: string
 *  custom: CTCustomFields
 *  customerGroup: CTReference<'customer-group'>
 *  discounted: CTDiscountedPrice
 *  id: string
 *  tiers: CTPriceTier[]
 *  validFrom: Date
 *  validUntil: Date
 *  value: CTBaseMoney
 * }} CTPrice
 */

/**
 * @typedef {{
 *  minimumQuantity: Number
 *  value: CTBaseMoney
 * }} CTPriceTier
 */

/**
 * @typedef {{
 *  categories: CTCategoryReference[]
 *  categoryOrderHints: CTCategoryOrderHint[]
 *  createdAt: Date
 *  description: string
 *  hasStagedChanges: boolean
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  masterVariant: CTProductVariant
 *  metaDescription: CTLocalizedString
 *  metaTitle: CTLocalizedString
 *  name: string
 *  priceMode: ('Embedded'|'Standalone')
 *  productType: CTReference<'product-type'>
 *  published: boolean
 *  searchKeywords: CTSearchKeyword[]
 *  slug: string
 *  state: CTStateReference
 *  taxCategory: CTReference<'tax-category'>
 *  variants: CTProductVariant[]
 *  version: number
 * }} CTProduct
 */

/**
 * @typedef {{
 *  filters: GQLInputProductFilter[]
 *  limit: number
 *  locale: string
 *  offset: number
 *  search: string
 *  siteId: string
 *  subcategoryOrderHints: string[]|undefined
 * }} CTProductListOptions
 */

/**
 * @typedef {{
 *  categories: CTReference<'category'>[]
 *  categoryOrderHints: CTCategoryOrderHints
 *  createdAt: Date
 *  description: string
 *  hasStagedChanges: Boolean
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  masterVariant: CTProductVariant
 *  metaDescription: CTLString
 *  metaKeywords: CTLString
 *  metaTitle: CTLString
 *  name: string
 *  productType: CTReference<'product-type'>
 *  publishes: Boolean
 *  reviewRatingStatitstics: CTReviewRatingStatistics
 *  searchKeywords: CTSearchKeywords
 *  slug: CTLString
 *  state: CTReference<'state'>
 *  taxCategory: CTReference<'tax-category'>
 *  version: Number
 * }} CTProductProjection
 */

/**
 * @typedef {{
 *  attributes: CTAttributeDefinition[]
 *  createdAt: Date
 *  createdBy: CTCreatedBy
 *  description: string
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: CTLastModifiedBy
 *  name: string
 *  version: Number
 * }} CTProductType
 */

/**
 * @typedef {{
 *  assets: CTAsset[]
 *  attributes: CTAttribute[]
 *  availability: CTProductVariantAvailability
 *  id: string
 *  isMatchingVariant: boolean
 *  images: CTImage[]
 *  key: string
 *  price: CTPrice
 *  prices: CTPrice[]
 *  scopedPrice: CTScopedPrice
 *  scopedPriceDiscounted: CTScopedPriceDiscounted
 *  sku: string
 * }} CTProductVariant
 */

/**
 * @typedef {{
 *  count: number
 *  from: number
 *  fromStr: string
 *  max: numer
 *  mean: number
 *  min: number
 *  productCount: string
 *  to: number
 *  toStr: string
 *  total: number
 * }} CTRange
 */

/**
 * @typedef {{
 *  type: 'range'
 *  ranges: CTRange[]
 * }} CTRangeFacetResult
 */

/**
 * @template {CTReferenceTypeEnum} T
 * @template O
 * @typedef {{
 *  id: string
 *  obj: O
 *  typeId: T
 * }} CTReference<T,O>
 */

/**
 * @typedef {(
 *  'cart' |
 *  'cart-discount' |
 *  'category' |
 *  'channel' |
 *  'customer' |
 *  'customer-group' |
 *  'discount-code' |
 *  'key-value-document' |
 *  'payment' |
 *  'product' |
 *  'product-discount' |
 *  'product-price' |
 *  'product-type' |
 *  'order' |
 *  'order-edit' |
 *  'shipping-method' |
 *  'shopping-list' |
 *  'state' |
 *  'store' |
 *  'tax-category' |
 *  'type' |
 *  'zone'
 * )} CTReferenceTypeEnum
 */

/**
 * @typedef {(
 *  'MR' |
 *  'MRS'
 * )} CTAddressContactTitleEnum
 */

/**
 * @typedef {(
 *  'COMPANY' |
 *  'FREELANCE' |
 *  'INDIVIDUAL' |
 *  'INDIVIDUAL_COMPANY'
 * )} CTAddressTypeEnum
 */

/**
 * @typedef {(
 *  '0' |
 *  '1' |
 *  '2'
 * )} CTAddressMomentEnum
 */

/**
 * @typedef {(
 *  'CEREMONY' |
 *  'NO_ADDRESS' |
 *  'STANDARD' |
 *  'SELF' |
 *  'TOMB'
 * )} CTAddressModeEnum
 */

/**
 * @typedef {{
 *  channel: CTReference<'channel'>
 *  country: string
 *  custom: CTCustomFields
 *  customerGroup: CTReference<'customer-group'>
 *  currentValue: CTBaseMoney
 *  discounted: CTDiscountedPrice
 *  id: string
 *  validFrom: Date
 *  validUntil: Date
 *  value: CTBaseMoney
 * }} CTScopedPrice
 */

/**
 * @typedef {{
 *  suggestTokenizer: CTSuggestTokenizer
 *  text: string
 * }} CTSearchKeyword
 */

/**
 * @typedef {Object.<('en'|'en-US'|'it'|'it-IT'), CTSearchKeyword[]>} CTSearchKeywords
 */

/**
 * @typedef {{
 *  deliveries: CTDelivery[]
 *  discountedPrice?: CTDiscountedLineItemPrice
 *  price: CTCentPrecisionMoney
 *  shippingMethod: CTShippingMethod
 *  shippingMethodName: string
 *  shippingMethodState: CTShippingMethodState
 *  shippingRate: CTShippingRate
 *  taxCategory: CTReference<'tax-category'>
 *  taxedPrice: CTTaxedItemPrice
 *  taxRate: CTTaxRate
 * }} CTShippingInfo
 */

/**
 * @typedef {{
 *  createdAt: Date
 *  description: string
 *  id: string
 *  isDefault: boolean
 *  key: string
 *  lastModifiedAt: Date
 *  localizedName: CTLocalizedString
 *  name: string
 *  predicate: string
 *  taxCategory: CTReference<CTTaxCategory>
 *  version: Number
 *  zoneRates: CTZoneRate[]
 * }} CTShippingMethod
 */

/**
 * @typedef {{
 *  freeAbove: CTCentPrecisionMoney
 *  isMatching: boolean
 *  price: CTCentPrecisionMoney
 *  tiers: CTShippingRatePriceTier[]
 * }} CTShippingRate
 */

/**
 * @typedef {(CTCartValueType | CTCartClassificationType | CTCartScoreType)} CTShippingRatePriceTier
 */

/**
 * @typedef {{
 *  amount: number
 *  name: string
 * }} CTSubRate
 */

/**
 * @typedef {{
 *  countries: CTStoreCountry[]
 *  createdAt: Date
 *  createdBy: CTByUser
 *  custom: CTCustomFields
 *  distributionChannels: CTReference<"channel", CTChannel>[]
 *  id: string
 *  key: string
 *  languages: CTLocate[]
 *  name: string
 *  productSelections: CTProductSelectionSetting[]
 *  supplyChannels: CTReference<"channel">[]
 *  updatedAt: Date
 *  updatedBy: CTByUser
 *  version: number
 * }} CTStore
 */

/**
 * @typedef {{
 *  createdAt: Date
 *  createdBy: CTByUser
 *  description: string
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: CTByUser
 *  name: string
 *  rates: CTTaxRate[]
 *  version: number
 * }} CTTaxCategory
 */

/**
 * @typedef {{
 *  totalGross: CTCentPrecisionMoney
 *  totalNet: CTCentPrecisionMoney
 *  totalTax: CTCentPrecisionMoney
 * }} CTTaxedItemPrice
 */

/**
 * @typedef {{
 *  taxPortions: CTTaxPortion[]
 *  totalGross: CTCentPrecisionMoney
 *  totalNet: CTCentPrecisionMoney
 *  totalTax: CTCentPrecisionMoney
 * }} CTTaxedPrice
 */

/**
 * @typedef {{
 *  amount: number
 *  country: string
 *  id: string
 *  includedInPrice: boolean
 *  name: string
 *  state: string
 *  subRates: CTSubRate[]
 * }} CTTaxRate
 */

/**
 * @typedef {{
 *  type: 'custom'
 *  inputs: string[]
 * }} CTCustomTokenizer
 * @typedef {{type: 'whitespace'}} CTWhitespaceTokenizer
 * @typedef {CTCustomTokenizer|CTWhitespaceTokenizer} CTSuggestTokenizer
 */

/**
 * @typedef {{
 *  type: 'terms'
 *  dataType: ('boolean' | 'date' | 'datetime' | 'number' | 'text' | 'time')
 *  missing: number
 *  other: number
 *  terms: CTFacetTerm[]
 *  total: number
 * }} CTTermFacetResult
 */

/**
 * @typedef {('SingleLine' | 'MultiLine')} CTTextInputHint
 */

/**
 * @typedef {{
 *  access_token: string
 *  expires_in: Number
 *  refresh_token: string
 *  scope: string
 *  token_type: 'Bearer'
 * }} CTToken
 */

/**
 * @typedef {{
 *  amount: CTCentPrecisionMoney
 *  custom: CTCustomFields
 *  id: string
 *  interactionId: string
 *  state: CTTransactionState
 *  timestamp: Date
 *  type: CTTransactionType
 * }} CTTransaction
 */

/**
 * @see https://docs.commercetools.com/api/projects/payments#transactiondraft
 * @typedef {{
 *  amount: CTMoney
 *  custom: CTCustomFields
 *  interactionId: String
 *  state: CTTransactionState
 *  timestamp: CTDateTime
 *  type: CTTransactionType
 * }} CTTransactionDraft
 */

/**
 * @typedef {(
 *  'Failure' |
 *  'Initial' |
 *  'Pending' |
 *  'Success'
 * )} CTTransactionState
 */

/**
 * @typedef {(
 *  'Authorization' |
 *  'CancelAuthorization' |
 *  'Charge' |
 *  'ChargeBack' |
 *  'Refund'
 * )} CTTransactionType
 */

/**
 * @typedef {{
 *  createdAt: Date
 *  createdBy: string
 *  description: CTLString
 *  fieldDefinitions: CTFieldDefinition[]
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: string
 *  name: CTLString
 *  resourceTypeIds: string[]
 *  version: Number
 * }} CTType
 */

/** @typedef {CTBaseMoney} CTTypedMoney */

/**
 * @typedef {{
 *  createdAt: Date
 *  createdBy: string
 *  description: string
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: string
 *  locations: CTLocation[]
 *  name: string
 *  version: Number
 * }} CTZone
 */

/**
 * @typedef {{
 *  shippingRates: CTShippingRate[]
 *  zone: CTReference<CTZone>
 * }} CTZoneRate
 */

/**
 * @typedef {{
 *  count: number
 *  facets: CTFacetResult[]
 *  limit: number
 *  offset: number
 *  results: GQLProduct[]
 *  total: number
 * }} CTAccessoriesList
 */

/**
 * @typedef {{
 *  action: 'setCustomShippingMethod'
 *  externalTaxRate: {
 *    amount: number
 *    country: string
 *    includedInPrice: boolean | undefined
 *    name: string
 *    state: string | undefined
 *  } | undefined
 *  shippingMethodName: string
 *  shippingRate: {
 *    price: CTMoney
 *    freeAbove: CTMoney | undefined
 *    tiers: CTShippingRatePriceTier[] | undefined
 *  }
 *  taxCategory: {
 *   id: string
 *   typeId: 'tax-category'
 *  } | {
 *   key: string
 *   typeId: 'tax-category'
 *  } | undefined
 * }} CTSetCustomShippingMethodAction
 */

/**
 * @typedef {{
 *  cart?: CTCart,
 *  customer: CTCustomer,
 * }} CTCustomerSignInResult
 */

/**
 * @typedef {{
 *  availableQuantity: number
 *  createdAt: Date
 *  createdBy: string
 *  expectedDelivery: Date
 *  custom: {}
 *  id: string
 *  key: string
 *  lastModifiedAt: Date
 *  lastModifiedBy: string
 *  quantityOnStock: number
 *  restockableInDays: number
 *  sku: string
 *  supplyChannel: {
 *   id: string
 *   typeId: 'channel'
 *  }
 *  version: Number
 * }} CTInventoryEntry
 */

/**
 * @typedef {{
 *   id: string
 *   method: string
 *   methodName: string
 *   rewardType: ("awardit")
 *   providerId: string
 *   providerName: string
 *   providerResponse: string
 * }} customLineItemReward
 */
