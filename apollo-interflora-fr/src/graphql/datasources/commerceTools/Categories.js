const CommerceTools = require('./CommerceTools')

/** @typedef {import('./_typedef')} */

class Categories extends CommerceTools {
  /**
   * @override
   * @returns {CTApi}
   */
  get productProjections() {
    return super.productProjections.categories()
  }

  async getCategory(slug, lang) {
    const response = await this.productProjections
      .get({
        queryArgs: {
          where: `slug(${lang}="${slug}")`,
        },
      })
      .execute()

    return response?.body?.results[0]
  }

  /**
   * @param {string} id
   * @returns {CTCategory[]}
   */
  async getSubcategories(id) {
    const response = await this.api
      .categories()
      .get({
        queryArgs: {
          where: `ancestors(id="${id}")`,
        },
      })
      .execute()
    return response?.body?.results
  }

  /**
   * @param {string} id
   * @param {string} organizationId
   * @returns {CTCategory[]}
   */
  async getOrganizationSubcategories(id, organizationId) {
    const response = await this.api
      .categories()
      .get({
        queryArgs: {
          where: `ancestors(id="${id}") and custom(fields(organizationId="${organizationId}"))`,
        },
      })
      .execute()
    return response?.body?.results
  }
}

module.exports = Categories
