const CommerceTools = require('./CommerceTools')

/** @typedef {import('./_typedef')} */

class Inventories extends CommerceTools {
  /**
   * @param {string} variantSku
   * @returns {Promise<CTInventoryEntry | null>}
   */
  async getInventoryForVariantBySku(variantSku) {
    return (
      (
        await this.execAdmin(
          this.api.inventory().get({
            queryArgs: { where: [`sku="${variantSku}"`, `supplyChannel(id= "${await this.priceChannelId()}")`] },
          })
        )
      )?.body.results?.[0] || null
    )
  }

  /**
   *
   * @param {string} variantSku
   * @param {number} quantity
   */
  async updateInventoryByVariantSku(variantSku, quantity) {
    const inventoryEntry = await this.getInventoryForVariantBySku(variantSku)
    if (inventoryEntry && inventoryEntry?.quantityOnStock !== quantity) {
      await this.execAdmin(
        this.api
          .inventory()
          .withId({ ID: inventoryEntry.id })
          .post({
            body: {
              actions: [{ action: 'changeQuantity', quantity }],
              version: inventoryEntry.version,
            },
          })
      )
    }
  }
}

module.exports = Inventories
