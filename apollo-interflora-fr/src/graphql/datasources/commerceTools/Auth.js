const CommerceTools = require('./CommerceTools')
const config = require('../../../config')

/** @typedef {import("./_typedef")} */

/**
 * @class
 * @extends CommerceTools
 */
class AuthDataSource extends CommerceTools {
  constructor() {
    super()

    const projectKey = config.commerceTools.auth.projectKey
    this.baseURL = `https://auth.europe-west1.gcp.commercetools.com/oauth/${projectKey}/`

    {
      const { clientId, clientSecret } = config.commerceTools.auth.credentials
      this.asUserHeaders = {
        Authorization: `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
      }
    }
  }

  /**
   * @returns {Promise<CTToken>}
   */
  adminLogin() {
    const { commerceToolsAdminClientId, commerceToolsAdminSecretId } = config.tokens

    return this.post(`https://auth.europe-west1.gcp.commercetools.com/oauth/token`, {
      body: 'grant_type=client_credentials',
      headers: {
        Authorization: `Basic ${Buffer.from(`${commerceToolsAdminClientId}:${commerceToolsAdminSecretId}`).toString(
          'base64'
        )}`,
      },
    })
  }

  /**
   * @returns {Promise<CTToken>}
   */
  anonymousLogin() {
    return this.post('anonymous/token', {
      body: 'grant_type=client_credentials',
      headers: this.asUserHeaders,
    })
  }

  /**
   * @param {{
   *  email: string,
   *  password: string
   * }} opts
   * @returns {Promise<CTToken>}
   */
  customerLogin(opts) {
    const { email, password } = opts

    const params = new URLSearchParams()
    params.set('grant_type', 'password')
    params.set('username', email)
    params.set('password', password)

    return this.post(`in-store/key=${this.storeKey}/customers/token`, {
      body: params.toString(),
      headers: this.asUserHeaders,
    })
  }

  /**
   * ⚠️ customerSignin does not support query expansion
   * Don't use this method to retrieve a customer if you need the expanded customer group
   *
   * @param {CTCustomerSignin} payload
   * @returns {Promise<CTCustomerSigninResult>}
   */
  async customerSignin(payload) {
    const req = this.api.inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey }).login().post({
      body: payload,
    })

    const response = await this.execAdmin(req)
    return response?.body
  }

  /**
   * @param {string} refreshToken
   * @returns {Promise<CTToken>}
   */
  async refreshAnonymousToken(refreshToken) {
    return this.post('anonymous/token', {
      body: `grant_type=refresh_token&refresh_token=${refreshToken}`,
      headers: this.asUserHeaders,
    })
  }

  /**
   * @param {string} token
   * @returns {Promise<CTToken>}
   */
  async refreshToken(token) {
    const params = new URLSearchParams()
    params.set('grant_type', 'refresh_token')
    params.set('refresh_token', token)

    return this.post(`https://auth.europe-west1.gcp.commercetools.com/oauth/token`, {
      body: params.toString(),
      headers: this.asUserHeaders,
    })
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   */
  willSendRequest(_path, request) {
    request.headers['content-type'] = 'application/x-www-form-urlencoded'
  }

  /**
   * @returns {Promise<CTClient>}
   */
  readLogin() {
    const { commerceToolsReadClientId, commerceToolsReadSecretId } = config.tokens

    return this.post(`https://auth.europe-west1.gcp.commercetools.com/oauth/token`, {
      body: 'grant_type=client_credentials',
      headers: {
        Authorization: `Basic ${Buffer.from(`${commerceToolsReadClientId}:${commerceToolsReadSecretId}`).toString(
          'base64'
        )}`,
      },
    })
  }
}

module.exports = AuthDataSource
