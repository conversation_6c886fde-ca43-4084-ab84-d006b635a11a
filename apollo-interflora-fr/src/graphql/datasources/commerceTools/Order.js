const CommerceTools = require('./CommerceTools')
const _ = require('lodash')

const { isUUID } = require('../../../helpers/string')
const apm = require('elastic-apm-node')
const CONST = require('../../../const')

class Order extends CommerceTools {
  /**
   * @param {CTOrder} order
   * @param {string} userId
   * @param {string} email
   * @returns {Promise<void>}
   */
  async attachOrderAsAdmin(order, userId, email) {
    const actions = []

    if (order.customerEmail !== email) {
      actions.push({
        action: 'setCustomerEmail',
        email,
      })
    }
    if (order.custom?.fields?.userId !== userId) {
      actions.push({
        action: 'setCustomField',
        name: 'userId',
        value: userId,
      })
    }

    if (actions.length === 0) return order

    const { id, version } = order
    const req = this.api.orders().withId({ ID: id }).post({
      body: {
        actions,
        version,
      },
    })

    return this.execAdmin(req)
  }

  /**
   * @param {string} cartId
   * @returns {Promise<CTOrder>}
   */
  async getByCartId(cartId) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({
        storeKey: this.storeKey,
      })
      .orders()
      .get({
        queryArgs: {
          where: `cart(id="${cartId.replace(/"/g, '')}")`,
        },
      })

    const response = await this.execRead(req)
    return response?.body?.results?.[0]
  }

  /**
   * @param {string} orderNumber
   * @param {string} email
   */
  async getByNumberAndEmail(orderNumber, email) {
    const req = this.api.orders().get({
      queryArgs: {
        limit: 1,
        where: [
          // @todo To check: we are using id in order transformer as a number, shouldn't be orderNumber
          `orderNumber = "${orderNumber.replace(/"/g, '')}"`,
          `customerEmail = "${email.replace(/"/g, '')}"`,
          `store(key="${this.storeKey}")`,
        ],
      },
    })

    const response = await this.execRead(req)

    return response?.body?.results[0] || null
  }

  /**
   * @param {string} id
   * @returns {CTOrder}
   */
  async getOrder(id) {
    const _id = id.replace(/"/g, '')
    if (!isUUID(_id)) {
      this.context.log.warn(`attempt to load order from not a guid ${_id}`)
      return this.getOrderByNumber(id)
    }

    const req = this.api.inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey }).orders().withId({ ID: _id }).get()

    const response = await this.execRead(req)
    return response?.body || null
  }

  /**
   * @param {string} orderNumber
   * @returns {CTOrder}
   */
  async getOrderByNumber(orderNumber) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey })
      .orders()
      .get({
        queryArgs: {
          limit: 1,
          where: [`orderNumber = "${orderNumber.replace(/"/g, '')}"`],
        },
      })

    const response = await this.execRead(req)
    return response?.body?.results[0] || null
  }

  /**
   * @param {string} orderNumber
   * @returns {CTOrder}
   */
  async getOrderByLegacyOrderNumber(orderNumber) {
    const req = this.api
      .inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey })
      .orders()
      .get({
        queryArgs: {
          limit: 1,
          where: [`custom(fields(legacyOrderNumber="${orderNumber.replace(/"/g, '')}"))`],
        },
      })

    const response = await this.execRead(req)
    return response?.body?.results[0] || null
  }

  /**
   * @param {{
   *  after?: Date,
   *  before?: Date,
   *  customQueryParam?: string
   *  email: string,
   *  limit?: number,
   *  offset?: number,
   *  sort?: string,
   * }} opts
   * @param {string} [opts.customQueryParam]
   * @returns {CTPagedQueryResult<CTOrder>}
   */
  async getOrdersByCustomerEmail(opts = {}) {
    const { after, before, email, offset = 0, limit = 50, sort = 'createdAt DESC', customQueryParam = '' } = opts

    const where = [
      `custom(fields(src="${CONST.commerceTools.order.comingFromSystem.Commercetools}"))`,
      `customerEmail="${email.replace(/"/g, '')}"`,
    ]

    if (after) {
      where.push(`createdAt >= "${after.toISOString()}"`)
    }
    if (before) {
      where.push(`createdAt < "${before.toISOString()}"`)
    }
    if (customQueryParam) {
      where.push(customQueryParam)
    }

    if (!email || email === '')
      return {
        results: [],
        total: 0,
      }

    const req = this.api
      .inStoreKeyWithStoreKeyValue({ storeKey: this.storeKey })
      .orders()
      .get({
        queryArgs: {
          limit,
          offset,
          sort,
          where: where.join(' AND '),
        },
      })
    const response = await this.execAdmin(req)

    return response.body
  }

  /**
   * @param {CTOrder} order
   * @param {{
   *  billingAddress: CTAddress,
   *  shippingAddress: CTAddress,
   *  customFields: Record<string, any>
   * }} newData
   * @returns {CTOrder}
   */
  async updateOrder(order, newData) {
    const { billingAddress, shippingAddress, customFields } = newData
    const actions = []

    if (!_.isEqual(order.billingAddress, billingAddress)) {
      actions.push({
        action: 'setBillingAddress',
        address: billingAddress,
      })
    }

    if (!_.isEqual(order.shippingAddress, shippingAddress)) {
      actions.push({
        action: 'setShippingAddress',
        address: shippingAddress,
      })
    }

    for (const [name, value] of Object.entries(customFields)) {
      if (value) {
        actions.push({
          action: 'setCustomField',
          name,
          value,
        })
      } else if (order.custom?.fields?.[name]) {
        actions.push({
          action: 'setCustomField',
          name,
        })
      }
    }

    if (!actions.length) {
      return order
    }

    const req = this.api
      .orders()
      .withId({ ID: order.id })
      .post({
        body: {
          actions,
          version: order.version,
        },
      })

    const response = await this.execAdmin(req)

    return response?.body || null
  }

  /**
   * @param {string} discountCode
   * @returns {Promise<CTDiscountCode>}
   */

  async getDiscountCode(discountCode) {
    const req = this.api.discountCodes().get({
      queryArgs: {
        where: `code="${discountCode.replace(/"/g, '')}"`,
      },
    })
    const {
      body: { results },
    } = await this.execRead(req)
    return results[0]
  }

  /**
   * @param {number} discountId
   * @returns {Promise<CTDiscountCode>}
   */

  async getCartDiscount(discountId) {
    const req = this.api
      .cartDiscounts()
      .withId({ ID: `${discountId}` })
      .get()
    const res = await this.execRead(req)
    return res?.body
  }

  /**
   * Adds a refund transaction and interface interaction to a CT Payment.
   * Use only after a confirmed refund.
   * @param {string} paymentId
   * @param {number} amount
   * @param {string} currency
   * @param {string} transactionId
   * @param {string | Object} response
   * @returns {CTPayment | null}
   */
  async addRefundInfoToPayment(paymentId, amount, currency, transactionId, response) {
    let payment
    try {
      const paymentReq = this.api.payments().withId({ ID: paymentId }).get()
      payment = (await this.execRead(paymentReq))?.body
    } catch (e) {
      const err = new Error(
        `[addRefundInfoToPayment] unable to get payment with id '${paymentId}', error: ${e.message}`
      )
      apm.captureError(err, { custom: err })
      return null
    }

    const req = this.api
      .payments()
      .withId({ ID: `${paymentId}` })
      .post({
        body: {
          actions: [
            {
              action: 'addTransaction',
              transaction: {
                amount: {
                  centAmount: amount,
                  currencyCode: currency,
                },
                interactionId: transactionId,
                state: 'Success',
                timestamp: new Date(),
                type: 'Refund',
              },
            },
            {
              action: 'addInterfaceInteraction',
              fields: {
                serialized: typeof response === 'string' ? response : JSON.stringify(response),
              },
              type: {
                key: CONST.commerceTools.customTypes.paymentInterfaceInteraction,
                typeId: 'type',
              },
            },
          ],
          version: payment.version,
        },
      })
    try {
      const res = await this.execAdmin(req)
      return res?.body
    } catch (e) {
      const err = new Error(
        `[addRefundInfoToPayment] unable to add refund info with args (${arguments}), error: ${e.message}`
      )
      apm.captureError(err, { custom: err })
      return null
    }
  }
}

module.exports = Order
