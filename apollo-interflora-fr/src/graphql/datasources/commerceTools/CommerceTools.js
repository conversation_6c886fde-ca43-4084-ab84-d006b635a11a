const config = require('../../../config')
const CONST = require('../../../const')
const { ClientBuilder, createAuthForClientCredentialsFlow, createHttpClient } = require('@commercetools/sdk-client-v2')
const { createApiBuilderFromCtpClient } = require('@commercetools/platform-sdk')
const fetch = require('../../../helpers/fetch')
const { RESTDataSource } = require('../RESTDataSource')

const { GraphQLApolloError } = require('../../errors')
const CtSessionTransformer = require('../../transformers/commerceTools/Session')

/** @typedef {import('./_typedef')} */

/**
 * @class CommerceTools
 * @extends {RESTDataSource}
 * @abstract
 */
class CommerceTools extends RESTDataSource {
  constructor() {
    super()

    /** @type {CTDatocmsContext} */
    this.context

    /** @type {CTApi} */
    this._api

    /** @type {CTClient} */
    this._client

    /** @type {string} */
    this._gfsProductTypeId

    /** @type {Map<string, string>} */
    this._priceChannelIds = new Map()
  }

  /**
   * @returns {CTApi}
   */
  get api() {
    if (!this._api) {
      this._api = createApiBuilderFromCtpClient(this.client).withProjectKey({
        projectKey: config.commerceTools.auth.projectKey,
      })
    }
    return this._api
  }

  /**
   * @returns {CTClient}
   */
  get client() {
    if (!this._client) {
      const projectKey = config.commerceTools.auth.projectKey
      const authMiddlewareOptions = {
        ...config.commerceTools.auth,
        fetch,
      }
      const httpMiddlewareOptions = {
        ...config.commerceTools.http,
        fetch: (url, options = {}) => {
          const _options = { ...options }
          if (!_options.timeout) _options.timeout = config.datasources.timeout
          return fetch(url, _options)
        },
        // prevent the sdk to JSON stringity url-encoded payloads @see https://github.com/commercetools/commercetools-sdk-typescript/issues/1039
        headersWithStringBody: ['application/x-www-form-urlencoded'],
      }

      this._client = new ClientBuilder()
        .withProjectKey(projectKey)
        .withMiddleware(createAuthForClientCredentialsFlow(authMiddlewareOptions))
        .withMiddleware(createHttpClient(httpMiddlewareOptions))
        .withUserAgentMiddleware()
        .build()
    }

    return this._client
  }

  /**
   * @returns {string}
   */
  get currency() {
    return this.context.appConfig.currency
  }

  /**
   * @returns {string}
   */
  get storeKey() {
    return this.context.appConfig.commerceTools.storeKey
  }

  /**
   * @returns {Promise<string[]>}
   */
  async activeProductIds() {
    return this.context.loaders.commerceTools.activeProductIds.load(true)
  }

  async gfsProductTypeId() {
    if (!this._gfsProductTypeId) {
      const productType = await this.context.loaders.commerceTools.productTypesByKey.load('gfs')
      this._gfsProductTypeId = productType.id
    }

    return this._gfsProductTypeId
  }

  /**
   * @param {string} [key]
   * @returns {Promise<string>}
   */
  async priceChannelId(key = config.commerceTools.priceChannelKey) {
    if (!this._priceChannelIds.has(key)) {
      const channel = await this.context.loaders.commerceTools.channelsByKey.load(key)
      this._priceChannelIds.set(key, channel.id)
    }

    return this._priceChannelIds.get(key)
  }

  /**
   * ⚠ mutates req!
   * Will inject authorization header computed from session
   *
   * @param {CTApiRequest} req
   * @returns {Promise<*>}
   */
  async exec(req) {
    let { accessToken } = this.context.session?.commerceTools || {}

    if ((!accessToken && this.context.auth.sessionId) || (accessToken && Date.now() > accessToken.expiresAt)) {
      if (this.context.session?.commerceTools?.refreshToken) {
        const response = await this.context.dataSources.ctAuth.refreshToken(
          this.context.session.commerceTools.refreshToken.value
        )
        this.context.session.commerceTools = CtSessionTransformer.fromResponse({
          ...response,
          refresh_token: this.context.session.commerceTools.refreshToken.value,
        })
        await this.context.session?.save?.()
        accessToken = this.context.session.commerceTools.accessToken
      } else {
        try {
          const response = await this.context.dataSources.ctAuth.anonymousLogin()
          this.context.session.commerceTools = CtSessionTransformer.fromResponse(response)
          await this.context.session?.save?.()
          accessToken = this.context.session.commerceTools.accessToken
        } catch (e) {
          const error = new GraphQLApolloError(e.message, 500)
          error.stack = e.stack
          error.addError('CTAuthError', 'token', e.message)
          throw error
        }
      }
    }
    if (accessToken) req.request.headers.Authorization = `Bearer ${accessToken.value}`

    return this._exec(req)
  }

  /**
   * ⚠ mutates req!
   * With great power...
   *
   * @param {CTApiRequest} req
   * @returns {Promise<*>}
   */
  async execAdmin(req) {
    const auth = await this.context.loaders.commerceTools.tokens.load(CONST.commerceTools.tokenType.admin)
    req.request.headers.Authorization = `Bearer ${auth.access_token}`

    return this._exec(req)
  }

  /**
   * ⚠ mutates req!
   * Using read-only access
   *
   * @param {CTApiRequest} req
   * @returns {Promise<*>}
   */
  async execRead(req) {
    const auth = await this.context.loaders.commerceTools.tokens.load(CONST.commerceTools.tokenType.read)
    req.request.headers.Authorization = `Bearer ${auth.access_token}`

    return this._exec(req)
  }

  /**
   * Executes the mutated request
   * @param {CTApiRequest} req
   * @returns {Promise<*>}
   */
  async _exec(req) {
    try {
      const response = await req.execute()
      return response
    } catch (e) {
      const err = new GraphQLApolloError(e.message)
      err.originalError = e
      err.statusCode = e.statusCode
      throw err
    }
  }
}

module.exports = CommerceTools
