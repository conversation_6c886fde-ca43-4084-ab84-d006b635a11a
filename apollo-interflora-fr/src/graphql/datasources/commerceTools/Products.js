const config = require('../../../config')
const CONST = require('../../../const')
const CommerceTools = require('./CommerceTools')

const CTFacetService = require('../../../helpers/commercetools/facet/FacetService')
const CTSortService = require('../../../helpers/commercetools/SortService')
const { isUUID, isCodeWithVariant } = require('../../../helpers/string')

const CTProductTransformer = require('../../transformers/commerceTools/Product')

/** @typedef {import('./_typedef')} */

class Products extends CommerceTools {
  /**
   * @returns {CTApi}
   */
  get products() {
    return this.api.products()
  }

  /**
   * @returns {CTApiProductProjection}
   */
  get productProjections() {
    return this.api.productProjections()
  }

  /**
   * Retrieve the list of product ids which can currently be sold on the website
   * i.e. are online and have a valid price
   *
   * Is mainly used as "where in" clause for plp filter
   *
   * ⚠ Commerce tools does not enforce time availability checks on scopedPrice
   * We cannot filter on `scopedPrice.validXX` either because scopedPrice is the
   * first price matching the price selection, and may very well have invalid
   * dates.
   *
   * @returns {Promise<string[]>}
   */
  async getActiveProductIds() {
    const priceChannel = await this.priceChannelId()

    const limit = 100
    let offset = 0
    let total

    const activeIds = new Set()

    do {
      const queryArgs = {
        'filter.query': [`variants.attributes.cms_published_on.key:"${this.storeKey}"`],
        limit,
        offset,
        priceChannel,
        priceCurrency: this.currency,
        sort: ['id asc'],
        storeProjection: this.storeKey,
      }

      if (config.site === CONST.site.fr) {
        // fr only: check stocks
        queryArgs['filter.query'].push(
          `variants.availability.channels.${priceChannel}.isOnStock:true`,
          `variants.availability.channels.${priceChannel}.availableQuantity:range (1 to *)`
        )
      }

      // products with valid price interval set
      let response = await this.productProjections.search().get({ queryArgs }).execute()

      total = response.body.total

      for (const product of await Promise.all(
        (response.body?.results ?? []).map((r) => this._filterProduct(r, undefined, false))
      )) {
        if (product) {
          activeIds.add(product.id)
        }
      }

      offset += limit
    } while (total && offset < total && offset < 2000)

    return [...activeIds]
  }

  /**
   * @param {string} id
   * @returns {Promise<CTCategory>}
   */
  async getCategory(id) {
    const categoryApi = this.api.categories()
    const req = await (isUUID(id) ? categoryApi.withId({ ID: id }) : categoryApi.withKey({ key: id })).get()
    const response = await req.execute()
    return response?.body
  }

  /**
   * @returns {Promise<CTProduct[]>}
   */
  async getGFSMarketingFees() {
    const response = await this.productProjections
      .search()
      .get({
        queryArgs: {
          'filter.query': [
            `productType.id:"${await this.gfsProductTypeId()}"`,
            'variants.scopedPrice.value.centAmount:range (0 to *)',
          ],
          limit: 500,
          priceChannel: await this.priceChannelId(),
          priceCurrency: this.currency,
          storeProjection: this.storeKey,
        },
      })
      .execute()

    return response.body?.results
  }

  /**
   * @param {string} code
   * @param {string} [channel] price channel key to filter on
   * @returns {Promise<CTProduct>}
   */
  async getProduct(code, channel) {
    if (isUUID(code)) {
      return this.getProductById(code, channel)
    }

    if (isCodeWithVariant(code)) {
      const [productCode] = code.split('#', 2)
      return this.getProductById(productCode, channel)
    }

    return this.getProductByKey(code, channel)
  }

  /**
   * @param {string} id
   * @param {string} [channel] price channel key to filter on
   * @returns {Promise<CTProduct>}
   */
  async getProductById(id, channel) {
    const req = this.productProjections.search().get({
      queryArgs: {
        'filter.query': [`id:"${id.replace(/"/g, '')}"`, `variants.attributes.cms_published_on.key:"${this.storeKey}"`],
        priceChannel: await this.priceChannelId(channel),
        priceCurrency: this.currency,
        storeProjection: this.storeKey,
      },
    })
    const response = await req.execute()

    return this._filterProduct(response?.body?.results?.[0], channel)
  }

  /**
   * @param {string} key
   * @param {string} [channel] price channel key to filter on
   * @returns {Promise<CTProduct>}
   */
  async getProductByKey(key, channel) {
    const response = await this.productProjections
      .search()
      .get({
        queryArgs: {
          'filter.query': [
            `key:"${key.replace(/"/g, '')}"`,
            `variants.attributes.cms_published_on.key:"${this.storeKey}"`,
          ],
          priceChannel: await this.priceChannelId(channel),
          priceCurrency: this.currency,
          storeProjection: this.storeKey,
        },
      })
      .execute()

    return this._filterProduct(response?.body?.results?.[0], channel)
  }

  /**
   * @param {string} key
   * @param {string} [channel] price channel key to filter on
   * @returns {Promise<CTProduct>}
   */
  async getProductByVariantKey(key, channel) {
    const response = await this.productProjections
      .search()
      .get({
        queryArgs: {
          'filter.query': [
            `variants.key:"${key.replace(/"/g, '')}"`,
            `variants.attributes.cms_published_on.key:"${this.storeKey}"`,
          ],
          priceChannel: await this.priceChannelId(channel),
          priceCurrency: this.currency,
          storeProjection: this.storeKey,
        },
      })
      .execute()

    return this._filterProduct(response?.body?.results?.[0], channel)
  }

  /**
   * Returns unpaginated list of { id, key } matching plp filters,
   * sorted by price
   *
   * @param {string} categoryId
   * @param {CTProductListOptions} options
   * @returns {Promise<{ key: string, id: string, price: number }[]>}
   */
  async getProductKeysPriceSorted(categoryId, options) {
    const queryArgs = await this._getProductListFilters(categoryId, options)

    const limit = 100
    let offset = 0
    let total

    /** @type {{ key: string, id: string, price: number }[]} */
    const keyprices = []

    do {
      // products with valid price interval set
      const response = await this.productProjections
        .search()
        .get({ queryArgs: { ...queryArgs, limit, offset, sort: 'id asc' } })
        .execute()

      for (const product of await Promise.all(
        (response.body?.results ?? []).map((r) => this._filterProduct(r, undefined, false))
      )) {
        if (!product) continue

        // @dirty - we need the gql product to fetch the defaultLowCostPrice
        const gqlProduct = CTProductTransformer.toGQL(product, this.context, {
          priceChannelKey: config.commerceTools.priceChannelKey,
        })
        if (!gqlProduct.defaultVariantLowCost) continue

        const variant = [product.masterVariant, ...product.variants].find(
          (v) => v.id === gqlProduct.defaultVariantLowCost.id
        )

        // ⚠ scopedPrice does not check date validity - do not use!
        let price = variant.price?.value.centAmount
        if (!price) {
          this.context.log.error(
            {
              key: product.key,
              variantId: variant.id,
            },
            `ct.getProductKeysPriceSorted - not valid price found --> skipping`
          )
          continue
        }
        const minQuantityAttr = variant.attributes.find((attr) => attr.name === 'min_quantity')
        if (minQuantityAttr?.value > 1) {
          price = price * minQuantityAttr.value
        }

        keyprices.push({
          id: product.id,
          key: product.key,
          price,
        })
      }

      total = response.body.total
      offset += limit
    } while (total && offset < total && offset < 2000)

    keyprices.sort((a, b) => a.price - b.price)
    return keyprices
  }

  /**
   * @param {string} categoryId
   * @param {CTProductListOptions} options
   * @returns {Promise<{facets: CTFacetResults, results: CTProductProjection[], total: number}>}
   */
  async getProductListByCategory(categoryId, options) {
    const { filters, limit, locale, offset } = options
    const facetService = CTFacetService.instance(locale)
    const sortService = CTSortService.instance(locale)

    const queryArgs = await this._getProductListFilters(categoryId, options)

    queryArgs.facet = facetService.getAllCtFields()
    // @tmp dirty @hack - remove me when catalog is ready to be filtered on price channel
    queryArgs.limit = config.site === CONST.it ? limit * 2 : limit
    queryArgs.offset = offset

    // sort - NB: sorting by price is handled in ProductList resolver
    {
      const orderHintSort = [
        `categoryOrderHints.${categoryId} asc`,
        ...(options.subcategoryOrderHints || []).filter((id) => isUUID(id)).map((id) => `categoryOrderHints.${id} asc`),
      ]
      queryArgs.sort = [...orderHintSort, 'createdAt desc']

      const sortFilter = filters.find(({ filterId }) => filterId === CONST.product.filterAsSort)
      if (sortFilter && sortFilter.values.length) {
        const sortValue = sortService.getSortValue(sortFilter.values[0])
        if (sortValue) {
          queryArgs.sort = [sortValue, 'createdAt desc']
        }
      }
    }

    // create a get request to let the sdk compute the url-encoded params for us
    const getReq = this.productProjections.search().get({
      queryArgs,
    })
    const url = new URL(`https://example.com${getReq.request.uri}`)
    const body = url.searchParams.toString()

    const response = await this.productProjections
      .search()
      .post({ body })
      .execute()
      .catch((e) => {
        // throw a new error to update the stacktrace
        throw new Error(e, { cause: e })
      })

    const { total, results, facets } = response?.body || {}

    // filtering is needed to exclude variants without price (i.e. when price expired)
    return {
      facets,
      results: (await Promise.all((results ?? []).map((product) => this._filterProduct(product))))
        .filter(Boolean)
        .slice(0, limit),
      total,
    }
  }

  /**
   * @param {{
   *  filters: string[],
   *  keys: string[],
   *  limit: number,
   *  offset: number
   * }} opts
   * @returns {Promise<CTProduct[]>}
   */
  async getProductsByKey(opts) {
    const { filters = [], keys = [], limit = 50, offset = 0 } = opts

    if (keys.length === 0) return []

    const queryArgs = {
      filter: filters,
      'filter.query': [
        `key:${keys.map((k) => `"${k.replace(/"/g, '')}"`).join(',')}`,
        `variants.attributes.cms_published_on.key:"${this.storeKey}"`,
      ],
      limit,
      offset,
      priceChannel: await this.priceChannelId(),
      priceCurrency: this.currency,
      storeProjection: this.storeKey,
    }

    // @tmp until we use the stock system in core model.
    if (config.site === 'fr') {
      queryArgs['filter.query'].push(
        `variants.availability.channels.${queryArgs.priceChannel}.isOnStock:true`,
        `variants.availability.channels.${queryArgs.priceChannel}.availableQuantity:range (1 to *)`
      )
    }

    // get request to let the sdk compute the url-encoded body
    const getReq = this.productProjections.search().get({
      queryArgs,
    })
    const url = new URL(`https://example.com${getReq.request.uri}`)
    const body = url.searchParams.toString()
    const req = this.productProjections.search().post({ body })
    const response = await req.execute()

    const docs = response.body?.results
    if (!docs) return

    const products = (await Promise.all(docs.map((doc) => this._filterProduct(doc)))).filter(Boolean)
    const keySort = {}
    for (const [index, key] of keys.entries()) {
      keySort[key] = index
    }
    products.sort((a, b) => keySort[a.key] - keySort[b.key])

    return products
  }

  /**
   * @param {string} typeId
   * @returns {Promise<CTProductType>}
   */
  async getProductType(typeId) {
    const req = await this.api.productTypes().withId({ ID: typeId }).get()
    const response = await this.execRead(req)
    return response?.body
  }

  /**
   * @param {string} key
   * @returns {Promise<CTProductType>}
   */
  async getProductTypeByKey(key) {
    const req = await this.api.productTypes().withKey({ key }).get()
    const response = await this.execRead(req)
    return response?.body
  }

  /**
   * removes variants with no price
   *
   * @param {CTProduct} product
   * @param {string} [channel] price channel key to filter on
   * @param {boolean} [keepIfExpiredPrice]
   * @returns {Promise<CTProduct|null>}
   */
  async _filterProduct(product, channel, keepIfExpiredPrice = config.availability.invalidPriceIsOutOfStock) {
    if (!product) return null

    const { masterVariant, variants = [], ...rest } = product

    if (keepIfExpiredPrice) {
      this.nullifyInvalidPrices([masterVariant, ...variants])
    }

    const filtered = await this._filterVariants([masterVariant, ...variants], channel, keepIfExpiredPrice)
    if (filtered.length === 0) {
      return null
    }
    const filteredVariants = filtered.filter((v) => v.id !== masterVariant.id)
    let filteredMasterVariant = filtered.find((v) => v.id === masterVariant.id)

    if (!filteredMasterVariant) {
      this.context.log.warn(`no valid price found for master variant of ${product.key}`)

      if (filteredVariants.length === 0) {
        this.context.log.warn(`no valid price found for product ${product.key}`)
        return null
      }

      if (this.context.appConfig.defaultZone) {
        const newMasterIndex = filteredVariants.findIndex((variant) => {
          const zone = variant.sku.split('-').pop()
          return zone === this.context.appConfig.defaultZone
        })
        if (newMasterIndex >= 0) {
          filteredMasterVariant = filteredVariants.splice(newMasterIndex, 1)[0]
        } else {
          this.context.log.warn(`no valid zone found on variant for product ${product.key}`)
          filteredMasterVariant = filteredVariants.shift()
        }
      } else {
        filteredMasterVariant = filteredVariants.shift()
      }
    }

    return {
      ...rest,
      masterVariant: filteredMasterVariant,
      variants: filteredVariants,
    }
  }

  /**
   * @param {CTProductVariant[]} variants
   */
  nullifyInvalidPrices(variants) {
    if (!config.availability.invalidPriceIsOutOfStock) {
      return
    }

    for (const variant of variants) {
      variant.prices = variant.prices.map((price) => {
        if (
          (price.validFrom && new Date(price.validFrom) > new Date()) ||
          (price.validUntil && new Date(price.validUntil) < new Date())
        ) {
          price.value = null
        }
        return price
      })
    }
  }

  /**
   * @param {CTProductVariant[]} variants
   * @param {string} [channel] price channel key to filter on
   * @param {boolean} [keepIfExpiredPrice]
   * @returns {Promise<CTProductVariant[]>}
   */
  async _filterVariants(variants, channel, keepIfExpiredPrice = config.availability.invalidPriceIsOutOfStock) {
    const priceChannelId = await this.priceChannelId(channel)

    const expiredPriceVariants = new Set()

    let filtered = variants.filter((variant) => {
      // free price variant are not used by octopus for not at least, pfs might use it
      if (
        variant.attributes.some((attr) => attr.name === 'size' && attr.value?.key === CONST.product.sizes.free_price)
      ) {
        return false
      }

      let isExpiredPrice = false

      for (const price of variant.prices) {
        if (price.channel?.id !== priceChannelId) {
          continue
        }
        if (price.validFrom && new Date(price.validFrom) > new Date()) {
          isExpiredPrice = true
        } else if (price.validUntil && new Date(price.validUntil) < new Date()) {
          isExpiredPrice = true
        } else {
          // price is active and valid
          return true
        }
      }

      if (!isExpiredPrice) {
        // no past, present or future valid price => exclude
        return false
      }

      if (keepIfExpiredPrice) {
        // not active but past or future valid price => keep it for now
        expiredPriceVariants.add(variant)
        return true
      }

      return false
    })

    if (expiredPriceVariants.size !== filtered.length) {
      // we have a mix of active and expired prices => exclude expired ones
      filtered = filtered.filter((variant) => !expiredPriceVariants.has(variant))
    }

    return filtered
  }

  /**
   * @param {string} categoryId
   * @param {CTProductListOptions} options
   * @returns {Promise<{
   *  filter: string[],
   *  'filter.query': string[],
   *  priceChannel: string,
   *  priceCurrency: string,
   *  storeProjection: string
   * }>}
   */
  async _getProductListFilters(categoryId, options) {
    const { filters, locale, search } = options
    const facetService = CTFacetService.instance(locale)

    const priceChannel = await this.priceChannelId()

    if (!isUUID(categoryId)) {
      throw new Error('Category by key does not support subtree search')
    }

    const filterQuery = [
      `categories.id: subtree("${categoryId}")`,
      `variants.attributes.cms_published_on.key:"${this.storeKey}"`,
    ]
    if (options.siteId === CONST.siteIds.catalog) {
      filterQuery.push(...config.apps.catalog.filters.product)
    }
    if (config.commerceTools.productList.extraFilters) {
      for (const filter of filters ?? []) {
        const extraFilters = config.commerceTools.productList.extraFilters[filter.filterId]
        if (extraFilters) {
          filterQuery.push(...extraFilters)
        }
      }
    }
    // @tmp dirty @hack - remove me when catalog is ready to be filtered on price channel
    if (config.site !== CONST.site.it) filterQuery.push('variants.scopedPrice.value.centAmount:range (0 to *)')

    // @tmp until we use the stock system in core model.
    if (config.site === CONST.site.fr) {
      filterQuery.push(
        `variants.availability.channels.${priceChannel}.isOnStock:true`,
        `variants.availability.channels.${priceChannel}.availableQuantity:range (1 to *)`
      )

      // FR: we want only sellable products on plp, while being able to display
      // out of stock / no valid price products on PDP for SEO.
      // Filter on sellable products only
      // @todo: check if can/needs to be extended to other countries
      const activeProductIds = await this.activeProductIds()
      filterQuery.push(`id:${activeProductIds.map((id) => `"${id}"`).join(', ')}`)
    }

    const queryArgs = {
      filter: [...facetService.getCtFilters(filters)],
      'filter.query': filterQuery,
      priceChannel,
      priceCurrency: this.currency,
      storeProjection: this.storeKey,
    }

    const dateFilter = filters.find(({ filterId }) => filterId === config.filters.date.id)
    if (dateFilter && dateFilter.values.length) {
      // @todo temporary solution - if the selected date is today filter out the products with carrier delivery mode
      if (new Date().toISOString().startsWith(dateFilter.values[0])) {
        queryArgs.filter.push(`variants.attributes.delivery_type.key:"FLORIST"`)
      }
    }

    if (search) {
      queryArgs[`text.${locale}`] = search
    }

    return queryArgs
  }
}

module.exports = Products
