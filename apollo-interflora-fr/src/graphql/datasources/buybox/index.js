const config = require('../../../config')
const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

/** @typedef {import('./_typedef')} */
/** @typedef {import('@apollo/utils.fetcher').FetcherResponse} */

class BuyboxDataSource extends RESTDataSource {
  constructor() {
    super(...arguments)
    this.baseURL = config.buybox.baseUrl
  }

  get frontBaseUrl() {
    return config.apps[this.context.siteId].baseUrl
  }

  /**
   * @param {string} [cartId]
   * @returns {string}
   */
  getReturnUrl(cartId) {
    let returnUrl = `${this.frontBaseUrl}${config.buybox.returnPath}`
    if (cartId) returnUrl += `?cartId=${cartId}`
    return returnUrl
  }

  /**
   * @param {string} [cartId]
   * @returns {string}
   */
  getCancelUrl(cartId) {
    let cancelUrl = `${this.frontBaseUrl}${config.buybox.cancelPath}`
    if (cartId) cancelUrl += `?cartId=${cartId}`
    return cancelUrl
  }

  get currency() {
    return config.apps[this.context.siteId].currency
  }

  getRequestVariables() {
    const variables = new URLSearchParams()
    variables.set('USER', config.tokens.buyboxUser)
    variables.set('PWD', config.tokens.buyboxPassword)
    variables.set('SIGNATURE', config.tokens.buyboxApiSignature)
    variables.set('VERSION', config.buybox.apiVersion)

    return variables
  }

  /**
   * @param {BuyBoxGetCheckoutParams} requestParams
   * @returns {Promise<BuyBoxGetCheckoutResponse>}
   */
  async setExpressCheckout(requestParams) {
    const variables = this.getRequestVariables()
    const { action, amount, cardCode, cardPin, custom, description, cartId, solutionType } = requestParams

    variables.set('METHOD', 'SetExpressCheckout')
    variables.set('RETURNURL', this.getReturnUrl(cartId))
    variables.set('CANCELURL', this.getCancelUrl(cartId))
    variables.set('AMT', `${amount}`)
    variables.set('CURRENCYCODE', this.currency)
    variables.set('INVNUM', cartId)

    if (description) {
      variables.set('DESC', description)
    }
    if (action) {
      variables.set('PAYMENTACTION', action)
    }
    if (cardCode) {
      variables.set('GIFTCARD_CODE', cardCode)
    }
    if (cardPin) {
      variables.set('GIFTCARD_PIN', cardPin)
    }
    if (custom) {
      variables.set('CUSTOM', custom)
    }
    if (solutionType) {
      variables.set('SOLUTIONTYPE', solutionType)
    }

    return this.post(this.baseURL, { body: variables })
  }

  /**
   * @param {BuyBoxDoPaymentParams} requestParams
   * @returns {Promise<BuyBoxDoPaymentResponse>}
   */
  async doExpressCheckoutPayment(requestParams) {
    const variables = this.getRequestVariables()
    const { action = 'Sale', amount, custom, description, token, payerId } = requestParams

    variables.set('METHOD', 'DoExpressCheckoutPayment')
    variables.set('TOKEN', token)
    variables.set('PAYERID', payerId)
    variables.set('PAYMENTACTION', action)
    variables.set('AMT', `${amount}`)
    variables.set('CURRENCYCODE', this.currency)

    if (description) {
      variables.set('DESC', description)
    }
    if (custom) {
      variables.set('CUSTOM', custom)
    }

    return this.post(this.baseURL, { body: variables })
  }

  /**
   * @param {string} transactionId
   * @returns {Promise<BuyboxGetTransactionDetailsResponse>}
   */
  async getTransactionDetails(transactionId) {
    const variables = this.getRequestVariables()

    variables.set('METHOD', 'GetTransactionDetails')
    variables.set('TRANSACTIONID', transactionId)

    return this.post(this.baseURL, { body: variables })
  }

  /**
   * Override the HTTP response to parse it into an appropriate format
   * @param {FetcherResponse} response
   * @returns {Promise<object | string>}
   */
  async parseBody(response) {
    const result = await super.parseBody(response)
    const variables = new URLSearchParams(result)

    const parsedResponse = Object.fromEntries(variables.entries())

    if (parsedResponse.ACK !== 'Success') {
      const code = parsedResponse.L_ERRORCODE0
      const message = parsedResponse.L_LONGMESSAGE0 ?? parsedResponse.L_SHORTMESSAGE0 ?? 'Unknown buybox error'

      const error = new Error(`[${code}] ${message}`)
      apm.captureError(error, { custom: error })
      throw error
    }

    return parsedResponse
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   */
  willSendRequest(_path, request) {
    request.headers['content-type'] = 'application/x-www-form-urlencoded'
    apm.currentTransaction?.addLabels({
      'buybox-api': this.baseURL,
      'buybox-cartId': request.body.get('INVNUM'),
      'buybox-solutiontype': request.body.get('SOLUTIONTYPE'),
    })
  }

  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    return super.didEncounterError(...arguments)
  }
}

module.exports = {
  buybox: BuyboxDataSource,
}
