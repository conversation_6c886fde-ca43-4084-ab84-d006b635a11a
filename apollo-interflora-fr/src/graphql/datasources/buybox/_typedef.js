/**
 * @typedef {{
 *   amount: number,
 *   custom?: string,
 *   description?: string,
 *   action?: BuyBoxAction,
 *   cardCode?: string,
 *   cardPin?: string,
 *   cartId?: string,
 *   solutionType?: string,
 * }} BuyBoxGetCheckoutParams
 */

/**
 * @typedef {{
 *   TOKEN: string
 * }} BuyBoxGetCheckoutResponse
 */

/**
 * @typedef {{
 *   token: string,
 *   payerId: string,
 *   action?: BuyBoxAction,
 *   amount: number,
 *   custom?: string,
 *   description?: string,
 * }} BuyBoxDoPaymentParams
 */

/**
 * @typedef {{
 *   TOKEN: string,
 *   TRANSACTIONID: string,
 *   ORDERTIME: string,
 *   AMT: string,
 *   CURRENCYCODE: string,
 *   TAXAMT: string,
 *   PAYMENTSTATUS: BuyBoxPaymentStatus,
 *   REASONCODE: 'None',
 * }} BuyBoxDoPaymentResponse
 */

/**
 * @typedef {'Sale'|'Authorization'|'Order'} BuyBoxAction
 */

/**
 * @typedef {'Completed'|'Pending'|'Failed'} BuyBoxPaymentStatus
 */

/**
 * @typedef {'Authorization'|'Order'|'None'} BuyBoxPendingReason
 */

/**
 * @typedef {{
 *   TRANSACTIONID: string
 *   PARENTTRANSACTIONID: string
 *   TRANSACTIONTYPE: string
 *   PAYMENTTYPE: string
 *   AMT: string
 *   PAYMENTSTATUS: string
 *   PENDINGREASON: string
 *   REASONCODE: string
 * }} BuyboxGetTransactionDetailsResponse
 */
