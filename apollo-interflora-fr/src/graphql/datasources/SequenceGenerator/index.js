const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import('apollo-datasource-rest').RequestOptions} RequestOptions */

/**
 * @extends {RESTDataSource<GraphQLContext>}
 */
class SequenceGeneratorDatasource extends RESTDataSource {
  constructor() {
    super({ timeout: 10000 })
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {string | undefined} prefix Used to add a prefix to the generated number if necessary.
   * @returns {Promise<string>}
   */
  next(prefix = undefined) {
    return this.get('/GenerateNextSequence', { params: prefix ? new URLSearchParams({ prefix }) : undefined })
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.sequenceGenerator.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.sequenceGenerator.headers) {
      for (const [name, value] of Object.entries(config.sequenceGenerator.headers)) {
        request.headers[name] = value
      }
    }
  }
}

module.exports = {
  sequenceGenerator: SequenceGeneratorDatasource,
}
