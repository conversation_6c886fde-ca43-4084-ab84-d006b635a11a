const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import('./_typedef')} */

const { getCountryInfo } = require('../../../helpers/countries')

/**
 * @extends {RESTDataSource<GraphQLContext>}
 */
class AvailabilityDatasource extends RESTDataSource {
  constructor() {
    super({ timeout: config.availability.timeout })
  }

  /**
   * @override
   * @param {Error} error
   * @param {RequestOptions} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {{
   *  codes: string[],
   *  country: string,
   *  date: Date,
   *  province: string,
   * }} opts
   * @returns {Promise<Number[]>}
   */
  async getMoments(opts) {
    const codes = this.filterProductsSubjectToAvailability(opts.codes)
    const { Moments } = await this.post('GetMoments', {
      body: {
        CountryCode: opts.country || this.context.countryId,
        DeliveryDate: (opts.date || new Date()).toISOString(),
        Province: opts.province,
        Skus: codes,
      },
    })

    return Moments
  }

  /**
   * @param {{cartId: string, postalCode: string}}opts
   * @return {Promise<unknown>}
   */
  async validateRegionCart(opts) {
    return this.post('ValidateRegionCart', {
      body: {
        CartId: opts.cartId,
        PostalCode: opts.postalCode,
      },
    })
  }

  /**
   * @param {{
   *  city: string,
   *  codes: string[],
   *  country: string,
   *  date: Date,
   *  floristsIds: string,
   *  hasRibbon: boolean,
   *  isFuneral: boolean,
   *  latitude: Number,
   *  longitude: Number,
   *  moment: Number,
   *  orderId: string,
   *  postalCode: string,
   *  province: string,
   *  street: string,
   *  productVariantSKUs: string[]
   * }} opts
   * @returns {Promise<CheckAvailabilityResponse>}
   */
  checkAvailability(opts) {
    if (opts.codes.length === 0) {
      this.context.log.error(opts, 'checkAvailability: Missing products codes')
      return { Result: false }
    }
    if (!opts.date) {
      this.context.log.error(opts, 'checkAvailability: Missing delivery date')
      return { Result: false }
    }
    if (!opts.postalCode && !opts.province) {
      this.context.log.error(opts, 'checkAvailability: Missing postal code or province')
      return { Result: false }
    }

    const codes = this.filterProductsSubjectToAvailability(opts.codes)
    const sku = codes[0]
    const skus = codes.slice(1)

    let skusVariants
    if (opts.productVariantSKUs?.length) {
      skusVariants = this.filterProductsSubjectToAvailability(opts.productVariantSKUs)
    }
    /**
     * BusinessUnitId and FloristId are only used for Sweden B2B users
     */
    const body = {
      BusinessUnitId: this.context?.session?.external?.user?.companyNumber || null,
      City: opts.city,
      CountryCode: opts.country || this.context._countryId,
      DeliveryDate: opts.date ? new Date(opts.date).toISOString() : undefined,
      FloristId: opts?.floristsIds || [],
      HasRibbon: Boolean(opts.hasRibbon),
      IsFuneral: Boolean(opts.isFuneral),
      Latitude: opts.latitude,
      Longitude: opts.longitude,
      Moment: opts.moment ? Number.parseInt(opts.moment) : undefined,
      OrderId: opts.orderId,
      PostalCode: opts.postalCode,
      ProductSKU: sku,
      ProductSKUs: skus,
      ProductVariantSKUs: skusVariants,
      Province: opts.province,
      Street: opts.street,
    }

    return this.post('CheckAvailability', { body })
  }

  /**
   * @param {{
   *  code: string,
   *  country: string,
   *  language: string,
   *  search: string
   * }} opts
   * @returns {Promise<{
   *  Name: string,
   *  PostalCode: string,
   *  Province: string
   * }[]>}
   */
  async getTowns(opts) {
    const search = opts.search || ''
    const searches = [search]
    if (/\s+/.test(search.trim())) {
      const newSearch = search.replace(/\s+/g, '')
      if (newSearch.length >= 3) {
        searches.push(newSearch)
      }
    }

    for (const search of searches) {
      const { Places } = await this.post('PlaceAutoComplete', {
        body: {
          CountryCode: opts.country,
          Language: opts.language || this.context.language,
          Sku: opts.code,
          Text: search,
        },
      })

      if (Places.length) {
        return Places
      }
    }

    return []
  }

  /**
   * @param {{
   *  code: string,
   *  city: string,
   *  country: string,
   *  days: Number,
   *  postalCode: string,
   *  province: string,
   *  productVariantSku: string
   * }} opts
   * @returns {Promise<string[]>}
   */
  async getUnavailabilities(opts) {
    if (!opts.code) {
      this.context.log.error(opts, 'getUnavailabilities: Missing product code')
      return []
    }
    if (!opts.postalCode && !opts.province) {
      this.context.log.error(opts, 'getUnavailabilities: Missing postal code or province')
      return []
    }

    const { city, code, country, days, postalCode, productVariantSku } = opts
    if (this.filterProductsSubjectToAvailability([code]).length === 0) {
      return []
    }

    let { province } = opts

    if (!province && country && postalCode) {
      const countryInfo = getCountryInfo(country)
      province = countryInfo.postalCode2Province(postalCode)?.name
    }

    const { UnvailableDays } = await this.post('GetUnavailableDays', {
      body: {
        City: city,
        CountryCode: country,
        DaysNumber: days || config.availability.daysToCheck,
        PostalCode: postalCode,
        ProductVariantSku: productVariantSku,
        Province: province,
        Sku: code,
      },
    })

    return UnvailableDays
  }

  /**
   * @param {{
   *  country: string,
   *  days: Number
   * }} opts
   * @returns {Promise<string[]>}
   */
  async getUndeliverableDays(opts = {}) {
    const { UndeliverableDays } = await this.post('GetUndeliverableDays', {
      body: {
        CountryCode: opts.country || this.context.countryId,
        DaysNumber: opts.days || config.availability.daysToCheck,
      },
    })

    return UndeliverableDays
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.availability.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.availability.headers) {
      for (const [name, value] of Object.entries(config.availability.headers)) {
        request.headers[name] = value
      }
    }
  }

  /**
   * Returns an array of products that are subject to an availability check
   *
   * @param {string[]} serviceCodes
   * @return {string[]}
   */
  filterProductsSubjectToAvailability(serviceCodes) {
    return serviceCodes.filter((c) => {
      return ![config.products.tombDelivery.key, config.products.interfloraplus.key].includes(c)
    })
  }
}

module.exports = {
  availability: AvailabilityDatasource,
}
