/**
 @typedef {Object} Fee
 @property {string} Name - The name of the fee.
 @property {number} Price - The price of the fee.
 @property {number} Tax - The applied tax.
 */

/**.
 @typedef {Object} Texts
 @property {string} Name - The name of the text.
 @property {string} ShortInfo - A short description.
 @property {string} LongtInfo - A detailed description.
 */

/**
 * Response object for checking availability.
 * @typedef {Object} CheckAvailabilityResponse
 * @property {number} Price - The main price.
 * @property {number} Compensation - The compensation amount.
 * @property {boolean} SelectedByDistributionQuota - Indicates if selected by distribution quota.
 * @property {Texts} Texts - The descriptive texts.
 * @property {Fee[]} Fees - An array of fees.
 */
