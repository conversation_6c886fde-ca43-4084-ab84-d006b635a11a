const crypto = require('crypto')
const apm = require('elastic-apm-node')
const debug = require('debug')('itf:frisbii')

const config = require('../../../config/index.js')
const FrisbiiBase = require('./Base.js')

/** @typedef {import('../_typedef.js')} */

/**
 * @typedef {{
 *  id: string
 *  event_id: string
 *  event_type: 'invoice_authorized' | 'invoice_settled'
 *  timestamp: string
 *  signature: string
 *  customer: string
 *  payment_method: string
 *  payment_method_reference: string
 *  subscription: string
 *  invoice: string
 *  transaction: string
 *  credit_note: string
 *  credit: string
 * }} FrisbiiWebhookContent
 */

class Frisbii extends FrisbiiBase {
  constructor() {
    super()
    this.baseURL = config.frisbii.baseUrl
  }

  /**
   * Get the charge status from Frisbii (payment completed, rejected...)
   * @param {string} id
   * @returns {Promise<FrisbiiInvoice>}
   */
  async getInvoice(id) {
    if (!id) {
      throw new Error('No invoice id given')
    }
    try {
      return this.get(`v1/invoice/${id}`)
    } catch (e) {
      console.error(e)
      return null
    }
  }

  /**
   * Get the charge status from Frisbii (payment completed, rejected...)
   * @param {{ invoiceId: string }} opts
   * @returns {Promise<FrisbiiCharge>}
   */
  async getCharge(opts) {
    if (!opts?.invoiceId) {
      throw new Error('No invoiceId given')
    }

    return this.get(`v1/charge/${opts.invoiceId}`)
  }

  /**
   * Get the transaction from Frisbii, useful to get more information about the specific payment method used.
   * @param {{ invoiceId: string, transactionId: string }} opts
   * @returns {Promise<FrisbiiTransaction>}
   */
  async getTransaction(opts) {
    if (!opts?.invoiceId) {
      throw new Error('No invoiceId given')
    }

    return this.get(`v1/invoice/${opts.invoiceId}/transaction/${opts.transactionId}`)
  }

  /**
   * Get the metadata of the invoice from the create charge in checkout
   * @param {{ invoiceId: string }} opts
   * @returns {Promise<unknown>} - The metadata - such as the orderId
   */
  async getMetadata(opts) {
    if (!opts?.invoiceId) {
      throw new Error('No invoiceId given')
    }

    return this.get(`v1/invoice/${opts.invoiceId}/metadata`)
  }

  /**
   * @param {{ invoiceId: string, transactionId: string }} opts
   */
  async getTransactionDetails(opts) {
    return this.get(`v1/invoice/${opts.invoiceId}/transaction/${opts.transactionId}/details`)
  }

  static fastifyPlugin(fastify, _options, done) {
    const conf = config.apps[config.apps.defaultSiteId]

    /**
     * Verify the signature of the webhook
     * @param {FrisbiiWebhookContent} webhook
     * @returns {boolean}
     * @see https://optimize-docs.billwerk.com/reference/intro_webhooks#webhook-content
     */
    function verifySignature(webhook) {
      if (!webhook || !webhook?.signature) return false
      if (!config.tokens.billwerkSignatureSecret) return false

      const payload = webhook.timestamp + webhook.id
      const hmac = crypto.createHmac('sha256', config.tokens.billwerkSignatureSecret)
      const digest = hmac.update(payload).digest('hex')

      return digest === webhook.signature
    }

    //This is an endpoint for async order transformation (for example if client is leaving just after 3ds)
    fastify.post(`/graphql${config.frisbii.checkout.urlPath}/webhook`, async (request, reply) => {
      /** @type {FrisbiiWebhookContent} */
      const webhookContent = request.body

      if (!['invoice_settled', 'invoice_authorized'].includes(webhookContent.event_type)) {
        //No other webhook events expected for now
        return reply.status(400).send('Not expected')
      }

      if (!verifySignature(webhookContent)) {
        console.error('Payload tempered, signature can not be verified')
        throw new Error('Payload tempered, signature can not be verified')
      }

      // let 3s to prefer human confirmation
      setTimeout(() => {
        // call graphql createOrder - do not wait for response
        fastify
          .inject({
            body: JSON.stringify({
              query: `
              mutation FrisbiiS2S($context: InputContext!, $query: String!) {
                confirmRedirectOrder(
                  context: $context,
                  urlPath: "${config.frisbii.checkout.urlPath}",
                  urlQuery: $query
                ) {
                  statusCode
                  statusMessage
                }
              }
            `,
              variables: {
                context: {
                  countryId: conf.country,
                  language: conf.language,
                  siteId: config.apps.defaultSiteId,
                },
                query: `invoice=${webhookContent.invoice}`,
              },
            }),
            headers: {
              'Content-Type': 'application/json',
            },
            method: 'POST',
            url: '/graphql',
          })
          .then((response) => {
            debug('webhook confirmRedirectORder: %O', response)
            const { statusCode, statusMessage } = response
            fastify.log.info('frisbii s2s: %O', {
              json: response.json(),
              query: `invoice=${webhookContent.invoice}`,
              statusCode,
              statusMessage,
            })
          })
          .catch((e) => {
            apm.captureError(e, { custom: e })
            fastify.log.error('[frisbii webhook] error: %O', e)
          })
      }, 3000)
      debug('webhook payload: %O', request.body)

      return reply.status(200).send('Success')
    })
    done()
  }
}

module.exports = Frisbii
