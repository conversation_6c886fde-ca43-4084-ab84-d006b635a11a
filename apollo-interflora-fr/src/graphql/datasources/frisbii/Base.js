const config = require('../../../config')
const { RESTDataSource } = require('../RESTDataSource')

class FrisbiiBase extends RESTDataSource {
  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   */
  willSendRequest(_path, request) {
    const credentials = `${config.tokens.billwerkKey}:`
    const basicAuthHeader = Buffer.from(credentials).toString('base64')

    request.headers.authorization = `Basic ${basicAuthHeader}`
    request.headers.accept = 'application/json'
    request.headers['content-type'] = 'application/json'
  }
}

module.exports = FrisbiiBase
