const config = require('../../../config/index.js')
const FrisbiiBase = require('./Base.js')

/** @typedef {import('./_typedef.js')} */

class FrisbiiRefundDataSource extends FrisbiiBase {
  constructor() {
    super()
    this.baseURL = config.frisbii.baseUrl
  }

  /**
   *
   * @param {string} orderId
   * @param {number} amount
   * @returns {Promise<FrisbiiRefundResponse>}
   */
  async createRefund(orderId, amount = null) {
    /** @type {FrisbiiRefundPayload} */
    const body = {
      invoice: orderId,
    }

    if (amount) {
      body.amount = amount
    }

    return this.post('v1/refund', { body })
  }

  /**
   *
   * @param {string} refundId
   * @returns {Promise<Object>}
   */
  async getRefund(refundId) {
    return this.get(`v1/refund/${refundId}`)
  }
}

module.exports = FrisbiiRefundDataSource
