const config = require('../../../config/index.js')
const CONST = require('../../../const.js')
const FrisbiiBase = require('./Base.js')

/** @typedef {import('./_typedef.js')} */

// Used to map queries to fris<PERSON>i's api payment method parameter
const paymentModeToFrisbiiPaymentMode = {
  [CONST.payment.paymentMode.APPLEPAY]: CONST.payment.frisbii.paymentTypes.applepay,
  [CONST.payment.paymentMode.CARD]: CONST.payment.frisbii.paymentTypes.card,
  [CONST.payment.paymentMode.GOOGLEPAY]: CONST.payment.frisbii.paymentTypes.googlepay,
  [CONST.payment.paymentMode.MOBILE_PAY]: CONST.payment.frisbii.paymentTypes.mobile_pay,
}

class FrisbiiCheckout extends FrisbiiBase {
  constructor() {
    super()
    this.baseURL = config.frisbii.checkout.baseUrl
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @param {"CARD"|"APPLEPAY"|"GOOGLEPAY"|undefined} mode
   * @returns {Promise<Object>}
   */
  async createChargeSession(cart, context, mode) {
    const ctTotalToPay = cart.taxedPrice?.totalGross ?? cart.totalPrice

    const handle = this._computeUniqueHandle(cart)

    const body = {
      accept_url: `${context.appConfig.baseUrl}${config.frisbii.checkout.redirections.cases.success}${cart.id}`,
      cancel_url: `${context.appConfig.baseUrl}${config.frisbii.checkout.redirections.cases.failure}`,
      order: {
        billing_address: this._computeBillingAddressObject(cart, context),
        currency: ctTotalToPay.currencyCode,
        customer: this._computeCustomerObject(cart, context),
        handle,
        metadata: {
          orderId: cart.id,
        },
      },
      settle: config.frisbii.checkout.options.settle ?? true, // Captures the payment instantly if true
      ttl: config.frisbii.checkout.options.ttl ?? 'P3D', // 3 days, WARNING: increasing the payment session TTL increases the risks of collision for countries with small handle (SE)
    }

    if (mode) {
      const restrictedPaymentMethod = paymentModeToFrisbiiPaymentMode[mode]
      if (restrictedPaymentMethod) {
        body['payment_methods'] = [paymentModeToFrisbiiPaymentMode[mode]]
      } // else allow to pay with all payment methods available in Frisbii config (not all works in modal mode tho)
    }

    if (config.frisbii.checkout.locale) {
      body.locale = config.frisbii.checkout.locale
    }

    if (config.frisbii.checkout.options.detailedInvoice) {
      // Generating detailed order lines increases the chances of weird errors if we have poor contrib/weird data model changes.
      body.order['order_lines'] = await this._computeOrderLines(cart, context)
    } else {
      // If we don't use order lines we need to set an amount.
      body.order.amount = ctTotalToPay.centAmount
    }

    return this.post('v1/session/charge', { body })
  }

  /**
   *
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {FrisbiiSessionAddress}
   */
  _computeBillingAddressObject(cart, context) {
    const city = cart.billingAddress?.city?.replace(/\(?\d+\)?/g, '').trim() || ''
    return {
      address: cart.billingAddress.streetName,
      city,
      country: context.appConfig.country,
      email: cart.customerEmail,
      first_name: cart.billingAddress.firstName,
      last_name: cart.billingAddress.lastName,
      phone: cart.billingAddress.phone ?? cart.billingAddress.mobile,
      postal_code: cart.billingAddress.postalCode,
    }
  }

  /**
   *
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {FrisbiiSessionCustomer}
   */
  _computeCustomerObject(cart, context) {
    const userId = context.session?.user?.id ?? null
    /** @type {FrisbiiSessionCustomer} */
    let customer
    if (config.site === CONST.site.se) {
      // SE uses a specific external session system.
      customer = {
        email: context.session?.user?.email ?? cart.customerEmail,
        first_name: context.session?.external?.user?.firstName ?? cart.billingAddress.firstName ?? '',
        last_name: context.session?.external?.user?.lastName ?? cart.billingAddress.lastName ?? '',
        phone: context.session?.external?.user?.phone
          ? `${context.session?.external?.user?.phone?.prefix}${context.session?.external?.user?.phone?.number}`
          : cart.billingAddress?.phone ?? cart.billingAddress?.mobile ?? null,
      }
    } else {
      customer = {
        email: cart.customerEmail,
        first_name: cart.billingAddress.firstName ?? '',
        last_name: cart.billingAddress.lastName ?? '',
        phone: context.session?.external?.user?.phone
          ? `${context.session?.external?.user?.phone?.prefix}${context.session?.external?.user?.phone?.number}`
          : cart.billingAddress?.phone ?? cart.billingAddress?.mobile ?? null,
      }
    }

    if (userId) {
      customer.handle = userId
    } else {
      customer.generate_handle = true
    }

    return customer
  }

  /**
   *
   * @param {CTCart} cart
   * @returns {string}
   */
  _computeUniqueHandle(cart) {
    if (config.site === CONST.site.se) {
      // Some providers in SE needs the frisbii handle to be 10 chars long maximum, it's only 2025 ¯\_(ツ)_/¯
      return cart.id.replace(/-/g, '').slice(-10)
    } else {
      return cart.id
    }
  }

  /**
   *
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {FrisbiiSessionOrderLine[]}
   */
  async _computeOrderLines(cart, context) {
    const payments =
      (
        await context.loaders.commerceTools.payments.loadMany(cart.paymentInfo?.payments?.map((p) => p.id) ?? [])
      )?.filter(
        // Reward are loyalty points already deduced from the payment.
        (payment) =>
          payment.paymentMethodInfo.method !== CONST.payment.paymentMode.REWARD &&
          payment.paymentStatus.interfaceCode === CONST.commerceTools.paymentStates.paid
      ) ?? []
    const orderLines = cart.lineItems.map((item) => ({
      amount: item.discountedPrice?.value.centAmount ?? item.price.value.centAmount, // Use the discounted price if the item is discounted
      amount_incl_vat: true,
      ordertext: item.name[context.language] || item.name.en || Object.values(item.name)[0], // Using en or first available name if no translation available
      quantity: item.quantity,
      vat: item.taxRate.amount,
    }))

    orderLines.push({
      amount: cart.shippingInfo.discountedPrice?.value.centAmount ?? cart.shippingInfo.price.centAmount, // Use the discounted shipping if the shipping is discounted
      amount_incl_vat: true,
      ordertext: 'Delivery',
      quantity: 1,
      vat: cart.shippingInfo.taxRate.amount,
    })

    for (const customLine of cart.customLineItems || []) {
      // Handle bonus and promotions
      if ([CONST.reward.types.bonus, CONST.reward.types.promotion].includes(customLine?.custom?.fields?.rewardType)) {
        orderLines.push({
          amount: customLine.totalPrice.centAmount,
          amount_incl_vat: true,
          ordertext: customLine.name[context.language] || 'Bonus check',
          vat: 0,
        })
      } else if (context.appConfig.country !== cart.shippingAddress.country) {
        // In case of international orders, international products are custom line items.
        orderLines.push({
          amount: customLine.discountedPrice?.value.centAmount ?? customLine.money.centAmount,
          amount_incl_vat: customLine.taxRate.amount > 0,
          ordertext: customLine.name[context.language] || customLine.name.en || Object.values(customLine.name)[0], // Using en or first available name if no translation available
          quantity: customLine.quantity ?? 1,
          vat: customLine.taxRate.amount,
        })
      }
    }

    // Substracting giftcards payments amount
    for (const payment of payments) {
      orderLines.push({
        amount: payment.amountPlanned.centAmount * -1,
        amount_incl_vat: true,
        ordertext: payment.paymentMethodInfo.name[context.language] || 'Partial payment',
        vat: 0,
      })
    }

    return orderLines
  }
}

module.exports = FrisbiiCheckout
