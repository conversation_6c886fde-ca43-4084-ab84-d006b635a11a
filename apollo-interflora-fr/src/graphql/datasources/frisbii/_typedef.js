/**
 * @see https://optimize-docs.billwerk.com/reference/getcharge
 *
 * The FrisbiiInvoice typedef is incomplete
 * @typedef {{
 *  amount: number
 *  currency: string
 *  customer: string
 *  handle: string
 *  id: string
 *  refunded_amount: number
 *  state: 'created' | 'pending' | 'dunning' | 'settled' | 'cancelled' | 'authorized' | 'failed'
 * }} FrisbiiInvoice
 *
 * @typedef {{
 *  acquirer_reference?: string
 *  amount?: number
 *  amount_incl_vat?: boolean
 *  invoice: string
 *  key?: string
 *  manual_transfer?: {
 *   comment?: string
 *   method: string
 *   payment_date: string
 *   reference?: string
 *  }
 *  note_lines?: {
 *   amount: number
 *   amount_incl_vat?: boolean
 *   quantity: number
 *   origin?: string
 *   order_line_id?: string
 *   text: string
 *   vat?: number
 *  }[]
 *  text?: string
 *  vat?: number
 * }} FrisbiiRefundPayload
 *
 * @typedef {{
 *  acquirer_message?: string
 *  accounting_number?: string
 *  amount: number
 *  created: Date
 *  credit_note_id?: string
 *  currency: string
 *  error?: string
 *  error_state?: 'hard_declined' | 'processing_error'
 *  id: string
 *  invoice: string
 *  ref_transaction?: string
 *  state: 'refunded' | 'failed' | 'processing'
 *  transaction: string
 *  type: string
 * }} FrisbiiRefundResponse
 *
 *
 * @typedef {{
 *  address?: string
 *  address2?: string
 *  city?: string
 *  company?: string
 *  country?: string
 *  debtor_id?: number
 *  email?: string
 *  first_name?: string
 *  generate_handle?: boolean
 *  handle?: string
 *  language?: string
 *  last_name?: string
 *  metadata?: Object
 *  phone?: string
 *  postal_code?: string
 *  test?: boolean
 *  vat?: string
 * }} FrisbiiSessionCustomer
 *
 * @typedef {{
 *  address?: string
 *  address2?: string
 *  attention?: string
 *  city?: string
 *  company?: string
 *  country?: string
 *  email?: string
 *  first_name?: string
 *  last_name?: string
 *  phone?: string
 *  postal_code?: string
 *  state_or_province?: string
 *  vat?: string
 * }} FrisbiiSessionAddress
 *
 * @typedef {{
 *  amount?: number
 *  billing_address?: FrisbiiSessionAddress
 *  currency?: string
 *  customer: FrisbiiSessionCustomer
 *  customer_handle?: string
 *  metadata?: Object
 *  handle: string
 *  key?: string
 *  ordertext?: string
 *  order_lines?: FrisbiiSessionOrderLine[]
 *  shipping_address?: FrisbiiSessionAddress
 * }} FrisbiiSessionOrder
 *
 * @typedef {{
 *  handle: string
 *  state: 'authorized' | 'created' | 'settled' | 'failed' | 'cancelled' | 'pending'
 *  customer: string
 *  amount: number
 *  currency: string
 *  authorized: string
 *  created: string
 *  transaction: string
 *  source: FrisbiiChargeSource
 *  order_lines: FrisbiiSessionOrderLine[]
 *  refunded_amount: number
 *  authorized_amount: number
 *  payment_context: string
 * }} FrisbiiCharge
 *
 * @typedef {{
 *  type: string
 *  card: string
 *  mps: string
 *  iban: string
 *  fingerprint: string
 *  provider: string
 *  frictionless: boolean
 *  vipps_recurring: string
 *  sepa_mandate: string
 *  offline_agreement_handle: string
 *  auth_transaction: string
 *  card_type: string
 *  transaction_card_type: string
 *  exp_date: string
 *  masked_card: string
 *  card_country: string
 *  strong_authentication_status: string
 *  three_d_secure_status: string
 *  risk_rule: string
 *  acquirer_code: string
 *  acquirer_message: string
 *  acquirer_reference: string
 *  text_on_statement: string
 *  surchage_fee: number
 * }} FrisbiiChargeSource
 *
 * @typedef {{
 *  id: string
 *  orderText: string
 *  amount: number
 *  vat: number
 *  quantity: number
 *  origin: string
 *  timestamp: string
 *  amount_vat: number
 *  amount_ex_vat: number
 *  unit_amount: number
 *  unit_amount_ex_vat: number
 *  amount_defined_incl_vat
 * }} FrisbiiSessionOrderLine
 */
