/**
 * @typedef {{
 *  attributeType: 'category'
 *  name: string
 *  value: string
 * }} BloomreachAttributeSuggestion
 */

/**
 * @typedef {{
 *  queryContext: {
 *    originalQuery: string
 *  }
 *  suggestionGroups: BloomreachSuggestionGroup[]
 * }} BloomreachAutoSuggestResponse
 */

/**
 * @typedef {{
 *  displayText: string
 *  query: string
 * }} BloomreachQuerySuggestion
 */

/**
 * @typedef {{
 *  pid: string
 *  sale_price: number
 *  thumb_image: string
 *  title: string
 *  url: string
 * }} BloomreachSearchSuggestion
 */

/**
 * @typedef {{
 *  attributeSuggestions?: BloomreachAttributeSuggestion[]
 *  catalogName: string
 *  querySuggestions: BloomreachQuerySuggestion[]
 *  searchSuggestions?: BloomreachSearchSuggestion[]
 *  view: string
 * }} BloomreachSuggestionGroup
 */
