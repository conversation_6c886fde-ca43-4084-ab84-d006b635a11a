const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')
const { getBloomreachTracking } = require('../../../helpers/tracking')
const { getCachedFeatureEnabled } = require('../../../helpers/features')

/** @typedef {import('./_typedef')} */
/** @typedef {import('@apollo/datasource-rest').DataSourceRequest} DataSourceRequest */
/** @typedef {import('@apollo/datasource-rest').DataSourceFetchResult} DataSourceFetchResult */

class BloomReach extends RESTDataSource {
  /**
   * @returns {URLSearchParams}
   */
  get params() {
    const params = new URLSearchParams()
    params.set('account_id', config.bloomreach.accountId)
    params.set('auth_key', config.tokens.bloomreachKey)
    params.set('domain_key', config.bloomreach.domainKey)

    return params
  }

  /**
   * @param {string} q
   * @param {string} fromUrl
   * @returns {Promise<BloomreachAutoSuggestResponse>}
   */
  async autosuggest(q, fromUrl = this.context.appConfig.baseUrl) {
    const { uid } = await getBloomreachTracking(this.context)
    const params = this.params
    params.set('_br_uid_2', uid || config.bloomreach.defaultUid)
    params.set('catalog_views', config.bloomreach.domainKey)
    params.set('q', q)
    params.set('ref_url', fromUrl)
    params.set('request_id', `${Math.round(Math.random() * 10e13)}`.padStart(13, '0'))
    params.set('request_type', 'suggest')
    params.set('url', fromUrl)

    return this.get(`${config.bloomreach.autosuggest.baseUrl}`, {
      params,
      signal: AbortSignal.timeout(config.bloomreach.autosuggest.timeout),
    })
  }

  /**
   * @param {string} path
   * @param {DataSourceRequest} init
   * @returns {Promise<DataSourceFetchResult>}
   */
  async fetch(path, init) {
    const result = await super.fetch(path, init)

    const requestUrl = new URL(path)
    const searchTerm = requestUrl.searchParams.get('q')

    const debugMode = await getCachedFeatureEnabled(
      this.context.server.redis,
      CONST.unleash.features.F_BLOOMREACH_DEBUG
    )
    if (debugMode) {
      this.context.log.info(
        `Bloomreach query url for country ${this.context.countryId} and term ${searchTerm}: ${requestUrl?.href}`
      )
    }

    return result
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {{
   *  string: id,
   *  number: limit,
   *  number: offset,
   *  string: search,
   *  string: slug
   * }} opts
   * @returns {{
   *  keys: string[],
   *  keywordRedirect: string,
   *  numFound: number,
   *  start: number
   * }}
   */
  async search(opts) {
    const { id, limit = 50, offset = 0, search, slug, uid = config.bloomreach.defaultUid } = opts
    const url = new URL(`${this.context.appConfig.baseUrl}/c/${slug}`)

    const params = this.params
    params.set('fl', 'pid')

    let searchType = 'keyword'

    if (search) {
      params.set('q', search)
    } else if (id) {
      params.set('q', id)
      searchType = 'category'
    }

    params.set('_br_uid_2', uid)
    params.set('ref_url', url)
    params.set('request_id', `${Math.round(Math.random() * 10e13)}`.padStart(13, '0'))
    params.set('request_type', 'search')
    params.set('rows', limit)
    params.set('search_type', searchType)
    params.set('url', url)
    params.set('start', offset)

    const {
      response: { numFound, start, docs },
      keywordRedirect,
    } = await this.get(`${config.bloomreach.search.baseUrl}`, {
      params,
      signal: AbortSignal.timeout(config.bloomreach.search.timeout),
    })

    const keys = docs.map((doc) => doc.pid)

    return {
      keys,
      keywordRedirect,
      numFound,
      start,
    }
  }
}

module.exports = {
  bloomreach: BloomReach,
}
