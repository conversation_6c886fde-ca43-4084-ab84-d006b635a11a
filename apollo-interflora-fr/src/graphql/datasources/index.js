const config = require('../../config')
const CONST = require('../../const')
/**
 * Import typedefs to make them available through index import
 * @example
 *  `@typedef {import('src/services/GraphQL/datasources')}`
 *
 * @typedef {import('./_typedef')}
 */

const ApplePayDatasources = require('./applepay')
const AvailabilityDatasources = require('./availability')
const AwarditDataSources = require('./awardit')
const AmazonPayDataSources = require('./amazonPay')
const AxerveDataSources = require('./axerve')
const BankIdDataSources = require('./bankId')
const BitnetDataSources = require('./bitnet')
const FrisbiiDataSources = require('./frisbii')
const BloomreachDataSources = require('./bloomreach')
const BuyboxDataSources = require('./buybox')
const CadeauxDataSource = require('./cadeaux')
const CommerceToolsDataSources = require('./commerceTools')
const CheckoutDataSources = require('./checkout')
const DatoCmsDataSources = require('./datocms')
const DiscountCodeDataSources = require('./discountCode')
const GFSDataSources = require('./gfs')
const HittaDataSources = require('./hitta')
const HybrisDataSources = require('./hybris')
const ITCadastreDataSources = require('./ITCadastre')
const ITUserDatasources = require('./ITUser')
const MetaDataSources = require('./meta')
const OccasionRemindersSource = require('./occasionreminders')
const PaycometDatasources = require('./paycomet')
const PaymentDatasources = require('./payment')
const PaypalDatasources = require('./paypal')
const SelfCareDataSource = require('./selfcare')
const SequenceGeneratorDataSources = require('./SequenceGenerator')
const SolrDataSources = require('./solr')
const SEServices = require('./SEServices')
const TimeOneDataSources = require('./timeone')
const { DKServices } = require('./DKServices')
const ITAuthenticationDatasource = require('./ITAuthentication')

/** @var {DataSources} */
const DataSources = {}

switch (config.site) {
  case CONST.site.es:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...AvailabilityDatasources,
      ...BloomreachDataSources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...MetaDataSources,
      ...PaycometDatasources,
      ...PaypalDatasources,
      ...SequenceGeneratorDataSources,
      ...ITAuthenticationDatasource,
    })
    break
  case CONST.site.pt:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...AvailabilityDatasources,
      ...BloomreachDataSources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...MetaDataSources,
      ...PaycometDatasources,
      ...PaypalDatasources,
      ...SequenceGeneratorDataSources,
    })
    break
  case CONST.site.fr:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...AvailabilityDatasources,
      ...FrisbiiDataSources,
      ...BloomreachDataSources,
      ...BuyboxDataSources,
      ...CadeauxDataSource,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...DiscountCodeDataSources,
      ...GFSDataSources,
      ...MetaDataSources,
      ...HybrisDataSources,
      ...OccasionRemindersSource,
      ...PaycometDatasources,
      ...PaymentDatasources,
      ...PaypalDatasources,
      ...SequenceGeneratorDataSources,
      ...CadeauxDataSource,
      ...SelfCareDataSource,
    })
    break
  case CONST.site['fr-hybris']:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...HybrisDataSources,
      ...PaypalDatasources,
      ...SequenceGeneratorDataSources,
      ...SolrDataSources,
      ...TimeOneDataSources,
      ...BuyboxDataSources,
      ...SelfCareDataSource,
    })
    break
  case CONST.site.it:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...AmazonPayDataSources,
      ...AvailabilityDatasources,
      ...AxerveDataSources,
      ...BloomreachDataSources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...MetaDataSources,
      ...ITCadastreDataSources,
      ...PaypalDatasources,
      ...SequenceGeneratorDataSources,
      ...ITUserDatasources,
    })
    break
  case CONST.site.se:
    Object.assign(DataSources, {
      ...ApplePayDatasources,
      ...AmazonPayDataSources,
      ...AvailabilityDatasources,
      ...AwarditDataSources,
      ...AxerveDataSources,
      ...BloomreachDataSources,
      ...BitnetDataSources,
      ...FrisbiiDataSources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...HittaDataSources,
      ...MetaDataSources,
      ...SequenceGeneratorDataSources,
      ...SEServices,
      ...BankIdDataSources,
    })
    break
  case CONST.site.dk:
    Object.assign(DataSources, {
      ...DKServices,
      ...ApplePayDatasources,
      ...AmazonPayDataSources,
      ...BloomreachDataSources,
      ...FrisbiiDataSources,
      ...CheckoutDataSources,
      ...CommerceToolsDataSources,
      ...DatoCmsDataSources,
      ...GFSDataSources,
      ...MetaDataSources,
      ...SequenceGeneratorDataSources,
    })
    break
  default:
    throw new Error(`invalid config.site: ${config.site}`)
}

/**
 * @param { cache: KeyValueCache<string>, context: GraphQLContext } opts
 * @returns {Promise<Object<string, DataSource>>}
 */
const createDataSources = (opts) => {
  const dataSources = Object.entries(DataSources).reduce((o, [key, DataSource]) => {
    o[key] = new DataSource()
    if (typeof o[key].initialize === 'function') {
      o[key].initialize(opts)
    }
    return o
  }, {})

  return dataSources
}

module.exports = {
  DataSources,
  createDataSources,
}
