/** @typedef {import('@apollo/datasource-rest').AugmentedRequest} AugmentedRequest */
/** @typedef {import('@apollo/datasource-rest').RequestOptions} RequestOptions */
/** @typedef {import('@apollo/utils.keyvaluecache').KeyValueCache} KeyValueCache */

/** @typedef {import('./amazonPay').amazonPay} AmazonPayDataSource */
/** @typedef {import('./applepay').applePay} ApplePayDataSource */
/** @typedef {import('./availability').availability} AvailabilityDataSource */
/** @typedef {import('./axerve').axerve} AxerveDataSource */
/** @typedef {import('./bitnet').bitnet} BitnetDataSource */
/** @typedef {import('./frisbii').frisbii} FrisbiiDataSource */
/** @typedef {import('./frisbii').frisbiiCheckout} FrisbiiCheckoutDataSource */
/** @typedef {import('./frisbii').frisbiiRefund} FrisbiiRefundDataSource */
/** @typedef {import('./bloomreach').bloomreach} BloomreachDataSource */
/** @typedef {import('./checkout').checkout} CheckoutDataSource */

/** @typedef {import('./commerceTools').carts} CTCartsDataSource */
/** @typedef {import('./commerceTools').categories} CTCategoriesDataSource */
/** @typedef {import('./commerceTools').ctAuth} CTAuthDataSource */
/** @typedef {import('./commerceTools').customObjects} CTCustomObjectsDataSource */
/** @typedef {import('./commerceTools').customers} CTCustomersDataSource */
/** @typedef {import('./commerceTools').orders} CTOrdersDataSource */
/** @typedef {import('./commerceTools').products} CTProductsDataSource */

/** @typedef {import('./datocms').datocms} DatoCmsDataSource */
/** @typedef {import('./gfs').gfs} GFSDataSource */

/** @typedef {import('./hybris').hybrisAddress} HybrisAddressDataSource */
/** @typedef {import('./hybris').hybrisAuth} HybrisAuthDataSource */
/** @typedef {import('./hybris').hybrisCart} HybrisCartDataSource */
/** @typedef {import('./hybris').hybrisCatalog} HybrisCatalogDataSource */
/** @typedef {import('./hybris').hybrisCms} HybrisCmsDataSource */
/** @typedef {import('./hybris').hybrisContact} HybrisContactDataSource */
/** @typedef {import('./hybris').hybrisDepartment} HybrisDepartmentDataSource */
/** @typedef {import('./hybris').hybrisForgottenPasswords} HybrisForgottenPasswordsDataSource */
/** @typedef {import('./hybris').hybrisGeo} HybrisGeoDataSource */
/** @typedef {import('./hybris').hybrisInterflora} HybrisInterfloraDataSource */
/** @typedef {import('./hybris').hybrisNewsletter} HybrisNewsletterDataSource */
/** @typedef {import('./hybris').hybrisOrder} HybrisOrderDataSource */
/** @typedef {import('./hybris').hybrisProduct} HybrisProductDataSource */
/** @typedef {import('./hybris').hybrisUser} HybrisUserDataSource */

/** @typedef {import('./ITCadastre').itCadastre} ITCadastreDataSource */

/** @typedef {import('./ITUser').itAuth} ITUserAuthDatasource */
/** @typedef {import('./ITUser').itForgottenPassword} ITUserForgottenPasswordDataSource */
/** @typedef {import('./ITUser').itUser} ITUserUserDataSource */

/** @typedef {import('./SEServices').seApi} SEApi */
/** @typedef {import('./SEServices').seFlorist} SEFlorist */

/** @typedef {import('./paycomet').paycomet} PayCometDataSource */
/** @typedef {import('./paypal').paypal} PaypalDataSource */
/** @typedef {import('./SequenceGenerator').sequenceGenerator} SequenceGeneratorDataSource */
/** @typedef {import('./solr').solr} SolrDataSource */
/** @typedef {import('./timeone').timeone} TimeOneDataSource */
/** @typedef {import('./buybox').buybox} BuyboxDataSource */
/** @typedef {import('./cadeaux').cadeaux} CadeauxDataSource */
/** @typedef {import('./awardit').awardit} AwarditDataSource */
/** @typedef {import('./occasionreminders').occasionReminders} OccasionRemindersDataSource */

/** @typedef {import('./selfcare').selfCare} SelfCareDataSource  */

/** @typedef {import('./ITAuthentication').ITAuthenticationDatasource} ITAuthenticationDatasource */

/**
 * @typedef {{
 *  cache: (undefined | KeyValueCache)
 *  context: (undefined | GraphQLContext)
 * }} DataSourceConfig
 */

/**
 * @typedef {{
 *  applePay: ApplePayDataSource
 *  bloomreach: BloomreachDataSource
 *  bitnet: BitnetDataSource
 *  frisbii: FrisbiiDataSource
 *  frisbiiCheckout: FrisbiiCheckoutDataSource
 *  frisbiiRefund: FrisbiiRefundDataSource
 *  carts: CTCartsDataSource
 *  categories: CTCategoriesDataSource
 *  checkout: CheckoutDataSource
 *  ctAuth: CTAuthDataSource
 *  customObjects: CTCustomObjectsDataSource
 *  customers: CTCustomersDataSource
 *  datocms: DatoCmsDataSource
 *  gfs: GFSDataSource
 *  occasionReminders: OccasionRemindersDataSource
 *  orders: CTOrdersDataSource
 *  paypal?: PaypalDataSource
 *  products: CTProductsDataSource
 *  sequenceGenerator: SequenceGeneratorDataSource
 *  cadeaux: CadeauxDataSource
 *  buybox: BuyboxDataSource
 *  awardit: AwarditDataSource
 * }} DataSources
 */

/**
 * @typedef {DataSources & {
 *  paycomet: PayCometDataSource
 *  paypal: PaypalDataSource
 *  itAuthentication: ITAuthenticationDatasource
 * }} ESDataSources
 */

/**
 * @typedef {DataSources & {
 *  hybrisAddress: HybrisAddressDataSource
 *  hybrisAuth: HybrisAuthDataSource
 *  hybrisCart: HybrisCartDataSource
 *  hybrisCatalog: HybrisCatalogDataSource
 *  hybrisCms: HybrisCmsDataSource
 *  hybrisContact: HybrisContactDataSource
 *  hybrisDepartment: HybrisDepartmentDataSource
 *  hybrisForgottenPasswords: HybrisForgottenPasswordsDataSource
 *  hybrisGeo: HybrisGeoDataSource
 *  hybrisInterflora: HybrisInterfloraDataSource
 *  hybrisNewsletter: HybrisNewsletterDataSource
 *  hybrisOrder: HybrisOrderDataSource
 *  hybrisProduct: HybrisProductDataSource
 *  hybrisUser: HybrisUserDataSource
 *  selfCare: SelfCareDataSource
 * }} FRDataSources
 */

/**
 * @typedef {{
 *  applePay: ApplePayDataSource
 *  checkout: CheckoutDataSource
 *  gfs: GFSDataSource
 *  hybrisAddress: HybrisAddressDataSource
 *  hybrisAuth: HybrisAuthDataSource
 *  hybrisCart: HybrisCartDataSource
 *  hybrisCatalog: HybrisCatalogDataSource
 *  hybrisCms: HybrisCmsDataSource
 *  hybrisContact: HybrisContactDataSource
 *  hybrisDepartment: HybrisDepartmentDataSource
 *  hybrisForgottenPasswords: HybrisForgottenPasswordsDataSource
 *  hybrisGeo: HybrisGeoDataSource
 *  hybrisInterflora: HybrisInterfloraDataSource
 *  hybrisNewsletter: HybrisNewsletterDataSource
 *  hybrisOrder: HybrisOrderDataSource
 *  hybrisProduct: HybrisProductDataSource
 *  hybrisUser: HybrisUserDataSource
 *  sequenceGenerator: SequenceGeneratorDataSource
 *  solr: SolrDataSource
 *  timeone: TimeOneDataSource
 *  buybox: BuyboxDataSource
 * }} FRHybrisDataSources
 */

/**
 * @typedef {DataSources & {
 *  amazonPay: AmazonPayDataSource
 *  axerve: AmazonPayDataSource
 *  itAuth: ITUserAuthDatasource
 *  itCadastre: ITCadastreDataSource
 *  itForgottenPassword: ITUserForgottenPasswordDataSource
 *  itUser: ITUserUserDataSource
 * }} ITDataSources
 */

/**
 * @typedef {DataSources & {
 *  seApi: SEApi
 *  seFlorist: SEFlorist
 * }} SEDataSources
 */
