const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')

class ITUserBaseDatasource extends RESTDataSource {
  constructor() {
    super()
    this.baseURL = config.itUser.baseUrl
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   */
  willSendRequest(_path, request) {
    request.headers.authorization = `Basic ${Buffer.from(config.tokens.itUserBasicAuthHeader).toString('base64')}`
    request.headers.accept = 'application/json'

    if (
      !request.path.endsWith('/authenticate') &&
      this.context.auth &&
      this.context.auth.oauthToken &&
      !request.headers['authorization']
    ) {
      request.headers.authorization = `Bearer ${this.context.auth.oauthToken}`
    }
  }
}

module.exports = ITUserBaseDatasource
