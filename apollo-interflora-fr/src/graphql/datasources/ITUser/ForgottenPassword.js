const User = require('./User')

/** @typedef {import('../../types/_typedef')} */

/**
 * A Data Source service to interact with ITUser Forgotten Passwords endpoints.
 *
 * @class
 * @extends User
 * @example
 *  // In a GraphQL resolver
 *  await context.dataSources.forgottenPasswords.resetPassword(...)
 */
class ForgottenPasswordDataSource extends User {
  /**
   * Generates a token to restore a customer's forgotten password and send it by email.
   * @param {string} userId
   * @returns {Promise<void>}
   */
  async sendResetToken(userId) {
    /** Use this endpoint as anonymous user */
    return this.post('api/v1/susers/lostpassword', {
      body: {
        Email: userId,
        Language: null,
        ResetPasswordUrl: null,
      },
    })
  }

  /**
   * @param {string} token
   * @param {string} newPassword
   * @returns {Promise<void>}
   */
  async resetPassword(token, newPassword) {
    return this.post('api/v1/susers/resetpassword', {
      body: {
        Code: token,
        NewPassword: newPassword,
      },
    })
  }

  /**
   * @param {string} token
   * @returns {Promise<void>}
   * @throws
   */
  async validateResetToken() {
    throw new Error('not implemented')
  }
}

module.exports = ForgottenPasswordDataSource
