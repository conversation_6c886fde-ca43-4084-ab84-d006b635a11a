const CONST = require('../../../const')
const ITUserBaseDatasource = require('./Base')

/** @typedef {import('../../types/_typedef')} */

class ITUserUserDatasource extends ITUserBaseDatasource {
  /**
   *
   * @param {string} newPassword
   * @param {string} oldPassword
   * @returns {Promise<any>}
   */
  changePassword(newPassword, oldPassword) {
    return this.post('api/v1/susers/password', {
      body: {
        isnewuser: false,
        newpassword: newPassword,
        oldPassword: oldPassword,
        username: this.context.session.itUser.username,
      },
    })
  }

  /**
   * @param {string} userId
   * @param {string} code
   * @returns {Promise<any>}
   */
  async confirmUserEmail(userId, code) {
    return this.post('api/v1/susers/confirmation', {
      body: {
        code,
        userId,
      },
    })
  }

  /**
   * @param {string} userId
   * @param {GQLAddressTypeEnum} type
   * @returns {Promise<any>}
   */
  getAddresses(userId, type) {
    return this.get('api/v1/susers/addresses', {
      params: {
        type: type === CONST.address.addressType.delivery ? 1 : 2,
        userid: userId,
      },
    })
  }

  /**
   * @param {string} userId
   * @param {string} addressId
   * @returns {Promise<any>}
   */
  removeAddress(userId, addressId) {
    return this.delete('api/v1/susers/address', {
      body: JSON.stringify({
        AddressId: addressId,
        UserId: userId,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  /**
   * @param {string} userId
   * @param {string} address
   * @returns {Promise<any>}
   */
  setAddress(userId, address) {
    return this.post('api/v1/susers/address', {
      body: {
        Address: address,
        UserId: userId,
      },
    })
  }

  /**
   * @param {string} userId
   * @returns {Promise<any>}
   */
  getUser(userId) {
    return this.get('api/v1/suser', {
      params: {
        userid: userId,
      },
    })
  }

  /**
   *
   * @param {string} email
   * @returns {Promise<any>}
   */
  getUserByEmail(email) {
    return this.get('api/v1/susers/email', {
      params: {
        email,
      },
    })
  }

  /**
   * @param {Object} user
   * @returns {Promise<Object>}
   */
  async register(user) {
    const response = await this.post('api/v1/suser', {
      body: {
        ...user,
      },
    })

    await this.post('api/v1/susers/password', {
      body: {
        isnewuser: true,
        newpassword: user.password,
        oldpassword: user.password,
        username: user.username,
      },
    })

    return response.User
  }

  /**
   * @param {any} user
   * @returns {Promise<any>}
   */
  async update(user) {
    return this.post('api/v1/suser', {
      body: {
        ...user,
      },
    })
  }
}

module.exports = ITUserUserDatasource
