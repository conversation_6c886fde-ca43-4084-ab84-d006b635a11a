const ITUserBaseDatasource = require('./Base')

class ITUser<PERSON><PERSON><PERSON>ata<PERSON>urce extends ITUserBaseDatasource {
  /**
   * @param {string} username
   * @param {string} password
   * @returns {Promise<{
   *  createdAt: string,
   *  error: ('ACCOUNT_NOT_CONFIRMED'|'ACCOUNT_LOCKED'|'WRONG_GRANT_TYPE'),
   *  lifetime: number,
   *  refreshLifetime: number,
   *  refreshToken: string,
   *  user: object,
   *  value: string
   * }>}
   */
  login(username, password) {
    return this.post('api/v1/saccount/authenticate', {
      body: {
        grant_type: 'password',
        password,
        username,
      },
    })
  }

  /**
   * @param {string} refreshToken
   * @returns {Promise<any>}
   */
  refresh(refreshToken) {
    return this.post('api/v1/saccount/refresh', {
      body: {
        refreshToken,
      },
    })
  }
}

module.exports = ITUserAuthDatasource
