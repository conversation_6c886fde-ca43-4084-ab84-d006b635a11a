/**
 * @typedef {('COMPLETED'|'FAILED'|'PENDING')} BankIdStatus
 */

/** @typedef {{
 *   autoStartToken: string;
 *   qrStartSecret: string;
 *   qrStartToken: string;
 *   orderRef: string;
 *   startTime: number;
 * }} BankIdAuthResponse
 */

/**
 * @typedef {{
 *   referenceId: string
 *   status: BankIdStatus
 *   qrData: string
 *   hintCode: string
 *   message: string
 *   autoStartToken: string
 *   completionData: {
 *       user: {
 *       givenName: string
 *       personalNumber: string
 *       surname: string
 *       name: string
 *      }
 *   }
 * }} BankIdAuthentication
 */
