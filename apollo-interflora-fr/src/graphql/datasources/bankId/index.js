const { RESTDataSource } = require('../RESTDataSource')
const config = require('../../../config')
const { Agent } = require('node:https')
const { readFileSync } = require('node:fs')
const { CustomCache } = require('../../../helpers/CustomCache')
const CONST = require('../../../const')
const fetch = require('../../../helpers/fetch')

class BankIdDataSource extends RESTDataSource {
  constructor() {
    const agentCertificate = readFileSync(config.bankId.ca, 'utf-8')
    const agent = new Agent({
      ca: agentCertificate,
      passphrase: config.tokens.bankIdPassPhrase,
      pfx: Buffer.from(config.tokens.bankIdPfx, 'base64'),
    })

    super({
      fetch: (url, options) =>
        fetch(url, {
          ...(options ?? {}),
          agent,
        }),
    })

    /**
     * How to update certificate
     * https://myflower.atlassian.net/wiki/spaces/ITFFR/pages/**********/BankId+Authentication#Certificate
     * Convert the certificate to base64
     * fs.readFileSync(join(__dirname, '/FPTestcert5_20240610.p12')).toString('base64')
     * Update the value of the pfx variable (bankIdPfx) in the local.sv.js file or in the environment variable (bankIdPfx)
     */
    if (!config.tokens.bankIdPassPhrase || !config.tokens.bankIdPfx) {
      throw new Error('BankId tokens not found')
    }

    /** @typedef {import('./_typedef')} */

    this.baseURL = config.bankId.baseUrl
  }

  /**
   * @param {string} ip
   * @returns {Promise<BankIdAuthResponse>}
   */
  async authenticate(ip) {
    const response = await this.post('auth', {
      body: {
        endUserIp: ip,
      },
    })

    const authResponse = {
      ...response,
      startTime: Date.now(),
    }

    await this._getCustomCache().set(response.orderRef, authResponse)

    return authResponse
  }

  /**
   * @param {string} referenceId
   * @return {Promise<BankIdAuthentication>}
   */
  collect(referenceId) {
    return this.post('collect', { body: { orderRef: referenceId } })
  }

  /**
   * @param {string} referenceId
   * @returns {Promise<BankIdAuthResponse|null>}
   */
  getCachedAuthResponse(referenceId) {
    return this._getCustomCache().get(referenceId)
  }

  /**
   * @private
   * @returns {CustomCache<BankIdAuthResponse>}
   */
  _getCustomCache() {
    return new CustomCache(this.context.server.redis, CONST.bankId.cachePrefix, CONST.bankId.ttl)
  }
}

module.exports = {
  bankId: BankIdDataSource,
}
