/** @typedef {import("./_typedef")} */

const { RESTDataSource } = require('../RESTDataSource')
const config = require('../../../config')

/**
 * Data source for interacting with the Occasion Reminders microservice.
 * Utilizes REST-based communication to perform CRUD operations on reminders.
 *
 * @extends {RESTDataSource<GraphQLContext>}
 */
class OccasionRemindersSource extends RESTDataSource {
  constructor() {
    super()
    this.baseURL = config.occasionReminders.baseUrl
  }

  /**
   * @param {string} userId - The ID of the user whose reminders are being requested.
   * @returns {Promise<APIOccasionReminder[]>} - A promise resolving to a list of reminders.
   */
  async getRemindersByUser(userId) {
    return this.get(`api/v1/OccasionReminders/${userId}`)
  }

  /**
   * @param {string} userId - The ID of the user whose reminders are being requested.
   * @param {Object} reminder - Reminder data to be created.
   * @returns {Promise<APIOccasionReminder>} - The created reminder object.
   */
  async createReminder(userId, reminder) {
    const body = {
      ...reminder,
      userId,
    }
    return this.post('api/v1/OccasionReminders', { body })
  }

  /**
   * @param {string} id - The ID of the reminder to update.
   * @param {Object} updates - The fields to update.
   * @returns {Promise<APIOccasionReminder>} - The updated reminder object.
   */
  async updateReminder(id, updates) {
    const body = { ...updates }
    return this.patch(`api/v1/OccasionReminders/${id}`, { body })
  }

  /**
   * @param {string} id - The ID of the reminder to delete.
   * @returns {Promise<void>} - Resolves when the reminder is deleted.
   */
  async deleteReminder(id) {
    return this.delete(`api/v1/OccasionReminders/${id}`)
  }
}

module.exports = {
  occasionReminders: OccasionRemindersSource,
}
