const { RESTDataSource } = require('../RESTDataSource')
const apm = require('elastic-apm-node')
const config = require('../../../config')
const CONST = require('../../../const')
const { Agent } = require('https')
const fs = require('node:fs')

const nodeFetch = require('../../../helpers/fetch')

/** @typedef {import('./_typedef')} */

/**
 * AwardIt API data source.
 * https://api-docs.giftcards.awardit.com/api/mvalue/index.html
 */
class AwarditDataSource extends RESTDataSource {
  constructor() {
    if (fs.existsSync(config.awardit.certificates.cert) && fs.existsSync(config.awardit.certificates.key)) {
      config.tokens.awarditCert = fs.readFileSync(config.awardit.certificates.cert, 'utf-8')
      config.tokens.awarditKey = fs.readFileSync(config.awardit.certificates.key, 'utf-8')
    }

    const httpAgent = new Agent({
      cert: Buffer.from(config.tokens.awarditCert),
      key: Buffer.from(config.tokens.awarditKey),
    })

    super({
      fetch: (url, options) => {
        return nodeFetch(url, {
          ...(options ?? {}),
          agent: httpAgent,
        })
      },
    })

    /**
     * How to update the certificate and key:
     * Convert the certificate in key and cert
     * openssl pkcs12 -in XLENT_TEST_Butik.pfx -nocerts -out private_Butik.key -nodes
     * openssl pkcs12 -in XLENT_TEST_Butik.pfx -nokeys -out certificate_Butik.crt
     * Update the local.sv.js config file with the new key and cert or the environment variables (awarditCert, awarditKey)
     * certificates are injected by k8s
     */
    this.baseURL = config.awardit.baseUrl
    this.paths = config.awardit.paths
    this.httpAgent = httpAgent
  }

  /**
   * Update a gift card.
   * @param {string} multicode
   * @param {object} body
   * @returns {Promise<{GQLGiftCards, GQLGiftCardsReservation, GQLGiftCardTransaction}>}
   */
  async updateCard(multicode, body) {
    this.context.log.info(`[AwardIt-GiftCards] Updating card ${multicode} with body: ${JSON.stringify(body)}`)
    return this.put(`${this.paths.CARDS}/${multicode}`, { body })
  }

  /**
   * Reserve an amount on a gift card.
   * @param {string} multicode
   * @param {string} pin
   * @param {number} amountToReserve
   * @param {Date} reservationExpDate
   * @returns {Promise<GiftCardsTransaction>}
   */
  async reserveAmount(multicode, pin, amountToReserve, reservationExpDate) {
    const body = {
      amountToReserve,
      pin,
      reservationExpDate: reservationExpDate.toISOString(),
    }

    return this.updateCard(multicode, body)
  }

  /**
   * Redeem an amount on a gift card.
   * @param {string} multicode
   * @param {string} reservationId
   * @param {number} amount
   * @returns {Promise<{GQLGiftCards, GQLGiftCardsReservation, GQLGiftCardTransaction}>}
   */
  async redeemReservation(multicode, reservationId, amount) {
    const body = {
      amountToRedeemReservation: amount,
      redeemReservation: reservationId,
    }

    return this.updateCard(multicode, body)
  }

  /**
   * Cancel a transaction
   * @param {string} multicode
   * @param {string} reservationId
   * @returns {Promise<{GQLGiftCards, GQLGiftCardsReservation, GQLGiftCardTransaction}>}
   */
  async cancelTransaction(multicode, reservationId) {
    const body = {
      cancelReservation: reservationId,
    }

    return this.updateCard(multicode, body)
  }

  /**
   * List a gift card´s transactions.
   * @param {string} multicode
   * @returns {Promise<GiftCardData>}
   */
  async getTransactions(multicode) {
    return this.get(`${this.paths.CARDS}/${multicode}/transactions`)
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }
}

module.exports = {
  awardit: AwarditDataSource,
}
