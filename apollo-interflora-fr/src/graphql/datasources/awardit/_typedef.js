/**
 * @typedef {Object} Card
 * @property {string} id - The Unique id (pattern: ^[0-9]{1,19}_[0-9]{1,19}$, minLength: 3, maxLength: 21).
 * @property {string} multicode - The multi code (pattern: ^[a-zA-Z0-9-]{3,30}$, minLength: 3, maxLength: 30).
 * @property {number} templateId - The id of the template (integer, min: 1, max: 2147483647).
 * @property {boolean} loadable - If a top-up is allowed.
 * @property {number} balance - The balance amount (float, min: 0, max: 99999.99).
 * @property {string} startDate - The start date (pattern: yyyy-MM-ddTHH:mm:ss.SSSZ).
 * @property {string} endDate - The expiry date (pattern: yyyy-MM-ddTHH:mm:ss.SSSZ).
 * @property {string} currency - The currency.
 * @property {CardStatusCode} status - The status code.
 */

/**
 * @typedef {1|2|3|4|5|6|7|8|9|10|11} CardStatusCode
 *
 * @description Information of card status codes.
 *
 * @enum {number}
 * @property {1} Created - The card has been created.
 * @property {2} QueuedForSending - The card is queued for sending.
 * @property {3} SendingError - An error occurred while sending.
 * @property {4} Sending - The card is being sent.
 * @property {5} DeliveryError - An error occurred during delivery.
 * @property {6} Delivered - The card has been delivered.
 * @property {7} PartlyOrFullyRedeemed - The card has been partly or fully redeemed.
 * @property {8} Closed - The card is closed.
 * @property {9} Canceled - The card has been canceled.
 * @property {10} ExternDistribution - The card is externally distributed.
 * @property {11} NotActivated - The card has not been activated.
 */

/**
 * @typedef {Object} Transaction
 * @property {number} amount - The amount (float, min: 0.01, max: 99999.99).
 * @property {number} transactionId - The unique id (integer, min: 1, max: 2147483647).
 * @property {string} timestamp - The timestamp (pattern: yyyy-MM-ddTHH:mm:ss.SSSZ).
 * @property {string} userName - The user name (pattern: ^.{1,100}$).
 * @property {string} transactionType - The type of transaction.
 * @property {string} currency - The currency.
 * @property {Load[]} loads - A list of loads.
 */

/**
 * @typedef {Object} Load
 * @property {string} activationId - The activation id (pattern: ^[0-9]{1,19}_[0-9]{1,19}$, minLength: 3, maxLength: 21).
 * @property {number} amount - The amount (float, min: 0.01, max: 99999.99).
 */

/**
 * @typedef {Object} GiftCardData
 * @property {Card} card - Information of a gift card.
 * @property {Transaction[]} transactions - List of transactions.
 */

/**
 * @typedef {Object} GiftCardsTransaction
 * @property {Card} card - Information of a gift card.
 * @property {Transaction} transaction - Transaction.
 * @property {Reservation} reservation - Reservation.
 */

/**
 * @typedef {Object} Reservation
 * @property {string} reservationId - The unique id (pattern: ^[a-zA-Z0-9-]{1,100}$, minLength: 1, maxLength: 100).
 * @property {string} reservationExpires - The expiry date (pattern: yyyy-MM-ddTHH:mm:ss.SSSZ).
 * @property {string} timestamp - The timestamp (pattern: yyyy-MM-ddTHH:mm:ss.SSSZ).
 * @property {number} userId - The id of the user (integer, min: 1, max: 2147483647).
 * @property {string} userName - The name of the user (pattern: ^.{1,100}$, minLength: 1, maxLength: 100).
 * @property {string} transactionType - The type of the transaction.
 * @property {string} status - The status [ activated, redeemed, canceled, expired ].
 * @property {number} amount - The amount (float, min: 0.01, max: 99999.99).
 * @property {Load[]} loads - A list of loads.
 */
