const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')

/** @typedef {import('./__typedef')} */

class GFS extends RESTDataSource {
  /**
   * @returns {Promise<GFSCategory[]>}
   */
  getCategories() {
    return this.get('category')
  }

  /**
   * @param {number} id
   * @returns {Promise<GFSCategory[]>}
   */
  getCategory(id) {
    return this.get(`category/${id}`)
  }

  /**
   * @returns {Promise<GFSCountry[]>}
   */
  getCountries() {
    return this.get('Country')
  }

  /**
   * @param {string} countryId
   * @returns {Promise<GFSProduct[]>}
   */
  getProducts(countryId) {
    const code = `${countryId}`.substr(0, 2).toUpperCase()
    return this.get(`Product/${code}`)
  }

  /**
   *
   * @param {string} countryId
   * @returns {Promise<GFSHoliday[]>}
   */
  getHolidays(countryId) {
    return this.get(`Holiday/${countryId}`)
  }

  /**
   * @param {string} countryId
   * @returns {Promise<GFSRegion[]>}
   */
  getRegions(countryId) {
    return this.get(`Region/${countryId}`)
  }

  /**
   *
   * @param {string} id
   * @returns {Promise<GFSUnit>}
   */
  getUnit(id) {
    return this.get(`/Unit/${id}`)
  }

  /**
   * @param {RequestOptions} request
   * @returns {Promise<URL>}
   */
  async resolveURL(request) {
    if (!this.baseURL) {
      this.baseURL = config.gfs.baseUrl
    }

    return super.resolveURL(request)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    if (config.gfs.headers) {
      for (const [name, value] of Object.entries(config.gfs.headers)) {
        request.headers[name] = value
      }
    }
  }
}

module.exports = {
  gfs: GFS,
}
