/**
 * @typedef {{
 *  EndDate: string
 *  IsTemplate: boolean
 *  LastUpdate: string
 *  MaxPrice: Number
 *  MidPrice: Number
 *  MinPrice: Number
 *  StartDate: string
 * }} GFSAvailability
 */

/**
 * @typedef {{
 *  Id: number
 *  Name: string
 *  Description: string
 *  ParentId: number
 *  LastUpdate: string
 * }} GFSCategory
 */

/**
 * @typedef {{
 *  CountryCode: string
 *  CountryTimes: GFSCountryTimes[]
 *  CurrencyId: Number
 *  DialingCode: Number
 *  Enabled: boolean
 *  Id: Number
 *  LastUpdate: string
 *  Name: string
 *  TimeZone: Number
 *  UnitId: Number
 * }} GFSCountry
 */

/**
 * from data exploration:
 *  - DayOfWeek are 0-based, with 0 = sunday
 *  - times are local
 * @typedef {{
 *  CloseTime: string
 *  CountryCode: string
 *  DayOfWeek: Number
 *  Id: Number
 *  OpenTime: string
 *  SameDayDeadline: string
 * }} GFSCountryTimes
 */

/**
 * @typedef {{
 *  Availabilities: GFSAvailability[]
 *  CountryCode: string
 *  CreateDate: string
 *  DeliveryDelay: Number
 *  Id: Number
 *  IntercatCode: string
 *  InternalDescription: string
 *  InternalName: string
 *  IsAddon: boolean
 *  IsGeneric: boolean
 *  LastUpdate: string
 *  LongDescription: string
 *  MaxPriceImages: GFSImage[]
 *  MidPriceImages: GFSImage[]
 *  MinPriceImages: GFSImage[]
 *  Name: string
 *  Ordering: number
 *  PeakPeriods: GFSAvailability[]
 *  ProductCategories: Number[]
 *  RemovedDate: string
 *  ShortDescription: string
 * }} GFSProduct
 */

/**
 * @typedef {{
 *  BeginDate: string
 *  CountryCode: string
 *  EndDate: string
 *  Id: Number
 *  IsTemplate: boolean
 *  LastUpdate: string
 *  Name: string
 * }} GFSHoliday
 */

/**
 * @typedef {{
 *  ClearingCode: string | null
 *  Contact: GFSContact
 *  ContactLastName: string | null
 *  Id: number
 *  LastConnected: string | null
 *  LastUpdate: string | null
 *  Name: string | null
 *  UnitTimes: GFSUnitTime[] | null
 *  UnitTimeSpecial: GFSUnitTimeSpecial | null
 * }} GFSUnit
 */

/**
 * @typedef {{
 *  CloseTime: string
 *  DayOfWeek: number
 *  Id: number
 *  OpenTime: string
 *  UnitId: number
 * }} GFSUnitTime
 */

/**
 * @typedef {{
 *  BeginDate: string
 *  Closed: boolean
 *  EndDate: string
 *  Id: number
 *  Static: boolean
 *  UnitId: number
 * }} GFSUnitTimeSpecial
 */
