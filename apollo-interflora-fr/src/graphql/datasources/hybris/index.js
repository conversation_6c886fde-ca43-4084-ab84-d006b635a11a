/**
 * Import typedefs to make them available through index import
 * @example
 *  `@typedef {import('src/services/GraphQL/datasources/hybris')}`
 *
 * @typedef {import("./_typedef")}
 */

const AddressDataSource = require('./Address')
const AuthDataSource = require('./Auth')
const CartDataSource = require('./Cart')
const CatalogDataSource = require('./Catalog')
const CmsDataSource = require('./Cms')
const ContactDataSource = require('./Contact')
const DepartmentDataSource = require('./Department')
const GeoDataSource = require('./Geo')
const InterfloraDataSource = require('./Interflora')
const NewsletterDataSource = require('./Newsletter')
const ProductDataSource = require('./Product')
const UserDataSource = require('./User')
const OrderDataSource = require('./Order')
const ForgottenPasswordsDataSource = require('./ForgottenPasswords')

module.exports = {
  hybrisAddress: AddressDataSource,
  hybrisAuth: AuthDataSource,
  hybrisCart: CartDataSource,
  hybrisCatalog: CatalogDataSource,
  hybrisCms: CmsDataSource,
  hybrisContact: ContactDataSource,
  hybrisDepartment: DepartmentDataSource,
  hybrisForgottenPasswords: ForgottenPasswordsDataSource,
  hybrisGeo: GeoDataSource,
  hybrisInterflora: InterfloraDataSource,
  hybrisNewsletter: NewsletterDataSource,
  hybrisOrder: OrderDataSource,
  hybrisProduct: ProductDataSource,
  hybrisUser: UserDataSource,
}
