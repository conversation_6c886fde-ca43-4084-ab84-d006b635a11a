const Hybris = require('./Hybris')
const { randomString } = require('../../../helpers/string')
const config = require('../../../config')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris User endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const user = await context.dataSources.hybrisUser.getCurrent()
 */
class UserDataSource extends Hybris {
  async initUser(email, details) {
    const {
      title,
      firstName,
      lastName,
      password = await randomString(config.interflora.initUserPasswordLength),
    } = details
    const body = {
      email,
      firstName,
      lastName,
      password,
      titleCode: title,
      uid: email,
    }
    return this.post(this._getBasePath(), { body })
  }

  /**
   * Get the current user.
   * @returns {Promise<APIUser>}
   */
  getCurrent() {
    return this.get(this._getBaseUserPath())
  }

  /**
   * @param {string} userId
   * @returns {Promise<APIUser>}
   */
  getUser(userId) {
    return this.get(`${this._getBasePath()}/${userId}`)
  }

  /**
   * @param {APIUserSignUp} user
   * @return {Promise<boolean>}
   */
  async update(user) {
    const response = await this.put(this._getBaseUserPath(), { body: user })

    return response.result || false
  }

  /**
   * @param {string} oldPassword
   * @param {string} newPassword
   * @returns {Promise<boolean>}
   */
  async updatePassword(oldPassword, newPassword) {
    const params = new URLSearchParams({ new: newPassword, old: oldPassword })

    /** Will use application/x-www-form-urlencoded as Content-Type header */
    const response = await this.put(`${this._getBaseUserPath()}/password`, { body: params })

    return response.result || false
  }

  /**
   * @param {APIUserSignUp} user
   * @returns {Promise<boolean>}
   */
  async register(user) {
    /** Use this endpoint as anonymous user */
    const { access_token } = await this.context.dataSources.hybrisAuth.login()

    const response = await this.post(this._getBasePath(), {
      body: user,
      headers: { authorization: `Bearer ${access_token}` },
    })

    return response.result || false
  }

  /**
   * Get the base User API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/users`
  }

  /**
   * Get the base user id scoped API path.
   * @private
   * @returns {string}
   */
  _getBaseUserPath() {
    const ctx = this.getContextInfo()
    return `${this._getBasePath()}/${ctx.user}`
  }
}

module.exports = UserDataSource
