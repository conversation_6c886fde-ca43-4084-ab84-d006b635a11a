const Hybris = require('./Hybris')
const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Product endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const product = await context.dataSources.hybrisProduct.getProduct(productCode)
 */
class ProductDataSource extends Hybris {
  /**
   * Get a product from Hybris.
   * @param {string} productCode
   * @param {string} [countryId]
   * @returns {Promise<APIProduct>}
   */
  getProduct(productCode, countryId) {
    return this.get(this._getBasePath(productCode, countryId), {
      params: {
        fields: 'FULL,additionalInfo,productLabel,variants(FULL)',
      },
    })
  }

  /**
   * Get a product gallery images from Hybris.
   * @param {string} productCode
   * @param {string} [format]
   * @returns {Promise<APIImage[]>}
   */
  async getGalleryImages(productCode, format) {
    const response = await this.get(
      `${this._getBasePath(productCode)}/galleryImages${format ? `/format/${format}` : ''}`
    )

    return response.galleryImages
  }

  /**
   * Get product references from Hybris.
   * @param {string} productCode
   * @param {string} referenceType
   * @returns {Promise<APIProductReference[]>}
   */
  async getReferences(productCode, referenceType) {
    const response = await this.get(`${this._getBasePath(productCode)}/references/${referenceType}`)

    return response.references
  }

  /**
   * Decrement stock of a product variant in Hybris
   * @param {string} variantCode
   * @param {number} quantity
   * @returns {Promise<void>}
   * @throws {Error}
   */
  async decrementStock(variantCode, quantity) {
    const params = new URLSearchParams({ fields: 'FULL', qty: quantity })
    try {
      await this.put(`${this._getBasePath(variantCode)}/stock?${params.toString()}`)
    } catch (error) {
      // Handle specific error case: Product for which stock cannot be decremented. Eg. Network products
      if (error.extensions.code === 400 && error.errors[0]?.type === config.product.stockErrors.stockIsUnlimited) {
        return
      } else {
        throw error
      }
    }
  }

  /**
   * Check stock
   * @param {string} productCode
   * @param {string} variantSku
   * @param {string} countryId
   * @returns {Promise<APIStock>}
   */
  async checkStock(productCode, variantSku, countryId) {
    const product = await this.getProduct(productCode, countryId)

    /** @type {APIStock} */
    const noStock = {
      stockLevel: 0,
      stockLevelStatus: Object.entries(CONST.product.stockStatuses).find(
        ([, val]) => val === CONST.product.stockStatuses.outOfStock
      )[0],
    }

    const variants = product.variants ?? product.variantOptions
    if (!Array.isArray(variants)) {
      this.context.log?.warn?.(
        `[datasource.hybris.checkStock] no variant found for product ${productCode} sku ${variantSku}`
      )
      return noStock
    }
    const variant = variants.find((variant) => variant.code === variantSku)

    if (!variant) {
      this.context.log?.warn?.(
        `[datasource.hybris.checkStock] no variant found for product ${productCode} sku ${variantSku}`
      )
      return noStock
    }

    return variant.stock ?? noStock
  }

  /**
   * Get the base Product API path.
   * @private
   * @param {string} productCode
   * @param {string} countryId
   * @returns {string}
   */
  _getBasePath(productCode, countryId) {
    const ctx = this.getContextInfo()
    return `v2/${
      countryId ? ctx.baseSiteId.replace(/-[A-Z]+$/, `-${countryId}`) : ctx.baseSiteId
    }/products/${productCode}`
  }
}

module.exports = ProductDataSource
