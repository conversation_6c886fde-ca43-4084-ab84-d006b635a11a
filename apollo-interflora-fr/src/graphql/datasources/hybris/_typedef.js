/** @typedef {import('./Address')} AddressDataSource */
/** @typedef {import('./Auth')} AuthDataSource */
/** @typedef {import('./Cart')} CartDataSource */
/** @typedef {import('./Catalog')} CatalogDataSource */
/** @typedef {import('./Cms')} CmsDataSource */
/** @typedef {import('./Contact')} ContactDataSource */
/** @typedef {import('./Department')} DepartmentDataSource */
/** @typedef {import('./Geo')} GeoDataSource */
/** @typedef {import('./Interflora')} InterfloraDataSource */
/** @typedef {import('./Newsletter')} NewsletterDataSource */
/** @typedef {import('./Product')} ProductDataSource */
/** @typedef {import('./User')} UserDataSource */
/** @typedef {import('./Order')} OrderDataSource */
/** @typedef {import('./ForgottenPasswords') ForgottenPasswords} */

/**
 * @typedef {{
 *  address: AddressDataSource
 *  auth: AuthDataSource
 *  cart: CartDataSource
 *  catalog: CatalogDataSource
 *  cms: CmsDataSource
 *  contact: ContactDataSource
 *  department: DepartmentDataSource
 *  forgottenPasswords: ForgottenPasswords
 *  geo: GeoDataSource
 *  interflora: InterfloraDataSource
 *  newsletter: NewsletterDataSource
 *  product: ProductDataSource
 *  user: UserDataSource
 *  order: OrderDataSource
 * }} HybrisDataSources
 */

/**
 * @typedef {{
 *  baseSiteId: string
 *  cartCountryId: string|undefined
 *  cartId: string
 *  countryId: string
 *  siteId: string
 *  user: string
 * }} DataSourceContext
 */

/**
 * @typedef {{
 *  result: boolean
 * }} APIResult
 */

/**
 * @typedef {{
 *  code: string
 *  features: APIClassificationFeature[]
 *  icon: string
 *  name: string
 * }} APIClassification
 */

/**
 * @typedef {{
 *  code: string
 *  featureValues: string[]
 *  icon: string
 *  name: string
 * }} APIClassificationFeature
 */

/**
 * @typedef {{
 *  isDefault: boolean
 *  isocode: string
 *  name: string
 *  topCountriesIndex: number
 * }} APICountry
 */

/**
 * @typedef {{
 *  date: string,
 *  id: string,
 *  label: string
 * }} APIFestMode
 */

/**
 * @typedef {{
 *  codePostale: string
 *  desservie: string
 *  etat: string
 *  isoCode: string
 *  label: string
 *  name: string
 *  province: string
 *  valide: boolean
 * }} APILocalite
 */

/**
 * @typedef {{
 *  countryIso: string
 *  isocode: string
 *  isocodeShort: string
 *  name: string
 * }} APIRegion
 */

/**
 * @typedef {{
 *  access_token: string
 *  expires_in: Number
 *  refresh_token: string
 *  refresh_token_expires_in: Number
 *  scope: string
 *  token_type: string
 * }} APIToken
 */

/**
 * @typedef {{
 *  b2bCustomer: ('false'|'true')
 *  firstName: string
 *  lastName: string
 *  login: string
 *  phone: string
 *  title: ('mr'|'mrs')
 * } & APIToken} APIUserToken
 */

/**
 * @typedef {{
 *  additionalInfo: string
 *  batiment: string
 *  codeLocalite: string
 *  companyName: string
 *  country: APICountry
 *  defaultAddress: boolean
 *  digiCode: string
 *  email: string
 *  etage: string
 *  firstName: string
 *  formattedAddress: string
 *  id: string
 *  lastName: string
 *  lieu: string
 *  line1: string
 *  line2: string
 *  natureAddress: string
 *  phone: string
 *  porte: string
 *  postalCode: string
 *  region: {
 *      countryIso: string
 *      isocode: string
 *      isocodeShort: string
 *      name: string
 *  }
 *  shippingAddress: boolean
 *  title: string
 *  titleCode: string
 *  town: string
 *  twitterId: string
 *  typeCeremonie: string
 *  visibleInAddressBook: boolean
 * }} APIAddress
 */

/**
 * @typedef {{
 *  currencyIso: string
 *  formattedValue: string
 *  maxQuantity: number
 *  minQuantity: number
 *  priceType: 'DISCOUNT_MIN' | 'DISCOUNT_MAX' | 'MIN' | 'MEDIUM' | 'MAX' | 'BUY' | 'FROM' | 'PRICE_VARIANT' | 'DELIVERY'
 *  value: number
 * }} APIPrice
 */

/**
 * @typedef {{
 *  discount: APIPrice
 *  price: APIPrice
 *  productVariantCode: string
 *  productVariantName: string
 * }} APIVariantPrice
 */

/**
 * @typedef {{
 *  alternativeAccessories: Object[]
 *  alternativeCategoryUrl: string
 *  alternativeProduct: APIVariantPrice
 *  isValid: boolean
 *  postalCodeZoneGeo: string
 * }} APIValidateLocalite
 */

/**
 * @typedef {{
 *  endHour: number
 *  endMinute: number
 *  messageSpecifique: string
 *  startHour: number
 *  startMinute: number
 * }} APICeremonyHourRange
 */

/**
 * @typedef {{
 *  messageSpecifique: string
 *  moments: APIMomentLivraison[]
 * }} APIMomentLivraisonList
 */

/**
 * @typedef {{
 *  code: string
 *  enabled: boolean
 *  label: string
 * }} APIMomentLivraison
 */

/**
 * @typedef {{
 *  accessoires: APIProduct[]
 *  accessory: boolean
 *  addMode: string
 *  additionalInfo: APIMessageInfo
 *  availableForPickup: boolean
 *  averageRating: number
 *  baseOptions: APIBaseOption[]
 *  baseProduct: string
 *  bebloom: boolean
 *  categories: Object<string, APICategory>
 *  classifications: Object<string, Classification>
 *  code: string
 *  color: string
 *  currentStep: string
 *  deliveryCost: APIPrice
 *  deliveryDetail: string
 *  deliveryZoneGeo: string
 *  description: string
 *  descriptionTitle: string
 *  enablePrixLibre: boolean
 *  expressDelivery: boolean
 *  expressDeliveryAvailable: boolean
 *  externalProductUrl: string
 *  futureStocks: APIFutureStock[]
 *  image: string
 *  imagePlp: string
 *  images: Object<string, APIImage>
 *  isCardMessage: boolean
 *  isBundle: boolean
 *  isRubanDeuil: boolean
 *  label: string
 *  legalNotices: string
 *  mainCategory: string
 *  manufacturer: string
 *  maxOrderQuantity: number
 *  multidimensional: boolean
 *  name: string
 *  noteMoyenne: number
 *  numberOfReviews: number
 *  picto: string
 *  potentialPromotions: Object<string, Promotion>
 *  price: APIPrice
 *  pricePromo: APIPrice
 *  priceRange: APIPriceRange
 *  productLabel: APIProductLabel
 *  productReferences: APIProductReference[]
 *  productType: string
 *  produitDeuil: boolean
 *  purchasable: boolean
 *  reassurancesInfo: APIMessageInfo[]
 *  reviews: Object<string, Review>
 *  stock: APIStock
 *  style: string
 *  summary: string
 *  url: string
 *  variantMatrix: APIVariantMatrixElement[]
 *  variantOptions: APIVariantOption[]
 *  variantType: string
 *  variants: APIProductVariantData[]
 *  volumePrices: APIPrice[]
 *  volumePricesFlag: boolean
 * }} APIProduct
 */

/**
 * @typedef {{
 *  content: string
 *  icon: string
 *  type: 'ERROR' | 'INFO' | 'WARNING'
 * }} APIMessageInfo
 */

/**
 * @typedef {{
 *  options: APIVariantOption[]
 *  selected: APIVariantOption
 *  variantType: string
 * }} APIBaseOption
 */

/**
 * @typedef {{
 *  content: string
 *  cssClass: string
 *  image: APICMSMedia
 *  link: APICMSLink
 *  subTitle: string
 *  title: string
 * }} APICMSBanner
 */

/**
 * @typedef {{
 *  components: APICMSBanner[]
 *  content: string
 *  link: APICMSLink
 *  title: string
 * }} APICMSBannerContainer
 */

/**
 * @template {Object} T
 * @typedef {T & {
 *  modifiedTime: string
 *  name: string
 *  template: string
 *  typeCode: string
 *  uid: string
 * }} APICMSComponent
 */

/**
 * @typedef {{
 *  background: 'ALTERNATE' | 'DELIGHT' | 'BSOFT' | 'NONE'
 *  components: APICMSComponent[]
 *  modifiedTime: string
 *  name: string
 *  params: string[]
 *  slot: APICMSSlot
 *  template: string
 *  typeCode: string
 *  uid: string
 *  wrapper: 'NONE' | 'XSMALL'
 * }} APICMSTemplate
 */

/**
 * @typedef {{
 *   catalogVersion: string
 *   code: string
 *   mime: string
 *   url: string
 * }} APICMSMedia
 */

/**
 * @typedef {{
 *  pages: string
 *  name: string
 *  obfuscated: string
 *  uid: string
 *  typeCode: string
 * }} APICMSObfuscateLink
 */

/**
 * @typedef {{
 *  categoryCode: string
 *  categoryParentCode: string
 *  contentPageLabel: string
 *  cssClass: string
 *  highlight: string
 *  linkName: string
 *  modifiedTime: string
 *  name: string
 *  target: 'sameWindow'|'newWindow'
 *  typeCode: string
 *  uid: string
 *  url: string
 * obfuscateLink:APICMSObfuscateLink
 * }} APICMSLink
 */

/**
 * @typedef {{
 *  uid: string
 *  typeCode: string
 *  modifiedTime: string
 *  name: string
 *  image: APIImage
 *  subTitle: string
 *  content: string
 *  links: APICMSLink[]
 * }} APICMSMomentFirst
 */

/**
 * @typedef {{
 *  content: string
 *  subTitle: string
 *  title: string
 * }} APICMSParagraph
 */

/**
 * @typedef {{
 *  content: string
 *  link: APICMSLink
 *  products: APIProduct[]
 *  title: string
 * }} APICMSProductCarousel
 */

/**
 * @typedef {{
 *  components: {
 *    component: {
 *      modifiedtime: string
 *      name: string
 *      typeCode: string
 *      uid: string
 *    }[]
 *  }
 *  uid: string
 *  position: string
 *
 * }} APICMSSlot
 */

/**
 * @typedef {{
 *  date: string
 *  formattedDate: string
 *  stock: APIStock
 * }} APIFutureStock
 */

/**
 * @typedef {APICMSComponent<{
 *  navigationNode: APINavigationNode
 *  navigationNodeSecondary: APINavigationNode
 *  navigationNodeOthers: APINavigationNode
 * }>} APIMenuComponent
 */

/**
 * @typedef {{
 *  categoryCode: string
 *  children: APINavigationNode[]
 *  visibleOnlyInMobile: boolean
 *  link: APICMSLink
 *  name: string
 *  uid: string
 *  promote: boolean
 * }} APINavigationNode
 */

/**
 * @typedef {{
 *  maxPrice: APIPrice
 *  minPrice: APIPrice
 * }} APIPriceRange
 */

/**
 * @typedef {{
 *  code: string
 *  image: string
 *  text: string
 * }} APIProductLabel
 */

/**
 * @typedef {{
 *  description: string
 *  preselected: boolean
 *  quantity: number
 *  referenceType: string
 *  target: APIProduct
 * }} APIProductReference
 */

/**
 * @typedef {{
 *  stockLevel: number
 *  stockLevelStatus: string
 * }} APIStock
 */

/**
 * @typedef {{
 *  elements: APIVariantMatrixElement[]
 *  isLeaf: boolean
 *  parentVariantCategory: APIVariantCategory
 *  variantOption: APIVariantOption
 *  variantValueCategory: APIVariantValueCategory
 * }} APIVariantMatrixElement
 */

/**
 * @typedef {{
 *  code: string
 *  priceData: APIPrice
 *  stock: APIStock
 *  url: string
 *  variantOptionQualifiers: Object<string, APIVariantOptionQualifier>
 * }} APIVariantOption
 */

/**
 * @typedef {{
 *  accessoires: APIProduct[]
 *  code: string
 *  deliveryCost: APIPrice
 *  description: string
 *  image: string
 *  label: string
 *  name: string
 *  price: APIPrice
 *  pricePromo: APIPrice
 *  stock: APIStock
 *  url: string
 *  variantLabel: string
 *  variantLabelCode: string
 *  variantLabelIcon: string
 *  variantOptionQualifiers: APIVariantOptionQualifier[]
 * }} APIProductVariantData
 */

/**
 * @typedef {{
 *  hasImage: boolean
 *  name: string
 *  priority: number
 * }} APIVariantCategory
 */

/**
 * @typedef {{
 *  name: string
 *  sequence: number
 *  superCategories: Object<string, APIVariantCategory>
 * }} APIVariantValueCategory
 */

/**
 * @typedef {{
 *  image: APIImage
 *  name: string
 *  qualifier: string
 *  value: string
 * }} APIVariantOptionQualifier
 */

/**
 * @typedef {{
 *  altText: string
 *  description: string
 *  format: string
 *  galleryIndex: number
 *  imageType: string
 *  url: string
 * }} APIImage
 */

/**
 * @typedef {{
 *  code: string
 *  description: string
 *  geographicalArea: string
 *  image: Image
 *  maxOfProduct: Number
 *  name: string
 *  showFiltre: boolean
 *  showSort: boolean
 *  title: string
 *  type: string
 *  url: string
 *  visibleFilters: string[]
 * }} APICategory
 */

/**
 * @typedef {{
 *  breadcrumbs: APIProductSearchBreadcrumb[]
 *  categoryCode: string
 *  categoryName: string
 *  currentQuery: APISearchState
 *  facets: APIFacet[]
 *  freeTextSearch: string
 *  keywordRedirectUrl: string
 *  pagination: APIPagination
 *  products: Product[]
 *  sorts: APISort[]
 *  spellingSuggestion: APISpellingSuggestion
 * }} APIProductSearchPage
 */

/**
 * @typedef {{
 *  facetCode: string
 *  facetName: string
 *  facetValueCode: string
 *  facetValueName: string
 *  removeQuery: APISearchState
 *  truncateQuery: APISearchState
 * }} APIProductSearchBreadcrumb
 */

/**
 * @typedef {{
 *  query: APISearchQuery
 *  url: string
 * }} APISearchState
 */

/**
 * @typedef {{
 *  category: boolean
 *  multiSelect: boolean
 *  name: string
 *  priority: number
 *  topValues: APIFacetValue[]
 *  values: APIFacetValue[]
 *  visible: boolean
 * }} APIFacet
 */

/**
 * @typedef {{
 *  currentPage: number
 *  pageSize: number
 *  sort: string
 *  totalPages: number
 *  totalResults: number
 * }} APIPagination
 */

/**
 * @typedef {{
 *  code: string
 *  name: string
 *  selected: boolean
 * }} APISort
 */

/**
 * @typedef {{
 *  query: string
 *  suggestion: string
 * }} APISpellingSuggestion
 */

/**
 * @typedef {{
 *  value: string
 * }} APISearchQuery
 */

/**
 * @typedef {{
 *  count: number
 *  name: string
 *  query: APISearchState
 *  selected: boolean
 * }} APIFacetValue
 */

/**
 * @typedef {{
 *  breadcrumb: APIBreadcrumb[]
 *  contentSlots: APIContentSlotList
 *  defaultPage: boolean
 *  isPro: boolean
 *  metaData: APIMetaData[]
 *  name: string
 *  template: string
 *  title: string
 *  typeCode: string
 *  uid: string
 *  version: string
 * }} APICMSPage
 */

/**
 * @typedef {{
 *  name: string
 *  property: string
 *  type: 'META' | 'LINK'
 *  value: string
 * }} APIMetaData
 */

/**
 * @typedef {{
 *  categoryCode: string
 *  contentPageLabel: string
 *  departmentSlugUrl: string
 *  label: string
 *  productCode: string
 *  url: string
 * }} APIBreadcrumb
 */

/**
 * @typedef {{
 *  uid: string
 *  typeCode: string
 *  modifiedTime: string
 *  name: string
 *  icon: string
 *  label: string
 *  content: string
 *  products: {
 *    code: string
 *  }[]
 * }} APICmsTownPageTab
 */

/**
 * @typedef {{
 *  component: {
 *    modifiedTime: string
 *    name: string
 *    typeCode: string
 *    uid: string
 *  }[]
 * }} APIComponentList
 */

/**
 * @typedef {{
 *  contentSlot: APIContentSlot[]
 * }} APIContentSlotList
 */

/**
 * @typedef {{
 *  components: APIComponentList
 *  name: string
 *  position: string
 *  slotId: string
 *  slotShared: boolean
 *  slotStatus: string
 * }} APIContentSlot
 */

/**
 * @typedef {{
 *  listDepartmentsDomtom: APIFloraDepartment[]
 *  listDepartmentsFr: APIFloraDepartment[]
 * }} APIFloraDepartmentList
 */

/**
 * @typedef {{
 *  canonicalUrlForDepartment: string
 *  chefLieu: APIFloraCity
 *  codeDepartment: string
 *  codeRegion: string
 *  content: string
 *  contentTopVilles: string
 *  inseeLocalite: string
 *  isDomtom: boolean
 *  listCity: APIFloraCity[]
 *  nearestDepartments: APIFloraDepartment[]
 *  nomDepartment: string
 *  nomRegion: string
 *  safeNomDepartement: string
 *  slugUrl: string
 *  urlImage: string
 * }} APIFloraDepartment
 */

/**
 * @typedef {{
 *  agglomerationData: APIFloraCity
 *  alternateUrlForCity: string
 *  canonicalUrlForCity: string
 *  cityCode: string
 *  cityName: string
 *  content: string
 *  contentBis: string
 *  contentTer: string
 *  departmentData: APIFloraDepartment[]
 *  inseeLocalite: string
 *  nearestCity: APIFloraCity[]
 *  postalCode: string
 *  safeCityName: string
 *  titleCity: string
 *  urlImage: string
 *  slugUrl: string
 * }} APIFloraCity
 */

/**
 * @typedef {{
 *  listCity: APIFloraCity[]
 * }} APIFloraCityList
 */

/**
 * @typedef {{
 *  b2bCustomer: boolean
 *  currency: APICurrency
 *  defaultAddress: APIAddress
 *  displayUid: string
 *  email: string
 *  firstName: string
 *  internalB2bIdentifier: string
 *  itfPlusExpirationDate: string
 *  language: APILanguage
 *  lastName: string
 *  name: string
 *  premium: boolean
 *  premiumSubscriptionDate: string
 *  title: string
 *  titleCode: string
 *  uid: string
 * }} APIUser
 */

/**
 * @typedef {{
 *  active: boolean
 *  isocode: string
 *  name: string
 *  nativeName: string
 * }} APILanguage
 */

/**
 * @typedef {{
 *  active: boolean
 *  isocode: string
 *  name: string
 *  symbol: string
 * }} APICurrency
 */

/**
 * @typedef {{
 *  appliedOrderPromotions: APIPromotionResult[]
 *  appliedProductPromotions: APIPromotionResult[]
 *  appliedVouchers: APIVoucher[]
 *  billingAddress: APIAddress
 *  calculated: boolean
 *  ceremony: boolean
 *  code: string
 *  codeOccasion: string
 *  contactFirstName: string
 *  contactLastName: string
 *  contactPhone: string
 *  contactTitle: string
 *  dateNotificationIfloraSms: string
 *  deliveryAddress: APIAddress
 *  deliveryCost: APIPrice
 *  deliveryDate: string
 *  deliveryDiscounts: APIPrice
 *  deliveryHour: string
 *  deliveryItemsQuantity: number
 *  deliveryMinute: string
 *  deliveryMode: APIDeliveryMode
 *  deliveryOrderGroups: APIDeliveryOrderEntryGroup
 *  description: string
 *  deuil: boolean
 *  entries: APIOrderEntry[]
 *  expirationTime: string
 *  guid: string
 *  hasInterfloraPlusProduct: boolean
 *  interfloraplusEntryNumber: number
 *  message: string
 *  messageRubanDeuil: string
 *  moment: string
 *  momentLibre: string
 *  momentLabel: string
 *  name: string
 *  categoryCode: string
 *  net: boolean
 *  orderDiscounts: APIPrice
 *  orderStatus: string
 *  orderType: string
 *  paymentIdentifier: string
 *  paymentInfo: APIPaymentDetails
 *  paymentUrl: string
 *  pickupItemsQuantity: number
 *  pickupOrderGroups: APIPickupOrderEntryGroup[]
 *  potentialOrderPromotions: APIPromotionResult[]
 *  potentialProductPromotions: APIPromotionResult[]
 *  productDiscounts: APIPrice
 *  referenceCommande: string
 *  saveTime: string
 *  savedBy: APIPrincipal
 *  signature: string
 *  site: string
 *  store: string
 *  subTotal: APIPrice
 *  tombe: boolean
 *  totalDiscounts: APIPrice
 *  totalItems: number
 *  totalPrice: APIPrice
 *  totalPriceWithTax: APIPrice
 *  totalTax: APIPrice
 *  totalUnitCount: number
 *  user: APIPrincipal
 * }} APICart
 */

/**
 * @typedef {{
 *  accessories: {code: string, quantity: Number}[]
 *  billingAddress: APIAddress
 *  customPrice: Number
 *  dateNotificationIfloraSms: string
 *  deliveryAddress: APIAddress
 *  deliveryContact: APIAddress
 *  deliveryDate: string
 *  deliveryHour: string
 *  deliveryMinute: string
 *  fromGoogleShopping?: boolean
 *  message: string
 *  messageRubanDeuil: string
 *  moment: string
 *  momentLibre: string
 *  occasion: string
 *  orderType: string
 *  productCode: string
 *  qty: Number
 *  signature: string
 *  templateMessage: string
 * }} APICartInput
 */

/**
 * @typedef {{
 *  accessoires: APIAddProduct[]
 *  billingAddress: APIAddress
 *  customPrice: number
 *  dateNotificationIfloraSms: string
 *  deliveryAddress: APIAddress
 *  deliveryContact: APIAddress
 *  deliveryDate: string
 *  deliveryHour: string
 *  deliveryMinute: string
 *  fromGoogleShopping?: boolean
 *  message: string
 *  messageRubanDeuil: string
 *  moment: string
 *  momentLibre: string
 *  occasion: string
 *  orderType: string
 *  productCode: string
 *  qty: number
 *  signature: string
 *  templateMessage: string
 * }} APICreateCart
 */

/**
 * @typedef {{
 *  code: string
 *  quantity: number
 * }} APIAddProduct
 */

/**
 * @typedef {{
 *  consumedEntries: APIPromotionOrderEntryConsumed[]
 *  description: string
 *  promotion: APIPromotion
 * }} APIPromotionResult
 */

/**
 * @typedef {{
 *  appliedValue: APIPrice
 *  code: string
 *  currency: APICurrency
 *  description: string
 *  endDate: string
 *  freeShipping: boolean
 *  name: string
 *  startDate: string
 *  value: number
 *  valueFormatted: string
 *  valueString: string
 *  voucherCode: string
 * }} APIVoucher
 */

/**
 * @typedef {{
 *  code: string
 *  deliveryCost: APIPrice
 *  description: string
 *  name: string
 * }} APIDeliveryMode
 */

/**
 * @typedef {{
 *  deliveryAddress: APIAddress
 *  entries: Object<string, APIOrderEntry>
 *  quantity: number
 *  totalPriceWithTax: APIPrice
 * }} APIDeliveryOrderEntryGroup
 */

/**
 * @typedef {{
 *  basePrice: APIPrice
 *  deliveryMode: APIDeliveryMode
 *  deliveryPointOfService: APIPointOfService
 *  entryNumber: number
 *  isNotRemovable: boolean
 *  product: APIProduct
 *  quantity: number
 *  totalPrice: APIPrice
 *  updateable: boolean
 * }} APIOrderEntry
 */

/**
 * @typedef {{
 *  accountHolderName: string
 *  billingAddress: APIAddress
 *  birthdayDay: string
 *  birthdayMonth: string
 *  birthdayYear: string
 *  cardNumber: string
 *  cardToken: string
 *  cardType: APICardType
 *  defaultPayment: boolean
 *  expiryMonth: string
 *  expiryYear: string
 *  id: string
 *  issueNumber: string
 *  issueNumberV: string
 *  payementMode: string
 *  saved: boolean
 *  startMonth: string
 *  startYear: string
 *  subscriptionId: string
 * }} APIPaymentDetails
 */

/**
 * @typedef {{
 *  browserJavaEnabled: boolean
 *  browserLanguage: string
 *  browserColorDepth: number
 *  browserScreenHeight: number
 *  browserScreenWidth: number
 *  browserTimeZone: number
 *  browserJavascriptEnabled: boolean
 * }} APIDSP2Params
 */

/**
 * @typedef {{
 *  deliveryPointOfService: APIPointOfService
 *  distance: number
 *  entries: Object<string, APIOrderEntry>
 *  quantity: number
 *  totalPriceWithTax: APIPrice
 * }} APIPickupOrderEntryGroup
 */

/**
 * @typedef {{
 *  name: string
 *  uid: string
 * }} APIPrincipal
 */

/**
 * @typedef {{
 *  adjustedUnitPrice: number
 *  code: string
 *  orderEntryNumber: number
 *  quantity: number
 * }} APIPromotionOrderEntryConsumed
 */

/**
 * @typedef {{
 *  code: string
 *  couldFireMessages: string[]
 *  description: string
 *  enabled: boolean
 *  endDate: string
 *  firedMessages: string[]
 *  priority: number
 *  productBanner: APIImage
 *  promotionGroup: string
 *  promotionType: string
 *  restrictions: Object<string, APIPromotionRestrictionDTO>
 *  startDate: string
 *  title: string
 * }} APIPromotion
 */

/**
 * @typedef {{
 *  address: APIAddress
 *  description: string
 *  displayName: string
 *  distanceKm: number
 *  features: Object
 *  formattedDistance: string
 *  geoPoint: APIGeoPoint
 *  mapIcon: APIImage
 *  name: string
 *  openingHours: APIOpeningSchedule
 *  storeContent: string
 *  storeImages: Object<string, APIImage>
 *  url: string
 * }} APIPointOfService
 */

/**
 * @typedef {{
 *  brandCode: string
 *  code: string
 *  name: string
 * }} APICardType
 */

/**
 * @typedef {{
 *  latitude: number
 *  longitude: number
 * }} APIGeoPoint
 */

/**
 * @typedef {{
 *  code: string
 *  name: string
 *  specialDayOpeningList: APISpecialOpeningDay[]
 *  weekDayOpeningList: APIWeekdayOpeningDay[]
 * }} APIOpeningSchedule
 */

/**
 * @typedef {{
 *  closed: boolean
 *  closingTime: APITime
 *  comment: string
 *  date: string
 *  formattedDate: string
 *  name: string
 *  openingTime: APITime
 * }} APISpecialOpeningDay
 */

/**
 * @typedef {{
 *  closed: boolean
 *  closingTime: APITime
 *  openingTime: APITime
 *  weekDay: string
 * }} APIWeekdayOpeningDay
 */

/**
 * @typedef {{
 *  formattedHour: string
 *  hout: string
 *  minute: string
 * }} APITime
 */

/**
 * @typedef {{
 *  result: boolean
 *  alternativeCategoryUrl: string
 * }} APIFeasibility
 */

/**
 * @typedef {{
 *  customerEligibleToProlongation: boolean
 *  eligibleToAddPremiumProduct: boolean
 *  potentialDeliveryCost: number
 *  premiumProduct: APIFloraPlusProductDTO
 * }} APIPremiumProduct
 */

/**
 * @typedef {{
 *  description: string
 *  name: string
 *  price: string
 *  targetCode: string
 * }} APIFloraPlusProduct
 */

/**
 * @typedef {{
 *  accountHolderName: string
 *  billingAddress: APIAddress
 *  birthdayDay: string
 *  birthdayMonth: string
 *  birthdayYear: string
 *  cardNumber: string
 *  cardType: APICardType
 *  defaultPayment: boolean
 *  expiryMonth: string
 *  expiryYear: string
 *  id: string
 *  issueNumber: string
 *  payementMode: string
 *  saved: boolean
 *  startMonth: string
 *  startYear: string
 *  subscriptionId: string
 * }} APIPaymentDetails
 */

/**
 * @typedef {{
 *  errorCode: string
 *  order: APIOrder
 *  paymentErrorCode: string
 *  paymentErrorMessage: string
 *  paymentResult: 'ERROR' | 'SUCESS' | 'REDIRECT' | 'AWAITING_CAPTURE'
 *  redirectHTML: string
 *  redirectPostParams: Object
 *  redirectPostUrl: string
 *  redirectUrl: string
 *  token: APIToken
 * }} APIPaymentResult
 */

/**
 * @typedef {{
 *  appliedOrderPromotions: APIPromotionResult[]
 *  appliedProductPromotions: APIPromotionResult[]
 *  appliedVouchers: APIVoucher[]
 *  billingAddress: APIAddress
 *  calculated: boolean
 *  ceremony: boolean
 *  code: string
 *  codeOccasion: string
 *  codeOccasionLabel: string
 *  consignments: APIConsignment
 *  contactFirstName: string
 *  contactLastName: string
 *  contactPhone: string
 *  contactTitle: string
 *  countryCode: string
 *  created: string
 *  customer: APIUser
 *  dateNotificationIfloraSms: string
 *  deliveryAddress: APIAddress
 *  deliveryCost: APIPrice
 *  deliveryDate: string
 *  deliveryHour: string
 *  deliveryItemsQuantity: number
 *  deliveryMinute: string
 *  deliveryMode: string
 *  deliveryOrderGroups: APIDeliveryOrderEntryGroup[]
 *  deliveryStatus: string
 *  deliveryStatusDisplay: string
 *  deuil: boolean
 *  entries: APIOrderEntry[]
 *  guestCustomer: boolean
 *  guid: string
 *  isValidSelfCare: boolean
 *  message: string
 *  messageRubanDeuil: string
 *  moment: string
 *  momentLibre: string
 *  net: boolean
 *  orderDiscounts: APIPrice
 *  orderType: string
 *  paymentIdentifier: string
 *  paymentInfo: APIPaymentDetails
 *  paymentMethod: string
 *  pickupItemsQuantity: number
 *  pickupOrderGroups: APIPickupOrderEntryGroup[]
 *  productDiscounts: APIPrice
 *  selfcareKey: string
 *  signature: string
 *  site: string
 *  status: string
 *  statusDisplay: string
 *  store: string
 *  subTotal: APIPrice
 *  tombe: boolean
 *  totalDiscounts: APIPrice
 *  totalItems: number
 *  totalPrice: APIPrice
 *  totalPriceWithTax: APIPrice
 *  totalTax: APIPrice
 *  trackingUrl: string
 *  unconsignedEntries: APIOrderEntry[]
 *  urlFacture: string
 *  user: APIPrincipal
 * }} APIOrder
 */

/**
 * @typedef {{
 *  key: string
 *  value: string
 * }} APIOrderReason
 */

/**
 * @typedef {{
 *  code: string
 *  deliveryPointOfService: APIPointOfService
 *  entries: APIConsignmentEntry[]
 *  shippingAddress: APIAddress
 *  status: string
 *  statusDate: string
 *  trackingID: string
 * }} APIConsignment
 */

/**
 * @typedef {{
 *  orderEntry: APIOrderEntry
 *  quantity: number
 *  shippedQuantity: number
 * }} APIConsignmentEntry
 */

/**
 * @typedef {{
 *  deliveryModeChanged: boolean
 *  entry: APIOrderEntry
 *  quantity: number
 *  quantityAdded: number
 *  statusCode: string
 *  statusMessage: string
 * }} APICartModification
 */

/**
 * @typedef {{
 *  code: string
 *  iflora: boolean
 *  paymentEncryptedData: string
 *  paymentHop: boolean
 *  paymentProvider: string
 * }} APIPaymentMethod
 */

/**
 * @typedef {{
 *  billingAddress: APIAddress
 *  dateNotificationIfloraSms: string
 *  deliveryAddress: APIAddress
 *  deliveryContact: APIAddress
 *  deliveryDate: string
 *  deliveryHour: string
 *  deliveryMinute: string
 *  message: string
 *  messageRubanDeuil: string
 *  moment: string
 *  momentLibre: string
 *  occasion: string
 *  orderType: string
 *  signature: string
 *  templateMessage: string
 * }} APIUpdateOrder
 */

/**
 * @typedef {{
 *  orders: APIOrderHistory[]
 *  pagination: APIPagination
 *  sorts: APISort[]
 * }} APIOrderHistoryList
 */

/**
 * @typedef {{
 *  code: string
 *  countryCode: string
 *  dateLivraison: string
 *  guid: string
 *  isValidSelfCare: boolean
 *  placed: string
 *  recipientFirstName: string
 *  recipientLastName: string
 *  recipientTitle: string
 *  status: string
 *  statusDisplay: string
 *  total: APIPrice
 * }} APIOrderHistory
 */

/**
 * @typedef {{
 *  ticketSubjectList: APITicketSubject[]
 * }} APITicketSubjectList
 */

/**
 * @typedef {{
 *  code: string
 *  mandatoryDeliveryAddress: boolean
 *  mandatoryDeliveryDate: boolean
 *  mandatoryDoc: boolean
 *  mandatoryMessage: boolean
 *  mandatoryOrderId: boolean
 *  name: string
 *  ticketSubSubject: APITicketSubject[]
 *  type: string
 * }} APITicketSubject
 */

/**
 * @typedef {{
 *  companyName: string
 *  email: string
 *  fileContent: string
 *  firstName: string
 *  lastName: string
 *  grecaptchaResponse: string
 *  message: string
 *  objetMessage: string
 *  objetMessagePrecision: string
 *  orderId: string
 *  phone: string
 *  precisionMessage: string
 *  title: string
 * }} APIContactUs
 */

/**
 * @typedef {{
 *  address: string
 *  companyName: string
 *  email: string
 *  fileContent: string
 *  fileName: string
 *  firstName: string
 *  grecaptchaResponse: string
 *  lastName: string
 *  message: string
 *  objetMessage: string
 *  objetMessagePrecision: string
 *  orderDate: string
 *  orderId: string
 *  phone: string
 *  postalCode: string
 *  title: string
 *  town: string
 * }} APIRetractation
 */

/**
 * @typedef {{
 *  additionalInfo: string
 *  birthDate: string
 *  companyName: string
 *  country: APICountry
 *  customerType: string
 *  email: string
 *  firstName: string
 *  lastName: string
 *  line1: string
 *  line2: string
 *  password: string
 *  phone: string
 *  postalCode: string
 *  region: APIRegion
 *  titleCode: string
 *  town: string
 *  uid: string
 * }} APIUserSignUp
 */

/**
 * @typedef {{
 *  availableDates: string[]
 *  excludedDates: string[]
 *  firstDate: string
 *  lastDate: string
 *  toDay: string
 * }} APIExcludedDateList
 */
