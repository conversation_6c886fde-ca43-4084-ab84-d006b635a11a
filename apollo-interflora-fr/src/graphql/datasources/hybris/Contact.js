const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Contact endpoints.
 *
 * @class
 * @extends Hybris
 */
class ContactDataSource extends Hybris {
  /**
   * @returns {Promise<APITicketSubject[]>}
   */
  async getSubjects() {
    const response = await this.get(`${this._getBasePath()}/getTicketSubject`)

    return (response && response.ticketSubjectList) || []
  }

  /**
   * @param {String} orderId
   * @param {String} email
   * @returns {Promise<boolean>}
   */
  async isCommandAttachedToEmail(orderId, email) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/checkOrderEmail`, {
      params: {
        email,
        orderId,
      },
    })

    return response.result
  }

  /**
   * @param {APIContactUs} contactMessage
   * @returns {Promise<void>}
   */
  sendContact(contactMessage) {
    return this.post(this._getBasePath(), { body: contactMessage })
  }

  /**
   * @param {APIRetractation} retractation
   * @returns {Promise<APIRetractation>}
   */
  sendRetractation(retractation) {
    return this.post(`${this._getBasePath()}/retractation`, { body: retractation })
  }

  /**
   * Get the base Contact API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/contactus`
  }
}

ContactDataSource.keyName = 'contact'

module.exports = ContactDataSource
