const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Address endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const address = await context.dataSources.hybrisAddress.getAddress(addressId)
 */
class AddressDataSource extends Hybris {
  /**
   * Get all user addresses.
   * @param {string} [cartId]
   * @returns {Promise<APIAddress[]>}
   */
  async getAll(cartId) {
    const params = new URLSearchParams()

    if (cartId) params.set('cartId', cartId)

    const response = await this.get(this._getBasePath(), { params })

    return response.addresses || []
  }

  /**
   * Get an Address from Hybris.
   * @param {string} addressId
   * @returns {Promise<APIAddress>}
   */
  getAddress(addressId) {
    return this.get(`${this._getBasePath()}/${addressId}`)
  }

  /**
   * Add an Address to Hybris.
   * @param {APIAddress} address
   * @returns {Promise<APIAddress>}
   */
  addAddress(address) {
    return this.post(this._getBasePath(), { body: address })
  }

  /**
   * Update an Address from Hybris.
   * @param {APIAddress} address
   * @returns {Promise<APIAddress>}
   */
  async updateAddress(address) {
    await this.put(`${this._getBasePath()}/${address.id}`, { body: address })

    return this.getAddress(address.id)
  }

  /**
   * Remove an Address from Hybris.
   * @param {string} addressId
   * @returns {Promise<APIAddress>}
   */
  async removeAddress(addressId) {
    const address = await this.getAddress(addressId)
    await this.delete(`${this._getBasePath()}/${addressId}`)

    return address
  }

  /**
   * Get the base Address API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/users/${ctx.user}/addresses`
  }
}

module.exports = AddressDataSource
