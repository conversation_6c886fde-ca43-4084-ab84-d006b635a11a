const Hybris = require('./Hybris')

const { dates } = require('../../../helpers')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Interflora endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const messages = await context.dataSources.hybrisInterflora.getSuggestedMessages(codeOccasion)
 */
class InterfloraDataSource extends Hybris {
  /**
   * @param {string} orderId
   * @returns {Promise<APIFeasibility>}
   */
  async checkFeasibility(orderId) {
    return this.get(`${this._getBasePath()}/checkFeasibility/${orderId}`)
  }

  /**
   * Return true if the account is valid otherwise return message error.
   *
   * @param {string} twitterId
   * @returns {Promise<boolean>}
   */
  async checkTwitterId(twitterId) {
    const response = await this.get(`${this._getBasePath()}/checkTwitterId`, { params: { twitterId } })

    return response.result || false
  }

  /**
   * @returns {Promise<APIOrderReason[]>}
   */
  async getAllActiveOccasions() {
    const response = await this.get(`${this._getBasePath()}/getAllActiveOccasions`)

    return Array.isArray(response.occasions) ? response.occasions : []
  }

  /**
   * @returns {Promise<Object<string, any>>}
   */
  async getConfigs() {
    return this.get(`${this._getBasePath()}/getInterfloraConfigs`)
  }

  /**
   * Get the suggested messages based on occasion code.
   * @param {string} codeOccasion
   * @returns {Promise<{key: string, value: string}[]>}
   */
  async getSuggestedMessages(codeOccasion) {
    const response = await this.get(`${this._getBasePath()}/getSuggestedMessages`, {
      params: {
        codeOccasion,
      },
    })

    return response.suggestedMessages || []
  }

  /**
   * Get Celebration Text for the given product.
   * @param {string} productCode
   * @param {string} countryId
   * @returns {Promise<string[]>}
   */
  async getCelebrationTexts(productCode, countryId) {
    const response = await this.get(`${this._getBasePath(countryId)}/getCelebrationTexts`, { params: { productCode } })

    return response.texts || []
  }

  /**
   * Get delivery time interavals for interflora or bebloom product.
   * @param {Date} [date]
   * @param {string} [productCode]
   * @returns {Promise<APICeremonyHourRange>}
   */
  getCeremonyHourRange(date, productCode) {
    const params = new URLSearchParams()

    if (date) {
      params.append('deliveryDate', dates.formatToAPI(date))
    }

    if (productCode) {
      params.append('productCode', productCode)
    }

    return this.get(`${this._getBasePath()}/getCeremonyHourRange`, { params })
  }

  /**
   * @param {string} [productCode]
   * @param {string} [postalCode]
   * @param {string} [countryId]
   * @returns {Promise<APIExcludedDateList>}
   */
  getDeliveryDates(productCode, postalCode, countryId) {
    const params = new URLSearchParams({})

    if (productCode) params.append('productCode', productCode)
    if (postalCode) params.append('postalCode', postalCode)

    return this.get(`${this._getBasePath(countryId)}/getDeliveryDates`, { params })
  }

  /**
   * Get delivery time intervals for interflora product.
   * NB: bebloom = interflora "atelier" product
   *
   * @param {Date} deliveryDate
   * @param {string} [occasion]
   * @param {string} [postalCode]
   * @param {string} [productCode]
   * @returns {Promise<APIMomentLivraisonList>}
   */
  getDeliveryTimeIntervals(deliveryDate, occasion, postalCode, productCode) {
    const params = { deliveryDate: dates.formatToAPI(deliveryDate) }

    if (occasion) params.occasion = occasion
    if (productCode) params.productCode = productCode
    if (postalCode) params.postalCode = postalCode

    return this.get(`${this._getBasePath()}/getDeliveryTimeIntervals`, { params })
  }

  /**
   * @param {string} userId
   * @return {Promise}
   */
  itfPlusExtend(userId) {
    const params = new URLSearchParams({
      userId,
    })
    return this.put(`${this._getBasePath()}/interfloraPlus/extend?${params.toString()}`)
  }

  /**
   * @param {string} userId
   * @return {Promise}
   */
  itfPlusSubscribe(userId) {
    const params = new URLSearchParams({
      userId,
    })
    return this.put(`${this._getBasePath()}/interfloraPlus/subscription?${params.toString()}`)
  }

  /**
   * @param {string} email
   * @returns {Promise<boolean>}
   */
  async verifyEmailHasRegisteredUser(email) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/verifyEmailHasRegisteredUser`, { params: { email } })

    return response.result || false
  }

  /**
   * @param {string} email
   * @returns {Promise<boolean>}
   */
  async verifyEmailHasPremiumUser(email) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/verifyEmailHasPremiumUser`, { params: { email } })

    return response.result || false
  }

  /**
   * @param {string} address
   * @param {string} town
   * @param {string} postalCode
   * @param {string} deliveryDate DD-MM-YYYY format
   * @param {string} moment Hybris moments, known values: 'A', 'J', 'M', 'S'
   * @param {string[]} variantSkusList Hybris product SKU e.g 1-VAFR-1
   * @param {string} productType Commercetools product types e.g "mourning", "warehouse", "plants"
   * @param {number} price Float price
   * @param {string} warehouseDeliveryData String returned by delivery company API (in case of FR warehouse products)
   * @param {string} cartId
   * @returns {Promise<APIFeasibility>}
   */
  async checkCartFeasibility(
    address,
    town,
    postalCode,
    deliveryDate,
    moment,
    variantSkusList,
    productType,
    price,
    warehouseDeliveryData = null,
    cartId
  ) {
    const body = {
      address,
      cartId,
      country: 'FR',
      dateLivraison: deliveryDate,
      horaireLibre: warehouseDeliveryData,
      iflora: false,
      moment,
      postalCode,
      productIdList: variantSkusList,
      productType: productType,
      totalPrice: price,
      town,
    }
    return this.post(`v2/fr/checkFeasibility`, { body })
  }

  /**
   * Get the base Interflora API path.
   * @private
   * @param {string} countryId
   * @returns {string}
   */
  _getBasePath(countryId) {
    const ctx = this.getContextInfo()
    let baseSiteId = ctx.baseSiteId

    if (countryId) {
      baseSiteId = ctx.baseSiteId.includes('-') ? ctx.baseSiteId.replace(/-[A-Z]+$/, `-${countryId}`) : countryId
    }

    return `v2/${baseSiteId}`
  }
}

module.exports = InterfloraDataSource
