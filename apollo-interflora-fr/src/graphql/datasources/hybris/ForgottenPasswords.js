const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Forgotten Passwords endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  await context.dataSources.hybrisForgottenPasswords.resetPassword(...)
 */
class ForgottenPasswordsDataSource extends Hybris {
  /**
   * Generates a token to restore a customer's forgotten password and send it by email.
   * @param {string} userId
   * @returns {Promise<void>}
   */
  async sendResetToken(userId) {
    /** Use this endpoint as anonymous user */
    const { access_token } = await this.context.dataSources.hybrisAuth.login()

    const params = new URLSearchParams({ userId })

    return this.post(`${this._getBasePath()}/forgottenpasswordtokens`, {
      headers: { authorization: `Bearer ${access_token}` },
      params,
    })
  }

  /**
   * @param {string} token
   * @param {string} newPassword
   * @returns {Promise<void>}
   */
  async resetPassword(token, newPassword) {
    /** Use this endpoint as anonymous user */
    const { access_token } = await this.context.dataSources.hybrisAuth.login()

    const params = new URLSearchParams({ newPassword, token })

    return this.post(`${this._getBasePath()}/resetpassword`, {
      headers: { authorization: `Bearer ${access_token}` },
      params,
    })
  }

  /**
   * @param {string} token
   * @returns {Promise<void>}
   * @throws
   */
  async validateResetToken(token) {
    /** Use this endpoint as anonymous user */
    const { access_token } = await this.context.dataSources.hybrisAuth.login()

    const params = new URLSearchParams({ token })

    return this.post(`${this._getBasePath()}/validatepasswordtoken`, {
      headers: { authorization: `Bearer ${access_token}` },
      params,
    })
  }

  /**
   * Get the base API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}`
  }
}

module.exports = ForgottenPasswordsDataSource
