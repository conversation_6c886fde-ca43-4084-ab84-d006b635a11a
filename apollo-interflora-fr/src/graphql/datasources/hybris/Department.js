const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Department endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const departments = await context.dataSources.hybrisDepartment.getDepartments()
 */
class DepartmentDataSource extends Hybris {
  /**
   * Returns list of departements.
   * @returns {Promise<APIFloraDepartmentList>}
   */
  getDepartments() {
    return this.get(this._getBasePath())
  }

  /**
   * Returns details of a departement.
   * @param {string} departmentCode
   * @returns {Promise<APIFloraDepartment>}
   */
  getDepartment(departmentCode) {
    return this.get(`${this._getBasePath()}/${departmentCode}`)
  }

  /**
   * Returns list of department towns.
   * @param {string} departmentCode
   * @returns {Promise<APIFloraCityList>}
   */
  getCities(departmentCode) {
    return this.get(`${this._getBasePath()}/${departmentCode}/cities`)
  }

  /**
   * Return a department city.
   * @param {string} departmentCode
   * @param {string} cityName
   * @returns {Promise<APIFloraCity>}
   */
  getCity(departmentCode, cityName) {
    return this.get(`${this._getBasePath()}/${departmentCode}/${cityName}`, {
      params: { fields: 'DEFAULT,contentTer' },
    })
  }

  /**
   * Get the base Department API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/departements`
  }
}

module.exports = DepartmentDataSource
