const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Countries/Towns endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const countries = await context.dataSources.hybrisGeo.getCountries()
 */
class GeoDataSource extends Hybris {
  /**
   * Get a list of countries from Hybris.
   * @param {boolean} includeFR
   * @param {boolean} allCountries
   * @returns {Promise<APICountry[]>}
   */
  async getCountries(includeFR = true, allCountries = false) {
    const response = await this.get(this._getBaseCountriesPath(), { params: { allCountries, includeFR } })

    return response.countries
  }

  /**
   * Get a list of towns based on a query term from Hybris.
   * @param {string} [search]
   * @param {string} [countryIsoCode]
   * @param {string} productCode
   * @returns {Promise<APILocalite[]>}
   */
  async getTowns(search = '', countryIsoCode, productCode) {
    const searches = [search]
    if (/\s+/.test(search.trim())) {
      const newSearch = search.replace(/\s+/g, '')
      if (newSearch.length >= 3) {
        searches.push(newSearch)
      }
    }

    for (const term of searches) {
      const params = { fields: 'FULL', term }

      if (countryIsoCode) params.countryIsoCode = countryIsoCode
      if (productCode) params.productCode = productCode

      const response = await this.get(this._getBaseTownsPath(), { params })
      if ((response.localites ?? []).length) {
        return response.localites
      }
    }

    return []
  }

  /**
   * Find a town from Hybris from its ID
   * @param {string} townId
   * @returns {Promise<APILocalite | undefined>}
   */
  async findTown(townId) {
    const [townPart] = townId.split('_')
    const response = await this.get(this._getBaseTownsPath(), { params: { term: townPart.toLowerCase() } })

    return response.localites.find((localite) => localite.isoCode === townId)
  }

  /**
   * Get the base Countries API path.
   * @private
   * @returns {string}
   */
  _getBaseCountriesPath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/countries`
  }

  /**
   * Get the base Towns API path.
   * @private
   * @returns {string}
   */
  _getBaseTownsPath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/getRegions`
  }
}

module.exports = GeoDataSource
