const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Catalog endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const category = await context.dataSources.hybrisCatalog.getCategory(categoryId)
 */
class CatalogDataSource extends Hybris {
  /**
   * Get a category from Hybris.
   * @param {string} categoryId
   * @returns {Promise<APICategory>}
   */
  getCategory(categoryId) {
    return this.get(`${this._getBasePath()}/${categoryId}`, { params: { fields: 'FULL' } })
  }

  /**
   * Get products for a given occasion from Hybris.
   * @param {string} occasion
   * @param {string} [dateLivraison]
   * @param {string} [moment]
   * @param {string} [localite]
   * @param {string} [sort]
   * @param {number} [currentPage]
   * @param {string} [showMode] Available values: Page / All
   * @param {number} [pageSize]
   * @param {object} [categoryFilterData]
   * @returns {Promise<APIProductSearchPage>}
   */
  getOccasionProducts(
    occasion,
    dateLivraison,
    moment,
    localite,
    sort,
    currentPage = 0,
    showMode = 'Page',
    pageSize = 20,
    categoryFilterData = {}
  ) {
    const params = { currentPage, fields: 'FULL', pageSize, showMode }

    if (dateLivraison) params.dateLivraison = dateLivraison
    if (moment) params.moment = moment
    if (localite) params.localite = localite
    if (sort) params.sort = sort

    return this.get(`${this._getBasePath()}/${occasion}/products`, {
      body: { categoryFilterData },
      params,
    })
  }

  /**
   * Get the base Catalog API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/categories`
  }
}

module.exports = CatalogDataSource
