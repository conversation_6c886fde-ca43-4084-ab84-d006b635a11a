const Hybris = require('./Hybris')
const CONST = require('../../../const')

const { randomUUID } = require('node:crypto')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Cart endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const validateRegion = await context.dataSources.hybrisCart.validateRegion()
 */
class CartDataSource extends Hybris {
  /**
   * Get a cart with a given identifier.
   * @param {string} [id]
   * @returns {Promise<APICart>}
   */
  getCart(id) {
    // nb: we need the ts to force the dataSource to not use its cache
    // else, changing a product will not be reflected in the output (calls getCart twice in the same request)
    return this.get(this._getBaseCartIdPath(id), {
      params: {
        fields:
          'DEFAULT,entries(DEFAULT,basePrice,product(DEFAULT,addMode,bebloom,description,legalNotices,maxOrderQuantity,productType,url),updateable),user(name,uid)',
        t: Date.now(),
      },
    })
  }

  /**
   * Creates a cart for a user or restore an anonymous cart.
   * @param {APICreateCart} cartDto Optional if `cartGuid` is provided
   * @param {string} [cartGuid]
   * @returns {Promise<APICart>}
   */
  createCart(cartDto, cartGuid) {
    const params = new URLSearchParams({ fields: 'FULL' })

    if (cartGuid) params.append('cartGuid', cartGuid)

    return this.post(`${this._getBaseCartPath()}?${params.toString()}`, { body: cartDto })
  }

  /**
   * Update a cart for a user with a given identifier.
   * @param {APICreateCart} cartDto
   * @returns {Promise<APICart>}
   */
  updateCart(cartDto) {
    const params = new URLSearchParams({ fields: 'FULL' })

    return this.put(`${this._getBaseCartIdPath()}?${params.toString()}`, { body: cartDto })
  }

  /**
   * Sets a billing address for the cart.
   * @param {string} addressId
   * @returns {Promise<void>}
   */
  setBillingAddress(addressId) {
    const body = new URLSearchParams({ addressId })

    return this.put(`${this._getBaseCartIdPath()}/billingAddress`, { body })
  }

  /**
   * check the feasibility of the cart.
   * @returns {Promise<APIFeasibility>}
   */
  checkFeasibility() {
    return this.get(`${this._getBaseCartIdPath()}/checkFeasibility`, {
      params: new URLSearchParams({
        _t: randomUUID(), // random param to force skip fasterize cache
      }),
    })
  }

  /**
   * Check if iflora is Authorized for the current cart.
   * @returns {Promise<boolean>}
   */
  async checkIfloraAuthorized() {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBaseCartIdPath()}/checkIfloraAuthorized`)

    return response.result || false
  }

  /**
   * Get a cart with a given identifier.
   * @param {string} [cartId]
   * @param {string} [deliveryFeesId]
   * @returns {Promise<APIPremiumProduct>}
   */
  checkPremiumProduct(cartId, deliveryFeesId) {
    const params = new URLSearchParams({
      ...(deliveryFeesId ? { deliveryFeesId } : {}),
    })
    return this.get(`${this._getBaseCartIdPath(cartId)}/checkPremiumProduct?${params.toString()}`)
  }

  /**
   * Delete the delivery address from the cart.
   * @returns {Promise<void>}
   */
  deleteDeliveryAddress() {
    return this.delete(`${this._getBaseCartIdPath()}/deliveryAddress`)
  }

  /**
   * Sets a delivery address for the cart.
   * @param {string} addressId
   * @returns {Promise<void>}
   */
  updateDeliveryAddress(addressId) {
    const body = new URLSearchParams({ addressId })

    return this.put(`${this._getBaseCartIdPath()}/deliveryAddress`, { body })
  }

  /**
   * Authorizes the cart and places the order. The response contains payment result and the new order data if payment was successful.
   * @param {APIPaymentDetails} paymentDetails
   * @param {APIDSP2Params} [dsp2]
   * @returns {Promise<APIPaymentResult>}
   */
  doPaymentAndPlaceOrder(paymentDetails, dsp2 = {}) {
    const params = new URLSearchParams({
      fields: 'FULL,order(FULL,appliedVouchers(FULL),billingAddress(FULL),orderType,paymentInfo(FULL)),token',
      ...dsp2,
    })

    return this.post(`${this._getBaseCartIdPath()}/doPaymentAndPlaceOrder?${params.toString()}`, {
      body: paymentDetails,
    })
  }

  /**
   * Add cart entry.
   * @param {string} productCode
   * @param {boolean} isFromGoogleShopping
   * @param {number} [qty]
   * @param {string} [category]
   * @param {string} [messageRubanDeuil]
   * @returns {Promise<APICartModification>}
   */
  addCartEntry(productCode, isFromGoogleShopping = false, qty = 1, category, messageRubanDeuil) {
    const body = new URLSearchParams({ code: productCode, qty })

    if (category) body.append('category', category)
    if (messageRubanDeuil) body.append('messageRubanDeuil', messageRubanDeuil)
    body.append('fromGoogleShopping', isFromGoogleShopping)

    return this.post(`${this._getBaseCartIdPath()}/entries`, { body })
  }

  /**
   * Despite the naming, retrieves the delivery modes for the current cart
   * @param {string} [cartId]
   * @returns {Promise<string[]>}
   */
  async checkoutModes(cartId) {
    const response = await this.get(`${this._getBaseCartIdPath(cartId)}/checkoutModes`)
    return response?.checkoutMode || []
  }

  /**
   * Deletes cart entry.
   * @param {number} entryNumber
   * @returns {Promise<void>}
   */
  deleteCartEntry(entryNumber) {
    return this.delete(`${this._getBaseCartIdPath()}/entry/${entryNumber}`)
  }

  /**
   * @param {string} cartId
   * @param {object} body
   * @returns {Promise<APIOrder>}
   */
  doPlaceOder(cartId, body) {
    /* transactionStatus : Enum  ACCEPTED, ERROR, REJECTED, REVIEW; */
    return this.post(`${this._getBaseCartIdPath(cartId)}/doPlaceOrder`, { body })
  }

  /**
   * Set quantity and store details of a cart entry.
   * Updates the quantity of a single cart entry and the details of the store where the cart entry will be picked up. Attributes not provided in request will be defined again (set to null or default).
   * @param {number} entryNumber
   * @param {number} [qty]
   * @param {string} [category]
   * @param {string} [messageRubanDeuil]
   * @returns {Promise<APICartModification>}
   */
  updateCartEntry(entryNumber, qty, category, messageRubanDeuil) {
    const body = new URLSearchParams()

    if (qty) body.append('qty', qty)
    if (category) body.append('category', category)
    if (messageRubanDeuil) body.append('messageRubanDeuil', messageRubanDeuil)

    return this.put(`${this._getBaseCartIdPath()}/entry/${entryNumber}`, { body })
  }

  /**
   * Set the cart as "TOMB" for the delivery
   * Under the hood, add the proper accessory to the cart to indicate that it is a tomb delivery
   * @param {boolean} isFromGoogleShopping
   * @returns {Promise<APICartModification>}
   */
  async setTombDelivery(isFromGoogleShopping = false) {
    return this.addCartEntry(CONST.cart.accessoriesIds.TOMB_ACCESSORY_ID, isFromGoogleShopping, 1)
  }

  /**
   * Remove the cart delivery as "TOMB"
   * Under the hood, remove the proper accessory from the cart
   * @param {APICart} apiCart
   * @returns {Promise<void>}
   */
  async removeTombDelivery(apiCart) {
    if (!Array.isArray(apiCart.entries)) return null

    const tombAccessoryIndex = apiCart.entries.findIndex(
      (entry) => entry.product.code === CONST.cart.accessoriesIds.TOMB_ACCESSORY_ID
    )

    if (tombAccessoryIndex < 0) return null

    return this.deleteCartEntry(tombAccessoryIndex)
  }

  /**
   * Get available paymentMethods for cart.
   * @param {string} [cartId]
   * @returns {Promise<APIPaymentMethod[]>}
   */
  async getPaymentMethods(cartId) {
    const response = await this.get(`${this._getBaseCartIdPath(cartId)}/paymentMethods`)

    return response.paymentMethods || []
  }

  /**
   * Return true if the cart is valid for payment, otherwise return false.
   * @returns {Promise<boolean>}
   */
  async validateBeforePayment() {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBaseCartIdPath()}/validateBeforePayment`)

    return response.result || false
  }

  /**
   * Validate the cart region for a given postal code.
   * @param {string} postalCode
   * @param {string} [cartId]
   * @returns {Promise<APIValidateLocalite>}
   */
  validateRegion(postalCode, cartId) {
    return this.get(`${this._getBaseCartIdPath(cartId)}/validateRegion`, {
      params: {
        postalCode,
      },
    })
  }

  /**
   * Verify payment response and place order.
   * @param {string} urlQuery
   * @returns {Promise<APIPaymentResult>}
   */
  verifyPaymentResponseAndPlaceOrder(urlQuery) {
    return this.post(
      `${this._getBasePath()}/verifyPaymentResponseAndPlaceOrder?cartId=${this.getContextInfo().cartId}&${urlQuery}`,
      {
        headers: {
          'Content-type': 'application/json',
        },
      }
    )
  }

  /**
   * Applies a voucher based on the voucherId defined for the cart.
   * @param {string} voucherId
   * @returns {Promise<APICart>}
   */
  applyVoucher(voucherId) {
    const body = new URLSearchParams({ voucherId })

    return this.post(`${this._getBaseCartIdPath()}/vouchers`, { body })
  }

  /**
   * Removes a voucher based on the voucherId defined for the current cart.
   * @param {string} voucherId
   * @returns {Promise<APICart>}
   */
  deleteVoucher(voucherId) {
    return this.delete(`${this._getBaseCartIdPath()}/vouchers`, { params: { voucherId } })
  }

  /**
   * Sets a payment identifier for the cart.
   * @param {string} paymentId
   * @param {string} [cartId]
   * @returns {Promise<APIResult>}
   */
  async setPaymentIdentifier(paymentId, cartId) {
    const params = new URLSearchParams({ paymentIdentifier: paymentId })
    return this.put(`${this._getBaseCartIdPath(cartId)}/paymentIdentifier?${params.toString()}`)
  }

  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/users/${ctx.user}`
  }

  /**
   * Get the base Cart API path.
   * @private
   * @returns {string}
   */
  _getBaseCartPath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/users/${ctx.user}/carts`
  }

  /**
   * Get the base Cart Scoped API path.
   * @private
   * @param {string} [cartId]
   * @returns {string}
   */
  _getBaseCartIdPath(cartId) {
    const ctx = this.getContextInfo()
    let _cartId = ctx.cartId || 'current'
    if (cartId) {
      if (ctx.user !== 'anonymous') {
        _cartId = cartId.slice(0, 10)
      } else {
        _cartId = cartId
      }
    }

    let baseSiteId = ctx.baseSiteId
    if (ctx.cartCountryId) {
      baseSiteId = ctx.baseSiteId.includes('-')
        ? ctx.baseSiteId.replace(/-[A-Z]+$/, `-${ctx.cartCountryId}`)
        : ctx.cartCountryId
    }

    return `v2/${baseSiteId}/users/${ctx.user}/carts/${_cartId}`
  }
}

module.exports = CartDataSource
