const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Order endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const orders = await context.dataSources.hybrisOrder.getOrderHistory(...)
 */
class OrderDataSource extends Hybris {
  /**
   * Reject ITF Order.
   * @param {string} orderHashCode
   * @returns {Promise<void>}
   */
  rejectIflOrder(orderHashCode) {
    return this.post(`${this._getBasePath()}/ifl/reject/${orderHashCode}`)
  }

  /**
   * Returns specific order details for iflora based on a iflora hash code.
   * The response contains detailed order information.
   * @param {string} orderHashCode
   * @returns {Promise<APIOrder>}
   */
  getIflOrder(orderHashCode) {
    return this.get(`${this._getBasePath()}/ifl/${orderHashCode}`, { params: { fields: 'FULL' } })
  }

  /**
   * Add the order with iflora hash code and complete the place order process.
   * @param {string} orderHashCode
   * @param {APIUpdateOrder} updateOrder
   * @param {boolean} [confirmOrder]
   * @returns {Promise<APIOrder>}
   */
  addIflOrder(orderHashCode, updateOrder, confirmOrder = true) {
    return this.post(`${this._getBasePath()}/ifl/${orderHashCode}?confirmOrder=${confirmOrder}&fields=FULL`, {
      body: updateOrder,
    })
  }

  /**
   * Update the order with iflora hash code and complete the place order process.
   * @param {string} orderHashCode
   * @param {APIUpdateOrder} updateOrder
   * @param {boolean} [confirmOrder]
   * @returns {Promise<APIOrder>}
   */
  updateIflOrder(orderHashCode, updateOrder, confirmOrder = false) {
    return this.put(`${this._getBasePath()}/ifl/${orderHashCode}?confirmOrder=${confirmOrder}&fields=FULL`, {
      body: updateOrder,
    })
  }

  /**
   * Get order with order code and sender email (used for order tracking).
   * @param {string} email
   * @param {string} orderCode
   * @returns {Promise<APIOrder>}
   */
  getOrderTracking(email, orderCode) {
    return this.get(`${this._getBasePath()}/tracking/${orderCode}`, { params: { email } })
  }

  /**
   * Returns order history data for all orders placed by a specified user for a specified base store.
   * The response can display the results across multiple pages, if required.
   * @param {string} statuses Filters only certain order statuses. For example, statuses=ATM,ATC would only return orders with status ATM or ATC.
   * @param {number} currentPage
   * @param {number} pageSize
   * @param {number} age
   * @param {string} sort
   * @returns {Promise<APIOrderHistoryList>}
   */
  getOrderHistory(statuses, currentPage = 0, pageSize = 20, age = 12, sort) {
    return this.get(this._getBaseUserPath(), {
      params: {
        age,
        currentPage,
        fields: 'FULL',
        pageSize,
        sort,
        statuses,
      },
    })
  }

  /**
   * Verify the payment of the cart and places the order.
   * The response contains the new order data.
   * @param {string} cartId
   * @param {string} [paymentToken]
   * @param {string} [paymentProvider]
   * @returns {Promise<APIUpdateOrder>}
   */
  createOrder(cartId, paymentToken, paymentProvider) {
    const body = new URLSearchParams({ cartId })

    if (paymentToken) body.append('paymentToken', paymentToken)
    if (paymentProvider) body.append('paymentProvider', paymentProvider)

    return this.post(this._getBaseUserPath(), { body })
  }

  /**
   * Returns specific order details based on a specific order code.
   * The response contains detailed order information.
   * @param {string} code
   * @returns {Promise<APIOrder>}
   */
  getOrder(code) {
    return this.get(`${this._getBaseUserPath()}/${code}`, {
      params: {
        fields: 'DEFAULT,paymentInfo(cardType(FULL),payementMode)', // the typo is intentional (is on hybris side)
      },
    })
  }

  /**
   * Returns specific voucher order confirmation details based on a specific order code.
   * The response contains detailed voucher information.
   * @param {string} code
   * @returns {Promise<APIVoucher>}
   */
  getOrderConfirmationVoucher(code) {
    return this.get(`${this._getBaseUserPath()}/${code}/getOrderConfirmationVoucher`, { params: { fields: 'FULL' } })
  }

  /**
   * Returns order history data for all orders placed by a specified user for a specified base store.
   * The response can display the results across multiple pages, if required.
   * @param {string} userId
   * @param {Number} currentPage
   * @param {Number} pageSize
   * @param {string[]} statuses
   * @returns {Promise<APIOrderHistoryList>}
   */
  getUserOrders(userId, currentPage = 0, pageSize = 100, statuses = []) {
    return this.get(this._getBaseUserPath(userId), {
      params: {
        currentPage,
        pageSize,
        ...(statuses.length ? { statuses } : {}),
      },
    })
  }

  /**
   * @param {string} code
   * @returns {Promise<APIVoucher>}
   */
  getVoucher(code) {
    return this.get(`${this._getBaseVouchersPath()}/${code}`)
  }

  /**
   * @param {string} code
   * @param {string} password
   * @returns {Promise<APIToken>}
   */
  loginAndAttachOrder(code, password) {
    const body = new URLSearchParams({
      password,
    })

    return this.post(`${this._getBaseUserPath()}/${code}/loginAndAttachOrder`, { body })
  }

  /**
   * @param {string} code
   * @returns {Promise<boolean>}
   */
  async canAttachOrder(code) {
    const response = await this.get(`${this._getBaseUserPath()}/${code}/canAttachOrder`)

    return response.result || false
  }

  /**
   * Update an order with order code.
   * @param {string} code
   * @param {APIUpdateOrder} updateOrder
   * @returns {Promise<APIOrder>}
   */
  updateOrder(code, updateOrder) {
    return this.put(`${this._getBaseUserPath()}/${code}`, { body: updateOrder })
  }

  /**
   * Update an order occasion with order code.
   * @param {string} code
   * @param {string} codeOccasion
   * @returns {Promise<APIOrder>}
   */
  updateOrderOccasion(code, codeOccasion) {
    const body = new URLSearchParams({ codeOccasion })

    return this.post(`${this._getBaseUserPath()}/${code}/occasion`, { body })
  }

  /**
   * Get the base Order API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/orders`
  }

  /**
   * Get the base Order (User scoped) API path.
   * @private
   * @param {string} [userId] - if not given, will user context user
   * @returns {string}
   */
  _getBaseUserPath(userId) {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/users/${userId || ctx.user}/orders`
  }

  /**
   * Get the base Vouchers API path.
   * @private
   * @returns {string}
   */
  _getBaseVouchersPath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/vouchers`
  }
}

module.exports = OrderDataSource
