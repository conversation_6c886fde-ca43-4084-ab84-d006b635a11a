const _ = require('lodash')

const config = require('../../../config')

const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with <PERSON>ybris Auth endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const address = await context.dataSources.hybrisAuth.login(username, password)
 */
class AuthDataSource extends Hybris {
  /**
   * @param {string} [username]
   * @param {string} [password]
   * @returns {Promise<(APIToken | APIUserToken)>}
   */
  login(username, password) {
    // Handle anonymous login
    let grant_type = 'password'

    if (!username && !password) {
      grant_type = 'client_credentials'
    }

    const body = this.getQueryString({ grant_type, password, username })
    return this.post('oauth/token', {
      body,
    })
  }

  /**
   * @param {string} refreshToken
   * @returns {Promise<APIToken>}
   */
  refreshToken(refreshToken) {
    const qs = this.getQueryString({ grant_type: 'refresh_token', refresh_token: refreshToken })
    return this.post('oauth/token', { body: qs })
  }

  /**
   * @param {Object<string, any>} [options]
   * @returns {URLSearchParams}
   * @private
   */
  getQueryString(options = {}) {
    const { clientId, clientSecret } = config.interflora

    const qs = new URLSearchParams({
      client_id: clientId,
      client_secret: clientSecret,
      ..._.pickBy(options, _.identity),
    })

    return qs
  }
}

module.exports = AuthDataSource
