const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Newsletter endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const isSubscribed = await context.dataSources.hybrisNewsletter.subscribe(email)
 */
class NewsletterDataSource extends Hybris {
  /**
   * Subscribe email to the newsletter.
   * @param {string} email
   * @returns {Promise<boolean>}
   */
  async subscribe(email) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/subscription`, { params: { email } })

    return response.result
  }

  /**
   * Unsubscribe email from the newsletter.
   * @param {string} email
   * @returns {Promise<boolean>}
   */
  async unsubscribe(email) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/unsubscription`, { params: { email } })

    return response.result
  }

  /**
   * Unsubscribe email from the cart relaunch.
   * @param {string} idpanier
   * @param {string} pkpanier
   * @returns {Promise<boolean>}
   */
  async unsubscribeRelancePanier(idpanier, pkpanier) {
    /** @type {APIResult} */
    const response = await this.get(`${this._getBasePath()}/unsubscriptionRelancePanier`, {
      params: { idpanier, pkpanier },
    })

    return response.result
  }

  /**
   * Get the base Newsletter API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/newsletter`
  }
}

module.exports = NewsletterDataSource
