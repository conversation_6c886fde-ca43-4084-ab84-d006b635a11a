const _ = require('lodash')

const { dates } = require('../../../helpers')

const Hybris = require('./Hybris')

/** @typedef {import("./_typedef")} */

/**
 * A Data Source service to interact with Hybris Cms endpoints.
 *
 * @class
 * @extends Hybris
 * @example
 *  // In a GraphQL resolver
 *  const pages = await context.dataSources.hybrisCms.getPages()
 */
class CmsDataSource extends Hybris {
  /**
   * Given a component identifier, return cms component data.
   * @param {'ContentPage' | 'ProductPage' | 'CategoryPage' | 'CatalogPage' | 'DepartmentPage' | 'CityPage' | 'CitiesPage'} [pageType]
   * @param {string} [pageLabelOrId] Page label or id
   * @param {string} [code] If pageType is ProductPage, code should be product code; if pageType is CategoryPage, code should be category code; if pageType is CatalogPage, code should be catalog code
   * @returns {Promise<APICMSPage>}
   */
  getPage(pageType = 'ContentPage', pageLabelOrId, code) {
    const params = { fields: 'FULL', pageType }

    if (pageLabelOrId) params.pageLabelOrId = pageLabelOrId
    if (code) params.code = code

    return this.get(`${this._getBasePath()}/pages`, { params })
  }

  /**
   * Given a page identifier, return the page data with a list of cms content slots, each of which contains a list of cms component data.
   * @param {string} componentId Component identifier
   * @param {string} [catalogCode] Catalog code
   * @param {string} [productCode] Product code
   * @param {string} [categoryCode] Catalog code
   * @returns {Promise<APICMSComponent<{}>>}
   */
  getComponent(componentId, catalogCode, productCode, categoryCode) {
    const params = { fields: 'FULL' }

    if (catalogCode) params.catalogCode = catalogCode
    if (productCode) params.productCode = productCode
    if (categoryCode) params.categoryCode = categoryCode

    return this.get(`${this._getBasePath()}/components/${componentId}`, { params })
  }

  getTownProductInfoComponent() {
    return this.get(`${this._getBasePath()}/getTownProductInfoComponent`)
  }

  /**
   * @returns {Promise<{
   *   id: string,
   *   date: Date,
   *   label: string
   * }|undefined>}
   */
  async getFeteFlorale() {
    const response = await this.get(`${this._getBasePath()}/getFeteFloral`)

    /**
     * response might be an empty object, so we need to make sure
     * it's not empty.
     */
    return !_.isEmpty(response)
      ? {
          ...response,
          date: dates.formatToGQL(response.date),
        }
      : undefined
  }

  /**
   * Get the base Cms API path.
   * @private
   * @returns {string}
   */
  _getBasePath() {
    const ctx = this.getContextInfo()
    return `v2/${ctx.baseSiteId}/cms`
  }
}

module.exports = CmsDataSource
