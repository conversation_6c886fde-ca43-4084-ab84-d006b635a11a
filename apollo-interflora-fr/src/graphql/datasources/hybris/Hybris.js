const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

const { GraphQLHybrisError } = require('../../errors')

/** @typedef {import('../../Context')} GraphQLContext */
/** @typedef {import('./_typedef')} */

/**
 * @class Hybris
 * @abstract
 */
class Hybris extends RESTDataSource {
  constructor() {
    super({ timeout: config.hybris?.timeout })

    /** @type {string} */
    this.anonymousToken
    this.baseURL = config.interflora.host
  }

  /**
   * @param {Error} error
   * @returns {void}
   * @throws
   */
  didEncounterError(error) {
    const response = error?.extensions?.response || {}

    const messages = [error.message]
    if (response?.body?.errors?.[0]) {
      messages.push(response.body.errors[0].message)
    }

    const _error = new GraphQLHybrisError(messages.join(' | '), response.url, response.status)
    _error.stack = error.stack

    for (const err of response?.body?.errors || []) {
      _error.addError(err.type, err.subject, err.message)
    }

    throw _error
  }

  /**
   * @returns {Promise<string>}
   */
  async getAnonymousToken() {
    if (this.anonymousToken) return this.anonymousToken

    const { clientId, clientSecret } = config.interflora
    this.anonymousToken = await this.post('/oauth/token', {
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'client_credentials',
      }),
      headers: {
        'X-is-anonymous-auth-request': 'true',
      },
    })

    // auto-expire token
    setTimeout(() => {
      delete this.anonymousToken
    }, this.anonymousToken.expires_in * 1000 - 30000)

    return this.anonymousToken
  }

  /**
   * Set Authorization header to perform Hybris requests.
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    {
      // Transmit x-forwarded-for to hybris
      let ips = [
        this.context.req.headers['true-client-ip'] || this.context.req.headers['x-real-ip'] || this.context.req.ip,
      ]
      if (this.context.req.headers['x-forwarded-for']) {
        ips = ips.concat(this.context.req.headers['x-forwarded-for'].split(',').map((raw) => raw.trim()))
      }
      request.headers['x-forwarded-for'] = ips.join(', ')
    }

    // Transmit client user agent and accept headers (used by DSP2)
    request.headers['customer-accept'] = this.context.req.headers.accept
    request.headers['customer-user-agent'] = this.context.req.headers['user-agent']

    if (config.site === CONST.site.fr && this.context.auth && !request.headers['x-is-anonymous-auth-request']) {
      if (this.context.session?.hybris?.token) {
        request.headers.authorization = `Bearer ${this.context.session.hybris.token}`
        return
      }

      // returning a promise to trigger the parent awaiting
      return this.getAnonymousToken().then(
        (anonymousToken) => (request.headers.authorization = `Bearer ${anonymousToken.access_token}`)
      )
    }

    if (!request.headers.authorization) {
      const token = this.context.session?.hybris?.token ?? this.context.auth.oauthToken
      if (token) {
        request.headers.authorization = `Bearer ${token}`
      }
    }
  }

  /**
   * Build a data source context based on the GraphQL Context.
   * @returns {DataSourceContext}
   * @protected
   */
  getContextInfo() {
    const { auth, baseSiteId, countryId, siteId, session } = this.context
    const { apiCart } = session || {}

    const _siteId = siteId || config.apps.defaultSiteId
    const _countryId = countryId || (config.apps[_siteId] && config.apps[_siteId].country)

    return {
      baseSiteId,
      cartCountryId: apiCart?.countryId,
      cartId: apiCart ? (auth && auth.isAnonymous ? apiCart.guid : apiCart.code) : null,
      countryId: _countryId,
      siteId: _siteId,
      user: auth && auth.isAnonymous ? 'anonymous' : 'current',
    }
  }
}

module.exports = Hybris
