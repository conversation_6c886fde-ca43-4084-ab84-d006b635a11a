import appConfig from '../../../config'
import jwksClient, { JwksClient } from 'jwks-rsa'
export class SignaturgruppenBroker {
  private client!: JwksClient
  initialize(): void | Promise<void> {
    this.client = jwksClient({
      jwksUri: appConfig.ageVerification.jwksUri,
    })
    return
  }
  fetchSigningKey(kid: string) {
    return new Promise<string>((resolve, reject) => {
      this.client.getSigningKey(kid, (error, key) => {
        if (error) {
          reject(error)
          return
        }
        if (!key) {
          reject(new Error(`Cannot get signing key ${kid}`))
          return
        }
        resolve(key.getPublicKey())
      })
    })
  }
}
