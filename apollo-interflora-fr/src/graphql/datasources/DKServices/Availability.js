const { RESTDataSource } = require('../RESTDataSource')

class DKAvailabilityDatasource extends RESTDataSource {
  async getMoments() {
    return [0, 1, 2]
  }

  checkAvailability(opts) {
    if (opts.codes.length === 0) {
      this.context.log.error(opts, 'checkAvailability: Missing products codes')
      return { Result: false }
    }
    if (!opts.date) {
      this.context.log.error(opts, 'checkAvailability: Missing delivery date')
      return { Result: false }
    }
    if (!opts.postalCode && !opts.province) {
      this.context.log.error(opts, 'checkAvailability: Missing postal code or province')
      return { Result: false }
    }

    return { Result: true, SuggestedDeliveryDate: null }
  }

  async getTowns() {
    return [
      { Name: 'Test address 1', PostalCode: '00001', Province: null },
      { Name: 'Test address 2', PostalCode: '00002', Province: null },
      { Name: 'Test address 3', PostalCode: '00003', Province: null },
    ]
  }

  async getUnavailabilities(opts) {
    if (!opts.code) {
      this.context.log.error(opts, 'getUnavailabilities: Missing product code')
      return []
    }
    if (!opts.postalCode && !opts.province) {
      this.context.log.error(opts, 'getUnavailabilities: Missing postal code or province')
      return []
    }
    const now = new Date()

    return Array(10)
      .fill(0)
      .map((_, index) => {
        return new Date(now.getFullYear(), now.getMonth() + index, 10).toISOString()
      })
  }

  async getUndeliverableDays() {
    const now = new Date()
    const oneMonthFromNow = new Date(now.getFullYear(), now.getMonth() + 1, 1)
    return [oneMonthFromNow.toISOString()]
  }
}

module.exports = DKAvailabilityDatasource
