const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

/**
 * @see https://floristservice.integration.interflora.se/swagger/index.html
 */
class SEFloristDatasource extends RESTDataSource {
  constructor() {
    super()
    this.baseURL = config.seServices.florist.baseUrl
  }

  /**
   * @param {{
   *  postalCode: string,
   *  deliveryDates: (string | Date)[],
   *  productCategories: (string[]|undefined),
   *  onlyMemberIds: (number[]|undefined)
   * }} opts
   * @returns {Promise<SEBrokerDelivery[]>}
   */
  async delivery(opts) {
    if (!Array.isArray(opts.deliveryDates)) {
      return Promise.reject('checkDelivery: Missing delivery dates')
    }

    if (!opts.postalCode) {
      return Promise.reject('checkDelivery: Missing postal code')
    }

    const productCategories =
      Array.isArray(opts.productCategories) && opts.productCategories.length > 0
        ? opts.productCategories
        : ['BasicAssortment']
    const onlyMemberIds = Array.isArray(opts.onlyMemberIds) ? opts.onlyMemberIds : []

    return this.post('delivery', {
      body: {
        deliveryDatesUtc: opts.deliveryDates,
        onlyMemberIds,
        postalCode: opts.postalCode,
        productCategories,
      },
    })
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    request.headers['api-key'] = config.tokens.OctopusApiKey
  }
}

module.exports = SEFloristDatasource
