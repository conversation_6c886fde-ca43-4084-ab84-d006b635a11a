/**
 * @typedef {{
 *  access_token: string,
 *  token_type: string,
 *  refresh_token: string,
 *  expires_in: number,
 * }} loginReturnTokenResponse
 */

/**
 * @typedef {{
 *  bonusItems: BonusItem[],
 *  bonusPoints: number,
 *  level: string,
 *  pointsToNextCheck: number,
 *  pointsToNextLevel: number,
 * }} BonusInformation
 */

/**
 * @typedef {{
 *   id: string,
 *   name: string,
 *   heading: string,
 *   description: string,
 *   amount: number,
 *   amountType: ("Percent" | "Amount"),
 * }} BonusItem
 */

/**
 * @typedef {{
 *  Address: string|null,
 *  BonusInformation: BonusInformation,
 *  City: string|null,
 *  CO: string|null,
 *  CostCenter: string|null,
 *  Country: string,
 *  DoorCode: string,
 *  Email: string,
 *  FirstName: string,
 *  ICE: string|null,
 *  Id: string,
 *  IsOrganizationAdministrator: boolean,
 *  LastName: string,
 *  Locked: boolean,
 *  NoProfiling: boolean,
 *  Organization: string|null,
 *  OrganizationId: string|null,
 *  OtherInfo: string|null,
 *  PersonalIdentityNumber: string,
 *  Phone: string,
 *  PostalCode: string|null,
 *  ReceiveOffersEmail: boolean,
 *  ReceiveOffersSms: boolean,
 *  Status: string,
 *  UnitName: string,
 *  Username: string,
 *  VoyadoId: string,
 *  WorkingAtFloristId: string|null,
 *  hasGlobalDiscountOnProduct: boolean|null,
 *  hasOrganizationCategory: boolean|null,
 * }} OctopusCustomer
 */

/**
 * @typedef {{
 *  Ssn: string,
 *  FirstName: string,
 *  LastName: string,
 *  Email: string,
 *  MobilePhone: string,
 *  Password: string,
 *  type: GQLUserTypeEnum,
 * }} OctopusRegisterCustomer
 */

/**
 * @typedef {{
 *  UserFirstName: string,
 *  UserLastName: string,
 *  Email: string,
 *  MobilePhone: string,
 *  Password: string,
 *  OrganizationNumber: string,
 *  OrganizationName: string,
 *  CostCenter: string,
 *  InvoiceAddress: string,
 *  InvoicePostalCode: string,
 *  InvoiceCity: string,
 *  SummaryInvoice: string
 *  }} OctopusRegisterOrganization
 */

/**
 * @typedef {{
 *  deliveringMember: {
 *    id: number
 *    memberShopName: string
 *    storeType: string
 *    startDate: string
 *    endDate: (string | undefined)
 *    storeAddress: string
 *    storePhone: string
 *    storePostalCode: string
 *    storeCity: string
 *    storeEmail: string
 *    cannotDeliver: boolean
 *  }
 *  orderType: string
 *  deliveryType: string
 *  price: number
 *  selected: boolean
 *  selectedByDistributionQuota: boolean
 *  text: {
 *    name: string
 *    shortInfo: string
 *    longInfo: string
 *  }
 *  deliveryFees: SEBrokerDeliveryFee[]
 *  compensation: number
 * }} SEBrokerDelivery
 */

/**
 * @typedef {{
 *  name: string
 *  price: number
 *  tax: number
 * }} SEBrokerDeliveryFee
 */

/**
 * @typedef {{
 *  deliveryDateUtc: string
 *  items: BrokerDeliveryItem[]
 *  errorMessage: string | null
 * }} SEBrokerDeliverySlot
 */

/**
 * @typedef {{
 *  TotalCount: number,
 *  Orders: SeOrderHistory[]
 * }} SeOrderHistoryResponse
 */

/**
 * @typedef {{
 *  OrderNumber: string,
 *  OrderDate: string,
 *  DeliveryDate: string,
 *  Total: number,
 *  Status: string,
 *  ExternalId: string
 * }} SeOrderHistory
 */

/**
 * @typedef {{
 * id: string,
 * orgNr: string,
 * name: string,
 * showPricesIncludingVat: boolean,
 * invoiceDetails: InvoiceDetails,
 * users: OctopusCustomer[],
 * }} OctopusOrganization
 */

/**
 * @typedef {{
 * discountType: string,
 * discountAmount: number,
 * }} InvoiceDetails
 */
