const apm = require('elastic-apm-node')
const { RESTDataSource } = require('../RESTDataSource')

const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import('apollo-datasource-rest').Request} Request */
/** @typedef {import('apollo-datasource-rest').RequestOptions} RequestOptions */

class SEApiDatasource extends RESTDataSource {
  constructor() {
    super()
    this.baseURL = config.seServices.api.baseUrl
    this.apiPath = config.seServices.api.apiPath
  }

  /**
   * Initiates the forgot password process for a customer
   * @param {string} email - The email address of the customer
   * @returns {Promise<Object>} Response from the password reset request
   */
  async forgotPassword(email) {
    const params = new URLSearchParams({ Email: email })
    return this.post(this.apiPath.CUSTOMER_FORGOT_PASSWORD, { body: params })
  }

  /**
   * Gets authentication token using a social security number
   * @param {string} ssn - The social security number of the customer
   * @returns {Promise<Object>} Response containing the authentication token
   */
  getTokenWithSsn(ssn) {
    return this.post(`${this.apiPath.LOGIN_WITH_SSN}/${ssn}`)
  }

  /**
   *
   * @param {string} token
   * @returns {Promise<OctopusCustomer>}
   */
  async getCustomerInfosWithToken(token) {
    return this.get(this.apiPath.CUSTOMER, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    /* Want to test bonus Items and rewards ?
    Check this file in the `sweden-bonuses` branch
    */
  }

  /**
   *
   * @param {string} token
   * @returns {Promise<OctopusOrganization>}
   */
  async getCustomerOrganizationInfosWithToken(token) {
    return this.get(this.apiPath.ORGANIZATION, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  /**
   * @param {string} username
   * @param {string} password
   * @returns {Promise<loginReturnTokenResponse>}
   */
  async loginReturnToken(username, password) {
    const params = new URLSearchParams({
      grant_type: 'password',
      password,
      scope: '*',
      username,
    })

    return this.post(this.apiPath.OAUTH_TOKEN, { body: params })
  }

  /**
   * @param {string} refreshToken
   * @returns {Promise<loginReturnTokenResponse>}
   */
  async refreshToken(refreshToken) {
    const params = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    })

    return this.post(this.apiPath.OAUTH_TOKEN, { body: params })
  }

  /**
   * @param {OctopusRegisterCustomer} customer
   * @param {('INDIVIDUAL'|'PROFESSIONAL')} type
   * @returns {Promise<any>}
   */
  async registerCustomer(customer, type) {
    const params = new URLSearchParams(customer)

    if (type === CONST.user.userType.b2b) {
      return this.post(this.apiPath.ORGANIZATION, { body: customer })
    } else if (type === CONST.user.userType.b2c) {
      return this.post(this.apiPath.CUSTOMER_PRIVATE, { body: params })
    }
  }

  /**
   * Soft login using a token
   * @param {string} token
   * @returns {Promise<loginReturnTokenResponse>}
   */
  async softLogin(token) {
    return this.post(this.apiPath.SOFT_LOGIN, {
      body: { token },
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  /**
   * @param {OctopusRegisterCustomer} customer
   * @param {string} token
   * @returns {Promise<any>}
   */
  async updateCustomer(customer, token) {
    const params = new URLSearchParams(customer)
    if (customer.type === CONST.user.userType.b2b) {
      return this.put(this.apiPath.ORGANIZATION, {
        body: customer,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
    }

    return this.put(this.apiPath.CUSTOMER, {
      body: params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  /**
   * @param {string} token
   * @param {number} limit
   * @param {number} page
   * @returns {Promise<SeOrderHistoryResponse>}
   */
  async getOrders(token, limit = 20, page = 0) {
    return this.get(`${this.apiPath.ORDER_HISTORY}/${limit}/${page}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  /**
   * @param {string} oldPassword
   * @param {string} newPassword
   * @param {string} token
   * @returns {Promise<any>}
   */
  async updatePassword(oldPassword, newPassword, token) {
    const body = new URLSearchParams()
    body.append('newPassword', newPassword)
    body.append('oldPassword', oldPassword)

    return this.put(this.apiPath.CUSTOMER_PASSWORD, {
      body,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
  }

  /**
   * @override
   * @param {Error} error
   * @param {Request} _request
   */
  didEncounterError(error, _request) {
    apm.captureError(error, { custom: error })
    if (config.environment === CONST.environment.LOCAL) {
      console.error(error)
    }

    return super.didEncounterError(...arguments)
  }

  /**
   * @param {string} _path
   * @param {AugmentedRequest} request
   * @returns {void}
   */
  willSendRequest(_path, request) {
    request.headers['api-key'] = config.tokens.OctopusApiKey
  }
}

module.exports = SEApiDatasource
