const config = require('../../../config')
const { RESTDataSource } = require('../RESTDataSource')

class TimeOneDataSource extends RESTDataSource {
  /**
   * @param {string} orderId - order guid
   * @param {Number} amount - order amount, supposely without taxes and shipping
   * @param {string} campaignId - subtracking id given by TimeOne
   * @returns {Promise<any>}
   */
  convertCampaign(orderId, amount, campaignId) {
    return this.get(undefined, {
      price: amount,
      subtracking: campaignId,
      uniqid: orderId,
    })
  }

  /**
   * @override
   * @param {string} path
   * @param {Object<string, string>} params
   * @returns {Promise<any>}
   */
  get(path = '/img', params) {
    return super.get(`${config.tracking.timeOne.baseUrl}${path}`, {
      params: new URLSearchParams({
        comid: config.tracking.timeOne.comid,
        iu: config.tracking.timeOne.iu,
        progid: config.tracking.timeOne.progid,
        s2s: 1,
        ...params,
      }),
    })
  }
}

module.exports = {
  timeone: TimeOneDataSource,
}
