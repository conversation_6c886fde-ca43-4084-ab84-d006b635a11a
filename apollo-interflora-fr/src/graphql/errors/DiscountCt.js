const GraphQLCtError = require('./Ct')

class GraphQLDiscountCtError extends GraphQLCtError {
  /**
   * @param {string} message
   * @param {string} type
   * @param {string} subject
   * @param {string} [customMessage]
   * @param {number} [code]
   */
  constructor(message, type, subject, customMessage = '', code = 422) {
    super(message, code)

    this.addError(type, subject, customMessage)
  }
}

module.exports = GraphQLDiscountCtError
