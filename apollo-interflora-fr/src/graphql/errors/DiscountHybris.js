const GraphQLHybrisError = require('./Hybris')

/**
 * @param {string} message
 * @param {string} type
 * @param {string} subject
 * @param {string} [customMessage]
 * @param {number} [code]
 */
class GraphQLDiscountHybrisError extends GraphQLHybrisError {
  constructor(message, type, subject, customMessage = '', code = 422) {
    super(message, '', code)
    this.addError(type, subject, customMessage)
  }

  static fromError(e) {
    const error = e.errors?.[0]
    return new GraphQLDiscountHybrisError(e.message, error?.type, error?.subject, error?.message)
  }
}

module.exports = GraphQLDiscountHybrisError
