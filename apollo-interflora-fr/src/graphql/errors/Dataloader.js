const GraphQLBaseError = require('./Base')

class GraphQLDataloaderError extends GraphQLBaseError {
  /**
   * @param {Error | any} reason
   */
  constructor(reason) {
    if (reason instanceof Error) {
      super(reason.message, reason.statusCode)
      this.extensions.reason = {
        message: reason.message,
        stack: reason.stack,
      }
    } else {
      super(reason)
      this.extensions.reason = `${reason}`
    }
  }
}

module.exports = GraphQLDataloaderError
