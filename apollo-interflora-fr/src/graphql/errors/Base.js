const { GraphQLError } = require('graphql')

class GraphQLBaseError extends GraphQLError {
  /**
   * @param {string} message
   * @param {number} [code]
   * @param {Object<string, any>} extensions
   */
  constructor(message, code, extensions = {}) {
    const errors = []
    // It is not clear when apollo server v3 wraps the errors properties in the execption
    // For the time being, duplicate errors
    // @todo: unify error handling on front-end side before sticking to one location
    super(message, {
      extensions: {
        ...extensions,
        code,
        errors,
        exception: {
          errors,
        },
      },
    })
    this.errors = errors
  }

  /**
   * @param {string} type
   * @param {string} subject
   * @param {string} message
   */
  addError(type, subject, message) {
    this.errors.push({ message, subject, type })
  }
}

module.exports = GraphQLBaseError
