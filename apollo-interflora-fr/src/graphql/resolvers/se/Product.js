const BaseResolver = require('../ct-datocms/Product')
const { GQLAvailabilityDateRanges } = require('../../models')
const { AvailabilityDateRangeTransformer } = require('../../transformers/availability')
const CONST = require('../../../const')
const { getCeremonyTimeRange } = require('../../../helpers/dates')
const apm = require('elastic-apm-node')

class ProductResolver extends BaseResolver {
  /**
   * @param {{
   *   code: string,
   *   countryId: string,
   *   city: string,
   *   postalCode: string,
   *   province: string,
   * }} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAvailabilityDateRanges>}
   */
  async _getProductDeliveryDates(args, context) {
    const { code, countryId, city, postalCode, province } = args
    const product = await this._loadProductFromCT({ code }, context)

    if (!product) {
      return new GQLAvailabilityDateRanges()
    }

    try {
      const productType = await this._loadProductType({ code }, context)
      const ctProduct = await context.loaders.commerceTools.products.load({
        code: args.code,
      })
      const productVariantSku = `${ctProduct.key}#${ctProduct.masterVariant.key}`

      context.log.info('ProductResolver._getProductDeliveryDates', {
        city: city,
        code: code,
        countryId: countryId,
        postalCode: postalCode,
        productVariantSku: productVariantSku,
        province: province,
      })

      const unavailabilities = await context.loaders.availability.unavailabilities.load({
        city: city,
        code: product.key,
        country: countryId,
        postalCode: postalCode,
        productVariantSku: productVariantSku,
        province: province,
      })

      const result = new GQLAvailabilityDateRanges(AvailabilityDateRangeTransformer.toGQL(unavailabilities, context))
      if (productType === CONST.product.types.mourning && getCeremonyTimeRange(new Date(), context).length === 0) {
        result.removeToday()
      }

      return result
    } catch (e) {
      apm.captureError(e, { custom: e })
      return new GQLAvailabilityDateRanges()
    }
  }
}

module.exports = ProductResolver
