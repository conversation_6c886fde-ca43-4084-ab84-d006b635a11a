/** @typedef {import('./_typedef')}  */

const apm = require('elastic-apm-node')

const BaseResolver = require('../ct-datocms/Order')
const { GraphQLApolloError } = require('../../errors')
const { GQLOrderResult } = require('../../models')
const config = require('../../../config')
const CONST = require('../../../const')
const { CTOrderTransformer } = require('../../transformers/commerceTools')

const SeUserResolver = require('./User')
const { clearTagCache } = require('../../../helpers/cache')
const { hash } = require('../../../helpers/string')
const { getDistributionChannels } = require('../../../helpers/commercetools')

class OrderResolver extends BaseResolver {
  constructor() {
    super()
    this.seUserResolver = new SeUserResolver()
  }

  /**
   * @override
   * @param {CTDatocmsContext} context
   * @param {FrisbiiCharge} charge
   * @param {String} orderId
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmFrisbiiPayment(context, charge, orderId) {
    /** @type {CTCart} */
    let apiCart
    try {
      apiCart = await context.loaders.commerceTools.cartsAsRead.load(orderId)
    } catch (e) {
      // do not throw yet
    }
    const orderResult = new GQLOrderResult()

    if (apiCart?.cartState !== CONST.commerceTools.cartStates.active) {
      for (let i = 0; i < config.commerceTools.maxOrderFetchingAttempts; i++) {
        const order = await context.dataSources.orders.getByCartId(apiCart.id)
        if (order) {
          context.log.info(`[confirm Frisbii] order retrieved from payment info: ${order.id} on attempt ${i}`)
          orderResult.order = CTOrderTransformer.toGQL(order, context, {
            distributionChannels: await getDistributionChannels(context),
          })
          orderResult.status = CONST.order.statuses.success
          return orderResult
        }
        await new Promise((resolve) => setTimeout(resolve, config.commerceTools.waitOrderDelay))
      }

      const error = new GraphQLApolloError(`no order found for cart ${apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No order found for cart ${apiCart.id}`

      return orderResult
    }

    if (!apiCart) {
      const error = new GraphQLApolloError(`no cart found`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `Cart not found`

      return orderResult
    }

    const details = await context.dataSources.frisbii.getTransactionDetails({
      invoiceId: charge.handle,
      transactionId: charge.transaction,
    })

    apm.logger.info(`[SWEDEN - Frisbii] details: ${JSON.stringify(details)}`)

    /** @type {SECartAdditionalData} */
    let additionalData = {}
    if (apiCart.custom.fields.additional_data) {
      try {
        additionalData = JSON.parse(apiCart.custom.fields.additional_data)
      } catch (e) {
        const err = new Error(`[non blocking] ${e.message}`)
        apm.captureError(err, { custom: err })
      }
    }

    // @todo: typedef + doc - Used to get swish/specific payment method info.
    const transaction = await context.dataSources.frisbii.getTransaction({
      invoiceId: charge.handle,
      transactionId: charge.transaction,
    })

    additionalData.klarnaTransactionId = details?.klarnaOrderId || null
    additionalData.transaction_id = details?.transaction_id || null
    additionalData.approval_code = details?.approval_code || null
    additionalData.swishTransactionId = transaction.swish_transaction?.swish_id || null
    additionalData.swishRefTransactionId = transaction.swish_transaction?.ref_transaction || null

    if (context.session?.external?.accessToken) {
      const user = await context.dataSources.seApi.getCustomerInfosWithToken(context.session?.external?.accessToken)
      additionalData.organizationNumber = user.OrganizationId
      additionalData.costCenter = user.CostCenter
      // @todo - Find how to fill this
      additionalData.invoiceReference = null
    }

    try {
      const order = await this._convertCart(
        {
          amount: { centAmount: parseInt(details.amount), currencyCode: charge.currency },
          cart: apiCart,
          custom: {
            fields: {
              additional_data: JSON.stringify(additionalData),
            },
            type: {
              key: CONST.commerceTools.customTypes.cart,
              typeId: 'type',
            },
          },
          details: charge,
          mode: this._mapFrisbiiPaymentTypeToCT(charge.source.type),
          provider: CONST.payment.provider.FRISBII,
          state: CONST.commerceTools.paymentStates.paid,
          transactionId: `${apiCart.id}#${apiCart.version}`,
        },
        context
      )

      const result = new GQLOrderResult()

      result.status = CONST.order.statuses.success
      result.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
      return result
    } catch (error) {
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = error.message

      return orderResult
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  async getOrders(_parent, args, context) {
    const user = await this._getCurrentUser(context)
    return this.seUserResolver._getUserOrders(user, args, context)
  }

  /**
   * Called once we are sure we received a payment
   * Reflect the payment in commerce tools and transform the cart into an order
   *
   * @param {{
   *  cart: CTCart,
   *  details: object,
   *  custom: CTCustomFields?,
   *  mode: string,
   *  provider: string,
   *  state: string,
   *  transactionId: string,
   *  transactionType: string
   * }} input
   * @param {GraphQLContext} context
   * @return {Promise<CTOrder>}
   */
  async _convertCart(input, context) {
    let cart = await this._addPayment(input, context)
    cart = await this._beforeConvertingCart(cart, context)

    const cartId = cart.id

    let orderNumber
    try {
      orderNumber = await context.dataSources.sequenceGenerator.next()
      if (!orderNumber) {
        throw new Error(`/GenerateNextSequence returned => ${orderNumber}`)
      }
    } catch (e) {
      context.log.error(`[_convertCart] cart ${cartId} was unable to generate an order number: ${e}`)
      // tmp workaround - do not throw but keep error visible
      apm.captureError(e, { custom: e })
    }

    let order
    try {
      const custom = input.custom ?? undefined

      /** @type {CTOrder} */
      order = await context.dataSources.carts.createOrder({
        cart: {
          id: cartId,
          type: 'Cart',
        },
        custom,
        orderNumber: orderNumber ? `${orderNumber}` : undefined,
        paymentState: CONST.commerceTools.paymentStates.paid,
        version: cart?.version || 1,
      })

      if (order.customerEmail) {
        // In case user visits his orders the list will be cached and new order
        // won't be visible on it. This clears all user order list pages cache.
        await clearTagCache(`uol:${hash(order.customerEmail)}`, context.server.redis)
      }
    } catch (e) {
      apm.captureError(e, { custom: e })
      const error = new GraphQLApolloError(
        `createOrder failed - fallback to get order from cart id ${cartId}`,
        e.statusCode || 500
      )
      error.originalError = e
      apm.captureError(error, { custom: error })
      order = await context.dataSources.orders.getByCartId(cartId)
    }

    try {
      await this._afterCartConverted(order, context)
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    await this._decrementStock(input.cart, order.id, context)

    await Promise.all([
      context.loaders.commerceTools.carts.clear(cartId),
      context.loaders.commerceTools.cartsAsRead.clear(cartId),
    ])
    await this._clearSessionCart(context)
    return order
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrder}
   */
  async getOrder(_parent, args, context) {
    const ctOrder = await context.loaders.commerceTools.ordersByLegacyOrderNumber.load(args.orderId)
    // getOrder can be called both with legacy refs (i.e. my-accounts), or CT ids (i.e. after payment confirmation)
    if (!ctOrder) return super.getOrder(...arguments)

    const order = CTOrderTransformer.toGQL(ctOrder, context, {
      distributionChannels: await getDistributionChannels(context),
      skipProductAPI: false,
      useLegacyOrderNumber: true,
    })

    if (order.delivery) {
      order.delivery.ctOrder = ctOrder
    }

    return order
  }
}

module.exports = OrderResolver
