const BaseResolver = require('../ct-datocms/Authentication')
const { GraphQLApolloError } = require('../../errors')
const { CTSessionTransformer, CTTokenTransformer } = require('../../transformers/commerceTools')
const apm = require('elastic-apm-node')
const { SEUserUserTransformer } = require('../../transformers/seuser')
const CONST = require('../../../const')
const { isEmpty } = require('lodash')
const config = require('../../../config')
const BankIdAuthenticationTransformer = require('../../transformers/bankid/Authentication')

class AuthenticationResolver extends BaseResolver {
  /**
   * @returns { boolean } false
   */
  checkEmailExists() {
    // NOTE: We don't want to check if email exists in this context
    return false
  }

  /**
   * Login with BankID
   * @param {undefined} _parent
   * @param {string} referenceId
   * @param {GraphQLContext} context
   * @returns {Promise<SEAuthentication>}
   */
  async fetchAuthStatus(_parent, { referenceId }, context) {
    const authResponse = await context.dataSources.bankId.getCachedAuthResponse(referenceId)
    if (!authResponse) {
      return BankIdAuthenticationTransformer.fromError(new Error('Auth token expired'))
    }

    const collectResponse = await context.dataSources.bankId.collect(referenceId)
    if (collectResponse.status.toUpperCase() === CONST.bankId.status.complete) {
      try {
        const { access_token, expires_in, refresh_token } = await context.dataSources.seApi.getTokenWithSsn(
          collectResponse.completionData.user.personalNumber
        )
        const customerResponse = await context.dataSources.seApi.getCustomerInfosWithToken(access_token)

        const login = await this._login(customerResponse, context, expires_in, access_token, refresh_token)

        return BankIdAuthenticationTransformer.toGQL(authResponse, collectResponse, login)
      } catch (e) {
        const message = context.appConfig.errors.login || e.message

        apm.captureError(e, { custom: e })
        const error = new GraphQLApolloError(message, 401)
        error.addError('InvalidGrantError', 'username', message)
        throw error
      }
    }

    return BankIdAuthenticationTransformer.toGQL(authResponse, collectResponse)
  }

  /**
   * @param {Token|undefined} token
   * @param {GraphQLContextArg<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  getTokenUser(token, _args, context) {
    if (token && token.user) {
      return token.user
    }

    if (context.session?.external?.user) {
      return context.session.external.user
    }
  }

  /**
   * Login with username and password
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  password: string,
   *  username: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async login(_parent, args, context) {
    const { username, password } = args

    try {
      const { access_token, expires_in, refresh_token } = await context.dataSources.seApi.loginReturnToken(
        username,
        password
      )
      /** @type {OctopusCustomer} */
      const customerResponse = await context.dataSources.seApi.getCustomerInfosWithToken(access_token)

      return this._login(customerResponse, context, expires_in, access_token, refresh_token)
    } catch (e) {
      const message = context.appConfig.errors.login || e.message

      apm.captureError(e, { custom: e })
      const error = new GraphQLApolloError(message, 401)
      error.addError('InvalidGrantError', 'username', message)
      error.addError('InvalidGrantError', 'password', '')
      throw error
    }
  }

  /**
   * @param {string} refreshToken
   * @param {GraphQLContext} context
   * @returns {Promise<void>}
   */
  async refreshExternalToken(refreshToken, context) {
    const { access_token, expires_in, refresh_token } = await context.dataSources.seApi.refreshToken(refreshToken)

    context.session.external = {
      ...context.session.external,
      accessToken: access_token,
      expiresIn: expires_in,
      refreshToken: refresh_token,
    }

    await context.session.save()
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArg<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser|void>}
   */
  async registerUser(_parent, args, context) {
    const { type, email, password, ssn } = args.user
    const regex = /^[0-9]{8}-[0-9]{4}$/
    if (!regex.test(ssn)) {
      const error = new GraphQLApolloError('Ogiltigt personnummer', 400)
      error.addError('AUTHENTICATION', 'WRONG_SSN', 'Ogiltigt personnummer - YYYYMMDD-NNNN')
      throw error
    }

    if (type === CONST.user.userType.b2c) {
      const customer = SEUserUserTransformer.fromGQLForCustomer(args.user, context)
      await context.dataSources.seApi.registerCustomer(customer, CONST.user.userType.b2c)
    }

    if (type === CONST.user.userType.b2b) {
      const organization = SEUserUserTransformer.fromGQLForOrganization(args.user, context)
      await context.dataSources.seApi.registerCustomer(organization, CONST.user.userType.b2b)
    }

    return {
      ...(await this.login(_parent, { password, username: email }, context)),
      state: CONST.user.userRegistrationStates.CREATED,
    }
  }

  /**
   * Used for soft login, for Sweden campaigns
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async softLogin(_parent, args, context) {
    const { code } = args

    try {
      const { access_token, expires_in, refresh_token } = await context.dataSources.seApi.softLogin(code)
      /** @type {OctopusCustomer} */
      const customerResponse = await context.dataSources.seApi.getCustomerInfosWithToken(access_token)

      return this._login(customerResponse, context, expires_in, access_token, refresh_token)
    } catch (error) {
      apm.captureError(error, { custom: error })
      return {
        createdAt: new Date().toISOString(),
        state: CONST.user.userRegistrationStates.ERROR,
      }
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{referenceId}>}
   */
  async startAuthentication(_parent, _args, context) {
    try {
      const authResponse = await context.dataSources.bankId.authenticate(context.ip, context)
      const collectResponse = await context.dataSources.bankId.collect(authResponse.orderRef)

      return BankIdAuthenticationTransformer.toGQL(authResponse, collectResponse)
    } catch (error) {
      context.log.error('Error starting authentication:', error)
      return BankIdAuthenticationTransformer.fromError(error)
    }
  }

  /**
   * @param {OctopusCustomer} customer
   * @param {GraphQLContext} context
   * @param {number} expires_in
   * @param {string} accessToken
   * @param {string} refreshToken
   * @returns {Promise<GQLToken>}
   * @private
   */
  async _login(customer, context, expires_in, accessToken, refreshToken) {
    const organization = !isEmpty(customer.OrganizationId?.trim())
      ? await context.dataSources.seApi.getCustomerOrganizationInfosWithToken(accessToken)
      : null

    let commerceTools = context?.session?.commerceTools
    if (!commerceTools) {
      const anonymousLogin = await context.dataSources.ctAuth.anonymousLogin()
      await this._setCtAuthContext(context, anonymousLogin)
      commerceTools = CTSessionTransformer.fromResponse(anonymousLogin)
    }

    /** @type {CTCategory[] | null} */
    const organizationSubcategories = await context.loaders.commerceTools.organizationSubcategories
      .load({
        categoryIdOrKey: config.commerceTools.rootProductsCategoryKey,
        organizationId: customer.OrganizationId,
      })
      .catch((error) => context.log.error(`[login] Error loading organization subcategories: ${error?.message}`))
    customer.hasOrganizationCategory = organizationSubcategories?.length > 0
    customer.hasGlobalDiscountOnProduct =
      organization?.invoiceDetails?.discountType === 'Percentage' && organization?.invoiceDetails?.discountAmount > 0

    const gqlUser = SEUserUserTransformer.toGQL(customer, context)
    await this._setUserSEAuthContext(context, gqlUser, accessToken, refreshToken, expires_in)

    const token = CTTokenTransformer.toGQL(
      {
        expires_in,
        login: context.auth.login,
        refresh_token_expires_in: expires_in * 24 * 30,
        scope: commerceTools.scope,
      },
      context
    )

    this._assertValidTokenSessionId(token.value)

    return {
      ...token,
      ...{ user: gqlUser },
    }
  }

  /**
   * Updates session following login
   *
   * @param {GraphQLContext} context
   * @param {GQLUser} user
   * @param {string} accessToken
   * @param {string} refreshToken
   * @param {string} expiresIn
   * @returns {Promise<void>}
   * @protected
   */
  async _setUserSEAuthContext(context, user, accessToken, refreshToken, expiresIn) {
    const userChanged = !context.auth.isAnonymous && context.auth.login !== user.username

    context.auth.isAnonymous = false
    context.auth.login = user.username

    if (context.session.apiCart && userChanged) {
      // was logged in and changed user - kill current cart
      await this._clearSessionCart(context)
    }

    context.session.user = { email: user.email, id: user.id }

    context.session.external = {
      accessToken,
      expiresIn,
      refreshToken,
      user,
    }

    await context.session?.save?.()
  }
}

module.exports = AuthenticationResolver
