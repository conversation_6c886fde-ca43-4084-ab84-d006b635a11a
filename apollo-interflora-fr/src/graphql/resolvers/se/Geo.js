const BaseResolver = require('../ct-datocms/Geo')

class GeoResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLDepartment}[]>}
   */
  async getDepartments(_parent, _args, context) {
    const departments = await context.loaders.datocms.floristRegions.load(context.language)
    return this._sortDepartmentsByPriority(departments)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLDepartment}>}
   */
  async getDepartmentBySlug(_parent, args, context) {
    const department = await super.getDepartmentBySlug(_parent, args, context)
    department.others = this._sortDepartmentsByPriority(department.others)
    return department
  }

  /**
   *
   * @param {GQLDepartment[]} departments
   * @returns {GQLDepartment[]}
   */
  _sortDepartmentsByPriority(departments) {
    return departments.sort((a, b) => {
      const nA = parseInt(a.regionCode?.split('_')[0], 10)
      const nB = parseInt(b.regionCode?.split('_')[0], 10)
      return nA - nB
    })
  }
}

module.exports = GeoResolver
