const BaseResolver = require('../ct-datocms/ForgotPassword')

class ForgotPasswordResolver extends BaseResolver {
  /**
   * Initiates a password reset request for a user
   *
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>} True if request was successful, false otherwise
   */
  async resetPasswordRequest(_parent, args, context) {
    try {
      const { username } = args

      if (!username || !username.trim()) {
        context.log.warn('Password reset attempted with empty username')
        return false
      }

      await context.dataSources.seApi.forgotPassword(username.trim())
      context.log.info(`Password reset requested for user: ${username}`)
      return true
    } catch (e) {
      context.log.error(`Password reset failed: ${e.message}`, { error: e })
      return false
    }
  }
}

module.exports = ForgotPasswordResolver
