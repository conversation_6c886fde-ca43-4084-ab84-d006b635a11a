const BaseResolver = require('../ct-datocms/User')
const { SEUserUserTransformer } = require('../../transformers/seuser')
const CountryTransformer = require('../../transformers/gfs/Country')

const { getOrders } = require('../../../helpers/seApi/orders')

class UserResolver extends BaseResolver {
  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLCountry|undefined}
   */
  getUserCountry(user, _args, context) {
    if (!user.countryId) {
      return
    }

    return CountryTransformer.toGQL(user.countryId, context)
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLOrder|undefined}
   */
  async getUserLastOrder(user, _args, context) {
    const order = await this._getUserOrders(
      user,
      {
        pagination: {
          limit: 1,
          page: 0,
        },
      },
      context
    )
    return order.orders?.[0] || null
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  async getUserOrders(user, args, context) {
    if (!args.pagination) return user.orders
    return this._getUserOrders(user, args, context)
  }

  /**
   * @param {GQLUser} _user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderList>}
   */
  async _getUserOrders(_user, args, context) {
    const { pagination, filter } = args
    const { accessToken } = context.session?.external ?? {}
    let total = 0

    const orders = await getOrders(accessToken, pagination, filter, context, (totalOrders) => {
      total = totalOrders
    })

    return {
      orders: orders.sort((a, b) => b.createdAt - a.createdAt),
      total,
    }
  }

  async getUserAddressList() {
    return {
      addresses: [],
      total: 0,
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   */
  async updateUser(_parent, _args, context) {
    if (!context.session?.external?.accessToken) {
      throw new Error('External access token is missing')
    }
    const customer = SEUserUserTransformer.fromGQLForUpdate(_args.user, context)
    const response = await context.dataSources.seApi.updateCustomer(customer, context.session.external.accessToken)
    const userGQL = SEUserUserTransformer.toGQL(response, context)

    context.session.user = userGQL
    await context.session?.save?.()

    return userGQL
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  newPassword: string,
   * oldPassword: string
   * }>} _args
   * @param {GraphQLContext} context
   */
  async updatePassword(_parent, _args, context) {
    if (!context.session?.external?.accessToken) {
      throw new Error('External access token is missing')
    }
    const { newPassword, oldPassword } = _args
    await context.dataSources.seApi.updatePassword(oldPassword, newPassword, context.session.external.accessToken)

    return context.session.user
  }
}

module.exports = UserResolver
