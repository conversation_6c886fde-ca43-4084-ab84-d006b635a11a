const _ = require('lodash')
const apm = require('elastic-apm-node')

const BaseResolver = require('../ct-datocms/Cart')
const { GQLInvalidDestination, GQLPaymentMethod, GQLDelivery } = require('../../models')
const { AvailabilityTownTransformer } = require('../../transformers/availability')
const TownTransformer = require('../../transformers/hitta/Town')
const TxnToRewardDetails = require('../../transformers/awardit/TxnToRewardDetails')
const TxnToRewardEvent = require('../../transformers/awardit/TxnToRewardEvent')
const { GraphQLApolloError } = require('../../errors')
const dateFns = require('date-fns')
const config = require('../../../config')
const CONST = require('../../../const')
const { CTCartTransformer, CTCartPaymentTransformer } = require('../../transformers/commerceTools')
const { encryptAES, decryptAES } = require('../../../helpers/crypto')
const { getAvailablePaymentMethods } = require('../../../helpers/features')
const rewards = require('../../../helpers/rewards')
const { randomUUID } = require('crypto')
const { totalAmountPaid, pendingGiftCardAmount } = require('../../../helpers/payment')
const { winZonedTimeToUTC } = require('../../../helpers/dates')

/** @typedef {import('../../datasources/SEServices/_typedef')} */

class CartResolver extends BaseResolver {
  /**
   * Return the towns based on the search string from availability service
   * @param {GraphQLContext} context
   * @param {string} countryId
   * @param {string} search
   * @returns {Promise<{town: GQLTown, valid: boolean}[]>}
   */
  async availabilityTowns(context, countryId, search) {
    const cart = await this._getApiCart(context)

    const responses = await Promise.all(
      cart.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return context.loaders.availability.towns.load({
          code: product.key,
          country: countryId || context.countryId,
          search,
        })
      })
    )
    const places = _.intersectionWith(...responses, _.isEqual)
    return places.map((place) => ({
      town: AvailabilityTownTransformer.toGQL(place, context),
      valid: true,
    }))
  }

  /**
   * Used only for longInfo, name and shortInfo
   * @param {GQLDelivery} delivery
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getCartDeliveryProperty(delivery, _args, context, info) {
    if (!(delivery instanceof GQLDelivery)) return

    if (delivery[info.fieldName] !== undefined && delivery[info.fieldName] !== null) {
      return delivery[info.fieldName]
    }

    this._assertHasCart(context)
    const apiCart = await this._getApiCart(context)
    const deliveryInfo = await this._getDelivery(apiCart, context)

    return deliveryInfo?.text[info.fieldName] || null
  }

  /**
   * It's used for Validate the region for the products and accessories of the cart
   * and tries to propose replacement if not valid
   * but it's not used for the Swedish site so it always returns true
   * @return {GQLInvalidDestination}
   */
  async getInvalidDestination() {
    const invalidDestination = new GQLInvalidDestination()
    invalidDestination.isValid = true

    return invalidDestination
  }

  /**
   * Return the towns based on the search string from availability service and Hitta API
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: GQLTown, valid: boolean }[]>}
   */
  async getTownsForCart(_parent, args, context) {
    this._assertHasCart(context)
    const { countryId, search } = args

    const [availabilityTowns, publicLocations] = await Promise.all([
      this.availabilityTowns(context, countryId, search),
      this.publicSearch(context, search),
    ])

    const availabilityTownsUpdated = availabilityTowns.map((item) => ({
      town: { ...item.town, label: `${item.town.label} (${item.town.postalCode})` },
      valid: item.valid,
    }))

    const postalCodesSet = new Set(availabilityTownsUpdated.map((item) => item.town.postalCode))
    const locationsFiltered = publicLocations.filter((item) => postalCodesSet.has(item.town.postalCode))

    const combinedTowns = [...locationsFiltered, ...availabilityTownsUpdated]
    const uniqueTowns = _.uniqBy(combinedTowns, 'town.postalCode')

    return uniqueTowns.slice(0, context.appConfig.towns.max)
  }

  /**
   * Retrieves the available payment methods for the current cart.
   *
   * Checks the current cart's total price and any applied discount codes or rewards.
   * If the cart is fully paid or has a total price of zero with discounts or rewards, it returns
   * a payment method indicating that no payment is required. Otherwise, it fetches and returns
   * the available payment methods for the cart.
   *
   * @param {undefined} _parent
   * @param {object} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Array<GQLPaymentMethod>>}
   */
  async paymentMethods(_parent, _args, context) {
    const cart = await this._getApiCart(context)
    const cartRewards = rewards.getCartRewardItems(cart)
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )
    const amountPaid = totalAmountPaid(payments)
    const isCartPaid = amountPaid >= cart.totalPrice.centAmount

    if (
      (cart?.totalPrice.centAmount === 0 && (cart?.discountCodes?.length > 0 || cartRewards.length > 0)) ||
      isCartPaid
    ) {
      const paymentMethod = new GQLPaymentMethod()
      paymentMethod.availableForCart = true
      paymentMethod.mode = CONST.payment.paymentMode.NONE
      return [paymentMethod]
    }

    let availablePaymentMethods = await getAvailablePaymentMethods(cart, context)

    // Invoice is available only for B2B customers
    const isB2B = context?.session?.external?.user?.type === CONST.user.userType.b2b
    if (!isB2B) {
      availablePaymentMethods = availablePaymentMethods.filter((pm) => pm.mode !== CONST.payment.paymentMode.FACTURE)
    }

    return availablePaymentMethods
  }

  /**
   * Retrieves the payment methods associated with the current cart.
   * @param {undefined} _parent
   * @param {object} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Array<GQLPaymentMethod>>}
   */
  async payments(_parent, _args, context) {
    this._assertHasCart(context)
    const cart = await this._getApiCart(context)
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )

    return payments.map((payment) => CTCartPaymentTransformer.toGQL(payment, context))
  }

  /**
   * Return the towns based on the search string from Hitta API
   * @param {GraphQLContext} context
   * @param {string} search
   * @returns {Promise<{town: GQLTown, valid: boolean}[]>}
   */
  async publicSearch(context, search) {
    const locations = await context.loaders.hitta.publicSearch.load({
      search,
      searchType: CONST.hitta.searchTypes.LOCATION,
    })

    if (!locations?.length) {
      return []
    }

    return locations
      .filter((location) => location?.address?.zipcode)
      .map((location) => TownTransformer.toGQL(location, context))
  }

  async _addCustomLineItemBonus(cart, taxCategoryId, centAmount, rewardItem, context) {
    return context.dataSources.carts.updateCartAsAdmin(cart.id, {
      actions: [
        {
          action: CONST.commerceTools.actions.addCustomLineItem,
          custom: {
            fields: {
              id: rewardItem.id,
              label: rewardItem.name,
              provider: CONST.reward.services.octopusapi,
              providerResponse: encryptAES(JSON.stringify(rewardItem), config.tokens.aesSecret, config.tokens.aesIV),
              rewardType: rewardItem.type,
              transactionId: rewardItem.transactionId,
            },
            type: { key: CONST.commerceTools.customTypes.customLineItemReward },
          },
          money: {
            centAmount,
            currencyCode: config.apps[context.siteId].currency,
            fractionDigits: 2,
          },
          name: {
            en: `${rewardItem.name}`,
            sv: `${rewardItem.name}`,
          },
          priceMode: CONST.commerceTools.customLineItemPriceMode.external,
          slug: rewardItem.id,
          taxCategory: {
            id: taxCategoryId,
            typeId: CONST.commerceTools.taxCategories.taxCategory,
          },
        },
      ],
      version: cart.version,
    })
  }

  async _cancelBonusReward(reservationId, rewardType, context) {
    let cart = await this._getApiCart(context)
    const rewardItemInCart = cart.customLineItems.find(
      (item) => item.custom?.fields?.rewardType === rewardType && item.custom?.fields?.id === reservationId
    )

    if (!rewardItemInCart) {
      throw new GraphQLApolloError('Bonus not found in cart', 'BONUS_NOT_FOUND')
    }

    cart = await context.dataSources.carts.updateCartAsAdmin(cart.id, {
      actions: [
        {
          action: CONST.commerceTools.actions.removeCustomLineItem,
          customLineItemId: rewardItemInCart.id,
        },
      ],
      version: cart.version,
    })

    const custom = {
      fields: {
        id: rewardItemInCart.custom.fields.id,
        label: rewardItemInCart.custom.fields.label,
        provider: CONST.reward.services.octopusapi,
        providerResponse: JSON.stringify(rewardItemInCart.custom.fields.providerResponse),
        rewardType: rewardItemInCart.custom.fields.rewardType,
        transactionId: rewardItemInCart.custom.fields.transactionId,
      },
      type: { key: CONST.commerceTools.customTypes.customLineItemReward },
    }

    const cartUpdated = await this._addPayment(
      {
        amount: {
          centAmount: Math.abs(rewardItemInCart.totalPrice.centAmount),
          currencyCode: config.apps[context.siteId].currency,
        },
        cardType: rewardType,
        cart,
        custom,
        mode: CONST.payment.paymentMode.REWARD,
        provider: CONST.reward.services.octopusapi,
        state: CONST.commerceTools.paymentStates.failed,
        transactionId: `${rewardItemInCart.id}-${cart.version}`,
        transactionType: CONST.commerceTools.transactionTypes.CancelAuthorization,
      },
      context
    )
    return CTCartTransformer.toGQL(cartUpdated, context)
  }

  /**
   * Cancels a gift card reward transaction and updates the cart
   * @param {string} reservationId - The reservation ID to cancel
   * @param {string} rewardType - The type of reward
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>} The updated cart
   * @private
   */
  async _cancelGiftCardReward(reservationId, rewardType, context) {
    this._assertHasCart(context)
    context.log.info(`[AwardIt-GiftCards] Canceling gift card reservation: ${reservationId}`)

    // Get reservation
    const cart = await this._getApiCart(context)
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )
    const reservationPayments = payments.filter((p) => p.custom?.fields?.reservationId === reservationId)
    const pendingReservation = reservationPayments.find(
      (p) => p.paymentStatus.interfaceCode === CONST.commerceTools.paymentStates.pending
    )
    if (!pendingReservation) {
      throw new GraphQLApolloError('Reservation not found', 'RESERVATION_NOT_FOUND')
    }

    const nonPendingPayments = reservationPayments.filter(
      (p) => p.paymentStatus.interfaceCode !== CONST.commerceTools.paymentStates.pending
    )
    if (nonPendingPayments.length > 0) {
      throw new GraphQLApolloError(
        'Transaction already confirmed or canceled',
        'TRANSACTION_ALREADY_CONFIRMED_OR_CANCELED'
      )
    }

    try {
      const providerResponse = decryptAES(
        pendingReservation.custom.fields.providerResponse,
        config.tokens.aesSecret,
        config.tokens.aesIV
      )
      const { multicode, amount } = JSON.parse(providerResponse)

      // Cancel the transaction with the payment provider and clear the cache
      const [cart, transactionResponse] = await Promise.all([
        this._getApiCart(context),
        context.dataSources.awardit.cancelTransaction(multicode, reservationId),
      ])

      context.log.info(`[AwardIt-GiftCards] Successfully cancelled transaction: ${reservationId}`)

      // Add a failed payment record to the cart
      const cartUpdated = await this._addPayment(
        {
          amount: {
            centAmount: Math.round(amount * 100),
            currencyCode: config.apps[context.siteId].currency,
          },
          cardType: CONST.reward.types.giftCard,
          cart,
          custom: {
            fields: {
              balance: {
                centAmount: Math.round(transactionResponse.card.balance * 100),
                currencyCode: transactionResponse.card.currency,
              },
              id: multicode,
              label: multicode,
              provider: CONST.reward.services.retain24,
              providerResponse: encryptAES(
                JSON.stringify(transactionResponse),
                config.tokens.aesSecret,
                config.tokens.aesIV
              ),
              reservationId,
              rewardType: CONST.reward.types.giftCard,
            },
            type: { key: CONST.commerceTools.customTypes.customLineItemReward },
          },
          mode: CONST.payment.paymentMode.GIFT_CARD,
          provider: CONST.reward.services.retain24,
          state: CONST.commerceTools.paymentStates.failed,
          transactionId: randomUUID(),
          transactionType: CONST.commerceTools.transactionTypes.CancelAuthorization,
        },
        context
      )

      context.log.info(`[AwardIt-GiftCards] Added cancellation payment record for: ${reservationId}`)

      return CTCartTransformer.toGQL(cartUpdated, context)
    } catch (error) {
      context.log.error(`[AwardIt-GiftCards] Failed to cancel gift card: ${error?.message}`)
      throw new GraphQLApolloError(`Failed to cancel gift card: ${error?.message}`, 'CANCEL_GIFT_CARD_FAILED')
    }
  }

  /**
   * Confirms a gift card transaction and adds payment to the cart.
   * Checks if the cart has already been paid, retrieves the payment details for the specified reservation,
   * and processes the reservation by redeeming it with the payment provider. If successful, it updates the cart with
   * the payment information. If the reservation is not found or has already been confirmed or canceled, it throws
   * appropriate errors.
   *
   * @param {string} reservationId - The reservation ID to confirm
   * @param {string} rewardType - The type of reward
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>} The updated cart
   * @private
   */
  async _confirmGiftCardTransaction(reservationId, rewardType, context) {
    this._assertHasCart(context)
    context.log.info(`[AwardIt-GiftCards] Starting confirmation for reservation: ${reservationId}`)

    const cart = await this._getApiCart(context)
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )
    const reservationPayments = payments.filter((p) => p.custom?.fields?.reservationId === reservationId)
    const pendingReservation = reservationPayments.find(
      (p) => p.paymentStatus.interfaceCode === CONST.commerceTools.paymentStates.pending
    )
    if (!pendingReservation) {
      throw new GraphQLApolloError('Reservation not found', 'RESERVATION_NOT_FOUND')
    }

    const nonPendingPayments = reservationPayments.filter(
      (p) => p.paymentStatus.interfaceCode !== CONST.commerceTools.paymentStates.pending
    )
    if (nonPendingPayments.length > 0) {
      throw new GraphQLApolloError(
        'Transaction already confirmed or canceled',
        'TRANSACTION_ALREADY_CONFIRMED_OR_CANCELED'
      )
    }

    const amountPaid = totalAmountPaid(payments)
    const totalCartPrice = cart.totalPrice.centAmount
    if (totalCartPrice - amountPaid <= 0) {
      throw new GraphQLApolloError('Cart already paid', 'CART_ALREADY_PAID')
    }
    try {
      const providerResponse = decryptAES(
        pendingReservation.custom.fields.providerResponse,
        config.tokens.aesSecret,
        config.tokens.aesIV
      )
      const { multicode, pin, amount } = JSON.parse(providerResponse)

      const reservationResponse = await context.dataSources.awardit.redeemReservation(multicode, reservationId, amount)

      if (reservationResponse.reservation.status !== CONST.awardit.reservationStatus.redeemed) {
        throw new GraphQLApolloError(`Reservation failed: ${reservationId}`, 'RESERVATION_FAILED')
      }

      reservationResponse.card.pin = pin

      const paymentData = {
        amount: {
          centAmount: Math.round(amount * 100),
          currencyCode: reservationResponse.card.currency,
        },
        cardType: CONST.reward.types.giftCard,
        cart,
        custom: {
          fields: {
            balance: {
              centAmount: Math.round(reservationResponse.card.balance * 100),
              currencyCode: reservationResponse.card.currency,
            },
            id: multicode,
            label: multicode,
            provider: CONST.reward.services.retain24,
            providerResponse: encryptAES(
              JSON.stringify(reservationResponse),
              config.tokens.aesSecret,
              config.tokens.aesIV
            ),
            reservationId,
            rewardType: CONST.reward.types.giftCard,
            transactionId: reservationResponse.transaction.transactionId.toString(),
          },
          type: { key: CONST.commerceTools.customTypes.customLineItemReward },
        },
        mode: CONST.payment.paymentMode.GIFT_CARD,
        provider: CONST.reward.services.retain24,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: reservationResponse.transaction.transactionId.toString(),
        transactionType: CONST.commerceTools.transactionTypes.Charge,
      }

      const cartUpdated = await this._addPayment(paymentData, context)

      context.log.info(`[AwardIt-GiftCards] Confirmed gift card payment for reservation: ${reservationId}`)
      return CTCartTransformer.toGQL(cartUpdated, context)
    } catch (error) {
      context.log.error(`[AwardIt-GiftCards] Failed to confirm gift card: ${error?.message}`)
      throw new GraphQLApolloError(`Failed to confirm gift card: ${error?.message}`, 'CONFIRM_GIFT_CARD_FAILED')
    }
  }

  /**
   * @param {string} reservationId
   * @param {("BONUS"|"PROMOTION")} rewardType
   * @param {GraphQLContext} context
   * @returns {Promise<*>}
   * @private
   */
  async _confirmRewardTransaction(reservationId, rewardType, context) {
    this._assertHasCart(context)
    const accessToken = context.session?.external?.accessToken
    if (!accessToken) {
      throw new GraphQLApolloError('No access token found', 'NO_ACCESS_TOKEN')
    }

    const rewardKeys = {
      [CONST.reward.types.bonus]: 'bonusItems',
      [CONST.reward.types.promotion]: 'promotions',
    }

    const customerResponse = await context.dataSources.seApi.getCustomerInfosWithToken(accessToken)
    const rewardItem = customerResponse.BonusInformation[rewardKeys[rewardType]].find(
      (item) => item.id === reservationId && item.amountType === 'Amount'
    )

    if (!rewardItem) {
      throw new GraphQLApolloError('Invalid reward', 'INVALID_REWARD')
    }

    rewardItem.type = rewardType

    let cart = await this._getApiCart(context)
    const bonusItemInCart = cart.customLineItems.some((item) => item.custom?.fields?.id === rewardItem.id)

    if (bonusItemInCart) {
      throw new GraphQLApolloError('Reward already in cart', 'REWARD_IN_CART')
    }

    // calculate how much of the gift card amount can be used in the transaction
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )
    const amountPaid = totalAmountPaid(payments)
    const availableAmount = rewards.getAvailableAmount(rewardItem.amount, cart, amountPaid)

    if (availableAmount < rewardItem.amount) {
      throw new GraphQLApolloError('The reward amount exceeds the available amount', 'INVALID_REWARD_AMOUNT')
    }

    // Convert reward amount to cents (negative since it's a discount)
    const centAmount = -rewardItem.amount * 100
    const taxCategory = await context.loaders.commerceTools.taxCategoriesByKey.load(
      CONST.commerceTools.taxCategories.noTax
    )

    // Generate unique transaction ID to link custom line item with payment
    rewardItem.transactionId = randomUUID()

    try {
      cart = await this._addCustomLineItemBonus(cart, taxCategory.id, centAmount, rewardItem, context)
      return this._updateCartWithPayment(cart, centAmount, rewardItem, context)
    } catch (error) {
      context.log.error(`[_confirmRewardTransaction] Failed to add bonus to cart: ${error?.message}`)
      throw new GraphQLApolloError('Failed to add bonus to cart', 'ADD_BONUS_FAILED')
    }
  }

  /**
   * Creates a gift card transaction by reserving an amount on a gift card
   * @param {string} code - The gift card code
   * @param {string} pin - The PIN associated with the gift card
   * @param {number} desiredAmount - The amount to reserve on the gift card
   * @param {GraphQLContext} context
   * @returns {Promise<GQLRewardEvent>}
   * @private
   */
  async _createGiftCardTransaction(code, pin, desiredAmount, context) {
    this._assertHasCart(context)
    context.log.info(`[AwardIt-GiftCards] Processing gift card transaction for code: ${code}, amount: ${desiredAmount}`)

    // Check if the cart is already paid
    const cart = await this._getApiCart(context)
    const payments = await context.loaders.commerceTools.payments.loadMany(
      cart.paymentInfo?.payments.map((p) => p.id) || []
    )
    const amountPaid = totalAmountPaid(payments)
    const totalCartPrice = cart.totalPrice.centAmount

    if (totalCartPrice - amountPaid <= 0) {
      throw new GraphQLApolloError('Cart already paid', 'CART_ALREADY_PAID')
    }

    const pendingGiftCardPayments = pendingGiftCardAmount(payments)
    if (totalCartPrice - amountPaid - pendingGiftCardPayments <= 0) {
      throw new GraphQLApolloError(
        'Sum of the payments and pending payment is greater than the cart total price',
        'CART_PAID_WITH_PENDING_GIFT_CARD_PAYMENT'
      )
    }

    // Get gift card details and calculate available amount
    const rewardDetails = await this.getRewardDetails({}, { code, rewardType: CONST.reward.types.giftCard }, context)
    const cardBalance = rewardDetails.reward.balance
    const availableAmount = rewards.calculateAvailableAmount(desiredAmount, cardBalance)
    const usableAmount = rewards.getAvailableAmount(availableAmount, cart, amountPaid + pendingGiftCardPayments)

    context.log.info(`[AwardIt-GiftCards] Gift card balance: ${cardBalance}, usable amount: ${usableAmount}`)
    try {
      // Reserve amount on gift card
      const transaction = await this._reserveGiftCardAmount(code, pin, usableAmount, context)

      return TxnToRewardEvent.toGQL(transaction, context)
    } catch (error) {
      context.log.error(`[AwardIt-GiftCards] Failed to reserve gift card: ${error}`)
      throw new GraphQLApolloError(`Failed to reserve gift card: ${error?.message}`, 'RESERVE_GIFT_CARD_FAILED')
    }
  }

  /**
   * Checks if the cart has a previously selected delivery type and returns the matching delivery if found.
   * @param {string|null} additional_data
   * @param {{selectedDelivery: SEBrokerDelivery, otherDeliveries: Array<SEBrokerDelivery>}} selectedDelivery
   * @returns {SEBrokerDelivery|null} The matching delivery if found, otherwise null
   * @private
   */
  _findPreviouslySelectedDelivery(additional_data, selectedDelivery) {
    if (additional_data) {
      // a delivery was selected by the customer - try to pick the matching one
      try {
        /** @type {SECartAdditionalData} */
        const additionalData = JSON.parse(additional_data)
        const cartDeliveryType = additionalData?.deliveryType
        if (cartDeliveryType !== selectedDelivery.selectedDelivery.deliveryType) {
          const matchingDelivery = selectedDelivery.otherDeliveries.find((d) => d.deliveryType === cartDeliveryType)
          if (matchingDelivery) {
            return matchingDelivery
          }
        }
      } catch (e) {
        apm.captureError(e, { custom: e })
      }
    }
    return null
  }

  /**
   * Extracts common florist IDs from line items in the cart.
   * @param {Array<CTLineItem>} lineItems - Array of line items in the cart
   * @returns {Array<string>} Array of common florist IDs
   */
  _getCommonFloristIds(lineItems) {
    const floristIds = []
    for (const lineItem of lineItems) {
      const memberIds = lineItem.variant.attributes.find((a) => a.name === 'se_only_members_id')
      if (memberIds) {
        if (Array.isArray(memberIds.value)) {
          floristIds.push(memberIds.value.join(','))
        } else if (typeof memberIds.value === 'string') {
          floristIds.push(memberIds.value)
        }
      }
    }

    const commonFloristIds = floristIds
      .map((ids) => ids.split(','))
      .reduce((acc, ids) => {
        if (acc.length === 0) {
          return ids
        }
        return acc.filter((id) => ids.includes(id))
      }, [])

    if (floristIds.length > 0 && commonFloristIds.length === 0) {
      const err = new GraphQLApolloError('No common florist IDs found')
      err.addError('no-delivery', 'No common florist IDs found')
      throw err
    }

    return commonFloristIds
  }

  /**
   * Retrieves delivery options for a specific product line item.
   * @param {String} memberIds - The florist member IDs
   * @param {String} date - The delivery date
   * @param {String} postalCode - The postal code for delivery
   * @param {String} productType - The type of product (e.g., funeral or standard)
   * @param {SEContext} context - The GraphQL context
   * @returns {Promise<{selectedDelivery: SEBrokerDelivery, otherDeliveries: Array<SEBrokerDelivery>}>} The selected delivery and other available options
   * @throws {GraphQLApolloError} If no matching delivery slot or selected delivery is found
   */
  async _getDeliveriesForProduct(memberIds, date, postalCode, productType, context) {
    // Mattias Malmqvist
    // The requirement has always been Swedish start of day in UTC
    // That is, 2025-05-25 is 2024-05-24T22:00Z
    const swedenDeliveryDate = winZonedTimeToUTC(date, 110).toISOString()

    const deliveryOptions = {
      deliveryDates: [swedenDeliveryDate],
      onlyMemberIds: memberIds,
      postalCode,
      productCategories: [productType],
    }
    // 1 date given => 1 slot expected
    const [deliverySlot] = await context.loaders.se.delivery.load(deliveryOptions)

    if (!deliverySlot) {
      const err = new GraphQLApolloError('no matching delivery slot found')
      err.addError('no-delivery', 'date', date)
      err.extensions.deliveryOptions = deliveryOptions
      throw err
    }
    const selectedDelivery = deliverySlot.items.find((item) => item.selected)
    if (!selectedDelivery) {
      const err = new GraphQLApolloError('no selected delivery found')
      err.addError('no-delivery', 'date', date)
      err.extensions.deliveryOptions = deliveryOptions
      throw err
    }

    // Filter other deliveries for the same florist excluding store pickups for now
    // https://myflower.atlassian.net/wiki/spaces/OM/pages/2357723196/Availability+check+Broker
    const otherDeliveries = deliverySlot.items.filter(
      (item) => item.deliveringMember.id === selectedDelivery.deliveringMember.id && item.deliveryType !== 'StorePickup'
    )

    return {
      otherDeliveries,
      selectedDelivery,
    }
  }

  /**
   * Retrieves the delivery florist for the cart, only one delivery type is expected for the cart
   * 1 - Extract the common florist IDs from the cart line items
   * 2 - get the product delivery categorisation from the cart line items with BasicAssortment as fallback
   * 3 - Get the deliveries for the products with the common florist IDs, product categorisation, date and postal code
   * 4 - Check if the cart has a previously selected delivery type and return the matching delivery if found or the selected delivery
   *
   * @param {CTCart} cart
   * @param {SEContext} context
   * @returns {Promise<SEBrokerDelivery>}
   * @private
   */
  async _getDelivery(cart, context) {
    const { date } = cart.shippingAddress?.custom.fields ?? {}
    const postalCode = cart.shippingAddress.postalCode

    if (!date || !postalCode) {
      return null
    }

    if (cart.shippingInfo?.shippingMethodName === config.gfs.customShipping.name) {
      return null
    }

    const commonMembersIds = this._getCommonFloristIds(cart.lineItems)
    const productType = this._getProductDeliveryCategorisation(cart.lineItems)

    const selectedDelivery = await this._getDeliveriesForProduct(
      commonMembersIds,
      date,
      postalCode,
      productType,
      context
    )

    return (
      this._findPreviouslySelectedDelivery(cart.custom.fields?.additional_data, selectedDelivery) ??
      selectedDelivery.selectedDelivery
    )
  }

  /**
   * Calculates the delivery price and finds the matching tax category
   * @param {Array<SEBrokerDeliveryFee>} deliveryFees - Array of delivery fee objects with price and tax properties
   * @param {GraphQLContext} context - The GraphQL context
   * @returns {Promise<Object>} Object containing the total amount and tax category
   * @private
   */
  async _getDeliveryPrice(deliveryFees, context) {
    let amount = 0
    let taxRate = 0

    for (const fee of deliveryFees) {
      amount += fee.price
      taxRate = Math.max(taxRate, fee.tax)
    }

    const taxes = await context.loaders.commerceTools.allTaxCategories.load(true)
    let tax = taxes.find((tax) => {
      const rate = tax.rates.find((rate) => rate.country === context.appConfig.country)
      return rate.amount.toFixed(2) === (taxRate / 100).toFixed(2)
    })
    if (!tax) {
      context.log.warn(`[_updateShippingCostsActions] no tax found matchind rate ${taxRate} - switching to default`)
      tax = taxes.find((tax) => tax.key === 'vat')
    }
    return { amount, taxId: tax.id }
  }

  async _getGiftCardDetails(code, context) {
    try {
      const transactions = await context.dataSources.awardit.getTransactions(code)

      return TxnToRewardDetails.toGQL(transactions, context)
    } catch (error) {
      if (error.extensions?.response?.status === 404) {
        throw new GraphQLApolloError('Gift card not found', 'GIFT_CARD_NOT_FOUND')
      }

      context.log.error(`[AwardIt-GiftCards] Failed to get gift card details: ${error?.message}`)
      throw new GraphQLApolloError(`Failed to get gift card details: ${error?.message}`, 'GET_GIFT_CARD_DETAILS_FAILED')
    }
  }

  /**
   * Determines the common product type from line items in the cart or returns BasicAssortment as fallback.
   *
   * Rules for determining product type:
   * 1. If all items have the same product type, return that type
   * 2. If items have mixed types including BasicAssortment, return the non-BasicAssortment type
   * 3. If items have different product types (none being BasicAssortment), return BasicAssortment
   * 4. If no product types found or empty line items, return BasicAssortment
   *
   * @param {Array<CTLineItem>} lineItems - Array of line items in the cart
   * @returns {string} The common product type or BasicAssortment as fallback
   */
  _getProductDeliveryCategorisation(lineItems) {
    if (!lineItems?.length) {
      return CONST.product.seCategories.BasicAssortment
    }

    const productTypes = new Set()
    for (const lineItem of lineItems) {
      const productType = lineItem.variant.attributes.find((a) => a.name === 'product_delivery_categorisation')
      if (productType) {
        productTypes.add(productType.value)
      }
    }

    if (productTypes?.size === 1) {
      return Array.from(productTypes)[0]
    }

    const removeBasicAssortment = Array.from(productTypes).filter(
      (type) => type !== CONST.product.seCategories.BasicAssortment
    )
    if (removeBasicAssortment.length === 1) {
      return removeBasicAssortment[0]
    }

    return CONST.product.seCategories.BasicAssortment
  }

  /**
   * Reserves an amount on a gift card and stores the reservation details in Redis.
   *
   * @param {string} multicode - The gift card code used for reservation
   * @param {string} pin - The PIN associated with the gift card
   * @param {number} amount - The amount to reserve on the gift card
   * @param {GraphQLContext} context
   * @returns {Promise<Object>} The transaction details
   * @private
   */
  async _reserveGiftCardAmount(multicode, pin, amount, context) {
    context.log.info(`[AwardIt-GiftCards] Reserving ${amount} for gift card: ${multicode}`)

    const reservationExpDate = dateFns.addMinutes(new Date(), config.awardit.reservationExpMinutes)
    const transaction = await context.dataSources.awardit.reserveAmount(multicode, pin, amount, reservationExpDate)

    const reservationId = transaction.reservation.reservationId
    context.log.info(`[AwardIt-GiftCards] Reservation created with ID: ${reservationId}`)

    const reservation = {
      amount: transaction.reservation.amount,
      multicode,
      pin,
      reservationId,
      type: CONST.reward.types.giftCard,
    }

    await this._addPayment(
      {
        amount: {
          centAmount: Math.round(reservation.amount * 100),
          currencyCode: config.apps[context.siteId].currency,
        },
        cardType: CONST.reward.types.giftCard,
        cart: await this._getApiCart(context),
        custom: {
          fields: {
            balance: {
              centAmount: Math.round(transaction.card.balance * 100),
              currencyCode: config.apps[context.siteId].currency,
            },
            id: multicode,
            label: multicode,
            provider: CONST.reward.services.retain24,
            providerResponse: encryptAES(JSON.stringify(reservation), config.tokens.aesSecret, config.tokens.aesIV),
            reservationId: reservationId,
            rewardType: CONST.reward.types.giftCard,
          },
          type: { key: CONST.commerceTools.customTypes.customLineItemReward },
        },
        mode: CONST.payment.paymentMode.GIFT_CARD,
        provider: CONST.reward.services.retain24,
        state: CONST.commerceTools.paymentStates.pending,
        transactionId: reservationId,
        transactionType: CONST.commerceTools.transactionTypes.Charge,
      },
      context
    )

    return transaction
  }

  async _updateCartWithPayment(cart, centAmount, bonusItem, context) {
    const custom = {
      fields: {
        id: bonusItem.id,
        label: bonusItem.name,
        provider: CONST.reward.services.octopusapi,
        providerResponse: JSON.stringify(bonusItem),
        rewardType: bonusItem.type,
        transactionId: bonusItem.transactionId,
      },
      type: { key: CONST.commerceTools.customTypes.customLineItemReward },
    }

    const cartUpdated = await this._addPayment(
      {
        amount: {
          centAmount: Math.abs(centAmount),
          currencyCode: config.apps[context.siteId].currency,
        },
        cardType: bonusItem.type,
        cart,
        custom,
        mode: CONST.payment.paymentMode.REWARD,
        provider: CONST.reward.services.octopusapi,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: bonusItem.transactionId,
        transactionType: CONST.commerceTools.transactionTypes.Charge,
      },
      context
    )
    return CTCartTransformer.toGQL(cartUpdated, context)
  }

  /**
   * @override
   * @param {CTCart} cart
   * @param {SEContext} context
   * @returns {Promise<{ action: string }[]>} array of actions
   */
  async _updateShippingCostsActions(cart, context) {
    const actions = []

    if (!cart.shippingAddress?.postalCode) {
      return actions
    }
    if (!cart.shippingAddress?.custom?.fields.date) {
      return actions
    }

    const delivery = await this._getDelivery(cart, context)
    const { amount, taxId } = await this._getDeliveryPrice(delivery.deliveryFees, context)

    /** @type {SECartAdditionalData} */
    let additionalData = {}
    if (cart.custom.fields.additional_data) {
      try {
        additionalData = JSON.parse(cart.custom.fields.additional_data)
      } catch (e) {
        const err = new Error(`[non blocking] ${e.message}`)
        apm.captureError(err, { custom: err })
      }
    }
    additionalData.deliveringMemberId = delivery.deliveringMember.id
    additionalData.deliveringMemberSelectedByQuota = delivery.selectedByDistributionQuota
    additionalData.deliveryCompensation = delivery.compensation
    additionalData.deliveryFees = delivery.deliveryFees
    additionalData.deliveryType = delivery.deliveryType

    actions.push({
      action: 'setCustomField',
      name: 'additional_data',
      value: JSON.stringify(additionalData),
    })

    actions.push({
      action: 'setCustomShippingMethod',
      shippingMethodName: 'SE delivery fees',
      shippingRate: {
        price: {
          centAmount: amount * 100,
          currencyCode: context.appConfig.currency,
        },
      },
      taxCategory: {
        id: taxId,
        typeId: 'tax-category',
      },
    })

    return actions
  }
}

module.exports = CartResolver
