const BaseResolver = require('../ct-datocms/Address')
const FuneralAddressTransformer = require('../../transformers/bitnet/FuneralAddress')
const AddressTransformer = require('../../transformers/hitta/Address')
const { addressValidator } = require('../../../helpers/validator/address')
const TownTransformer = require('../../transformers/availability/Town')

class AddressResolver extends BaseResolver {
  /**
   * @param {Object} parent
   * @param {Object} args
   * @param {Object} context
   * @returns {Promise<FuneralAddressesResponse[]>}
   */
  async getFuneralAddresses(parent, args, context) {
    const funeralAddresses = await context.loaders.se.getFuneralAddresses.load(args.name)

    return (
      funeralAddresses?.value
        .filter((address) => address?.FuneralStatusType === 'Public')
        .map((address) => FuneralAddressTransformer.toGQL(address, context)) || []
    )
  }

  async getAddresses(parent, args, context) {
    const { search, searchType } = args
    const locations = await context.loaders.hitta.publicSearch.load({ search, searchType })

    if (!locations?.length) {
      return []
    }

    return locations.map((location) => AddressTransformer.toGQL(location, context))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLValidationResult>}
   */
  async validateAddress(_parent, args, context) {
    return addressValidator(args.address, context.language, {
      townSearch: async (search, country, language) => {
        const towns = await context.loaders.availability.towns.load({
          country,
          language,
          search: search.replace(/\s*\(\d+\)\s*$/, ''),
        })

        return towns.map((place) => TownTransformer.toGQL(place, context))
      },
    })
  }
}

module.exports = AddressResolver
