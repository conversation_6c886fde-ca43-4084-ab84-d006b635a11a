/** @typedef {import('../../datasources/_typedef').SEDataSources} SEDataSources */

/** @typedef {import('../../dataloaders/availability').loadersDefinition} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/bloomreach').loadersDefinition} BloomreachDataLoaders */
/** @typedef {import('../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs').loadersDefinition} GFSDataLoaders */
/** @typedef {import('../../datasources/bankId/_typedef').BankIdAuthentication} BankIdAuthentication */

/**
 * @typedef {{
 *  BankIdAuthentication,
 *  token: GQLToken
 * }} SEAuthentication
 */

/**
 * @typedef {{
 *  deliveringMemberId: string
 *  deliveringMemberSelectedByQuota: boolean
 *  deliveryCompensation: number
 *  deliveryFees: SEBrokerDeliveryFee[]
 *  deliveryType: string
 *  klarnaTransactionId: string
 * }} SECartAdditionalData
 */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: SEDataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    bloomreach: BloomreachDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *  }
 * }} SEContext
 */
