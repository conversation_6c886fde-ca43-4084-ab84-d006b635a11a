const apm = require('elastic-apm-node')

const BaseResolver = require('../ct-datocms/Order')

class OrderResolver extends BaseResolver {
  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async getCanAttachOrder(order, _args, context) {
    if (order.canAttachOrder !== undefined) return order.canAttachOrder
    if (!order.delivery?.sender?.email) return false

    /** @type {CTOrder} ctOrder */
    const ctOrder = await context.loaders.commerceTools.orders.load(order.id)

    if (!ctOrder) return false
    // is already logged in?
    if (ctOrder.custom?.fields?.userId) return false

    try {
      const user = await context.loaders.itUser.userByEmail.load(order.delivery.sender.email)
      if (!user) return false
    } catch (e) {
      apm.captureError(e, { custom: e })
      return false
    }

    return true
  }
}

module.exports = OrderResolver
