const BaseResolver = require('../ct-datocms/Geo')

class GeoResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ province: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ city: string, code: string, province: string }[]>}
   */
  getCadastralCities(_parent, args, context) {
    const { province, search } = args

    return context.dataSources.itCadastre.autocompleteCities(search, province)
  }
}

module.exports = GeoResolver
