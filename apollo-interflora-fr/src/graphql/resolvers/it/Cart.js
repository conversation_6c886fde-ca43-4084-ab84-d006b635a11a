const apm = require('elastic-apm-node')
const CONST = require('../../../const')
const BaseResolver = require('../ct-datocms/Cart')
const { ITUserAddressTransformer } = require('../../transformers/ituser')
const { getCurrentUser } = require('./shared')
const { getPaginatedItems } = require('../../../helpers')

class CartResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddressListType>}
   */
  async getUserAddressesForCart(_parent, args, context) {
    const user = await getCurrentUser(context)
    if (!user) {
      return {
        addresses: [],
        total: 0,
      }
    }

    const { pagination } = args

    const response = await context.loaders.itUser.addresses.load({
      type: CONST.address.addressType.delivery,
      userId: user.id,
    })
    const paginated = getPaginatedItems(response.addresses || [], pagination.page, pagination.limit)

    return {
      addresses: paginated.data.map((a) => ITUserAddressTransformer.toGQL(a, context)),
      total: response.total || 0,
    }
  }

  /**
   * @override
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @returns {Promise<{ action: string }[]>} array of actions
   */
  async _updateShippingCostsActions(apiCart, context) {
    const actions = await super._updateShippingCostsActions(apiCart, context)

    const currentScore = apiCart.shippingRateInput?.score
    const pricePerVariantScore = actions.find((action) => action.action === 'setShippingRateInput')
    const weight = (
      await Promise.all(
        apiCart.lineItems.map(async (lineItem) => {
          const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
          const variant = product.variants.find((v) => v.id === lineItem.variant.id)

          // min quantity > 1 means stem flowers (i.e. a bouquet of roses)
          const qty = variant.minQuantity > 1 ? 1 : lineItem.quantity
          return qty * (variant.weight || 0)
        })
      )
    ).reduce((totalScore, itemScore) => totalScore + itemScore, 0)

    // update delivery price score if needed, add the score calculate with price per variant/per price line feature if it exists
    let newScore = Math.floor(weight / 3) + (pricePerVariantScore?.shippingRateInput?.score || 0)
    if (currentScore !== newScore) {
      if (newScore < 0 || isNaN(newScore)) {
        apm.captureError(new Error(`Invalid weight calculation (${newScore}) for shipping rate input`), {
          custom: {
            cartId: apiCart.id,
            newScore,
          },
          labels: {
            gqlAction: 'updateCart',
          },
        })
        newScore = 0
      }
      const action = {
        action: 'setShippingRateInput',
        shippingRateInput: {
          score: newScore,
          type: 'Score',
        },
      }
      actions.push(action)
    }

    return actions
  }
}

module.exports = CartResolver
