const apm = require('elastic-apm-node')

const BaseResolver = require('../ct-datocms/ForgotPassword')

const { GraphQLApolloError } = require('../../errors')

class ForgotPasswordResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ code: string, password: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async resetPassword(_parent, args, context) {
    try {
      // temp hack to work around code not being url encoded in the email
      // @todo: remove when fixed
      const code = `${args.code}`.replace(/ /g, '+')
      await context.dataSources.itForgottenPassword.resetPassword(code, args.password)
      return true
    } catch (error) {
      if (error.extensions.response.status !== 400) {
        /** In case of a server error, or even authorization error */
        throw error
      }

      apm.captureError(error, { custom: error })
      return false
    }
  }

  /**
   * @returns {boolean}
   */
  resetPasswordCheckCode() {
    return true
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async resetPasswordRequest(_parent, args, context) {
    try {
      await context.dataSources.itForgottenPassword.sendResetToken(args.username)
      return true
    } catch (error) {
      if (error.extensions.response.status !== 400) {
        /** In case of a server error, or even authorization error */
        throw error
      }

      const message = context.appConfig.errors.password
      const err = new GraphQLApolloError(message, 400)
      err.addError('ValidationError', 'userId', message)

      throw err
    }
  }
}

module.exports = ForgotPasswordResolver
