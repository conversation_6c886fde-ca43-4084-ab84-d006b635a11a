const CONST = require('../../../const')

const { GraphQLApolloError } = require('../../errors')
const { GQLUser } = require('../../models')

const { ITUserAddressTransformer, ITUserTransformer } = require('../../transformers/ituser')
const CountryTransformer = require('../../transformers/gfs/Country')
const { getPaginatedItems } = require('../../../helpers')
const { getInterfloraPlus } = require('../../../helpers/commercetools/interfloraplus')
const { checkCode, generateCode } = require('../../../helpers/ITFiscaleCode')

class UserResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  birthDate: Date,
   *  birthPlace: string,
   *  gender: string,
   *  firstName: string,
   *  lastName: string,
   *  province: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {string}
   */
  async generateFiscalCode(_parent, args, context) {
    const { birthDate, birthPlace, firstName, lastName, gender, province } = args

    const city = context.dataSources.itCadastre.getCity(birthPlace, province)
    if (!city) {
      const err = new GraphQLApolloError(`No city found`, 400)
      err.addError('city', `No city found`)
      throw err
    }

    const code = generateCode({
      birthDate,
      birthPlaceCode: city.code,
      firstName,
      gender,
      lastName,
    })
    return code
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination}>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @return {GQLAddressListType}
   */
  async getUserAddressList(user, args, context, info) {
    if (!user.id) {
      return []
    }

    let addresses = []
    if (info.fieldName === 'deliveryAddresses') {
      const response = await context.dataSources.itUser.getAddresses(user.id, CONST.address.addressType.delivery)
      addresses = response.addresses
    } else {
      const response = await context.dataSources.itUser.getAddresses(user.id, CONST.address.addressType.sender)
      addresses = response.addresses
    }

    const page = args.pagination?.page || 0
    const limit = args.pagination?.limit || 10
    const { data, total } = getPaginatedItems(addresses, page, limit)

    return {
      addresses: data.map((request) => ITUserAddressTransformer.toGQL(request, context)),
      total,
    }
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLCountry|undefined}
   */
  getUserCountry(user, _args, context) {
    if (!user.countryId) {
      return
    }

    return CountryTransformer.toGQL(user.countryId, context)
  }

  /**
   * @param {GQLUser} user
   * @returns {GQLOrder|undefined}
   */
  getUserLastOrder(user) {
    return user.lastOrder
  }

  /**
   * @param {GQLUser} user
   * @param {GraphqlContextArgs<{pagination: GQLInputPagination}>} args
   * @returns {GQLOrderList}
   */
  getUserOrders(user, args) {
    if (!args.pagination) return user.orders

    const { page, limit } = args.pagination
    const { data: orders, total } = getPaginatedItems(user.orders.orders, page, limit)

    return {
      orders,
      total,
    }
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<GQLUser|undefined>}
   */
  async getUserProperty(user, _args, context, info) {
    if (info.fieldName === 'premium') {
      if (user.premium === undefined) {
        user.premium = (await getInterfloraPlus(user.email, context)).expirationDate
      }
      return user.premium
    }

    if (user[info.fieldName] !== undefined || user instanceof GQLUser) {
      return user[info.fieldName]
    }

    if (!user.id) {
      return
    }

    const apiUser = await context.loaders.itUser.userById.load(user.id)
    const _user = ITUserTransformer.toGQL(apiUser, context)

    return _user && _user[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  newPassword: string,
   * oldPassword: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  async updatePassword(_parent, args, context) {
    const { newPassword, oldPassword } = args

    if (!context.session?.itUser?.username) {
      throw new GraphQLApolloError('Token expired')
    }

    await context.dataSources.itUser.changePassword(newPassword, oldPassword)

    return this._currentUser(context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{user: GQLInputUpdateUser}>} args
   * @param {GraphQLContext} context
   */
  async updateUser(_parent, args, context) {
    // @todo: company number, pec, VAT number
    if (!context.session?.itUser?.userId) {
      throw new GraphQLApolloError('Token expired')
    }

    const {
      billingAddress,
      billingAddress2,
      billingCity,
      billingPostalCode,
      civility,
      countryId,
      townId,
      townLabel,
      ...apiUser
    } = args.user

    apiUser.country = CountryTransformer.toGQL(countryId, context)

    if (civility) {
      apiUser.civility = CONST.user.civility.mr === civility ? 1 : 2
    }

    apiUser.defaultSenderAddress = null
    if (billingAddress || billingAddress2 || billingCity || billingPostalCode) {
      apiUser.defaultSenderAddress = {
        address: billingAddress,
        address2: billingAddress2,
        company: args.company,
        country: apiUser.country,
        default: true,
        postalCode: billingPostalCode,
        town: {
          label: billingCity,
        },
        type: CONST.address.addressType.sender,
      }
    }

    if (apiUser.fiscalCode && !checkCode(apiUser.fiscalCode)) {
      context.log.error(`Invalid fiscal code ${apiUser.fiscalCode}`)
      const error = new GraphQLApolloError(`Invalid fiscal code`, 400)
      error.addError('invalid', 'fiscalCode', 'Campo Codice fiscale non valido!')
      throw error
    }

    apiUser.id = context.session?.itUser?.userId

    apiUser.town = {
      id: townId,
      label: townLabel,
      postalCode: apiUser.postalCode,
    }

    const response = await context.dataSources.itUser.update(apiUser)
    if (response?.User) {
      await context.loaders.itUser.userById.prime(response.User.id, response.User)
    }

    return ITUserTransformer.toGQL(response.User, context)
  }

  /**
   * @private
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  async _currentUser(context) {
    if (!context.session?.itUser?.userId) {
      return null
    }

    const apiUser = await context.loaders.itUser.userById.load(context.session.itUser.userId)

    return ITUserTransformer.toGQL(apiUser, context)
  }
}

module.exports = UserResolver
