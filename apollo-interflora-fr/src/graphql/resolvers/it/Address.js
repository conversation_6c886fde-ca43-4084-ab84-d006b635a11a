const BaseResolver = require('../ct-datocms/Address')

const { GraphQLApolloError } = require('../../errors')

const ITUserAddressTransformer = require('../../transformers/ituser/Address')
const { fromGQLInputAddress } = require('../../transformers/ituser/ApiAddress')
const TownTransformer = require('../../transformers/availability/Town')

const { addressValidator } = require('../../../helpers/validator/address')

class AddressResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {GQLAddress}
   */
  async addAddress(_parent, args, context) {
    if (!context.session?.itUser?.userId) {
      throw new GraphQLApolloError('Invalid token')
    }

    const userId = context.session.itUser.userId
    const address = fromGQLInputAddress(args.address, context)
    const response = await context.dataSources.itUser.setAddress(userId, address)

    return ITUserAddressTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{addressId: string}>} args
   * @param {GraphQLContext} context
   * @returns {GQLAddress}
   */
  async removeAddress(_parent, args, context) {
    if (!context.session?.itUser?.userId) {
      throw new GraphQLApolloError('Invalid token')
    }

    const userId = context.session.itUser.userId
    const response = await context.dataSources.itUser.removeAddress(userId, args.addressId)

    return ITUserAddressTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddress>}
   */
  async updateAddress(_parent, args, context) {
    if (!context.session?.itUser?.userId) {
      throw new GraphQLApolloError('Invalid token')
    }

    const userId = context.session.itUser.userId
    const address = fromGQLInputAddress(args.address, context)
    const response = await context.dataSources.itUser.setAddress(userId, address)

    return ITUserAddressTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLValidationResult>}
   */
  async validateAddress(_parent, args, context) {
    return addressValidator(args.address, context.language, {
      townSearch: async (search, country, language) => {
        return (
          await context.loaders.availability.towns.load({
            country,
            language,
            search,
          })
        ).map((place) => TownTransformer.toGQL(place, context))
      },
    })
  }
}

module.exports = AddressResolver
