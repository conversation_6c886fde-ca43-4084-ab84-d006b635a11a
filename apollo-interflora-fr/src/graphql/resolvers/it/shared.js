const ITUserUserTransformer = require('../../transformers/ituser/User')

/**
 * @param {GraphQLContext} context
 * @returns {Promise<GQLUser | undefined>}
 * @protected
 */
const getCurrentUser = async (context) => {
  if (context.auth && context.auth.isAnonymous) {
    return
  }

  if (!context.session?.itUser) {
    return
  }

  const apiUser = await context.loaders.itUser.userById.load(context.session.itUser.userId)

  return ITUserUserTransformer.toGQL(apiUser, context)
}

module.exports = {
  getCurrentUser,
}
