const apm = require('elastic-apm-node')
const config = require('../../../config')
const debug = require('debug')('it:auth')
const CONST = require('../../../const')

const BaseResolver = require('../ct-datocms/Authentication')

const { CTSessionTransformer, CTTokenTransformer } = require('../../transformers/commerceTools')

const { ItUserSessionTransformer, ITUserTransformer } = require('../../transformers/ituser')
const CountryTransformer = require('../../transformers/gfs/Country')
const { GraphQLApolloError } = require('../../errors')

/** @typedef {import('../_typedef')} */
/** @typedef {import('../../types/_typedef')} */
const { getCurrentUser } = require('./shared')
const { checkCode } = require('../../../helpers/ITFiscaleCode')
const { parseJwt } = require('../../../helpers')

class AuthenticationResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string}>} args
   * @param {GraphQLContext} context
   * @returns {boolean}
   */
  async checkEmailExists(_parent, args, context) {
    return !!(await context.loaders.itUser.userByEmail.load(args.email))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{userid: string, code: string}>} args
   * @param {*} context
   * @returns {Promise<boolean>}
   */
  async confirmUserEmail(_parent, args, context) {
    try {
      await context.dataSources.itUser.confirmUserEmail(args.userId, args.code)
    } catch (e) {
      apm.captureError(e, { custom: e })
      throw new GraphQLApolloError('Invalid code')
    }

    return true
  }

  /**
   * @param {Token|undefined} token
   * @param {GraphQLContextArg<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser|void>}
   */
  getTokenUser(token, _args, context) {
    if (token && token.user) {
      return token.user
    }

    if (context.auth && !context.auth.isAnonymous) {
      if (!context.session.itUser?.legacy && context.session.user) {
        return super.getTokenUser(...arguments)
      }
      return getCurrentUser(context)
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArg<{
   *  username: string,
   *  password: string,
   * }>} args
   * @param {GraphQLContext} context
   */
  async login(_parent, args, context) {
    try {
      const ctUser = await super.login(...arguments)
      if (ctUser) {
        return ctUser
      }
    } catch (e) {
      // do nothing
    }

    let commerceTools = context.session?.commerceTools
    if (!commerceTools) {
      const response = await context.dataSources.ctAuth.anonymousLogin()
      await this._setCtAuthContext(context, response)
      commerceTools = CTSessionTransformer.fromResponse(response)
    }

    let response
    try {
      response = await context.dataSources.itAuth.login(args.username, args.password)
    } catch (e) {
      const message = context.appConfig.errors.login || e.message

      apm.captureError(e, { custom: e })
      const error = new GraphQLApolloError(message, 401)
      error.addError('InvalidGrantError', 'username', message)
      error.addError('InvalidGrantError', 'password', '')
      throw error
    }

    if (response.error) {
      let message = config.apps[context.siteId].errors.login
      switch (response.error) {
        case 'ACCOUNT_NOT_CONFIRMED':
          message = config.apps[context.siteId].errors.loginUnconfirmed
          break
        case 'ACCOUNT_LOCKED':
          message = config.apps[context.siteId].errors.loginBlocked
          break
      }

      const error = new GraphQLApolloError(message, 401)
      error.addError('InvalidGrantError', 'username', message)
      error.addError('InvalidGrantError', 'password', '')
      throw error
    }

    const itUser = ItUserSessionTransformer.fromResponse(response)

    if (response?.user) {
      const { user } = response
      await context.loaders.itUser.userById.prime(user.id, user)
      const gqlUser = ITUserTransformer.toGQL(user, context)

      // attach last cart if exists
      if (!context.session.apiCart?.id && gqlUser.type !== CONST.user.userType.customerCare) {
        let apiCart = await context.dataSources.carts.getCartByUserId(user.id)
        if (apiCart) {
          if (apiCart.anonymousId !== context.session.commerceTools.anonymousId) {
            apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
              actions: [
                {
                  action: 'setAnonymousId',
                  anonymousId: context.session.commerceTools.anonymousId,
                },
              ],
              version: apiCart.version,
            })
          }
          context.session.apiCart = { countryId: apiCart.shippingAddress?.country, id: apiCart.id }
          await Promise.all([context.loaders.commerceTools.carts.prime(apiCart.id, apiCart), context.session?.save?.()])
        }
      }
    }

    if (itUser) {
      await this._setUserAuthContext(context, itUser, true)
    }

    const user = CTTokenTransformer.toGQL(
      {
        expires_in: itUser?.accessToken?.lifetime
          ? itUser?.accessToken?.lifetime * 60
          : commerceTools.accessToken.lifetime,
        login: context.auth.login,
        refresh_token_expires_in: itUser?.refreshToken?.lifetime ? itUser?.refreshToken?.lifetime * 60 : null,
        scope: commerceTools.scope,
      },
      context
    )
    this._assertValidTokenSessionId(user.value)

    await this.onLoggedIn(user, context)

    return user
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{refreshToken: string}>} args
   * @param {GraphQLContext} context
   */
  async refreshToken(_parent, args, context) {
    const refreshJWT = parseJwt(args.refreshToken)
    if (!refreshJWT?.sessionId) {
      throw new GraphQLApolloError('Token expired', 401)
    }

    const session = await this._loadSessionByToken(refreshJWT, context)
    if (!session) {
      throw new GraphQLApolloError('Token expired', 401)
    }

    const { commerceTools, itUser } = session

    if (!itUser || itUser.refreshToken.expiredAt < Date.now()) {
      throw new GraphQLApolloError('Token expired', 401)
    }

    const response = await context.dataSources.itAuth.refresh(itUser.refreshToken.value)
    session.itUser = ItUserSessionTransformer.fromResponse(response)
    await session.save()

    const token = CTTokenTransformer.toGQL(
      {
        expires_in: itUser?.accessToken?.lifetime || commerceTools.accessToken.lifetime,
        login: context.auth.login,
        refresh_token_expires_in: itUser?.refreshToken?.lifetime,
        scope: commerceTools.scope,
      },
      context
    )
    this._assertValidTokenSessionId(token.value)

    return token
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{user: GQLInputRegisterUser}>} args
   * @param {*} context
   * @returns {Promise<GQLToken>}
   */
  async registerUser(_parent, args, context) {
    // @todo: company number, pec, VAT number
    const {
      billingAddress,
      billingAddress2,
      billingCity,
      billingPostalCode,
      civility,
      countryId,
      townId,
      townLabel,
      username: _username,
      ...apiUser
    } = args.user

    const username = _username || apiUser.email || undefined

    apiUser.country = CountryTransformer.toGQL(countryId, context)

    if (civility) {
      apiUser.civility = CONST.user.civility.mr === civility ? 1 : 2
    }

    apiUser.defaultSenderAddress = null
    if (billingAddress || billingAddress2 || billingCity || billingPostalCode) {
      apiUser.defaultSenderAddress = {
        address: billingAddress,
        address2: billingAddress2,
        company: args.company,
        companyNumber: args.companyNumber,
        country: apiUser.country,
        default: true,
        pec: args.pec,
        postalCode: billingPostalCode,
        town: {
          label: billingCity,
        },
        type: CONST.address.addressType.sender,
      }
    }

    if (apiUser.fiscalCode && !checkCode(apiUser.fiscalCode)) {
      context.log.error(`Invalid fiscal code ${apiUser.fiscalCode}`)
      const error = new GraphQLApolloError(`Invalid fiscal code`, 400)
      error.addError('invalid', 'fiscalCode', 'Campo Codice fiscale non valido!')
      throw error
    }

    apiUser.town = {
      id: townId,
      label: townLabel,
      postalCode: apiUser.postalCode,
    }

    apiUser.username = username
    if (!apiUser.email) apiUser.email = username

    debug('register user %O', apiUser)

    const response = await context.dataSources.itUser.register(apiUser)
    const registrationTimestamp = Math.floor(new Date(response.createdAt).getTime() / 1000)

    return {
      createdAt: registrationTimestamp,
      state: CONST.user.userRegistrationStates.PENDING_EMAIL_VALIDATION,
    }
  }

  /**
   * Updates session following login
   *
   * @param {GraphQLContext} context
   * @param {SessionApiData | GQLUser} user
   * @param {boolean} legacy
   * @returns {Promise<void>}
   * @protected
   */
  async _setUserAuthContext(context, user, legacy = true) {
    const itUser = legacy
      ? {
          ...user,
        }
      : {
          userId: user.id,
          username: user.username,
        }

    const wasAnonymous = context.auth.isAnonymous
    const userChanged = context.auth.login !== itUser.username

    context.auth.isAnonymous = false
    context.auth.login = itUser.username

    if (context.session.apiCart) {
      if (legacy) {
        if (wasAnonymous) {
          // we have an anonymous cart - attach it to the user
          let apiCart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)
          const actions = [
            {
              action: 'setCustomField',
              name: 'userId',
              value: itUser.userId,
            },
          ]
          if (apiCart.anonymousId !== context.session.commerceTools.anonymousId) {
            actions.push({
              action: 'setAnonymousId',
              value: context.session.commerceTools.anonymousId,
            })
          }

          apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
            actions,
            version: apiCart.version,
          })
          await context.loaders.commerceTools.carts.prime(apiCart.id, apiCart)
        } else if (userChanged) {
          // was logged int and changed user - kill current cart
          await context.loaders.commerceTools.carts.clear(context.session.apiCart.id)
          delete context.session.apiCart
        }
      } else if (!wasAnonymous && userChanged && !legacy) {
        // was logged in and changed user - kill current cart
        await this._clearSessionCart(context)
      }
    }

    if (!legacy) {
      context.session.user = { email: user.email, id: user.id }
    }

    context.session.itUser = {
      ...itUser,
      legacy,
    }

    await context.session?.save?.()
  }
}

module.exports = AuthenticationResolver
