const BaseResolver = require('../ct-datocms/Contact')
const { getFRHybrisContext } = require('./shared')

class ContactResolver extends BaseResolver {
  /**
   * @param {undefined} parent
   * @param {{}} args
   * @param {FRContext} context
   * @returns {Promise<GQLContactSubject>}
   */
  async getContactFormSubjects(parent, args, context) {
    return context.resolvers.hybris.contact.getContactFormSubjects(parent, args, getFRHybrisContext(context))
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{contactMessage: GQLContactMessageType}>} args
   * @param {FRContext} context
   * @returns {Promise<GQLContactMessage>}
   */
  async sendContactMessage(parent, args, context) {
    return context.resolvers.hybris.contact.sendContactMessage(parent, args, getFRHybrisContext(context))
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{retractation: GQLInputRetractation}>} args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  async sendRetractation(parent, args, context) {
    return context.resolvers.hybris.contact.sendRetractation(parent, args, getFRHybrisContext(context))
  }
}

module.exports = ContactResolver
