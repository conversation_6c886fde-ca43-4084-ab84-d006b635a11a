const config = require('../../../config')
const CONST = require('../../../const')
const { removeAccents } = require('../../../helpers/string')
const { GraphQLApolloError } = require('../../errors')
const BaseResolver = require('../ct-datocms/Cms')
const {
  PageTransformer: HybrisPageTransformer,
  TownPageTabTransformer,
  TemplateTransformer,
} = require('../fr-hybris/transformers')

class CmsResolver extends BaseResolver {
  /**
   * @param {GQLComponentZoneType} componentZone
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTemplate>}
   */
  getTemplates(componentZone, _args, context) {
    /*
      @dirty and to change for full fr ct mig
      if we have a template then the content is in dato and we return it
      otherwise its content kept in hybris (florist, floristDepts and cms pages for fr mig)
      and we query it but we change the mediaBaseUrl to query it at the right place.
      "MVP" dirty style.
    */
    if (componentZone?.templates) {
      return componentZone?.templates
    } else {
      const hybrisMediaBaseUrl = config.apps[context.siteId].hybrisMediaBaseUrl
      return (
        componentZone?.templates ||
        Promise.all(
          componentZone.uids.map(async (uid) =>
            TemplateTransformer.toGQL(await context.loaders.hybris.components.load({ componentId: uid }), context, {
              mediaBaseUrl: hybrisMediaBaseUrl,
            })
          )
        ) ||
        []
      )
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *   fallback: boolean,
   *   previousRoute: GQLInputRoute,
   *   route: GQLInputRoute,
   *   zoneNames: string[]
   * }>} args
   * @param {FRContext} context
   * @returns {Promise<GQLPage>}
   */
  async getPage(_parent, args, context) {
    const { route } = args

    let productCategory
    let code
    let name
    let pageType = CONST.cms.pageTypes.content
    let type

    switch (route.name) {
      case 'cms':
        {
          const slugParam = route.params.find((param) => param.name === 'slug')
          if (!slugParam) {
            return null
          }
          name = slugParam.value.replace(/\/+/, '')
        }
        break

      case 'floristDepartment':
      case 'floristTown':
      case 'floristTowns':
        pageType = CONST.cms.pageTypes[route.name]
        {
          let paramName = pageType === CONST.cms.pageTypes.floristTown ? 'slugTown' : 'slugUrl'

          for (const param of route.params) {
            if (param.name === paramName) {
              code = param.value
            }
          }
        }
        break
      case 'floristDepartments':
        name = route.name.replace(/^\//, '')
        break
      case 'product': {
        const slug = route.params.find((param) => param.name === 'slugProduct')?.value

        // If we are on a google shopping session, redirect to hybris version
        // NB: used to redirect for GS products only. @see v4.2.1 if needed to switch back
        let isGoogleShopping = context.partner === 'google'
        if (!isGoogleShopping && context.req.headers['x-from-url']) {
          try {
            const url = new URL(context.req.headers['x-from-url'])
            if (url.searchParams.get('ggshopping') === '1' || url.searchParams.get('utm_medium') === 'organic-SHO') {
              isGoogleShopping = true
            }
          } catch (e) {
            context.log.error(e, `Error parsing x-from-url header: ${context.req.headers['x-from-url']}`)
          }
        }

        if (isGoogleShopping) {
          const error = new GraphQLApolloError('Moved permanently', 301)
          error.addError('location', 'location', `/p/${slug}?frz-forcenewhost=false&forcenewhost`)
          throw error
        }

        return super.getPage(_parent, args, context)
      }
      default:
        return super.getPage(_parent, args, context)
    }

    code = removeAccents(code)

    /** @type {APICMSPage} */
    let apiPage = await context.loaders.hybris.pages.load({
      code,
      name,
      pageType,
    })

    const fallback = args.defaultCmsPage ?? args.fallback // @todo: delete defaultCmsPage option when front is ready
    if (!apiPage && fallback) {
      name = route.name.replace(/^\//, '')
      code = config.apps[context.siteId].cms.emptyContentPageUid
      apiPage = await context.loaders.hybris.pages.load({
        code,
        name,
        pageType,
      })

      // no default page found - no 400 but empty result
      if (!apiPage) return null
    }

    const components = (
      await context.loaders.hybris.components.loadMany(
        apiPage.contentSlots?.contentSlot
          ?.map((slot) => (slot.components?.component || []).map((component) => component.uid))
          .flat()
          .map((uid) => ({ componentId: uid }))
          .filter(Boolean) ?? []
      )
    )?.reduce((o, component) => {
      o[component.uid] = component
      return o
    }, {})

    const gqlDatoHomePage = await super.getPage(
      undefined,
      {
        ...args,
        route: {
          name: 'index',
          params: [],
          zoneNames: [],
        },
      },
      context
    )

    const gqlPage = HybrisPageTransformer.toGQL(apiPage, context, {
      components,
      pageType,
      productCategory,
      type,
      zones: args.zoneNames,
    })

    // cross-pages content is fed from datocms
    gqlPage.festMode = gqlDatoHomePage.festMode
    gqlPage.footers = gqlDatoHomePage.footers
    gqlPage.menus = gqlDatoHomePage.menus

    return gqlPage
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string, includeAddress: (boolean|undefined)}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTownPage>}
   */
  async getTownProductInfoBySlug(_parent, args, context) {
    const { includeAddress } = args
    if (includeAddress) {
      return super.getTownProductInfoBySlug(...arguments)
    }
    return context.resolvers.hybris.cms.getTownProductInfoBySlug(_parent, args, context)
  }

  /**
   * @param {GQLTownPage} _townPage
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTownPageTab[]>}
   */
  async getTownPageTabs(_townPage, _args, context) {
    const apiTabs = await context.dataSources.hybrisCms.getTownProductInfoComponent()

    return (apiTabs.components || []).map(TownPageTabTransformer.toGQL)
  }
}

module.exports = CmsResolver
