const apm = require('elastic-apm-node')

const CONST = require('../../../const')

const BaseResolver = require('../ct-datocms/Product')

const {
  GQLAvailabilityDateRanges,
  GQLProductCustomizationPreview,
  GQLProductVariant,
  GQLStock,
} = require('../../models')

const { DateRangeTransformer: HybrisDateRangeTransformer, StockTransformer } = require('../fr-hybris/transformers')
const CustomizationTransformer = require('../../transformers/cadeaux/Customization')

const { variantIds, computeIsFromWorkshop } = require('../../../helpers/commercetools')
const { getFRHybrisContext } = require('./shared')
const { getFeatureEnabled } = require('../../../helpers/features')
const config = require('../../../config')

class ProductResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDateRange[]>}
   */
  async getDeliveryAvailabilities(_parent, _args, context) {
    const deliveryDates = await context.loaders.hybris.deliveryDates.load({ countryId: context.countryId })

    return HybrisDateRangeTransformer.toGQL(deliveryDates)
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} args
   * @param {FRContext} context
   * @param {Object} info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductImages(product, args, context, info) {
    if (product?.isLoaded?.(CONST.dataSources.hybris)) {
      return context.resolvers.hybris.product.getProductImages(product, args, getFRHybrisContext(context), info)
    }
    return super.getProductImages(...arguments)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  variantCode: string,
   *  customizations: GQLInputProductCustomizationLine[],
   * }>} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLProductCustomizationPreview>}
   */
  async getProductPreview(_parent, args, context) {
    const response = await context.dataSources.cadeaux.getPreview(args.variantCode, args.customizations)
    const preview = new GQLProductCustomizationPreview()

    preview.previewUrl = response.images[0]?.image
    return preview
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} args
   * @param {FRContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getProductProperty(product, args, context, info) {
    if (product?.isLoaded?.(CONST.dataSources.hybris)) {
      return context.resolvers.hybris.product.getProductProperty(product, args, getFRHybrisContext(context), info)
    }
    return super.getProductProperty(...arguments)
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductVariantImages(variant, args, context, info) {
    if (variant?.parent?.isLoaded?.(CONST.dataSources.hybris)) {
      return context.resolvers.hybris.product.getProductVariantImages(variant, args, getFRHybrisContext(context), info)
    }
    return super.getProductVariantImages(...arguments)
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} args
   * @param {FRContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getProductVariantProperty(variant, args, context, info) {
    if (variant?.parent?.isLoaded?.(CONST.dataSources.hybris)) {
      return context.resolvers.hybris.product.getProductVariantProperty(
        variant,
        args,
        getFRHybrisContext(context),
        info
      )
    }

    /**
     * WARNING
     * the check for APOLLO_F_PRODUCT_CUSTOMIZATION should be replaced by checking that the product type corresponds to one where prodcts are expected to have customizations.
     * These product types do not exist yet but will become a reality once we start creating Cadeaux products in our ERP.
     */
    if (variant instanceof GQLProductVariant && info.fieldName === 'customizations' && variant.sku) {
      const isCustomizationEnabled = await getFeatureEnabled(CONST.unleash.toggles.productCustomization, {
        sessionId: context.clientSessionId,
      })

      if (isCustomizationEnabled) {
        const customization = await context.loaders.cadeaux.customizations.load({ cadeauxVariantId: variant.sku })
        return customization?.form?.map((f) => CustomizationTransformer.toGQL(f.field, context))
      }
    }

    if (info.fieldName === 'stock') {
      const ctProduct = await this._loadCTProductFromVariant(variant, context)
      const { variantId } = variantIds(variant.code)

      const inventoryChannel = await context.loaders.commerceTools.channelsByKey.load(
        config.commerceTools.priceChannelKey
      )
      const ctVariant = [ctProduct.masterVariant, ...ctProduct.variants].find((v) => v.id === variantId)
      const validPrice = ctVariant.prices.some((price) => price.channel.id === inventoryChannel.id && price.value)
      const ctStockStatus = ctVariant.availability?.channels?.[inventoryChannel.id]?.isOnStock && validPrice
      const ctStockValue = ctVariant.availability?.channels?.[inventoryChannel.id]?.availableQuantity
      const isWorkshopProduct = computeIsFromWorkshop(ctVariant, context)

      try {
        // In CT FR CT stock also determines if the product is being sold or not to customers. 0 in stock means unpublished but we need its pdp to be viewable for SEO.
        if (!ctStockStatus || ctStockValue === 0) {
          const stock = new GQLStock()
          stock.value = 0
          stock.status = CONST.product.stockStatuses.outOfStock
          return stock
        }

        // In CT FR CT if the product is "atelier" (from workshop) its sold elsewhere so the source of truth is still hybris.
        if (isWorkshopProduct) {
          return StockTransformer.toGQL(
            await context.dataSources.hybrisProduct.checkStock(variant.parent.key, variant.sku, context.countryId)
          )
        } else {
          const stock = new GQLStock()
          stock.value = ctStockValue
          stock.status =
            ctStockStatus && ctStockValue > 0
              ? CONST.product.stockStatuses.inStock
              : CONST.product.stockStatuses.outOfStock
          return stock
        }
      } catch (e) {
        apm.captureError(e, {
          custom: e,
          message: `Couldn't get stock for product key ${variant.parent.key}, variant sku ${variant.sku}, country ${context.countryId}, ctStockStatus ${ctStockStatus}, ctStockValue ${ctStockValue}, isWorkshop ${isWorkshopProduct}. Error:${e}`,
        })
        return StockTransformer.toGQL()
      }
    }

    return super.getProductVariantProperty(...arguments)
  }

  async _getProductDeliveryDates(args, context) {
    const { productId, variantId } = variantIds(args.code)
    const product = await this._loadProductFromCT(
      {
        code: productId,
      },
      context
    )

    const variant = product.variants.find((variant) => variant.id === variantId)
    const code = variant?.sku ?? product.key

    const deliveryDates = await context.loaders.hybris.deliveryDates.load({
      code,
      countryId: args.countryId || context.countryId,
      postalCode: args.postalCode,
    })

    return new GQLAvailabilityDateRanges(HybrisDateRangeTransformer.toGQL(deliveryDates))
  }

  /**
   * @param {string[]} skipAvailabilityOn
   * @param {string} countryId
   * @returns {boolean}
   */
  _isGenericAccessory(_skipAvailabilityOn = [], _countryId) {
    return true
  }
}

module.exports = ProductResolver
