const config = require('../../../config')
const BaseResolver = require('../ct-datocms/Geo')
const { DepartmentTransformer } = require('../fr-hybris/transformers')

const HybrisTownTransformer = require('../fr-hybris/transformers/Town')

class GeoResolver extends BaseResolver {
  /**
   * @param {GQLDepartment} department
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<any>}
   */
  async getDepartmentProperty(department, _args, _context, info) {
    return department[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{countryId: string, search: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTown[]>}
   */
  async getTowns(_parent, args, context) {
    const localities = await context.loaders.hybris.towns.load({
      countryIsoCode: args.countryId || context.countryId,
      search: args.search,
    })

    return localities.map(HybrisTownTransformer.toGQL).map((town) => {
      town.label = town.label.replace(`(${town.postalCode})`, '').trim()
      return town
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string, includeAddress: (boolean|undefined)}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{id: string, label: string}[]>}
   */
  async getDepartmentBySlug(_parent, args, context) {
    const { slugUrl, includeAddress } = args
    if (includeAddress) {
      return super.getDepartmentBySlug(...arguments)
    }
    /** @todo loaders */
    /** @todo don't use hybris media url once depts data has been migrated to dato */
    const department = await context.dataSources.hybrisDepartment.getDepartment(slugUrl)
    return DepartmentTransformer.toGQL(department, context, {
      isDomtom: department.isDomtom,
      mediaBaseUrl: config.apps[context.siteId].hybrisMediaBaseUrl,
      type: 'floristDepartment',
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{includeAddress: (boolean|undefined)}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{id: string, label: string}[]>}
   */
  async getDepartments(_parent, args, context) {
    const { includeAddress } = args
    if (includeAddress) {
      return super.getDepartments(...arguments)
    }
    /** @todo loaders */
    const departmentList = await context.dataSources.hybrisDepartment.getDepartments()

    return departmentList.listDepartmentsFr
      .map((department) =>
        DepartmentTransformer.toGQL(department, context, {
          mediaBaseUrl: config.apps[context.siteId].hybrisMediaBaseUrl,
        })
      )
      .concat(
        departmentList.listDepartmentsDomtom.map((department) =>
          DepartmentTransformer.toGQL(department, context, {
            isDomtom: true,
            mediaBaseUrl: config.apps[context.siteId].hybrisMediaBaseUrl,
          })
        )
      )
  }
}

module.exports = GeoResolver
