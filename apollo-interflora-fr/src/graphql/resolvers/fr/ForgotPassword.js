const BaseResolver = require('../ct-datocms/ForgotPassword')

class ForgotPasswordResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, password: string}>} args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  async resetPassword(_parent, args, context) {
    await context.dataSources.hybrisForgottenPasswords.resetPassword(args.code, args.password)
    return true
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string}>} args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  async resetPasswordCheckCode(_parent, args, context) {
    try {
      await context.dataSources.hybrisForgottenPasswords.validateResetToken(args.code)
      return true
    } catch (error) {
      if (error.extensions.code !== 400) {
        /** In case of a server error, or even authorization error */
        throw error
      }

      return false
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string}>} args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  async resetPasswordRequest(_parent, args, context) {
    await context.dataSources.hybrisForgottenPasswords.sendResetToken(args.username)
    return true
  }
}

module.exports = ForgotPasswordResolver
