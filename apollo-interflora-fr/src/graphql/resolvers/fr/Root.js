const config = require('../../../config')

const BaseResolver = require('../ct-datocms/Root')

class RootResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ form: { placement: string, step: string, userCategory: string } }>} args
   * @returns {GQLForm}
   */
  getForm(_parent, args) {
    const { placement, step, userCategory } = args.form

    const fieldsets = config.forms[placement]?.[userCategory]?.[step]
    if (!fieldsets) return null

    return {
      fieldsets,
      placement,
      step,
      title: 'title',
      userCategory,
    }
  }
}

module.exports = RootResolver
