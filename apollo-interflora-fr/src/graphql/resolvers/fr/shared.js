const { randomUUID } = require('node:crypto')

const config = require('../../../config')
const CONST = require('../../../const')

const Context = require('../../Context')
const { Session } = require('../../../plugins/session')
const { UserTransformer: HybrisUserTransformer } = require('../fr-hybris/transformers')

const { GQLOrderList } = require('../../models')
const { getOrderList: getCTOrderList } = require('../../../helpers/commercetools/orders')
const { getUserOrders: getHybrisOrdersList } = require('../../../helpers/hybris/orders')

/** @typedef {import('./_typedef')} */

/** @type {WeakMap<FRHybrisContext, FRContext>} */
const frContexts = new WeakMap()

/** @type {WeakMap<FRContext, FRHybrisContext>} */
const hybrisContexts = new WeakMap()

/**
 * @param {FRContext} context
 * @returns {Promise<GQLUser|undefined>}
 */
const getCurrentHybrisUser = async (context) => {
  if (!context.auth || context.auth.isAnonymous) {
    return
  }

  const [apiUser, apiUserAddresses] = await Promise.all([
    context.dataSources.hybrisUser.getCurrent(),
    context.dataSources.hybrisAddress.getAll(),
  ])

  return HybrisUserTransformer.toGQL(apiUser, context, { addresses: apiUserAddresses })
}

/**
 * @param {FRHybrisContext} frHybrisContext
 * @returns {FRContext}
 */
const getFRContext = (frHybrisContext) => {
  const { createResolvers: createCTDatocmsResolvers } = require('.')

  if (!frContexts.has(frHybrisContext)) {
    const context = new Context(frHybrisContext.server, frHybrisContext.req, frHybrisContext.reply)

    const { _resolvers, ...rest } = frHybrisContext

    Object.assign(context, rest)
    context._resolvers = createCTDatocmsResolvers(context)

    frContexts.set(frHybrisContext, context)
  }

  return frContexts.get(frHybrisContext)
}

const toFR = (callArguments) => {
  const context = callArguments[2]
  const frContext = getFRContext(context)
  const frResolverArgs = [...callArguments]
  frResolverArgs[2] = frContext

  return { frContext, frResolverArgs }
}

/**
 * @param {FRContext} context
 * @returns {FRHybrisContext}
 */
const getFRHybrisContext = (context) => {
  if (!hybrisContexts.has(context)) {
    const frHybrisContext = new Context(context.server, context.req, context.reply)

    const { _resolvers, ...rest } = context

    Object.assign(frHybrisContext, rest)
    frHybrisContext._resolvers = context.resolvers.hybris

    hybrisContexts.set(context, frHybrisContext)
  }

  return hybrisContexts.get(context)
}

/**
 * FR specific rule about which product can or not be the main line item in a cart.
 * This ensures some checks in legacy FR systems don't crash on call.
 * @param {CTLineItem} lineItem
 * @returns {boolean}
 */
const isMainLineItem = (lineItem) => {
  return (
    lineItem.custom?.fields?.isAccessoryFor === undefined &&
    !(config.products?.notMainCartItemProducts?.keys?.includes(lineItem.productKey) ?? true) &&
    !(
      config.products?.notMainCartItemProducts?.productTypes?.includes(
        lineItem.variant.attributes.find((attr) => attr.name === 'product_type')?.value.key
      ) ?? true
    )
  )
}

/**
 * @param {APIUserToken} response
 * @param {FRContext} context
 * @returns {Promise}
 */
const updateContextOnHybrisLogin = async (response, context) => {
  const sessionId = context.auth?.sessionId ?? randomUUID()
  const login = response.login ?? CONST.user.anonymousLogin

  if (!context.auth) context.auth = {}

  Object.assign(context.auth, {
    isAnonymous: login === CONST.user.anonymousLogin,
    isPro: response.b2bCustomer === 'true',
    isRefreshToken: false,
    login,
    oauthToken: response.access_token,
    scope: response.scope,
    sessionId,
  })

  context.session = await Session.get(context.server.redis, context.auth.sessionId, {
    expiresAt: Math.ceil(Date.now() / 1000) + response.expires_in,
  })
  context.session.hybris = {
    refreshToken: response.refresh_token,
    token: response.access_token,
  }
  context.session.user = {}
  await context.session.save()

  const promises = []

  if (login !== CONST.user.anonymousLogin) {
    const user = await getCurrentHybrisUser(context)

    context.session.user = {
      email: user.email,
      type: user.type,
      username: response.login,
    }
    promises.push(context.session.save())
  }

  // attach cart if needed
  if (context.session.apiCart?.id) {
    /** @type {CTCart} */
    const apiCart = await context.resolvers.cart._getApiCart(context)
    if (apiCart.customerEmail !== context.session.user.email) {
      apiCart.customerEmail = context.session.user.email
      promises.push(
        context.dataSources.carts
          .updateCartAsAdmin(context.session.apiCart.id, {
            actions: [
              {
                action: 'setCustomerEmail',
                email: context.session.user.email,
              },
            ],
            version: apiCart.version,
          })
          .then((cart) =>
            Promise.all([
              context.loaders.commerceTools.carts.prime(apiCart.id, cart),
              context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, cart),
            ])
          )
      )
    }
  }

  await Promise.all(promises)
}

/**
 * @param {GQLUser} user
 * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
 * @param {FRContext} context
 * @returns {Promise<GQLOrderList>}
 */
const getUserOrders = async (user, args, context) => {
  const limit = args.pagination?.limit ?? 10
  const offset = args.pagination?.offset ?? Math.max(args.pagination?.page ?? 0, 0) * limit

  /** @type {Date} */
  let previousFirstDate
  let ctOffset = 0
  let total = 0

  if (offset > 0) {
    // we are not on the first page - getting last page recursively (but only the last <limit> results)
    const previous = await getUserOrders(
      user,
      {
        ...args,
        pagination: {
          limit,
          offset: Math.max(0, offset - limit),
        },
      },
      context
    )

    if (previous.orders.length < limit) {
      const result = new GQLOrderList()
      result.total = previous.total
      return result
    }

    total = previous.total
    previousFirstDate = previous.orders.at(-1).createdAt

    const list = await getCTOrderList(
      user,
      {
        limit: 0,
        offset: 0,
      },
      {
        ...args.filter,
        after: previousFirstDate,
      },
      context
    )
    ctOffset = list.total
  }

  /** @type {GQLOrder[]} */
  const orders = []

  const ctList = await getCTOrderList(user, { limit, offset: ctOffset }, args.filter, context)

  orders.push(...ctList.orders)

  // only the first page can give a valid total - missing hybris orders for now
  if (offset === 0) {
    total = ctList.total
  }

  const hybrisList = await getHybrisOrdersList(
    user,
    {
      ...args.filter,
      before: previousFirstDate,
    },
    context
  )
  orders.push(...hybrisList.orders)

  if (offset === 0) {
    total += hybrisList.total
  }

  orders.sort((a, b) => b.createdAt - a.createdAt)

  const orderList = new GQLOrderList()

  orderList.total = total
  orderList.orders = orders.slice(0, limit)

  return orderList
}

module.exports = {
  getCurrentHybrisUser,
  getFRContext,
  getFRHybrisContext,
  getUserOrders,
  isMainLineItem,
  toFR,
  updateContextOnHybrisLogin,
}
