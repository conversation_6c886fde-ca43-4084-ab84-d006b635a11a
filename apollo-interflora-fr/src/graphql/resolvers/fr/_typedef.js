/** @typedef {import('../../datasources/_typedef').FRDataSources} FRDataSources */

/** @typedef {import('../../dataloaders/availability').loaderDefinitions} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs/types').GFSDataLoaders} GFSDataLoaders */
/** @typedef {import('../../resolvers/fr-hybris/dataloaders')} HybrisDataLoaders */

/** @typedef {import('../../resolvers/fr').FRResolvers} FRResolvers */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: FRDataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *    hybris: HybrisDataLoaders
 *  }
 *  resolvers: FRResolvers
 * }} FRContext
 */
