const BaseResolver = require('../ct-datocms/Order')
const apm = require('elastic-apm-node')

const {
  getFRHybrisContext,
  updateContextOnHybrisLogin,
  isMainLineItem,
  getCurrentHybrisUser,
  getUserOrders,
} = require('./shared')
const { isUUID } = require('../../../helpers/string')
const config = require('../../../config')
const CONST = require('../../../const')
const { GQLOrderResult, GQLDelivery } = require('../../models')
const { CTOrderTransformer } = require('../../transformers/commerceTools')
const {
  DateRangeTransformer: HybrisDateRangeTransformer,
  DeliveryTimeRangesResultTransformer: HybrisDeliveryTimeRangesResultTransformer,
  OrderTransformer: HybrisOrderTransformer,
  TokenTransformer: HybrisTokenTransformer,
  TownTransformer: HybrisTownTransformer,
} = require('../fr-hybris/transformers')
const { getDistributionChannels, isByStemsLineItem } = require('../../../helpers/commercetools')
const { DeliveryTimeRangeService } = require('../../../helpers/hybris/DeliveryTimeRangeService')
const { SelfcareService } = require('../../../helpers/selfcare/SelfcareService')
const UserTransformer = require('../fr-hybris/transformers/User')
const InterfloraPlus = require('../../models/InterfloraPlus')

class OrderResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string}>} args
   * @param {FRContext} context
   * @param {GraphGQLResolveInfo} _info
   * @returns {GQLOrder}
   */
  async getOrder(_parent, args, context, _info) {
    // given order id is actually either the real ids, or the order number (ITXXXXXXXXX).
    if (!isUUID(args.orderId)) {
      try {
        const order = await context.resolvers.hybris.order.getOrder(_parent, args, getFRHybrisContext(context), _info)
        return order
      } catch (e) {
        const ctOrder = await context.loaders.commerceTools.ordersByOrderNumber.load(args.orderId)
        return CTOrderTransformer.toGQL(ctOrder, context, {
          distributionChannels: await getDistributionChannels(context),
        })
      }
    }
    return super.getOrder(...arguments)
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} args
   * @param {FRContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getOrderProperty(order, args, context, info) {
    if (!isUUID(order.id)) {
      const frContext = getFRHybrisContext(context)

      // preload products to avoid CT resolver chain
      if (['accessoryItems', 'productItems', 'serviceItems'].includes(info.fieldName)) {
        const entries = order[info.fieldName]

        return Promise.all(
          entries.map(async (entry) => {
            /** @type {GQLProduct} */
            const product = await context.resolvers.hybris.product.getProduct(
              undefined,
              entry.variant.parent,
              frContext
            )
            const variant = product.variants.find((variant) => variant.code === entry.variant.code)
            variant.parent = product

            return {
              ...entry,
              variant,
            }
          })
        )
      }

      return context.resolvers.hybris.order.getOrderProperty(order, args, frContext, info)
    }

    if (info.fieldName === 'availableEdit') {
      try {
        return context.dataSources.selfCare.isEditableOrder(order.number)
      } catch (e) {
        apm.captureError(e, { custom: { e, order } })
        return false
      }
    }

    return super.getOrderProperty(...arguments)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  async getOrders(_parent, args, context) {
    const user = await getCurrentHybrisUser(context)
    return getUserOrders(user, args, context)
  }

  async _afterCartConverted(order, context) {
    await super._afterCartConverted(order, context)
    const hasItfPlusItem = order.lineItems.some(
      (lineItem) => lineItem.productKey === config.products.interfloraplus.key
    )

    if (!hasItfPlusItem) {
      return
    }

    const email = order.customerEmail ?? order.billingAddress.email
    try {
      const user = await context.dataSources.hybrisUser.getUser(email)
      if (!user) {
        return
      }

      const interfloraPlus = new InterfloraPlus(user.premium ? new Date(user.itfPlusExpirationDate) : null)
      if (!user?.premium) {
        await context.dataSources.hybrisInterflora.itfPlusSubscribe(email)
      } else if (interfloraPlus.isAboutToExpire()) {
        await context.dataSources.hybrisInterflora.itfPlusExtend(email)
      }
    } catch (e) {
      const userDoesNotExist = e.extensions.code === 404
      if (!userDoesNotExist) {
        apm.captureError(e, {
          custom: e,
        })
        return
      }

      await context.dataSources.hybrisUser.initUser(email, {
        firstName: order.billingAddress.firstName,
        lastName: order.billingAddress.lastName,
        title: UserTransformer.toAPICivility(order.billingAddress.salutation),
      })
      await context.dataSources.hybrisInterflora.itfPlusSubscribe(email)
    }
  }

  /**
   * Resolve Order.delivery.availableDateRanges
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   * @param {GraphGQLResolveInfo} _info
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(delivery, _args, context, _info) {
    if (!(delivery instanceof GQLDelivery)) return []

    const orderId = delivery.order?.id ?? delivery.orderId
    // given order id is actually either the real ids, or the order number (ITXXXXXXXXX).
    if (!isUUID(orderId) && /[^\d]/i.test(orderId)) {
      try {
        const order = await context.resolvers.hybris.order.getAvailableDateRanges(
          delivery,
          _args,
          getFRHybrisContext(context),
          _info
        )
        return order
      } catch (e) {
        // do nothing
      }
    }

    const ctOrder =
      (await context.loaders.commerceTools.orders.load(orderId)) ??
      (await context.loaders.commerceTools.ordersByOrderNumber.load(orderId))
    if (!ctOrder) {
      this.context.log.warn(`[Order.getAvailableDateRanges] order ${orderId} not found from CT`)
      return []
    }
    const mainLineItem = ctOrder.lineItems.find((lineItem) => isMainLineItem(lineItem))

    if (!mainLineItem) return []

    const deliveryDates = await context.loaders.hybris.deliveryDates.load({
      code: !isByStemsLineItem(mainLineItem, context)
        ? mainLineItem.variant.sku
        : mainLineItem.variant.sku.replace('xxx', mainLineItem.quantity),
      countryId: delivery.address?.country?.id || context.countryId,
      postalCode: ctOrder.shippingAddress?.postalCode,
    })

    return HybrisDateRangeTransformer.toGQL(deliveryDates)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string, fallback: boolean, orderNumber: string}>} args
   * @param {FRContext} context
   * @param {ResoleInfo} _info
   * @returns {GQLOrder}
   */
  async getOrderByEmail(_parent, args, context, _info) {
    const order = await super.getOrderByEmail(...arguments)
    if (order) return order

    if (args.fallback !== false) {
      return context.resolvers.hybris.order.getOrderByEmail(
        _parent,
        { ...args, fallback: false },
        getFRHybrisContext(context),
        _info
      )
    }
    return null
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{date: Date, orderId: string}>} args
   * @param {FRContext} context
   * @returns {Promise<{ endHour: Number, endMinute: Number, startHour: Number, startMinute: Number }[]>}
   */
  async getOrderDeliveryRangeFixHour(_parent, args, context) {
    const ranges = await context.dataSources.hybrisInterflora.getCeremonyHourRange(args.date)
    return ranges ? [ranges] : []
  }

  /**
   * @param {null} parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getOrderDeliveryTimeRanges(parent, args, context) {
    if (!isUUID(args.orderId)) {
      const frContext = getFRHybrisContext(context)

      return context.resolvers.hybris.order.getOrderDeliveryTimeRanges(parent, args, frContext)
    }

    const ctOrder = await context.loaders.commerceTools.orders.load(args.orderId)
    if (!ctOrder) {
      return HybrisDeliveryTimeRangesResultTransformer.toGQL({})
    }

    const mainProductLineItem = ctOrder.lineItems.find(
      (lineItem) => lineItem.custom.fields.isAccessoryFor === undefined
    )
    if (!mainProductLineItem) return HybrisDeliveryTimeRangesResultTransformer.toGQL({})

    const categoryId = ctOrder.custom?.fields.occasionCode
    const date = ctOrder.shippingAddress.custom?.fields.date
      ? new Date(`${ctOrder.shippingAddress.custom?.fields.date}T21:00:00.000Z`)
      : null
    const postalCode = ctOrder.shippingAddress.postalCode

    const timeRangeService = new DeliveryTimeRangeService(context)

    return timeRangeService.getDeliveryTimeRanges(date, categoryId, postalCode, mainProductLineItem)
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getSuggestedMessages(parent, args, context) {
    if (!isUUID(args.orderId)) {
      const frContext = getFRHybrisContext(context)

      return context.resolvers.hybris.order.getSuggestedMessages(parent, args, frContext)
    }

    return super.getSuggestedMessages(...arguments)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, orderId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @param {GraphGQLResolveInfo} _info
   * @returns {Promise<{ town: GQLTown, valid: boolean }>}
   */
  async getTownsForOrder(_parent, args, context, _info) {
    const { countryId, orderId, search } = args

    // given order id is actually either the real ids, or the order number (ITXXXXXXXXX).
    if (!isUUID(orderId) && /[^\d]/i.test(orderId)) {
      try {
        const order = await context.resolvers.hybris.order.getTownsForOrder(
          _parent,
          args,
          getFRHybrisContext(context),
          _info
        )
        return order
      } catch (e) {
        // do nothing
      }
    }

    const order = await context.loaders.commerceTools.orders.load(orderId)
    const mainLineItem = order.lineItems.find((lineItem) => isMainLineItem(lineItem))

    const towns = await context.loaders.hybris.towns.load({
      countryIsoCode: countryId || context.countryId,
      productCode: !isByStemsLineItem(mainLineItem, context)
        ? mainLineItem.variant.key
        : mainLineItem.variant.key.replace('xxx', mainLineItem.quantity),
      search,
    })

    return towns.map((apiTown) => ({ town: HybrisTownTransformer.toGQL(apiTown), valid: apiTown.valide }))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string, order: GQLInputOrder}>} args
   * @param {GraphQLContext} context
   */
  async updateOrder(_parent, args, context) {
    if (!isUUID(args.orderId)) {
      const payload = HybrisOrderTransformer.toAPIUpdateOrder(args.order, context)
      const apiOrder = await context.dataSources.hybrisOrder.updateOrder(args.orderId, payload)

      if (apiOrder) {
        await Promise.all([
          context.loaders.hybris.orders.prime(apiOrder.guid, apiOrder),
          context.loaders.hybris.orders.prime(apiOrder.code, apiOrder),
        ])

        return HybrisOrderTransformer.toGQL(apiOrder, context)
      }

      throw new Error('Invalid order')
    }

    /** @type {GQLOrder} */
    let gqlOrder

    const { reasonId, ...orderInput } = args.order

    // occasion code - update CT
    if (reasonId) {
      gqlOrder = await super.updateOrder(
        _parent,
        {
          ...args,
          order: { reasonId },
        },
        context
      )
    }

    // order update - assume editable was checked, else will throw if not allowed
    if (Object.keys(orderInput).length > 0) {
      const selfcareService = new SelfcareService(context)
      gqlOrder = await selfcareService.updateOrder(args.orderId, args.order)
    }

    // no update - return existing order
    if (!gqlOrder) {
      const ctOrder = await this.context.loaders.commerceTools.orders.load(args.orderId)
      gqlOrder = CTOrderTransformer.toGQL(ctOrder, context, {
        distributionChannels: await getDistributionChannels(context),
      })
    }

    return gqlOrder
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{orderId: string, password: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async loginAndAttachOrder(parent, args, context) {
    const { orderId, password } = args
    const frContext = getFRHybrisContext(context)

    if (!isUUID(orderId)) {
      return frContext.resolvers.order.loginAndAttachOrder(parent, args, frContext)
    }

    const ctOrder = await context.loaders.commerceTools.orders.load(orderId)
    const email = ctOrder.customerEmail ?? ctOrder.billingAddress.email

    const response = await context.dataSources.hybrisAuth.login(email, password)
    if (!response.login) {
      response.login = email
    }
    await updateContextOnHybrisLogin(response, context)
    const token = HybrisTokenTransformer.toGQL(response, context)

    if (!context.auth.isAnonymous) {
      await context.dataSources.orders.attachOrderAsAdmin(ctOrder, email, email)
    }

    return token
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {string | undefined}
   */
  async _computeOrderNumberPrefix(cart, _context) {
    if (cart?.billingAddress?.custom?.fields?.type === 'COMPANY') {
      return CONST.order.prefixes.FR.B2B
    } else {
      return undefined
    }
  }

  /**
   * Decrement stock using a dedicated dataSource
   *
   * @param {CTCart} cart
   * @param {string} orderId
   * @param {GraphQLContext} context
   * @return {Promise<void>}
   */
  async _decrementStock(cart, orderId, context) {
    const redisRes = await context.server.redis.SET(
      `${config.graphql.dataloaders.orderIdempotency.cacheKey}${orderId}`,
      1,
      {
        GET: true,
        expiration: {
          type: 'EX',
          value: 3600,
        },
      }
    )
    if (redisRes === null) {
      await Promise.allSettled(
        cart.lineItems.map((item) =>
          context.dataSources.hybrisProduct.decrementStock(item.variant.sku, item.quantity, orderId, context)
        )
      )
    }
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @return {Promise<GQLOrderResult>}
   */
  async _createInvoiceOrder(cart, context) {
    const orderResult = new GQLOrderResult()
    try {
      // Check if user is allowed to pay by invoice
      const paymentMethods = await context.resolvers.cart.paymentMethods(this, arguments, context)
      if (paymentMethods.some((pm) => pm.mode === CONST.payment.paymentMode.FACTURE)) {
        const order = await this._convertCart(
          {
            cart: cart,
            mode: CONST.payment.paymentMode.FACTURE,
            provider: null,
            state: CONST.commerceTools.paymentStates.paid, // Specifically asked by French team, in legacy its was considered paid.
            transactionId: `${cart.id}-init-${CONST.payment.paymentMode.FACTURE.toLowerCase()}`,
          },
          context
        )

        orderResult.order = CTOrderTransformer.toGQL(order, context, {
          distributionChannels: await getDistributionChannels(context),
        })
        orderResult.status = CONST.order.statuses.success
      } else {
        orderResult.status = CONST.order.statuses.error
        orderResult.statusCode = 403
        orderResult.statusMessage = `Vous n'êtes pas autorisé à payer par facture.`
      }
    } catch (e) {
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = 500
      orderResult.statusMessage = `Une erreur est survenue, merci d'utiliser une autre méthode de paiement.`
    }
    return orderResult
  }

  /**
   * @override
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>}
   */
  async _beforeConvertingCart(cart, context) {
    cart = await super._beforeConvertingCart(cart, context)
    cart = await this._generateVoucher(cart, context)

    return cart
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>}
   */
  async _generateVoucher(cart, context) {
    try {
      const { Generated, DiscountCode } = await context.dataSources.discountCode.generateOrderVoucher(cart.id)
      context.log.info(`[_generateVoucher] discount code generated: ${Generated} (${DiscountCode})`)
      if (Generated && DiscountCode) {
        cart = await context.dataSources.carts.updateCart(cart.id, {
          actions: [
            {
              action: 'setCustomField',
              name: 'voucher',
              value: DiscountCode,
            },
          ],
          version: cart.version,
        })
      }
    } catch (e) {
      context.log.error(`[_generateVoucher] error occurred while generating discount code ${e.message}`)
      // Don't fail on converting cart to order, error is already logged by the data source
    }

    return cart
  }
}

module.exports = OrderResolver
