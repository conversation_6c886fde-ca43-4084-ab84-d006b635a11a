const BaseResolver = require('../ct-datocms/Address')

const { getFRHybrisContext } = require('./shared')

class AddressResolver extends BaseResolver {
  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {FRContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {Promise<GQLAddress>}
   */
  addAddress(parent, args, context, info) {
    return context.resolvers.hybris.address.addAddress(parent, args, getFRHybrisContext(context), info)
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{addressId: string}>} args
   * @param {FRContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {Promise<GQLAddress>}
   */
  removeAddress(parent, args, context, info) {
    return context.resolvers.hybris.address.removeAddress(parent, args, getFRHybrisContext(context), info)
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {FRContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {Promise<GQLAddress>}
   */
  updateAddress(parent, args, context, info) {
    return context.resolvers.hybris.address.updateAddress(parent, args, getFRHybrisContext(context), info)
  }
}

module.exports = AddressResolver
