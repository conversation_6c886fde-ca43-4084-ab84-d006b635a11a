const BaseResolver = require('../ct-datocms/User')

const { getCurrentHybrisUser, getUserOrders, getFRHybrisContext } = require('./shared')

const {
  CountryTransformer: HybrisCountryTransformer,
  PhoneTransformer,
  UserTransformer,
} = require('../fr-hybris/transformers')

class UserResolver extends BaseResolver {
  async updateUser(parent, args, context) {
    const { user } = args

    const apiUser = {
      additionalInfo: user.address2,
      codeLocalite: user.townId,
      companyName: user.company,
      displayUid: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      line1: user.address,
      line2: '',
      phone: user.phone && PhoneTransformer.toAPI(user.phone, context),
      postalCode: user.postalCode,
      titleCode: UserTransformer.toAPICivility(user.civility),
      town: user.townLabel,
    }

    if (user.countryId) {
      apiUser.country = {
        isocode: user.countryId,
      }
    }

    if (user.townId) {
      apiUser.region = {
        isocode: user.townId,
      }
    }

    await context.dataSources.hybrisUser.update(apiUser)

    return getCurrentHybrisUser(context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{oldPassword: string, newPassword: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  async updatePassword(_parent, args, context) {
    const { oldPassword, newPassword } = args

    await context.dataSources.hybrisUser.updatePassword(oldPassword, newPassword)

    return this._getCurrentUser(context)
  }

  /**
   * Resolve `deliveryAddresses` and `senderAddresses` from User type.
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination}>} args
   * @param {FRContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {{addresses: GQLAddress[], total: number}}
   */
  getUserAddressList(user, args, context, info) {
    return context.resolvers.hybris.user.getUserAddressList(user, args, getFRHybrisContext(context), info)
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   * @returns {Promise<GQLCountry>}
   */
  async getUserCountry(user, _args, context) {
    if (!user.countryId) {
      return
    }

    const apiCountries = await context.loaders.hybris.countries.load({ allCountries: true, includeFR: true })
    const userCountry = apiCountries.find((apiCountry) => apiCountry.isocode === user.countryId)

    if (!userCountry) {
      return
    }

    return HybrisCountryTransformer.toGQL(userCountry, context)
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} args
   * @param {FRContext} context
   * @returns {Promise<GQLOrder>}
   */
  async getUserLastOrder(user, args, context) {
    const orderList = await this
    this.getUserOrders(
      user,
      {
        ...args,
        pagination: {
          offset: 0,
        },
      },
      context
    )

    return orderList.orders?.[0]
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{ pagination: GQLInputPagination, filter: GQLInputOrderFilter }>} args
   * @param {FRContext} context
   * @returns {Promise<GQLOrderList>}
   */
  async getUserOrders(user, args, context) {
    return getUserOrders(user, args, context)
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser | undefined>}
   * @protected
   */
  async _getCurrentUser(context) {
    if (context.auth && context.auth.isAnonymous) {
      return
    }

    const [apiUser, apiUserAddresses] = await Promise.all([
      context.dataSources.hybrisUser.getCurrent(),
      context.dataSources.hybrisAddress.getAll(),
    ])

    return UserTransformer.toGQL(apiUser, context, { addresses: apiUserAddresses })
  }
}

module.exports = UserResolver
