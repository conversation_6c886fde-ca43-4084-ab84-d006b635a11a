const apm = require('elastic-apm-node')

const config = require('../../../config')
const CONST = require('../../../const')

const BaseResolver = require('../ct-datocms/Cart')
const DateScalar = require('../../scalars/Date')
const { variantIds, isByStemsLineItem, computeIsFromWorkshop } = require('../../../helpers/commercetools')
const { GraphQLApolloError } = require('../../errors')
const { GQLPaymentMethod, GQLDelivery, GQLCart } = require('../../models')

const {
  DeliveryTimeRangesResultTransformer: HybrisDeliveryTimeRangesResultTransformer,
  TownTransformer: HybrisTownTransformer,
  StockTransformer,
  DateRangeTransformer,
  DeliveryFeasibilityTransformer,
} = require('../fr-hybris/transformers')
const { getAvailablePaymentMethods, getFeatureEnabled } = require('../../../helpers/features')
const { DeliveryTimeRangeService } = require('../../../helpers/hybris/DeliveryTimeRangeService')
const { splitGFSCode } = require('../../../helpers/gfs')
const { isMainLineItem } = require('./shared')
const { capitalize } = require('../../../helpers/string')

/** @typedef {import('../../models')} */

class FRCartResolver extends BaseResolver {
  /**
   * @override
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  async canPremium(cart, _args, context) {
    const isAnonymous = context.auth?.isAnonymous ?? true

    if (isAnonymous) {
      return true
    }

    let user
    try {
      user = await context.dataSources.hybrisUser.getCurrent()
    } catch (e) {
      apm.captureError(e, { custom: e })
      return true
    }

    if (!user) {
      const error = new Error(
        `Cannot get logged user details. (email from cart: ${cart.delivery?.sender?.email ?? 'none'})`
      )
      apm.captureError(error, { custom: error })
      return true
    }

    if (user.b2bCustomer) {
      return false
    }

    const isPremiumUser = user?.premium ?? false
    const today = new Date()
    const userItfExpirationDate = new Date(user?.itfPlusExpirationDate ?? null)
    const premiumIsExpiredOrExpireThisMonth =
      userItfExpirationDate.getTime() < today.getTime() ||
      (userItfExpirationDate.getFullYear() === today.getFullYear() &&
        userItfExpirationDate.getMonth() === today.getMonth())

    return !isPremiumUser || premiumIsExpiredOrExpireThisMonth
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} args
   * @param {FRContext} context
   * @returns {Promise<string[]>}
   */
  async celebrationTexts(cart, args, context) {
    if (!(cart instanceof GQLCart)) return []
    const apiCart = await this._getApiCart(context)
    const mainLineItem = apiCart.lineItems.find((lineItem) => isMainLineItem(lineItem))
    if (!mainLineItem) return []
    let productCode = mainLineItem.variant.key
    if (isByStemsLineItem(mainLineItem, context)) {
      productCode = productCode.replace('xxx', mainLineItem.quantity)
    }
    try {
      return context.loaders.hybris.celebrationTexts.load({
        code: productCode,
        countryId: args.countryId ?? context.appConfig.country,
      })
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    return []
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{date: Date, productCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<CeremonyHourRange[]>}
   */
  async getCartDeliveryRangeFixHour(_parent, args, context) {
    const { productId, variantId } = variantIds(args.productCode)
    const product = await this._loadProductFromCT({ code: productId }, context)
    const variant = product.variants.find((v) => v.id === variantId)
    const range = await context.dataSources.hybrisInterflora.getCeremonyHourRange(args.date, variant.sku)
    if (range.messageSpecifique) {
      range.message = range.messageSpecifique
      delete range.messageSpecifique
    }

    return range ? [range] : []
  }

  /**
   * @param {null} _parent
   * @param {GraphQLContextArgs<{date: Date}>} args
   * @param {FRContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getCartDeliveryTimeRanges(_parent, args, context) {
    const cart = await this._getApiCart(context)
    if (!cart) return HybrisDeliveryTimeRangesResultTransformer.toGQL({})

    const mainProductLineItem = cart.lineItems.find((lineItem) => isMainLineItem(lineItem))
    if (!mainProductLineItem) return HybrisDeliveryTimeRangesResultTransformer.toGQL({})

    const categoryId = args.context.categoryId || cart.custom?.fields?.occasionCode
    const timeRangeService = new DeliveryTimeRangeService(context)

    return timeRangeService.getDeliveryTimeRanges(
      args.date,
      categoryId,
      cart.shippingAddress.postalCode,
      mainProductLineItem
    )
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: GQLTown, valid: boolean }[]>}
   */
  async getTownsForCart(_parent, args, context) {
    this._assertHasCart(context)
    const apiCart = await this._getApiCart(context)

    let productCode

    const mainLineItem = apiCart.lineItems.find((lineItem) => isMainLineItem(lineItem))
    if (mainLineItem) {
      productCode = mainLineItem.variant.key
      if (isByStemsLineItem(mainLineItem, context)) {
        productCode = productCode.replace('xxx', mainLineItem.quantity)
      }
    } else if (context.country !== context.appConfig.country) {
      // gfs
      const { code } = splitGFSCode(apiCart.customLineItems[0]?.slug ?? '')
      productCode = code
    }

    const towns = await context.loaders.hybris.towns.load({
      countryIsoCode: args.countryId || context.countryId,
      productCode,
      search: args.search,
    })

    return towns.map((apiTown) => ({ town: HybrisTownTransformer.toGQL(apiTown), valid: apiTown.valide }))
  }

  async _checkAvailabilityCompatibility(products, accessories, delivery, context, apiCart) {
    try {
      if (delivery?.address?.postalCode && (accessories.length > 0 || products.length > 0)) {
        const skus = new Set()

        for (const product of await Promise.all(
          [...accessories, ...products].map((entry) => {
            const { productId } = variantIds(entry.code)
            return this._loadProductFromCT({ code: productId }, context)
          })
        )) {
          //@temporary @todo include workshop checks in MS, currently workshop product are checked later through checkStock.
          if (
            product.key !== config.products.interfloraplus.key &&
            !product.isFromWorkshop &&
            product.key !== config.products.tombDelivery.key
          )
            skus.add(product.key)
        }

        if (skus.size === 0) {
          return
        }

        let checkAvailabilityResponse = null
        try {
          checkAvailabilityResponse = await context.dataSources.availability.checkAvailability({
            city: delivery.address.townLabel?.replace(/ *\(\d+\)/, ''),
            codes: [...skus],
            country: delivery.address.countryId,
            date: delivery.date,
            orderId: apiCart?.id,
            postalCode: delivery.address.postalCode,
            province: delivery.address.province,
          })
        } catch (error) {
          apm.captureError(error, { custom: error })
        }

        if (!checkAvailabilityResponse?.Result) {
          const err = new GraphQLApolloError(`availability check failed`, 400)
          err.addError('mix-availability', 'availability check failed')
          context.log.error(
            {
              request: {
                city: delivery.address.townLabel?.replace(/ *\(\d+\)/, ''),
                codes: [...skus],
                country: delivery.address.countryId,
                date: delivery.date,
                postalCode: delivery.address.postalCode,
                province: delivery.address.province,
              },
              response: checkAvailabilityResponse,
            },
            `Availability check failed`
          )
          throw err
        }
      }
    } catch (err) {
      apm.captureError(err, { custom: err })
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   */
  async getDeliveryFeasibility(_parent, _args, context) {
    // We return the above everytime we have an error that is not a business feasibility issue. Any technical issue will accept the order.
    const defaultFeasibleFallBack = {
      feasible: true,
    }
    this._assertHasCart(context)

    /** @type {CTCart} */
    const cart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)

    try {
      const feasibilityToggle = await getFeatureEnabled(CONST.unleash.toggles.feasibility, {
        sessionId: context.clientSessionId || cart.id,
      })
      if (!feasibilityToggle || feasibilityToggle.enabled === false) {
        // Can't get the toggle or feasibility is disabled ? We accept the cart.
        return defaultFeasibleFallBack
      }
    } catch (e) {
      context.log.error(
        `getDeliveryFeasibility: couldn't get the Feasibility unleash toggle, disabling by default: all carts are accepted.`
      )
      return defaultFeasibleFallBack
    }

    const mainItem = cart.lineItems.find((lineItem) => isMainLineItem(lineItem)) || cart.lineItems.at(0)
    const isFuneral =
      mainItem.variant.attributes?.find((attr) => attr.name === 'product_type')?.value?.key ===
        CONST.product.productType.MOURNING || mainItem.productKey === config.products.tombDelivery.key
    let productType = null
    let moment = Object.entries(CONST.commerceTools.momentsHybrisMap).find(
      (entry) => entry[1] === parseInt(cart.shippingAddress?.custom?.fields?.moment)
    )?.[0]

    if (isFuneral) {
      productType = CONST.product.productType.MOURNING
    } else if (computeIsFromWorkshop(mainItem.variant, context)) {
      productType = CONST.product.productType.WAREHOUSE
    } else {
      productType = mainItem.variant.attributes?.find((attr) => attr.name === 'product_type')?.value?.key
    }

    try {
      const feasibility = await context.dataSources.hybrisInterflora.checkCartFeasibility(
        cart.shippingAddress.streetName,
        capitalize(cart.shippingAddress.city), // The MS behind the hybris api is case sensitive (e.g "Barcelonnette" has an alternative route, "BARCELONNETTE" doesn't, with the same postal code).
        cart.shippingAddress.postalCode,
        new Date(cart.shippingAddress?.custom?.fields?.date).toLocaleDateString('fr-FR').replaceAll('/', '-'),
        moment || null,
        cart.lineItems
          .filter((item) => ![config.products.interfloraplus.key].includes(item.productKey))
          .map((item) =>
            isByStemsLineItem(item, context) ? item.variant.sku.replace('xxx', item.quantity) : item.variant.sku
          ),
        productType?.toLowerCase(),
        Math.round((cart.taxedPrice.totalGross.centAmount / 100).toFixed(2)),
        cart.shippingAddress.custom.fields.momentDescription || null,
        cart.id
      )

      return DeliveryFeasibilityTransformer.toGQL(feasibility)
    } catch (e) {
      // Technical error: we accept the cart.
      apm.captureError(e, { custom: e })
      return defaultFeasibleFallBack
    }
  }

  async paymentMethods(_parent, _args, context) {
    const cart = await this._getApiCart(context)

    if (cart.totalPrice.centAmount === 0 && cart.discountCodes?.length > 0) {
      const paymentMethod = new GQLPaymentMethod()
      paymentMethod.availableForCart = true
      paymentMethod.mode = CONST.payment.paymentMode.NONE
      return [paymentMethod]
    }

    const [paymentMethods, currentUser] = await Promise.all([
      getAvailablePaymentMethods(cart, context),
      context.dataSources.hybrisUser.getCurrent(),
    ])

    try {
      const price = Math.round((cart.taxedPrice?.totalGross.centAmount || cart.totalPrice.centAmount) / 100)
      paymentMethods.push(...(await this._getExtraPaymentMethods(currentUser, price, context)))
    } catch (e) {
      context.log.error(`[CTFR paymentMethods] error while trying to check if user can pay by invoice ${e.message}`)
    }

    return paymentMethods
  }

  /**
   * @param {APIUser} currentUser
   * @param {number} price
   * @param {FRContext} context
   * @return {Promise<GQLPaymentMethod[]>}
   * @private
   */
  async _getExtraPaymentMethods(currentUser, price, context) {
    const paymentMethods = []

    /** Check if b2b customer can pay by invoice */
    if (currentUser.b2bCustomer) {
      // @issue https://myflower.atlassian.net/browse/DIGI-1099
      // currentUser might be a child user of the main company user, whose ID is to be provided to check the user's authorization to pay by invoice
      // If the "internalB2bIdentifier" property is set, it means it's a child user, and the value of that property is the ID of the parent user
      // and this parent user's ID is to be provided to check the current user's authorization to pay by invoice
      const customerId = currentUser.internalB2bIdentifier ?? currentUser.uid
      const { IsCustomerExists, IsAuthorizedToPayByInvoice, IsBlocked } =
        await context.dataSources.payment.isCustomerAuthorizedToPayByInvoice(customerId, price)
      if (IsCustomerExists && IsAuthorizedToPayByInvoice) {
        paymentMethods.push({
          availableForCart: !IsBlocked,
          encrypted: null,
          excludedDeliveryModes: [],
          mode: CONST.payment.paymentMode.FACTURE,
          provider: null,
        })
      }
    }

    return paymentMethods
  }

  /**
   * @param {ShippingMethodMap} methods
   * @param {Object} param
   * @param {boolean} param.isFlowersAtChoice
   * @param {boolean} param.isReduced
   * @param {boolean} param.isFuneral
   * @param {boolean} param.isWarehouse
   * @param {boolean} param.hasCustomShippingValue
   * @returns {ShippingMethodMap[][]}
   */
  _getOrderedSubsetShippingMethods(
    methods,
    { isFlowersAtChoice, isReduced, isFuneral, isWarehouse, hasCustomShippingValue }
  ) {
    const subsets = []
    if (hasCustomShippingValue) subsets.push(methods.customShippingFee)
    if (methods.ab.length) subsets.push(methods.ab)
    if (isWarehouse) subsets.push(methods.warehouse)
    if (isFuneral) subsets.push(methods.mourning)
    if (isReduced) subsets.push(methods.reduced)
    if (isFlowersAtChoice) subsets.push(methods.flowersAtChoice)
    // The order of priority is in descending order
    // Example: The first item on the list will be the last one prioritized
    return subsets.reverse()
  }

  /**
   * @param {Entry[]} entries
   * @param {GraphQLContext} context
   */
  async _checkStocks(entries, context) {
    const variantsStockChecks = []
    const variantsStockCheckedEntry = []
    try {
      for (const entry of await Promise.all(
        entries.map(async (entry) => {
          const { channel, productId, variantId } = variantIds(entry.code)
          const product = await this._loadProductFromCT({ channel, code: productId }, context)
          const variant = product.variants.find((v) => v.id === variantId) ?? product.defaultVariant

          return {
            isAccessory: entry.isAccessory,
            quantity: entry.quantity,
            variant,
          }
        })
      )) {
        if ([config.products.interfloraplus.key, config.products.tombDelivery.key].includes(entry.variant.parent.key))
          continue

        // We only check stock if the product is from workshop and not an accessory/itf+/tombDelivery, otherwise CT is not allowing adding if out of stock.
        if (
          entry.variant.parent.isFromWorkshop &&
          !entry.isAccessory &&
          !(entry.variant.parent.productType === CONST.product.productType.ACCESSORIES)
        ) {
          variantsStockCheckedEntry.push({ quantity: entry.quantity, sku: entry.variant.sku })
          variantsStockChecks.push(
            context.dataSources.hybrisProduct.checkStock(entry.variant.parent.key, entry.variant.sku, context.countryId)
          )
        }
      }

      if (variantsStockCheckedEntry.length === 0) {
        return
      }
    } catch (e) {
      const err = new GraphQLApolloError(`stock check unexpected error: ${e}`, 400)
      err.stack = e.stack
      throw err
    }

    const stockChecksResults = await Promise.all(variantsStockChecks)

    for (let i = 0; i < variantsStockCheckedEntry.length; i++) {
      const result = stockChecksResults?.[i]

      if (!result || !result?.stockLevelStatus) {
        const err = new GraphQLApolloError(`stock-check-failed`, 400)
        err.addError('stock-check-failed', `Couldn't check stock for this product`)
        context.log.error(
          { result },
          `[_checkStocks] Couldn't check stock for ${variantsStockCheckedEntry?.[i]?.variant?.sku}`
        )
        throw err
      }

      const stock = StockTransformer.toGQL({ stockLevel: result.stockLevel, stockLevelStatus: result.stockLevelStatus })

      // We update the CT stock to be as close as possible from real stock instead of waiting for a sync.
      if (variantsStockCheckedEntry?.[i]?.sku) {
        if (stock.value && stock.status !== CONST.product.stockStatuses.outOfStock)
          context.dataSources.inventories.updateInventoryByVariantSku(variantsStockCheckedEntry?.[i]?.sku, stock.value)

        if (stock.status === CONST.product.stockStatuses.outOfStock)
          context.dataSources.inventories.updateInventoryByVariantSku(variantsStockCheckedEntry?.[i]?.sku, 0)
      }

      if (
        stock.status === CONST.product.stockStatuses.outOfStock ||
        stock.value < variantsStockCheckedEntry[i].quantity
      ) {
        const err = new GraphQLApolloError(CONST.product.stockCheck.errors.emptyStock, 400)
        err.addError('mix-stock-empty', 'Stock empty for this product') // This error is shown on front, 'mix-stock-empty' is a translation key.
        context.log.error({ result }, `[_checkStocks] Stock is empty for ${variantsStockCheckedEntry?.[i]?.sku}`)
        throw err
      }
    }
  }

  /**
   * @param {GQLInputDelivery} _delivery
   * @param {GraphQLContext} context
   * @returns {Promise<{ action: string }[]>} array of actions
   */
  async _updateDeliveryActions(_delivery, context) {
    const actions = await super._updateDeliveryActions(...arguments)

    const shippingAddressAction = actions.find((action) => action.action === 'setShippingAddress')
    if (!shippingAddressAction?.address) return actions

    const { date, moment } = shippingAddressAction.address.custom.fields
    if (!date || !moment) return actions

    /** @type {GQLDeliveryTimeRange} */
    let range
    try {
      const cart = await this._getApiCart(context)

      const mainProductLineItem = cart.lineItems.find((lineItem) => isMainLineItem(lineItem))
      if (!mainProductLineItem) return actions

      const categoryId = cart.custom?.fields?.occasionCode
      const timeRangeService = new DeliveryTimeRangeService(context)

      const { ranges } = await timeRangeService.getDeliveryTimeRanges(
        DateScalar.parseValue(date),
        categoryId,
        cart.shippingAddress.postalCode,
        mainProductLineItem
      )
      // in case the code received from hybris is not a "real" moment, and only in this case,
      // we need to propagate the label as the delivery information
      range = ranges.find(
        (r) => `${r.id}` === `${moment}` && CONST.commerceTools.momentsHybrisMap[r.hybrisCode] === undefined
      )
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    delete shippingAddressAction.address.custom.fields.momentDescription
    if (range) {
      shippingAddressAction.address.custom.fields.momentDescription = range.label
    }

    return actions
  }

  /**
   * @param {GQLDelivery} parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(parent, _args, context) {
    if (!(parent instanceof GQLDelivery)) return []
    this._assertHasCart(context)

    /** @type {CTCart} */
    const cart = await this._getApiCart(context)
    if (!cart) return []

    const mainLineItem = cart.lineItems.find((item) => isMainLineItem(item))
    if (!mainLineItem) {
      context.log.error(
        `No main lineItem in getAvailableDateRanges for the following codes: ${cart.lineItems.map(
          (item) => item.productKey
        )}`
      )
      return []
    }
    let sku = mainLineItem.variant.sku
    if (isByStemsLineItem(mainLineItem, context)) {
      sku = sku.replace('xxx', mainLineItem.quantity)
    }
    try {
      const deliveryDates = await context.loaders.hybris.deliveryDates.load({
        code: sku,
        countryId: cart.shippingAddress?.country || context.countryId,
        postalCode: cart.shippingAddress?.postalCode,
      })
      return DateRangeTransformer.toGQL(deliveryDates)
    } catch (e) {
      context.log.error(`CTFR getAvailableDateRanges error ${e} with mainLineItem ${mainLineItem.productKey}`)
      return []
    }
  }

  /**
   *
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   */
  async _updateAdditionalCartInfo(cart, context) {
    try {
      // Computing lineItem
      const bundledInfo = cart.lineItems.map((lineItem) => {
        return {
          bundledItems:
            lineItem.variant.attributes
              .find((attr) => attr.name === 'bundled_products')
              ?.value?.map((bundled) => bundled.id) ?? [],
          isByStemProduct: isByStemsLineItem(lineItem, context),
          lineItemId: lineItem.id,
          lineItemPrice: lineItem.price.value.centAmount,
          lineItemQuantity: lineItem.quantity,
          variantSku: lineItem.variant.sku,
          variantSuffix: lineItem.variant.sku.replace(lineItem.productKey, ''),
        }
      })

      // Generating update informations for correct "debundling" / bundle price computing in RAO (French legacy system)
      const actions = []
      for (const line of bundledInfo) {
        if (line.bundledItems.length === 0) continue
        const bundledProducts = await context.loaders.commerceTools.products.loadMany(
          line.bundledItems.map((id) => {
            return { code: id }
          })
        )

        line.data = bundledProducts
          .filter((bp) => bp)
          .map((product) => {
            /** @type {CTProductVariant} */
            const variant =
              product.variants.length > 1
                ? [product.masterVariant, ...product.variants].find((v) => v.sku.includes(line.variantSuffix))
                : product.masterVariant
            if (line.isByStemProduct && variant.sku.includes('-xxx-')) {
              // The checked bundled variant is actually a by stem product.
              const priceValue = variant.price.tiers.find((t) => t.minimumQuantity === line.lineItemQuantity)?.value
              return {
                base_price: variant.price.custom.fields.base_price?.centAmount,
                marketing_fee: variant.price.custom.fields.marketing_fee?.centAmount,
                price:
                  priceValue.type === 'highPrecision'
                    ? Math.round(
                        (priceValue.preciseAmount * line.lineItemQuantity) / Math.pow(10, priceValue.fractionDigits - 2)
                      )
                    : priceValue.centAmount * line.lineItemQuantity, // We handle highPrecision bundle prices
                quantity: line.lineItemQuantity,
                sku: variant.sku, // the transformation to an actual ROA variant (instead of -xxx-) is done MS side.
              }
            }
            return {
              base_price: variant.price.custom.fields.base_price?.centAmount,
              marketing_fee: variant.price.custom.fields.marketing_fee?.centAmount,
              price: variant.price.value.centAmount,
              quantity: 1,
              sku: variant.sku,
            }
          })

        line.price_checked = line.data.map((x) => x.price * x.quantity).reduce((a, b) => a + b, 0)
        if (line.price_checked !== line.lineItemPrice)
          context.log.error(
            `CTFR _updateAdditionalCartInfo price sent to ROA (${line.price_checked}) is different from price set (${line.lineItemPrice}) in cartId ${cart.id}`
          )
        actions.push({
          action: 'setLineItemCustomField',
          lineItemId: line.lineItemId,
          name: 'extra_bundle_infos',
          value: JSON.stringify({
            bundled: line.data,
            price_calculated: line.price_checked,
          }),
        })
      }

      return actions
    } catch (e) {
      context.log.error(`CTFR _updateAdditionalCartInfo error ${e} with cartId ${cart.id}`)
      return []
    }
  }

  /**
   * Used to save user id in cart in ct-dato cart resolver.
   *
   * @param {GraphQLContext} context
   * @returns {string|null}
   * @private
   */
  async _getExternalUserId(context) {
    const isAnonymous = context.auth?.isAnonymous ?? true

    if (isAnonymous) {
      return null
    }
    /** @type {APIUser|null} */
    let user
    try {
      user = await context.dataSources.hybrisUser.getCurrent()
      return user?.internalB2bIdentifier ?? user?.uid ?? null
    } catch (e) {
      apm.captureError(e, { custom: e })
      return null
    }
  }
}

module.exports = FRCartResolver
