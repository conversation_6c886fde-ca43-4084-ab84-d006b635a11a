const CONST = require('../../../const')

const BaseResolver = require('../ct-datocms/Authentication')

const { Session } = require('../../../plugins/session')
const {
  UserTransformer: HybrisUserTransformer,
  TokenTransformer: HybrisTokenTransformer,
} = require('../fr-hybris/transformers')
const PhoneTransformer = require('../../transformers/Phone')
const { getCurrentHybrisUser, updateContextOnHybrisLogin } = require('./shared')

const { parseJwt } = require('../../../helpers')

class AuthenticationResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   * @returns {Promise<GQLToken>}
   */
  async anonymousLogin(_parent, _args, context) {
    const token = await super.anonymousLogin(...arguments)

    const response = await context.dataSources.hybrisAuth.login()
    await updateContextOnHybrisLogin(response, context)

    return token
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string}>} args
   * @param {FRContext} context
   * @returns {Promise<boolean>}
   */
  checkEmailExists(_parent, args, context) {
    return context.dataSources.hybrisInterflora.verifyEmailHasRegisteredUser(args.email)
  }

  /**
   * @param {Token|undefined} token
   * @param {GraphQLContextArgs<{}>} _args
   * @param {FRContext} context
   * @returns {Promise<void|User>}
   */
  async getTokenUser(token, _args, context) {
    if (token && token.user) {
      return token.user
    }

    if (context.auth && !context.auth.isAnonymous) {
      return getCurrentHybrisUser(context)
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string, password: string}>} args
   * @param {FRContext} context
   * @returns {Promise<{ value: string, refreshLifetime: Number, refreshToken: string, lifetime: Number, createdAt: Number}>}
   */
  async login(_parent, args, context) {
    try {
      const response = await context.dataSources.hybrisAuth.login(args.username, args.password)

      if (!response.login) {
        response.login = args.username
      }

      await updateContextOnHybrisLogin(response, context)

      return HybrisTokenTransformer.toGQL(response, context)
    } catch (e) {
      const errors = e.errors ?? []
      const hasGrantError = errors.some((error) => error.type === 'InvalidGrantError')
      const hasNoPasswordError = !errors.some((error) => error.subject === 'password')
      if (hasGrantError && hasNoPasswordError) {
        e.addError('InvalidGrantError', 'password', '')
      }
      throw e
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{refreshToken: string}>} args
   * @param {FRContext} context
   * @returns {Promise<{ value: string, refreshLifetime: Number, refreshToken: string, lifetime: Number, createdAt: Number}>}
   */
  async refreshToken(_parent, args, context) {
    const refreshJWT = parseJwt(args.refreshToken)

    let response
    if (refreshJWT.isAnonymous) {
      response = await context.dataSources.hybrisAuth.login()
    } else {
      const session = await Session.get(context.server.redis, refreshJWT.sessionId)
      response = await context.dataSources.hybrisAuth.refreshToken(session.hybris?.refreshToken)
      if (!response.login) {
        response.login = refreshJWT.login
      }
    }

    await updateContextOnHybrisLogin(response, context)

    return HybrisTokenTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{user: GQLInputRegisterUser}>} args
   * @param {FRContext} context
   */
  async registerUser(_parent, args, context) {
    const { user } = args

    const payload = {
      additionalInfo: user.address2,
      // birthDate
      companyName: user.company,
      country: {
        isocode: user.countryId,
      },
      customerType: HybrisUserTransformer.toAPIType(user.type),
      displayUid: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      line1: user.address,
      password: user.password,
      phone: user.phone && PhoneTransformer.toAPI(user.phone, context),
      postalCode: user.postalCode,
      titleCode: HybrisUserTransformer.toAPICivility(user.civility),
      town: user.townLabel,
      uid: user.type === CONST.user.userType.b2c ? user.email : user.username,
    }

    if (user.townId) {
      payload.region = {
        isocode: user.townId,
      }
    }
    await context.dataSources.hybrisUser.register(payload)

    const response = await context.dataSources.hybrisAuth.login(
      user.type === CONST.user.userType.b2c ? user.email : user.username,
      user.password
    )
    await updateContextOnHybrisLogin(response, context)

    return HybrisTokenTransformer.toGQL(response, context)
  }
}

module.exports = AuthenticationResolver
