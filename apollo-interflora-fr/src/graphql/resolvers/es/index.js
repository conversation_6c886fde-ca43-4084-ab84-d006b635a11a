const { createBloomreachLoaders } = require('../../dataloaders/bloomreach')
const { createCTLoaders } = require('../../dataloaders/commercetools')
const { createDatoLoaders } = require('../../dataloaders/datocms')
const { createGFSLoaders } = require('../../dataloaders/gfs')
const { createAvailabilityLoaders } = require('../../dataloaders/availability')

const AddressResolver = require('./Address')
const AuthenticationResolver = require('./Authentication')
const CartResolver = require('./Cart')
const CmsResolver = require('./Cms')
const ContactResolver = require('./Contact')
const ForgotPasswordResolver = require('./ForgotPassword')
const GeoResolver = require('./Geo')
const OrderResolver = require('./Order')
const ProductResolver = require('./Product')
const RootResolver = require('./Root')
const TrackingResolver = require('./Tracking')
const UserResolver = require('./User')

const { createResolvers: createGFSResolvers } = require('../gfs')

class ESResolvers {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context

    if (context.isDefaultCountry()) {
      this.address = new AddressResolver()
      this.authentication = new AuthenticationResolver()
      this.cart = new CartResolver()
      this.cms = new CmsResolver()
      this.contact = new ContactResolver()
      this.forgotPassword = new ForgotPasswordResolver()
      this.geo = new GeoResolver()
      this.gfs = createGFSResolvers(this.context)
      this.order = new OrderResolver()
      this.product = new ProductResolver()
      this.root = new RootResolver()
      this.tracking = new TrackingResolver()
      this.user = new UserResolver()
    } else {
      Object.assign(this, createGFSResolvers(this.context))
    }
  }

  get dataloaders() {
    if (!this._dataloaders) {
      this._dataloaders = {
        availability: createAvailabilityLoaders(this.context),
        bloomreach: createBloomreachLoaders(this.context),
        commerceTools: createCTLoaders(this.context),
        datocms: createDatoLoaders(this.context),
        gfs: createGFSLoaders(this.context),
      }
    }

    return this._dataloaders
  }
}

/**
 *
 * @param {GraphQLContext} context
 * @returns {ESResolvers}
 */
const createResolvers = (context) => {
  return new ESResolvers(context)
}

module.exports = {
  createResolvers,
}
