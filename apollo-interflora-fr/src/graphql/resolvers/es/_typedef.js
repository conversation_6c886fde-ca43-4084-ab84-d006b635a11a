/** @typedef {import('../../datasources/_typedef').ESDataSources} ESDataSources */

/** @typedef {import('../../dataloaders/availability').loadersDefinition} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/bloomreach').loadersDefinition} BloomreachDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/datocms').DatocmsDataLoaders} DatoCmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs').loadersDefinition} GFSDataLoaders */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: ESDataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    bloomreach: BloomreachDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *  }
 * }} ESContext
 */
