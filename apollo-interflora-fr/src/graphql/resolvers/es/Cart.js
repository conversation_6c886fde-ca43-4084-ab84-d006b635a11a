const BaseResolver = require('../ct-datocms/Cart')

const { variantIds } = require('../../../helpers/commercetools')

class CartResolver extends BaseResolver {
  /**
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} args
   * @param {('customLineItems'|'lineItems')} source
   * @param {GraphQLContext} context
   * @returns {Promise<{
   *  accessoriesToAdd: { code: string, productId: string, quantity: number, variantId: string, isAccessory: boolean }[],
   *  entriesToAdd: { code: string, productId: string, quantity: number, variantId: string, isAccessory: boolean }[],
   *  entriesToDelete: { code: string, productId: string, quantity: number, variantId: string, lineItemId: number, isAccessory: boolean }[],
   *  entriesToUpdate: { code: string, productId: string, quantity: number, variantId: string, lineItemId: number, isAccessory: boolean }[],
   * }>}
   */
  async _updateCartSplitEntries(args, source, context) {
    const {
      cart: { products = [], accessories = [], services = [] },
    } = args
    const { accessoriesToAdd, entriesToAdd, entriesToDelete, entriesToUpdate } = await super._updateCartSplitEntries(
      args,
      source,
      context
    )

    const apiCart = await this._getApiCart(context)
    const currentItemsByCode = {}
    switch (source) {
      case 'customLineItems':
        for (const lineItem of apiCart.customLineItems) {
          const code = lineItem.slug
          currentItemsByCode[code] = lineItem
        }
        break
      case 'lineItems':
        for (const lineItem of apiCart.lineItems) {
          const code = `${lineItem.productId}#${lineItem.variant.id}`
          currentItemsByCode[code] = lineItem
        }
        break
    }

    // only one product can be added
    const newEntriesToAdd = entriesToAdd.reduce((previousValue, currentValue) => {
      if (currentValue.isAccessory || !previousValue.some((p) => !p.isAccessory)) {
        previousValue.push(currentValue)
      }
      return previousValue
    }, [])

    const serviceIds = new Set(services.map((s) => s.code))
    const isMainProduct = (entry) => !entry.isAccessory && !serviceIds.has(entry.code)
    const productToAdd = newEntriesToAdd.find(isMainProduct)
    if (!productToAdd) {
      return {
        accessoriesToAdd,
        entriesToAdd,
        entriesToDelete,
        entriesToUpdate,
      }
    }

    // search and determine if the product in the cart has changed
    const mainProduct = products.find((product) => !currentItemsByCode[product.code]?.custom?.fields?.isAccessoryFor)
    const mainProductChanged = mainProduct && variantIds(mainProduct.code).productId !== productToAdd.productId

    const entryValue = (lineItem) => {
      const code = lineItem.variant ? `${lineItem.productId}#${lineItem.variant.id}` : lineItem.slug
      const { productId, variantId } = variantIds(code)
      return {
        code,
        lineItemId: lineItem.id,
        productId,
        quantity: lineItem.quantity,
        variantId,
      }
    }

    const newEntriesToUpdate = entriesToUpdate.filter((entry) => entry.isAccessory)
    const newEntriesToDelete = []
    for (const [key, value] of Object.entries(currentItemsByCode)) {
      const inAccessories = accessories.some((accessory) => accessory.code === key)
      const inServices = [...serviceIds].includes(key)

      // remove products and accessories not matching
      if (mainProductChanged && key === mainProduct.code) {
        newEntriesToDelete.push(entryValue(value))
      } else if (key !== productToAdd.code && !(inAccessories || inServices)) {
        newEntriesToDelete.push(entryValue(value))
      }

      // to force check if the accessory can be linked to the product
      if (
        accessories.some((accessory) => accessory.code === key) &&
        !newEntriesToUpdate.some((accessory) => accessory.code === key)
      ) {
        accessoriesToAdd.push({
          ...entryValue(value),
          isAccessory: true,
        })
      }
    }

    return {
      accessoriesToAdd,
      entriesToAdd: newEntriesToAdd,
      entriesToDelete: newEntriesToDelete,
      entriesToUpdate: newEntriesToUpdate,
    }
  }
}

module.exports = CartResolver
