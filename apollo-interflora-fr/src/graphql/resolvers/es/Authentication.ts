import GraphQLContext from '../../Context'
import BaseResolver from '../ct-datocms/Authentication'
import TokenTransformer from '../../transformers/ITAuthentication/Token'
import { GraphQLError } from 'graphql'

class AuthenticationResolver extends BaseResolver {
  override async login(
    _parent: undefined,
    args: GraphQLContextArgs<{ password: string; username: string }>,
    context: GraphQLContext
  ): Promise<GQLToken> {
    const { password, username } = args
    const { authentication, ...customer } = (await context.dataSources.itAuthentication.login(username, password)) ?? {}
    if (!authentication) throw new GraphQLError('500 token not found')
    return TokenTransformer.toGQL(authentication, context, { customer })
  }

  // override async _customerLogin({ email, password }: { email: string; password: string }, context: GraphQLContext) {
  //     const { authentication } = (await context.dataSources.itAuthentication.login(email, password)) ?? {}
  //     if (!authentication) throw new GraphQLError('500 token not found')
  //     return authentication
  // }

  // override async _customerSignin(
  //     { anonymousCartSignInMode, anonymousId, email, password }: CTCustomerSignin,
  //     context: GraphQLContext
  // ) {
  //     const { authentication: token, ...customer } = (await context.dataSources.itAuthentication.login(email, password)) ?? {}
  //     if (!token) throw new GraphQLError('500 token not found')
  //     const gqlToken = TokenTransformer.toGQL(token, context, { customer })
  //     const cart = {}
  //     return {
  //         cart,
  //         customer,
  //         gqlToken,
  //         token,
  //     }
  // }
}

export = AuthenticationResolver
