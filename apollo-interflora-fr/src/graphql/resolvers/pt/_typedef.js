/** @typedef {import('../../datasources/_typedef').ESDataSources} PTDataSources */

/** @typedef {import('../../dataloaders/availability').loadersDefinition} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/bloomreach').loadersDefinition} BloomreachDataLoaders */
/** @typedef {import('../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs').loadersDefinition} GFSDataLoaders */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: PTDataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    bloomreach: BloomreachDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *  }
 * }} PTContext
 */
