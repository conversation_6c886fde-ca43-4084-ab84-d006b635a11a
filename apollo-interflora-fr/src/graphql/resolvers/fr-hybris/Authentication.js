const _ = require('lodash')
const BaseResolver = require('./Base')
const CONST = require('../../../const')
const { parseJwt } = require('../../../helpers')
const { PhoneTransformer, UserTransformer, TokenTransformer } = require('./transformers')
const { Session } = require('../../../plugins/session')

/** @typedef {import("../_typedef")} */

class AuthenticationResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{campaigns: GQLInputTrackingCampaign[]}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async anonymousLogin(_parent, args, context) {
    const response = await context.dataSources.hybrisAuth.login()

    await this._setAuthContext(context, response, args.context.siteId)
    await this._setTrackingCampaigns(context, args.campaigns)

    return TokenTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  checkEmailExists(_parent, args, context) {
    return context.dataSources.hybrisInterflora.verifyEmailHasRegisteredUser(args.email)
  }

  /**
   * @param {Token|undefined} token
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<void|User>}
   */
  async getTokenUser(token, _args, context) {
    if (token && token.user) {
      return token.user
    }

    if (context.auth && !context.auth.isAnonymous) {
      return this._getCurrentUser(context)
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string, password: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ value: string, refreshLifetime: Number, refreshToken: string, lifetime: Number, createdAt: Number}>}
   */
  async login(_parent, args, context) {
    const anonymousCartId = (!context.auth || context.auth.isAnonymous) && _.get(context, 'session.apiCart.guid')

    try {
      const response = await context.dataSources.hybrisAuth.login(args.username, args.password)

      if (!response.login) {
        response.login = args.username
      }

      delete context.session
      if (context.auth) {
        Session.destroy(context.server.redis, context.auth.sessionId)
        delete context.auth.sessionId
      }

      await this._setAuthContext(context, response, args.context.siteId)

      // if we are logging in from an anonymous session, transform our anonymous cart to current if any
      if (anonymousCartId) {
        await this._attachAnonymousCartToUser(anonymousCartId, context)
      }

      // NB: do not try to attach a logged-in cart to the current session as it is quite confusing

      return TokenTransformer.toGQL(response, context)
    } catch (e) {
      const errors = e.errors ?? []
      const hasGrantError = errors.some((error) => error.type === 'InvalidGrantError')
      const hasNoPasswordError = !errors.some((error) => error.subject === 'password')
      if (hasGrantError && hasNoPasswordError) {
        e.addError('InvalidGrantError', 'password', '')
      }
      throw e
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{refreshToken: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ value: string, refreshLifetime: Number, refreshToken: string, lifetime: Number, createdAt: Number}>}
   */
  async refreshToken(_parent, args, context) {
    const refreshJWT = parseJwt(args.refreshToken)

    let response
    if (refreshJWT.isAnonymous) {
      response = await context.dataSources.hybrisAuth.login()
    } else {
      response = await context.dataSources.hybrisAuth.refreshToken(refreshJWT.oauthToken)
      // workaround - avoid being considered anonymous
      // @todo: check if the api can send the login back
      if (!response.login) {
        response.login = refreshJWT.login
      }
    }

    await this._setAuthContext(context, response, args.context.siteId, refreshJWT.sessionId)

    return TokenTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{user: GQLInputRegisterUser}>} args
   * @param {GraphQLContext} context
   */
  async registerUser(_parent, args, context) {
    const { user } = args
    const payload = this._registerUserPayload(user, context)
    const anonymousCartId = (!context.auth || context.auth.isAnonymous) && _.get(context, 'session.apiCart.guid')

    await context.dataSources.hybrisUser.register(payload)

    const userLogin = user.type === CONST.user.userType.b2c ? user.email : user.username
    const response = await context.dataSources.hybrisAuth.login(userLogin, user.password)

    if (!response.login && user.password) {
      response.login = userLogin
    }

    await this._setAuthContext(context, response, args.context.siteId)

    if (anonymousCartId) {
      await this._attachAnonymousCartToUser(anonymousCartId, context)
    }

    return TokenTransformer.toGQL(response, context)
  }

  /**
   * @param {string} cartId - the **anonymous** cart guid
   * @param {GraphQLContext} context
   * @returns {Promise<void>}
   * @private
   */
  async _attachAnonymousCartToUser(cartId, context) {
    let apiCart

    try {
      apiCart = await context.dataSources.hybrisCart.createCart(undefined, cartId)
    } catch (error) {
      context.log.warn(`could not restore anonymous cart ${cartId}: ${error.message}`)
      return
    }

    context.session.apiCart = {
      ..._.pick(apiCart, ['guid', 'code']),
      countryId: apiCart.deliveryAddress?.country?.isocode,
    }

    const apiCartId = this._getCartId(apiCart, context)
    const productId = (await this._getContextCartMainProduct(context))?.product.code

    const promises = [
      context.loaders.checkPremiumProductsForCarts.clear({
        cartId: apiCartId,
        productId,
      }),
      context.loaders.carts.prime(apiCartId, apiCart),
      context.session.save(),
    ]
    // cart id changes btw anonymous and logged in - clean previous key
    const key = ['guid', 'code'].find((key) => apiCart[key] !== apiCartId)
    if (key) {
      promises.push(
        context.loaders.checkPremiumProductsForCarts.clear({
          cartId: apiCart[key],
          productId,
        })
      )
      promises.push(context.loaders.carts.clear(apiCart[key]))
    }

    await Promise.all(promises)
  }

  /**
   * @param {GQLInputRegisterUser} userPayload
   * @param {GraphQLContext} context
   * @returns {APIUserSignUp}
   */
  _registerUserPayload(userPayload, context) {
    const payload = {
      additionalInfo: userPayload.address2,
      // birthDate
      companyName: userPayload.company,
      country: {
        isocode: userPayload.countryId,
      },
      customerType: UserTransformer.toAPIType(userPayload.type),
      displayUid: userPayload.username,
      email: userPayload.email,
      firstName: userPayload.firstName,
      lastName: userPayload.lastName,
      line1: userPayload.address,
      password: userPayload.password,
      phone: userPayload.phone && PhoneTransformer.toAPI(userPayload.phone, context),
      postalCode: userPayload.postalCode,
      titleCode: UserTransformer.toAPICivility(userPayload.civility),
      town: userPayload.townLabel,
      uid: userPayload.type === CONST.user.userType.b2c ? userPayload.email : userPayload.username,
    }

    if (userPayload.townId) {
      payload.region = {
        isocode: userPayload.townId,
      }
    }

    return payload
  }

  /**
   * Mockup function for FR resolver
   * @param {undefined} _parent
   * @param {*} _args
   * @param {GraphQLContext} _context
   * @returns {Boolean}
   */
  async confirmUserEmail(_parent, _args, _context) {
    return true
  }
}

module.exports = AuthenticationResolver
