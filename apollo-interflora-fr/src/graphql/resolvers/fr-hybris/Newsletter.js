const BaseResolver = require('./Base')

/** @typedef {import("../../types/_typedef")}  */

class NewsletterResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{cartId: string, cartPk: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async unsubscribeFromCartMailing(_parent, args, context) {
    return await context.dataSources.hybrisNewsletter.unsubscribeRelancePanier(args.cartId, args.cartPk)
  }
}

module.exports = NewsletterResolver
