const { GraphQLApolloError } = require('../../errors')
const BaseResolver = require('./Base')
const {
  FestModeTransformer,
  PageTransformer,
  TemplateTransformer,
  TownPageTransformer,
  TownPageTabTransformer,
} = require('./transformers')
const CONST = require('../../../const')
const config = require('../../../config')
const { removeAccents, isUUID } = require('../../../helpers/string')
const { toFR } = require('../fr/shared')
const GQLRoute = require('../../models/Route')

/** @typedef {import('graphql').GraphQLResolveInfo} GraphQLResolveInfo */
/** @typedef {import("../../types/_typedef")}  */

class CmsResolver extends BaseResolver {
  /**
   * @param {GQLPage} _page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLFestMode>}
   */
  async getPageFest(_page, _args, context) {
    const apiData = await context.loaders.festModes.load({})

    if (apiData) {
      return FestModeTransformer.toGQL(apiData)
    }
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLMenu[]>}
   */
  async getPageFooters(page, _args, _context) {
    return page.footers
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLGelluleType[]>}
   */
  async getPageGellules(page, _args, _context) {
    return page.gellules
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLAccessoryCategoryType[]>}
   */
  async getPageAccessoryCategories(page, _args, _context) {
    return page.accessoryCategories
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLMenu[]>}
   */
  async getPageMenu(page, _args, _context) {
    return page.menus
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *   fallback: boolean,
   *   previousRoute: GQLInputRoute,
   *   route: GQLInputRoute,
   *   zoneNames: string[]
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLPage>}
   */
  async getPage(_parent, args, context) {
    const { route } = args

    let productCategory
    let code
    let name
    let pageType = CONST.cms.pageTypes.content
    let type
    let zones = args.zoneNames
    let customLink

    switch (route.name) {
      case 'index':
        name = route.name.replace(/^\//, '')
        type = 'index'
        break
      case 'multiple_parents_category':
      case 'parent_category':
      case 'category':
        try {
          pageType = CONST.cms.pageTypes.category
          type = 'category'
          {
            const param = route.params.find((p) => p.name === 'slugCategory')
            const slug = removeAccents(param ? param.value : config.apps[context.siteId].product.category.allSlug)
            code = (await context.dataSources.hybrisCatalog.getCategory(slug)).code
          }
        } catch (e) {
          if (e.extensions?.code === 404) {
            throw new GraphQLApolloError(`PageNotFound`, 404)
          }
        }

        break
      case 'cms':
        {
          const slugParam = route.params.find((param) => param.name === 'slug')
          if (!slugParam) {
            return null
          }
          name = slugParam.value.replace(/\/+/, '')
        }
        break
      case 'product':
      case 'subscribe_product':
        try {
          pageType = CONST.cms.pageTypes.product
          type = 'product'

          {
            const slug = route.params.find((param) => param.name === 'slugProduct')?.value

            if (slug) {
              if (route.name === 'subscribe_product') {
                code = slug
              } else {
                code = await context.loaders.productCodesBySlugUrls.load({ slugUrl: removeAccents(slug) })
              }
            }
          }

          if (context.categoryId) {
            try {
              if (!isUUID(context.categoryId)) {
                // During french progressive launch we could receive a CT uuid category as categoryId when switching from ct to hybris site
                productCategory = await context.dataSources.hybrisCatalog.getCategory(context.categoryId)
              } else {
                // Resolve ct category uuid if we detected it's one
                const datoCmsSlugProductCategory = await context.loaders.datocms.categorySlugsById.load({
                  id: context.categoryId,
                  locale: context.language,
                })
                productCategory = await context.dataSources.hybrisCatalog.getCategory(datoCmsSlugProductCategory)
              }
            } catch (e) {
              productCategory = undefined
            }
          }
        } catch (e) {
          if (e.extensions?.code === 404) {
            throw new GraphQLApolloError(`PageNotFound`, 404)
          }
        }

        break

      case 'floristAddressDepartments':
      case 'floristAddressDepartment':
      case 'floristAddressTown':
      case 'floristDepartment':
      case 'floristTown':
      case 'floristTowns':
        pageType = CONST.cms.pageTypes[route.name]

        {
          let paramName = pageType === CONST.cms.pageTypes.floristTown ? 'slugTown' : 'slugUrl'

          for (const param of route.params) {
            if (param.name === paramName) {
              code = param.value
            }
          }
        }

        // special case for florist store locator pages
        {
          if (['floristAddressDepartments', 'floristAddressDepartment', 'floristAddressTown'].includes(route.name)) {
            zones = ['none']
            const departmentsLink = new GQLRoute()
            departmentsLink.name = 'floristAddressDepartments'
            departmentsLink.params.push({
              name: 'url',
              value: config.apps[context.siteId].storeLocator.customPath,
            })
            const departmentLink = new GQLRoute()
            departmentLink.name = 'floristAddressDepartment'
            departmentLink.params.push({
              name: 'slugUrl',
              value: args.route.params.find((param) => param.name === 'slugDepartment')?.value,
            })
            customLink = {
              floristDepartment: departmentLink,
              floristDepartments: departmentsLink,
            }
          }
        }

        break
      default:
        name = route.name.replace(/^\//, '')
        break
    }

    code = removeAccents(code)
    /** @type {APICMSPage} */
    let apiPage = await context.loaders.pages.load({
      code,
      name,
      pageType,
    })

    const fallback = args.defaultCmsPage ?? args.fallback // @todo: delete defaultCmsPage option when front is ready
    if (!apiPage && fallback) {
      name = route.name.replace(/^\//, '')
      code = config.apps[context.siteId].cms.emptyContentPageUid
      apiPage = await context.loaders.pages.load({
        code,
        name,
        pageType,
      })

      // no default page found - no 400 but empty result
      if (!apiPage) return null
    }

    const breadcrumb = apiPage.breadcrumb
    if (route.name === 'category' && breadcrumb.length > 2) {
      throw new GraphQLApolloError(`PageNotFound`, 404)
    }

    const components = (
      await context.loaders.components.loadMany(
        apiPage.contentSlots?.contentSlot
          ?.map((slot) => (slot.components?.component || []).map((component) => component.uid))
          .flat()
          .map((uid) => ({ componentId: uid }))
          .filter(Boolean) ?? []
      )
    ).reduce((o, component) => {
      o[component.uid] = component
      return o
    }, {})
    return PageTransformer.toGQL(apiPage, context, {
      components,
      customLink,
      pageType,
      productCategory,
      type,
      zones,
    })
  }

  /**
   * @param {GQLRoute} route
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {GQLRouteParamType[]}
   */
  getRouteParams(route, _args, _context) {
    return route.params || []
  }

  /**
   * @param {GQLComponentZoneType} componentZone
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTemplate>}
   */
  getTemplates(componentZone, _args, context) {
    return Promise.all(
      componentZone.uids.map(async (uid) =>
        TemplateTransformer.toGQL(await context.loaders.components.load({ componentId: uid }), context)
      )
    )
  }

  /**
   * @param {GQLTownPage} _townPage
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTownPageTab[]>}
   */
  async getTownPageTabs(_townPage, _args, context) {
    const apiTabs = await context.dataSources.hybrisCms.getTownProductInfoComponent()

    return (apiTabs.components || []).map(TownPageTabTransformer.toGQL)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string, includeAddress: (boolean|undefined)}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTownPage>}
   */
  async getTownProductInfoBySlug(_parent, args, context) {
    const { slugUrl, includeAddress } = args
    const [department, slug] = slugUrl.split('/')

    if (includeAddress) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext._resolvers.cms.getTownProductInfoBySlug(...frResolverArgs)
    }

    const apiData = await context.dataSources.hybrisDepartment.getCity(department, slug)
    return TownPageTransformer.toGQL(apiData, context)
  }
}

module.exports = CmsResolver
