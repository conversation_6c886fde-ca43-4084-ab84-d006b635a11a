const BaseResolver = require('./Base')
const { PhoneTransformer, UserTransformer, ContactSubjectTransformer } = require('./transformers')
const { dates } = require('../../../helpers')

/** @typedef {import("../../types/_typedef")} */

class ContactResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string, numberOrder: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async checkAttachCommand(_parent, args, context) {
    return context.dataSources.hybrisContact.isCommandAttachedToEmail(args.numberOrder, args.email)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLContactSubject>}
   */
  async getContactFormSubjects(_parent, _args, context) {
    const subjects = await context.dataSources.hybrisContact.getSubjects()

    return subjects.map((apiSubject) => ContactSubjectTransformer.toGQL(apiSubject))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{contactMessage: GQLContactMessageType}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLContactMessage>}
   */
  async sendContactMessage(_parent, args, context) {
    const { contactMessage } = args

    const subject = (await this._getFlattenedContactSubjects(context)).find(
      (node) => node.id === contactMessage.subjectId
    )

    let precisionMessage
    if (contactMessage.modifiedAddress) {
      precisionMessage = contactMessage.modifiedAddress
    } else if (contactMessage.modifiedDate) {
      precisionMessage = contactMessage.modifiedDate
    } else if (contactMessage.modifiedMessage) {
      precisionMessage = contactMessage.modifiedMessage
    }

    const payload = {
      companyName: contactMessage.company,
      email: contactMessage.email,
      firstName: contactMessage.firstName,
      grecaptchaResponse: contactMessage.tokenCaptcha,
      lastName: contactMessage.lastName,
      message: contactMessage.message,
      objetMessage: (subject.parent || subject).id,
      objetMessagePrecision: contactMessage.subjectId,
      orderId: contactMessage.orderNumber,
      phone: contactMessage.phone && PhoneTransformer.toAPI(contactMessage.phone, context),
      precisionMessage,
      title: UserTransformer.toAPICivility(contactMessage.civility),
    }

    if (contactMessage.file || contactMessage.fileName) {
      payload.fileContent = contactMessage.file
      payload.fileName = contactMessage.fileName
    }

    try {
      await context.dataSources.hybrisContact.sendContact(payload)

      return Object.assign({}, contactMessage, { subject, success: true })
    } catch (error) {
      if (error.extensions.code === 400) {
        throw error // Validation error
      }

      return Object.assign({}, contactMessage, { subject, success: false })
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{retractation: GQLInputRetractation}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async sendRetractation(_parent, args, context) {
    const { dataSources } = context
    const { retractation } = args

    const orderDate = new Date(retractation.orderDate)

    const payload = {
      address: retractation.address,
      email: retractation.email,
      // fileContent: '', Not given
      // fileName: '', Not given
      firstName: retractation.firstName,
      grecaptchaResponse: retractation.tokenCaptcha,
      lastName: retractation.lastName,
      // message: '', Not given
      // objetMessage: '', Not given
      // objetMessagePrecision: '', Not given
      orderDate: dates.formatToAPI(orderDate),
      orderId: retractation.orderNumber,
      phone: retractation.phone && PhoneTransformer.toAPI(retractation.phone, context),
      postalCode: retractation.postalCode,
      title: UserTransformer.toAPICivility(retractation.civility),
      town: retractation.townId || retractation.townLabel,
    }

    if (retractation.address2) {
      payload.address += ` ${retractation.address2}`

      if (payload.address.length > 28) {
        payload.address = payload.address.slice(0, 28)
      }
    }

    if (retractation.company) {
      payload.companyName = retractation.company
    }

    try {
      await dataSources.contact.sendRetractation(payload)
      return true
    } catch (error) {
      if (error.extensions.code === 400) {
        throw error // Validation error
      }

      return false
    }
  }

  /**
   *
   * @param {GraphQLContext} context
   * @param {GQLContactSubject} nodes
   * @returns {Promise<GQLContactSubject[]>}
   * @private
   */
  async _getFlattenedContactSubjects(context, nodes) {
    let result = [...(nodes || (await this.getContactFormSubjects(undefined, undefined, context)))]

    for (const node of result) {
      if (node.subSubject) {
        result = result.concat(await this._getFlattenedContactSubjects(context, node.subSubject))
      }
    }

    return result
  }
}

module.exports = ContactResolver
