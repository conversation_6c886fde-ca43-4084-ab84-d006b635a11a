const config = require('../../../config')
const BaseResolver = require('./Base')

/** @typedef {import("../../types/_typedef")} */

class RootResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Object>}
   */
  async getConfig(_parent, _args, context) {
    const config = await context.dataSources.hybrisInterflora.getConfigs()
    return config
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ form: { placement: string, step: string, userCategory: string } }>} args
   * @returns {GQLForm}
   */
  getForm(_parent, args) {
    const { placement, step, userCategory } = args.form

    const fieldsets = config.forms[placement]?.[userCategory]?.[step]
    if (!fieldsets) return null

    return {
      fieldsets,
      placement,
      step,
      title: 'title',
      userCategory,
    }
  }

  /**
   * @param {undefined} _parents
   * @param {GraphQLContextArgs<{ campaigns: GQLInputTrackingCampaign[] }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async setCampaigns(_parents, args, context) {
    await this._setTrackingCampaigns(context, args.campaigns)
    return true
  }
}

module.exports = RootResolver
