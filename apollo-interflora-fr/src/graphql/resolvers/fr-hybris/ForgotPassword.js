const BaseResolver = require('./Base')

/** @typedef {import("../../types/_typedef")} */

class ForgotPasswordResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, password: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async resetPassword(_parent, args, context) {
    /**
     * If the reset token is invalid, let it throw.
     * Otherwise, always returns true.
     */
    await context.dataSources.hybrisForgottenPasswords.resetPassword(args.code, args.password)
    return true
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async resetPasswordCheckCode(_parent, args, context) {
    /**
     * If the reset token is invalid, let it throw.
     * Otherwise, always returns true.
     */
    try {
      await context.dataSources.hybrisForgottenPasswords.validateResetToken(args.code)
      return true
    } catch (error) {
      if (error.extensions.code !== 400) {
        /** In case of a server error, or even authorization error */
        throw error
      }

      return false
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{username: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async resetPasswordRequest(_parent, args, context) {
    /**
     * If the username is invalid, let it throw.
     * Otherwise, always returns true.
     */
    await context.dataSources.hybrisForgottenPasswords.sendResetToken(args.username)
    return true
  }
}

module.exports = ForgotPasswordResolver
