/** @typedef {import('../_typedef')} */

/**
 * @param {Object[]} options
 * @param {string} [options.search]
 * @param {string} [options.countryIsoCode]
 * @param {string} [options.productCode]
 * @param {GraphQLContext} context
 * @returns {Promise<{key: string, value: string}[]>}
 */
const genTowns = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) =>
      context.dataSources.hybrisGeo.getTowns(option.search, option.countryIsoCode, option.productCode)
    )
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {Object[]} options
 * @param {boolean} options.includeFR
 * @param {boolean} options.allCountries
 * @param {GraphQLContext} context
 * @returns {Promise<APICountry[]>}
 */
const genCountries = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisGeo.getCountries(option.includeFR, option.allCountries))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

module.exports = { genCountries, genTowns }
