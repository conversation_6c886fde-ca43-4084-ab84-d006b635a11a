/** @typedef {import('../_typedef')} */

/**
 * @param {Object[]} options
 * @param {string} options.code
 * @param {string} [options.countryId]
 * @param {GraphQLContext} context
 * @returns {Promise<APIProduct[]>}
 */
const genProducts = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisProduct.getProduct(option.code, option.countryId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {Object[]} options
 * @param {string} options.slugUrl
 * @param {string} [options.countryId]
 * @param {GraphQLContext} context
 * @returns {Promise<string[]>}
 */
const genProductCodesBySlugUrls = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisProduct.getProduct(option.slugUrl, option.countryId))
  )

  return Promise.all(
    results.map(async (result, i) => {
      if (result.status === 'fulfilled') {
        await (context.loaders.hybris?.products ?? context.loaders.products).prime(
          { code: result.value.code, countryId: options[i].countryId },
          result.value
        )
        return result.value.code
      }

      return result.reason
    })
  )
}

module.exports = { genProductCodesBySlugUrls, genProducts }
