/** @typedef {import('../_typedef')} */

/**
 * @param {string[]} ids
 * @param {GraphQLContext} context
 * @returns {Promise<APIOrder[]>}
 */
const genOrders = async (ids, context) => {
  const results = await Promise.allSettled(ids.map((id) => context.dataSources.hybrisOrder.getOrder(id)))

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {{email: string, orderNumber: string}[]} trackings
 * @param {GraphQLContext} context
 * @returns {Promise<string[]>}
 */
const genOrderIdsByEmails = async (trackings, context) => {
  const results = await Promise.allSettled(
    trackings.map((tracking) => context.dataSources.hybrisOrder.getOrderTracking(tracking.email, tracking.orderNumber))
  )

  return Promise.all(
    results.map(async (result) => {
      if (result.status === 'fulfilled') {
        const loader = context.loaders.orders ?? context.loaders.hybris.orders
        await loader.prime(result.value.guid, result.value)
        return result.value.guid
      }

      return result.reason
    })
  )
}

/**
 * @param {string[]} tokens
 * @param {GraphQLContext} context
 * @returns {Promise<string[]>}
 */
const genOrderIdsByTokens = async (tokens, context) => {
  const results = await Promise.allSettled(tokens.map((token) => context.dataSources.hybrisOrder.getIflOrder(token)))

  return Promise.all(
    results.map(async (result) => {
      if (result.status === 'fulfilled') {
        await context.loaders.orders.prime(result.value.guid, result.value)
        return result.value.guid
      }

      return result.reason
    })
  )
}

module.exports = { genOrderIdsByEmails, genOrderIdsByTokens, genOrders }
