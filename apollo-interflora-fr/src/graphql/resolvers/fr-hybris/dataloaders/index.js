const crypto = require('crypto')
const DataLoader = require('dataloader')
const { formatISO } = require('date-fns')

const config = require('../../../../config')

const {
  genCarts,
  genDeliveryModesForCarts,
  genPaymentMethodsForCarts,
  genValidateRegionsForCarts,
  genCheckPremium,
} = require('./Cart')
const { genComponents, genFestModes, genPages } = require('./Cms')
const { genOrderIdsByEmails, genOrders, genOrderIdsByTokens } = require('./Order')
const {
  genSuggestedMessages,
  genDeliveryDates,
  genDeliveryTimeIntervals,
  genCelebrationTexts,
  genIsPremiumUser,
} = require('./Interflora')
const { genTowns, genCountries } = require('./Geo')
const { genProducts, genProductCodesBySlugUrls } = require('./Product')
const RedisDataLoader = require('../../../dataloaders/RedisDataLoader')
const { genSearches } = require('./Solr')
const { genDepartments, genDepartment } = require('./Department')

/** @typedef {import('../../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../_typedef')} */
/** @typedef {import('./_typedef')} */

/**
 * @typedef {{
 *  carts: RedisDataLoader<string, APICart>
 *  celebrationTexts: RedisDataLoader<string, string[]>
 *  checkPremiumProductsForCarts: RedisDataLoader<{cartId: string, productId: string}, APIPremiumProduct>
 *  components: RedisDataLoader<{componentId: string, catalogCode: string, productCode: string, categoryCode: string}, APICMSComponent>
 *  countries: RedisDataLoader<{includeFR: boolean, allCountries: boolean}, APICountry>
 *  deliveryDates: RedisDataLoader<{code: string, postalCode: string, countryId: string}, APIExcludedDateList>
 *  deliveryTimeIntervals: RedisDataLoader<{date: Date, postalCode: string, productCode: string}, APIMomentLivraisonList>
 *  festModes: RedisDataLoader<undefined, APIFestMode>
 *  isPremiumUser: RedisDataLoader<string, boolean>
 *  occasions: RedisDataLoader<undefined, APIOrderReason[]>
 *  orderIdsByEmails: RedisDataLoader<{email: string, orderNumber: string}, string>
 *  orderIdsByTokens: RedisDataLoader<string, string>
 *  orders: RedisDataLoader<string, APIOrder>
 *  pages: RedisDataLoader<{pageType: string, name: string, code: string}, APICMSPage>
 *  paymentMethodsByCarts: RedisDataLoader<string, APIPaymentMethod>
 *  productCodesBySlugUrls: RedisDataLoader<{slugUrl: string, countryId: string}, string>
 *  products: RedisDataLoader<{code: string, countryId: string}, APIProduct>
 *  suggestedMessages: RedisDataLoader<string, {key: string, value: string}[]>
 *  towns: RedisDataLoader<{search: string, countryIsoCode: string, productCode: string}, APILocalite>
 *  validateRegionsByCarts: RedisDataLoader<{postalCode: string, productId: string, cartId: string}, APIValidateLocalite>
 *  departments: RedisDataLoader<undefined, APIFloraDepartmentList>
 *  department: RedisDataLoader<{slugUrl: string}, APIFloraDepartment>
 * }} DataLoaderMap
 *
 * @callback DataLoaderBuilder
 * @param {GraphQLContext} context
 * @returns {RedisDataLoader<any, any, any>}
 */

/**
 * @param {string} name
 * @returns {number}
 */
function getDataloaderTtl(name) {
  return config.graphql?.dataloaders?.[name]?.ttl || config.graphql?.dataloaders?.defaults?.ttl || 300
}

/**
 * @type {Object<string, DataLoaderBuilder>}
 */
const loaders = {
  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, APICart>}
   */
  carts: (context) =>
    new RedisDataLoader('carts', new DataLoader((ids) => genCarts(ids, context)), {
      expire: getDataloaderTtl('carts'),
      redis: context.server.redis,
    }),

  /**
   * @param {FRHybrisContext} context
   * @returns {RedisDataLoader<string, APICategory>}
   */
  categories: (context) =>
    new RedisDataLoader(
      'hybris:category',
      new DataLoader(async (categoryIds) => {
        const results = await Promise.allSettled(
          categoryIds.map((categoryId) => context.dataSources.hybrisCatalog.getCategory(categoryId))
        )
        return results.map((r) => (r.status === 'fulfilled' ? r.value : r.reason))
      }),
      {
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, string[]>}
   */
  celebrationTexts: (context) =>
    new RedisDataLoader(
      'celebration-texts',
      new DataLoader((productCodes) => genCelebrationTexts(productCodes, context)),
      {
        expire: getDataloaderTtl('celebrationTexts'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{cartId: string, productId: string}, APIPremiumProduct>}
   */
  checkPremiumProductsForCarts: (context) =>
    new RedisDataLoader(
      `${context.session.id}:cart-premium-product`,
      new DataLoader((options) => genCheckPremium(options, context)),
      {
        // productId is just for cache key generation
        cacheKeyFn: (option) => `${option.cartId}:${option.productId}:${option.deliveryFeesId}`,
        expire: getDataloaderTtl('checkPremiumProductsForCarts'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{componentId: string, catalogCode: string, productCode: string, categoryCode: string}, APICMSComponent>}
   */
  components: (context) =>
    new RedisDataLoader(
      `${context.baseSiteId}:${context.auth?.isAnonymous ? 'anonymous' : 'logged-in'}:components`,
      new DataLoader((options) => genComponents(options, context)),
      {
        cacheKeyFn: (key) => key.componentId,
        expire: getDataloaderTtl('components'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{includeFR: boolean, allCountries: boolean}, APICountry>}
   */
  countries: (context) =>
    new RedisDataLoader('countries', new DataLoader((options) => genCountries(options, context)), {
      cacheKeyFn: (option) => `${option.allCountries}:${option.includeFR}`,
      expire: getDataloaderTtl('countries'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{code: string, postalCode: string, countryId: string}, APIExcludedDateList>}
   */
  deliveryDates: (context) =>
    new RedisDataLoader('delivery-dates', new DataLoader((options) => genDeliveryDates(options, context)), {
      cacheKeyFn: (key) =>
        `${key.countryId}${key.postalCode ? `:${key.postalCode}` : ''}${key.code ? `:${key.code}` : ''}`,
      expire: getDataloaderTtl('deliveryDates'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, string[]>}
   */
  deliveryModes: (context) =>
    new RedisDataLoader(
      `${context.countryId}:${context.session.id}:delivery-modes`,
      new DataLoader((cartIds) => genDeliveryModesForCarts(cartIds, context)),
      {
        expire: getDataloaderTtl('deliveryModes'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{date: Date, occastion: string, postalCode: string, productCode: string}, APIMomentLivraisonList>}
   */
  deliveryTimeIntervals: (context) =>
    new RedisDataLoader(
      'delivery-time-intervals',
      new DataLoader((options) => genDeliveryTimeIntervals(options, context)),
      {
        cacheKeyFn: (key) =>
          `${key.postalCode || ''}:${formatISO(key.date, { representation: 'date' })}:${key.productCode || ''}:${
            key.occasion || ''
          }`,
        expire: getDataloaderTtl('deliveryTimeIntervals'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{slugUrl: string}, APIFloraDepartment>}
   */
  department: (context) =>
    new RedisDataLoader('department', new DataLoader((slugUrls) => genDepartment(slugUrls, context)), {
      cacheKeyFn: (slugUrl) => slugUrl,
      expire: getDataloaderTtl('getDepartment'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<undefined, APIFloraDepartmentList>}
   */
  departments: (context) =>
    new RedisDataLoader('departments', new DataLoader(() => genDepartments(context)), {
      expire: getDataloaderTtl('getDepartments'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<undefined, APIFestMode>}
   */
  festModes: (context) =>
    new RedisDataLoader('fest-modes', new DataLoader(() => genFestModes(context)), {
      expire: getDataloaderTtl('festModes'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, boolean>}
   */
  isPremiumUser: (context) =>
    new RedisDataLoader('premium-user', new DataLoader((emails) => genIsPremiumUser(emails, context)), {
      expire: getDataloaderTtl('isPremiumUser'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<undefined, APIOrderReason[]>}
   */
  occasions: (context) =>
    new RedisDataLoader(
      'occasions',
      new DataLoader(async () => [await context.dataSources.hybrisInterflora.getAllActiveOccasions()]),
      {
        expire: getDataloaderTtl('occasions'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{email: string, orderNumber: string}, string>}
   */
  orderIdsByEmails: (context) =>
    new RedisDataLoader('orders-emails', new DataLoader((trackings) => genOrderIdsByEmails(trackings, context)), {
      cacheKeyFn: (key) => `${key.email}:${key.orderNumber}`,
      deserialize: (rawValue) => rawValue,
      expire: getDataloaderTtl('orderIdsByEmails'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, string>}
   */
  orderIdsByTokens: (context) =>
    new RedisDataLoader('orders-tokens', new DataLoader((tokens) => genOrderIdsByTokens(tokens, context)), {
      deserialize: (rawValue) => rawValue,
      expire: getDataloaderTtl('orderIdsByTokens'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, APIOrder>}
   */
  orders: (context) =>
    new RedisDataLoader('orders', new DataLoader((ids) => genOrders(ids, context)), {
      expire: getDataloaderTtl('orders'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{pageType: string, name: string, code: string}, APICMSPage>}
   */
  pages: (context) =>
    new RedisDataLoader(
      `${context.baseSiteId}:${context.auth?.isAnonymous ? 'anonymous' : 'logged-in'}:pages`,
      new DataLoader((options) => genPages(options, context)),
      {
        cacheKeyFn: (key) => `${key.pageType}:${key.name || '-'}:${key.code || '-'}`,
        expire: getDataloaderTtl('pages'),
        redis: context.server.redis,
        tags: (_page, key) => {
          if (!key.name) return []

          const tag = `pg:${key.name}`
          context.cacheTags.add(tag)
          return [tag]
        },
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, APIPaymentMethod>}
   */
  paymentMethodsByCarts: (context) =>
    new RedisDataLoader(
      `${context.countryId}:${context.session.id}:payment-methods`,
      new DataLoader((cartIds) => genPaymentMethodsForCarts(cartIds, context)),
      {
        expire: getDataloaderTtl('paymentMethodsByCarts'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{slugUrl: string, countryId: string}, string>}
   */
  productCodesBySlugUrls: (context) =>
    new RedisDataLoader(
      'products-slugurls',
      new DataLoader((slugUrls) => genProductCodesBySlugUrls(slugUrls, context)),
      {
        cacheKeyFn: (key) => `${key.countryId || context.countryId}:${key.slugUrl}`,
        deserialize: (rawValue) => rawValue,
        expire: getDataloaderTtl('productCodesBySlugUrls'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{code: string, countryId: string}, APIProduct>}
   */
  products: (context) =>
    new RedisDataLoader('products', new DataLoader((productCodes) => genProducts(productCodes, context)), {
      cacheKeyFn: (key) => `${key.countryId || context.countryId}:${key.code}`,
      expire: getDataloaderTtl('products'),
      redis: context.server.redis,
      tags: (product) => {
        const tag = `p:${product.code}`
        context.cacheTags.add(tag)
        return [tag]
      },
    }),

  solrSearches: (context) =>
    new RedisDataLoader('solrSearches', new DataLoader((options) => genSearches(options, context)), {
      cacheKeyFn: (params) => `solr:${crypto.createHash('sha256').update(JSON.stringify(params)).digest('base64')}`,
      expire: getDataloaderTtl('solr'),
      redis: context.server.redis,
      tags: (response) => {
        if (!response?.docs) return []

        const tags = []
        for (const doc of response.docs) {
          const code = doc.code_string || doc.codeText_text
          if (code) {
            const tag = `p:${code}`
            context.cacheTags.add(tag)
            tags.push(tag)
          }
        }

        return tags
      },
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<string, {key: string, value: string}[]>}
   */
  suggestedMessages: (context) =>
    new RedisDataLoader(
      `${context.countryId}:suggested-messages`,
      new DataLoader((occasionCodes) => genSuggestedMessages(occasionCodes, context)),
      {
        expire: getDataloaderTtl('suggestedMessages'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{search: string, countryIsoCode: string, productCode: string}, APILocalite>}
   */
  towns: (context) =>
    new RedisDataLoader('towns', new DataLoader((options) => genTowns(options, context)), {
      cacheKeyFn: (key) =>
        `${key.countryIsoCode || context.countryId}:${key.productCode || ''}:${key.search.toUpperCase()}`,
      expire: getDataloaderTtl('towns'),
      redis: context.server.redis,
    }),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{ currentPage: number, pageSize: number, statuses: string[], userId: string, }, APIOrderHistoryList>}
   */
  userOrders: (context) =>
    new RedisDataLoader(
      'hybris:userOrders',
      new DataLoader(async (options) => {
        const results = await Promise.allSettled(
          options.map(({ currentPage, pageSize, statuses, userId }) =>
            context.dataSources.hybrisOrder.getUserOrders(userId, currentPage, pageSize, statuses)
          )
        )

        return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
      }),
      {
        expire: getDataloaderTtl('user'),
        redis: context.server.redis,
      }
    ),

  /**
   * @param {GraphQLContext} context
   * @returns {RedisDataLoader<{postalCode: string, productId: string, cartId: string}, APIValidateLocalite>}
   */
  validateRegionsByCarts: (context) =>
    new RedisDataLoader('validate-regions', new DataLoader((options) => genValidateRegionsForCarts(options, context)), {
      // productId is just for cache key generation
      cacheKeyFn: (key) => `${key.cartId}:${key.postalCode}:${key.productId}`,
      expire: getDataloaderTtl('validateRegionsByCarts'),
      redis: context.server.redis,
    }),
}

class DataLoaders {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    /** @type {CommerceToolsDataLoaders} */
    this.commerceTools
    /**
     * @private
     */
    this.context = context

    /**
     * @type {DataLoaderMap>}
     * @private
     */
    this.loaders = {}
  }

  /**
   * @returns {RedisDataLoader<string, APICart>}
   */
  get carts() {
    return this.getLoader('carts')
  }

  /**
   * @returns {RedisDataLoader<string, APICategory>}
   */
  get categories() {
    return this.getLoader('categories')
  }

  /**
   * @returns {RedisDataLoader<string, string[]>}
   */
  get celebrationTexts() {
    return this.getLoader('celebrationTexts')
  }

  /**
   * @returns {RedisDataLoader<{cartId: string, productId: string}, APIPremiumProduct>}
   */
  get checkPremiumProductsForCarts() {
    return this.getLoader('checkPremiumProductsForCarts')
  }

  /**
   * @returns {RedisDataLoader<{componentId: string, catalogCode: string, productCode: string, categoryCode: string}, APICMSComponent>}
   */
  get components() {
    return this.getLoader('components')
  }

  /**
   * @returns {RedisDataLoader<{includeFR: boolean, allCountries: boolean}, APICountry>}
   */
  get countries() {
    return this.getLoader('countries')
  }

  /**
   * @returns {RedisDataLoader<{code: string, postalCode: string, countryId: string}, APIExcludedDateList>}
   */
  get deliveryDates() {
    return this.getLoader('deliveryDates')
  }

  /**
   * @returns {RedisDataLoader<undefined, string[]>}
   */
  get deliveryModes() {
    return this.getLoader('deliveryModes')
  }

  /**
   * @returns {RedisDataLoader<{date: Date, postalCode: string, productCode: string}, APIMomentLivraisonList>}
   */
  get deliveryTimeIntervals() {
    return this.getLoader('deliveryTimeIntervals')
  }

  /**
   * @returns {RedisDataLoader<undefined, APIFestMode>}
   */
  get festModes() {
    return this.getLoader('festModes')
  }

  /**
   * @returns {RedisDataLoader<undefined, APIOrderReason[]>}
   */
  get occasions() {
    return this.getLoader('occasions')
  }

  /**
   * @return {RedisDataLoader<{pageType: string, name: string, code: string}, APICMSPage>}
   */
  get pages() {
    return this.getLoader('pages')
  }

  /**
   * @returns {RedisDataLoader<string, APIPaymentMethod>}
   */
  get paymentMethodsByCarts() {
    return this.getLoader('paymentMethodsByCarts')
  }

  /**
   * @returns {RedisDataLoader<{slugUrl: string, countryId: string}, string>}
   */
  get productCodesBySlugUrls() {
    return this.getLoader('productCodesBySlugUrls')
  }

  /**
   * @returns {RedisDataLoader<{code: string, countryId: string}, APIProduct>}
   */
  get products() {
    return this.getLoader('products')
  }

  /**
   * @returns {RedisDataLoader<string, boolean | number>}
   */
  get isPremiumUser() {
    return this.getLoader('isPremiumUser')
  }

  /**
   * @returns {RedisDataLoader<string, APIOrder>}
   */
  get orders() {
    return this.getLoader('orders')
  }

  /**
   * @returns {RedisDataLoader<{email: string, orderNumber: string}, string>}
   */
  get orderIdsByEmails() {
    return this.getLoader('orderIdsByEmails')
  }

  /**
   * @returns {RedisDataLoader<string, string>}
   */
  get orderIdsByTokens() {
    return this.getLoader('orderIdsByTokens')
  }

  get solrSearches() {
    return this.getLoader('solrSearches')
  }

  /**
   * @returns {RedisDataLoader<string, {key: string, value: string}[]>}
   */
  get suggestedMessages() {
    return this.getLoader('suggestedMessages')
  }

  /**
   * @returns {RedisDataLoader<{search: string, countryIsoCode: string, productCode: string}, APILocalite>}
   */
  get towns() {
    return this.getLoader('towns')
  }

  /**
   * @returns {RedisDataLoader<{ currentPage: number, pageSize: number, statuses: string[], userId: string }, APIOrderHistoryList>}
   */
  get userOrders() {
    return this.getLoader('userOrders')
  }

  /**
   * @returns {RedisDataLoader<{postalCode: string, productId: string, cartId: string}, APIValidateLocalite>}
   */
  get validateRegionsByCarts() {
    return this.getLoader('validateRegionsByCarts')
  }

  /**
   * @returns {RedisDataLoader<undefined, APIFloraDepartmentList>}
   */
  get departments() {
    return this.getLoader('departments')
  }

  /**
   * @returns {RedisDataLoader<{slugUrl: string}, APIFloraDepartment>}
   */
  get department() {
    return this.getLoader('department')
  }

  /**
   * @param {string} name
   * @returns {RedisDataLoader<any, any, any>}
   * @private
   */
  getLoader(name) {
    if (!(name in loaders)) {
      throw new Error(`No ${name} dataloader found`)
    }

    if (!(name in this.loaders)) {
      this.loaders[name] = loaders[name](this.context)
    }

    return this.loaders[name]
  }
}

module.exports = DataLoaders
