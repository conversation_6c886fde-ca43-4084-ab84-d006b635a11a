/** @typedef {import('../_typedef')} */

/**
 * @param {GraphQLContext} context
 * @returns {Promise<APIFloraDepartmentList[]>}
 */
const genDepartments = async (context) => {
  return [(await context.dataSources.hybrisDepartment.getDepartments()) || null]
}

/**
 * @param {string[]} slugUrls
 * @param {GraphQLContext} context
 * @returns {Promise<APIFloraDepartment[]>}
 */
const genDepartment = async (slugUrls, context) => {
  const results = await Promise.allSettled(
    slugUrls.map((slugUrl) => context.dataSources.hybrisDepartment.getDepartment(slugUrl))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}
module.exports = { genDepartment, genDepartments }
