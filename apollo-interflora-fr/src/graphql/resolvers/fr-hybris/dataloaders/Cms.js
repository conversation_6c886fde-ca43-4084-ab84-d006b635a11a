/** @typedef {import('../_typedef')} */

/**
 * @param {Object[]} options
 * @param {string} options.componentId
 * @param {string} [options.catalogCode]
 * @param {string} [options.productCode]
 * @param {string} [options.categoryCode]
 * @param {GraphQLContext} context
 * @returns {Promise<APICMSComponent[]>}
 */
const genComponents = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((options) =>
      context.dataSources.hybrisCms.getComponent(
        options.componentId,
        options.catalogCode,
        options.productCode,
        options.catalogCode
      )
    )
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {GraphQLContext} context
 * @returns {Promise<APIFestMode[]>}
 */
const genFestModes = async (context) => {
  return [(await context.dataSources.hybrisCms.getFeteFlorale()) || null]
}

/**
 * @param {Object[]} options
 * @param {string} [options.pageType]
 * @param {string} [options.name]
 * @param {string} [options.code]
 * @param {GraphQLContext} context
 * @returns {Promise<APICMSPage[]>}
 */
const genPages = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisCms.getPage(option.pageType, option.name, option.code))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

module.exports = { genComponents, genFestModes, genPages }
