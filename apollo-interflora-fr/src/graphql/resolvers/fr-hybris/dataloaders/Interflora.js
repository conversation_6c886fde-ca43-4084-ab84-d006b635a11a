/** @typedef {import('../_typedef')} */

/**
 * @param {string[]} occasionCodes
 * @param {GraphQLContext} context
 * @returns {Promise<{key: string, value: string}[]>}
 */
const genSuggestedMessages = async (occasionCodes, context) => {
  const results = await Promise.allSettled(
    occasionCodes.map((code) => context.dataSources.hybrisInterflora.getSuggestedMessages(code))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {Object[]} options
 * @param {string} options.code
 * @param {string} [options.postalCode]
 * @param {string} [options.countryId]
 * @param {GraphQLContext} context
 */
const genDeliveryDates = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) =>
      context.dataSources.hybrisInterflora.getDeliveryDates(option.code, option.postalCode, option.countryId)
    )
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {Object[]} options
 * @param {Date} options.date
 * @param {string} [options.occasion]
 * @param {string} [options.postalCode]
 * @param {string} [options.productCode]
 * @param {GraphQLContext} context
 */
const genDeliveryTimeIntervals = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) =>
      context.dataSources.hybrisInterflora.getDeliveryTimeIntervals(
        option.date,
        option.occasion,
        option.postalCode,
        option.productCode
      )
    )
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {string[]} productCodes
 * @param {GraphQLContext} context
 * @returns {Promise<string[]>}
 */
const genCelebrationTexts = async (productCodes, context) => {
  const results = await Promise.allSettled(
    productCodes.map(({ code, countryId }) => context.dataSources.hybrisInterflora.getCelebrationTexts(code, countryId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {string[]} emails
 * @param {GraphQLContext} context
 * @returns {Promise<boolean[]>}
 */
const genIsPremiumUser = async (emails, context) => {
  const results = await Promise.allSettled(
    emails.map((email) => context.dataSources.hybrisInterflora.verifyEmailHasPremiumUser(email))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

module.exports = {
  genCelebrationTexts,
  genDeliveryDates,
  genDeliveryTimeIntervals,
  genIsPremiumUser,
  genSuggestedMessages,
}
