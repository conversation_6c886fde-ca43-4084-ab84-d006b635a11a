/** @typedef {import('../_typedef')} */

/**
 * @param {Object[]} options
 * @param {string} [options.search]
 * @param {string} [options.countryIsoCode]
 * @param {string} [options.productCode]
 * @param {GraphQLContext} context
 * @returns {Promise<{key: string, value: string}[]>}
 */
const genSearches = async (options, context) => {
  const results = await Promise.allSettled(options.map((params) => context.dataSources.solr.search(params)))

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

module.exports = { genSearches }
