/** @typedef {import('../_typedef')} */

/**
 * @param {string[]} ids
 * @param {GraphQLContext} context
 * @returns {Promise<APICart[]>}
 */
const genCarts = async (ids, context) => {
  const results = await Promise.allSettled(ids.map((id) => context.dataSources.hybrisCart.getCart(id)))

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {string[]} cartIds
 * @param {GraphQLContext} context
 * @returns {Promise<string[]>}
 */
const genDeliveryModesForCarts = async (cartIds, context) => {
  const results = await Promise.allSettled(
    cartIds.map((cartId) => context.dataSources.hybrisCart.checkoutModes(cartId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {string[]} cartIds
 * @param {GraphQLContext} context
 * @returns {Promise<APIPaymentMethod[]>}
 */
const genPaymentMethodsForCarts = async (cartIds, context) => {
  const results = await Promise.allSettled(
    cartIds.map((cartId) => context.dataSources.hybrisCart.getPaymentMethods(cartId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {{postalCode: string, productId: string, cartId: string}[]} options
 * @param {GraphQLContext} context
 * @returns {Promise<APIValidateLocalite[]>}
 */
const genValidateRegionsForCarts = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisCart.validateRegion(option.postalCode, option.cartId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

/**
 * @param {{cartId: string, productId: string}[]} options
 * @param {GraphQLContext} context
 * @returns {Promise<APIPremiumProduct[]>}
 */
const genCheckPremium = async (options, context) => {
  const results = await Promise.allSettled(
    options.map((option) => context.dataSources.hybrisCart.checkPremiumProduct(option.cartId, option.deliveryFeesId))
  )

  return results.map((result) => (result.status === 'fulfilled' ? result.value : result.reason))
}

module.exports = {
  genCarts,
  genCheckPremium,
  genDeliveryModesForCarts,
  genPaymentMethodsForCarts,
  genValidateRegionsForCarts,
}
