/** @typedef {import('../../datasources/_typedef').FRHybrisDataSources} FRHybrisDataSources */

/** @typedef {import('./dataloaders')} FRHybrisDataLoaders */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: FRHybrisDataSources
 *  loaders: FRHybrisDataLoaders
 * }} FRHybrisContext
 */

/**
 * @typedef {{
 *  cartId: string
 *  sessionId?: string
 *  method: string
 *  type: string
 *  scheme: string
 *  status: string
 *  id: string
 *  amount: string
 *  details?: string
 * }} TransformPaymentInfo
 */
