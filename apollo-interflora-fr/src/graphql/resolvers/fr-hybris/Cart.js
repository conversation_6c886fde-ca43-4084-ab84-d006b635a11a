const _ = require('lodash')
const debug = require('debug')('itf:cart:type')

const config = require('../../../config')
const CONST = require('../../../const')

const BaseResolver = require('./Base')
const {
  DateRangeTransformer,
  DeliveryModesTransformer,
  DeliveryTimeRangesResultTransformer,
  PaymentMethodTransformer,
  PhoneTransformer,
  PriceTransformer,
  CartTransformer,
  UserTransformer,
  TownTransformer,
  InvalidDestinationTransformer,
  AddressTransformer,
  DeliveryFeasibilityTransformer,
  ProductTransformer,
} = require('./transformers')
const { GQLCart, GQLDelivery, GQLPaymentMethod } = require('../../models')
const { getPaginatedItems, dates } = require('../../../helpers')
const { prepareForEncoding, neq } = require('../../../helpers/string')
const { getAbElasticityDeliveryFees } = require('../../../helpers/features')
const { GraphQLApolloError, GraphQLDiscountHybrisError } = require('../../errors')

/** @typedef {import('../../types/_typedef')}  */

class CartResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{discountCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async addDiscountCodeToCart(_parent, args, context) {
    const { discountCode } = args

    this._assertHasCart(context)

    try {
      const apiCart = await context.dataSources.hybrisCart.applyVoucher(discountCode)
      await context.loaders.carts.prime(this._getContextCartId(context), apiCart)

      return CartTransformer.toGQL(apiCart, context)
    } catch (e) {
      throw GraphQLDiscountHybrisError.fromError(e)
    }
  }

  /**
   * @param {GQLCart} _cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<null|number>}
   */
  async ageVerification(_cart, _args, _context) {
    return null
  }
  /**
   * @param {GQLCart} _cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<boolean>}
   */
  async ageVerified(_cart, _args, _context) {
    return false
  }

  /**
   * Resolve Cart.canPremium
   * @param {GQLCart} parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async canPremium(parent, _args, context) {
    if (!(parent instanceof GQLCart)) return null
    this._assertHasCart(context)

    return this._canPremium(context)
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  celebrationTexts(cart, _args, context) {
    if (!(cart instanceof GQLCart)) return []

    const product = _.first(_.get(cart, 'productItems', []))
    if (!product) return []

    return context.loaders.celebrationTexts.load({
      code: product.variant.code,
      countryId: product.variant.countryId,
    })
  }

  /**
   * Used only for longInfo, name and shortInfo
   * @param {GQLDelivery} delivery
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getCartDeliveryProperty(delivery, _args, context, info) {
    return delivery[info.fieldName]
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Boolean>}
   */
  async getEmailHasAccountPremium(cart, _args, context) {
    const email = _.get(cart, 'delivery.sender.email')
    return this._isAccountPremium(email, context)
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getDeliveryModes(cart, _args, context) {
    const apiCartId = this._getContextCartId(context)
    return DeliveryModesTransformer.toGQL(await context.loaders.deliveryModes.load(apiCartId ?? cart.id))
  }

  getDeliveryOptionInfo() {
    return null
  }

  /**
   * @param {GQLCart} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLPaymentMethod[]>}
   */
  async paymentMethods(_parent, _args, context) {
    this._assertHasCart(context)

    const apiCartId = this._getContextCartId(context)
    const apiCart = await context.loaders.carts.load(apiCartId)

    // Check if it is a free order
    if (apiCart.totalPrice.value === 0 && apiCart.appliedVouchers?.length > 0) {
      const paymentMethod = new GQLPaymentMethod()
      paymentMethod.availableForCart = true
      paymentMethod.mode = CONST.payment.paymentMode.NONE
      return [paymentMethod]
    }

    return (await context.loaders.paymentMethodsByCarts.load(apiCartId))
      .map((method) => PaymentMethodTransformer.toGQL(method, { apiCart }))
      .filter(({ mode }) => Boolean(mode))
  }

  /**
   * Resolve Cart.delivery.availableDateRanges
   * @param {GQLDelivery} parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(parent, _args, context) {
    if (!(parent instanceof GQLDelivery)) return []

    const apiCartId = this._getContextCartId(context)
    if (!apiCartId) return []

    const [mainApiCartProduct, apiCart] = await Promise.all([
      this._getContextCartMainProduct(context),
      context.loaders.carts.load(apiCartId),
    ])

    if (!mainApiCartProduct) return []

    const deliveryDates = await context.loaders.deliveryDates.load({
      code: _.get(mainApiCartProduct, 'product.code'),
      countryId: parent.address?.country?.id || context.countryId,
      postalCode: _.get(apiCart, 'deliveryAddress.postalCode'),
    })

    return DateRangeTransformer.toGQL(deliveryDates)
  }

  /**
   * @param {GQLDelivery} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  getAvailableNoAddress(_parent, _args, context) {
    return context.dataSources.hybrisCart.checkIfloraAuthorized()
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<void | GQLCart>}
   */
  async getCart(_parent, _args, context) {
    const apiCartId = this._getContextCartId(context)

    let apiCart
    try {
      if (!apiCartId) {
        if (!_.get(context, 'auth.isAnonymous')) {
          // Try to get last user cart if exists
          apiCart = await context.dataSources.hybrisCart.getCart()

          if (apiCart) {
            context.session.apiCart = {
              ..._.pick(apiCart, ['guid', 'code']),
              countryId: apiCart.deliveryAddress?.country?.isocode,
            }
            await Promise.all([
              context.session.save(),
              context.loaders.carts.prime(this._getCartId(apiCart, context), apiCart),
            ])
          }
        }
      } else {
        apiCart = await context.loaders.carts.load(apiCartId)
      }
    } catch (error) {
      /** throws if cart is empty on hybris side when logged in (not created for now) */
      if (error.extensions.code !== 400) {
        throw error
      }
    }

    if (!apiCart) return
    if (apiCart.orderStatus === CONST.hybris.cart.orderStatus.pending) return

    return CartTransformer.toGQL(apiCart, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{date: Date, productCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<CeremonyHourRange[]>}
   */
  async getCartDeliveryRangeFixHour(_parent, args, context) {
    /** @todo what to do with date arg ? */
    /** @todo loaders ? */
    const range = await context.dataSources.hybrisInterflora.getCeremonyHourRange(args.date, args.productCode)
    if (range.messageSpecifique) {
      range.message = range.messageSpecifique
      delete range.messageSpecifique
    }

    return range ? [range] : []
  }

  /**
   * @param {null} _parent
   * @param {GraphQLContextArgs<{date: Date}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getCartDeliveryTimeRanges(_parent, args, context) {
    this._assertHasCart(context)

    const [apiCart, mainApiCartProduct] = await Promise.all([
      context.loaders.carts.load(this._getContextCartId(context)),
      this._getContextCartMainProduct(context),
    ])

    /** @type {APIMomentLivraisonList} */
    const intervals = await context.loaders.deliveryTimeIntervals.load({
      date: args.date,
      occasion: apiCart.codeOccasion,
      postalCode: _.get(apiCart, 'deliveryAddress.postalCode'),
      productCode: mainApiCartProduct.product.code,
    })

    return DeliveryTimeRangesResultTransformer.toGQL(intervals)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryFeasibility>}
   */
  async getDeliveryFeasibility(_parent, _args, context) {
    const feasibilityApi = await context.dataSources.hybrisCart.checkFeasibility()

    return DeliveryFeasibilityTransformer.toGQL(feasibilityApi)
  }

  /**
   * @param {GQLCart} cart
   * @returns {string[]}
   */
  getCartDiscountCodes(cart) {
    return cart.discountCodes || []
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{postalCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLInvalidDestination>}
   */
  async getInvalidDestination(_parent, args, context) {
    this._assertHasCart(context)

    const apiCartId = this._getContextCartId(context)

    const [apiCart, mainApiCartProduct] = await Promise.all([
      context.loaders.carts.load(apiCartId),
      this._getContextCartMainProduct(context),
    ])

    const validateRegion = await context.loaders.validateRegionsByCarts.load({
      cartId: apiCartId,
      postalCode: args.postalCode,
      productId: _.get(mainApiCartProduct, 'product.code'),
    })

    return InvalidDestinationTransformer.toGQL(validateRegion, { cart: apiCart })
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getMaxDeliveryDays(delivery, _args, context) {
    const apiCartId = this._getContextCartId(context)

    if (!apiCartId) return null

    const [apiCart, mainApiCartProduct] = await Promise.all([
      context.loaders.carts.load(apiCartId),
      this._getContextCartMainProduct(context),
    ])

    const countryId = delivery?.address?.country?.id || context.countryId

    // taking today from api to avoid having to deal with time zones
    const { lastDate, toDay } = await context.loaders.deliveryDates.load({
      code: _.get(mainApiCartProduct, 'product.code'),
      countryId: countryId,
      postalCode: _.get(apiCart, 'deliveryAddress.postalCode'),
    })

    if (!lastDate) return null

    return (dates.formatToGQL(lastDate).getTime() - dates.formatToGQL(toDay).getTime()) / 1000 / 3600 / 24
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{search: string, countryId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: { id: string, label: string, postalCode: string }, valid: boolean }[]>}
   */
  async getTownsForCart(_parent, args, context) {
    this._assertHasCart(context)

    const mainApiCartProduct = await this._getContextCartMainProduct(context)

    const localities = await context.loaders.towns.load({
      countryIsoCode: args.countryId,
      productCode: _.get(mainApiCartProduct, 'product.code'),
      search: args.search,
    })

    return localities.map((apiTown) => ({ town: TownTransformer.toGQL(apiTown), valid: apiTown.valide }))
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @returns {Promise<null|Number>}
   */
  async getUseFixHour(delivery, _args) {
    return delivery.useFixHour
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddressListType>}
   */
  async getUserAddressesForCart(_parent, args, context) {
    const { pagination } = args

    this._assertHasCart(context)

    const apiCart = await context.loaders.carts.load(this._getContextCartId(context))

    /** @todo loaders */
    const apiAddresses = await context.dataSources.hybrisAddress.getAll(apiCart.code)
    const paginatedItems = getPaginatedItems(apiAddresses, pagination.page, pagination.limit)

    return {
      addresses: paginatedItems.data.map((address) => AddressTransformer.toGQL(address, context)),
      total: paginatedItems.total,
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {string[]}
   */
  getNextCelebrationDayText(_parent, _args, _context) {
    return dates.getNextHolidays('FR').map((info) => `${info.name}: ${info.date.toISOString().slice(0, 10)}`)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ mode: string, args: Object}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Object>}
   */
  async getPaymentSetup(_parent, _args, context) {
    return await context.dataSources.applePay.getPaymentSetup()
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getSuggestedMessages(_parent, _args, context) {
    const apiCartId = this._getContextCartId(context)

    if (!apiCartId) return []

    const apiCart = await context.loaders.carts.load(apiCartId)
    const cartCategoryId = _.get(apiCart, 'codeOccasion')

    if (!context.categoryId && !cartCategoryId) return []

    const messages = await context.loaders.suggestedMessages.load(context.categoryId || cartCategoryId)

    return messages.map((m) => m.value)
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLPrice>}
   */
  async priceWithoutPremium(delivery, _args, context) {
    const [apiCart, mainApiCartProduct] = await Promise.all([
      context.loaders.carts.load(this._getContextCartId(context)),
      this._getContextCartMainProduct(context),
    ])

    // the delivery is premium and the cart does not contain ITF+ option > we have an ITF+ customer
    // in which case, we won't get the info from the api and we can save one query
    if (!apiCart.hasInterfloraPlusProduct && _.get(apiCart, 'deliveryMode.code') === 'interflora-plus') {
      return delivery.discount || delivery.price
    }

    const deliveryFeesId = await this._getABTestDeliveryFees(context, apiCart)

    const apiPremiumProduct = await context.loaders.checkPremiumProductsForCarts.load({
      cartId: this._getContextCartId(context),
      deliveryFeesId,
      productId: _.get(mainApiCartProduct, 'product.code'),
    })

    // could be set & = 0
    if (apiPremiumProduct.potentialDeliveryCost !== undefined) {
      return PriceTransformer.toGQL({
        currencyIso: 'EUR',
        value: delivery.discount
          ? Math.min(apiPremiumProduct.potentialDeliveryCost, delivery.discount.value / 100)
          : apiPremiumProduct.potentialDeliveryCost,
      })
    }

    return delivery.discount || delivery.price
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{codeVariantProduct: string}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async refuseSuggestedAccessoryToCart(_parent, _args, context) {
    this._assertHasCart(context)
    const {
      session: { apiCart },
    } = context

    return CartTransformer.toGQL(apiCart, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{discountCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async removeDiscountCodeToCart(_parent, args, context) {
    const { discountCode } = args

    this._assertHasCart(context)

    const apiCart = await context.dataSources.hybrisCart.deleteVoucher(discountCode, this._getContextCartId(context))
    await context.loaders.carts.prime(this._getContextCartId(context), apiCart)

    return CartTransformer.toGQL(apiCart, context)
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCartProductItemType[]>}
   */
  async suggestedAccessories(cart, _args, context) {
    this._assertHasCart(context)

    const apiEntry = await this._getContextCartMainProduct(context)
    if (!apiEntry) {
      return []
    }
    const countryId = cart.delivery?.address?.country?.id || context.countryId
    const product = ProductTransformer.toGQL(
      await context.loaders.products.load({
        code: apiEntry.product.code,
        countryId,
      }),
      context,
      {
        variantCode: apiEntry.product.code,
      }
    )

    const apiCart = await context.loaders.carts.load(this._getContextCartId(context))
    const cartAccessoryCodes = new Set()
    for (const entry of apiCart.entries.filter((entry) => entry.product.accessory)) {
      cartAccessoryCodes.add(entry.product.code)
    }

    return (product?.accessories || [])
      .filter((acc) => !cartAccessoryCodes.has(acc.code) && acc.addToCart === CONST.product.accessoryAddTypes.suggest)
      .map((acc) => ({
        discount: acc.discount,
        price: acc.price,
        quantity: 1,
        removable: true,
        variant: acc.defaultVariant,
      }))
  }

  async serviceItems(cart, _args, context) {
    if (!cart || !context) {
      return []
    }
    return cart.serviceItems
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateCart(_parent, args, context) {
    const { categoryId, countryId } = args.context
    const {
      cart: { accessories = [], delivery, externalReference, joinMessage, products = [], services = [] },
    } = args

    accessories.push(...services)

    /** @type {string} */
    let apiCartId = this._getContextCartId(context)

    /** @type {APICart} */
    let apiCart
    if (apiCartId) {
      try {
        apiCart = await context.loaders.carts.load(apiCartId)
      } catch (_error) {
        // If the cart does not exists, ignore the current cart ID, and create a new one.
        apiCartId = null
      }
      this._assertValidProductPrice(apiCart)
      if (apiCart?.orderStatus === CONST.hybris.cart.orderStatus.pending) {
        apiCartId = null
        apiCart = null
      }
    }

    if (context.session.apiCart && delivery?.address?.countryId !== context.session.apiCart.countryId) {
      context.session.apiCart.countryId = delivery?.address?.countryId || countryId
      await context.session.save()
    }

    /** @type {APICartInput} */
    const payload = {}

    if (context.partner === CONST.context.partner.google) {
      payload.fromGoogleShopping = true
    }

    let updateCart = false

    // could be set to null to erase it
    if (categoryId !== undefined) {
      payload.occasion = categoryId
    }

    if (externalReference !== undefined) {
      payload.referenceCommande = externalReference
    }

    if (apiCartId && apiCart) {
      if (neq(apiCart.codeOccasion, payload.occasion)) {
        updateCart = true
      }

      if (neq(apiCart.referenceCommande, payload.referenceCommande)) {
        updateCart = true
      }

      if (joinMessage) {
        const message = joinMessage.message ?? ''
        const signature = joinMessage.signature ?? ''

        if (neq(message, apiCart.message) || neq(signature, apiCart.signature)) {
          updateCart = true
          payload.message = prepareForEncoding(message, 'cp1252')
          payload.signature = prepareForEncoding(signature, 'cp1252')

          debug(
            `updateCart[${apiCart.guid}] - changed - message changed from [${
              apiCart ? `${apiCart.message} | ${apiCart.signature}` : 'none'
            }] to [${message} | ${signature}]`
          )
        }
      }

      let currentProductEntry
      const currentAccessoriesByCode = {}

      const inputAccessoriesByCode = {}
      for (const accessoryInput of accessories) {
        // force ignore ITF+ being added when is not elligible
        if (accessoryInput.code === CONST.product.premiumOption && !(await this._canPremium(context))) continue
        inputAccessoriesByCode[accessoryInput.code] = accessoryInput
      }

      for (const entry of apiCart.entries || []) {
        if (entry.product.accessory) {
          currentAccessoriesByCode[entry.product.code] = entry
        } else {
          currentProductEntry = entry
        }
      }

      const productChanged =
        Boolean(products[0]) ^ Boolean(currentProductEntry) || // xor
        (products[0] && currentProductEntry && products[0].code !== currentProductEntry.product.code)

      const entriesToDelete = productChanged
        ? []
        : Object.entries(currentAccessoriesByCode)
            .filter(([code]) => !inputAccessoriesByCode[code])
            .map(([, entry]) => entry)
            .sort((e1, e2) => e2.entryNumber - e1.entryNumber)

      // to add or update
      const accessoriesToAdd = []
      const accessoriesToUpdate = []

      for (const accessoryInput of accessories) {
        if (typeof accessoryInput.customText === 'string' && accessoryInput.customText !== apiCart.messageRubanDeuil) {
          debug(
            `updateCart[${apiCart.guid}] - changed - customText changed from ${apiCart.messageRubanDeuil} to ${accessoryInput.customText}`
          )
          updateCart = true
          payload.messageRubanDeuil = accessoryInput.customText
        }

        if (productChanged || !currentAccessoriesByCode[accessoryInput.code]) {
          accessoriesToAdd.push(accessoryInput)
        } else if (accessoryInput.quantity !== currentAccessoriesByCode[accessoryInput.code].quantity) {
          accessoriesToUpdate.push(accessoryInput)
        }
      }

      const entriesChanged =
        productChanged || accessoriesToAdd.length > 0 || accessoriesToUpdate.length > 0 || entriesToDelete.length > 0
      let apiCartUpdated
      let updateDeliveryFees = false

      if (entriesChanged) {
        if (productChanged) {
          if (!products[0]) {
            // main product deleted => just delete the corresponding entry & all accessories will be deleted too
            // does not need a call to PUT cart if no other change is made
            await context.dataSources.hybrisCart.deleteCartEntry(currentProductEntry.entryNumber)
            apiCart = await context.dataSources.hybrisCart.getCart(apiCartId)

            await Promise.all([
              context.loaders.carts.prime(apiCartId, apiCart),
              context.loaders.deliveryModes.clear(apiCartId),
              context.loaders.paymentMethodsByCarts.clear(apiCartId),
              context.loaders.checkPremiumProductsForCarts.clear(apiCartId),
            ])
          } else {
            // new product - we can use PUT cart to update it at once
            debug(
              `updateCart[${apiCart.guid}] - changed - main product changed from ${_.get(
                currentProductEntry,
                'product.code'
              )} to ${products[0].code}`
            )
            updateCart = true

            payload.productCode = products[0].code
            payload.qty = products[0].quantity
            payload.accessories = accessories.map((accessoryInput) => ({
              code: accessoryInput.code,
              quantity: accessoryInput.quantity,
            }))
          }
        } else {
          for (const entry of entriesToDelete) {
            debug(`updateCart[${apiCart.guid}] - deleting entry ${entry.product.code}`)
            await context.dataSources.hybrisCart.deleteCartEntry(entry.entryNumber)
          }

          for (const accessoryInput of accessoriesToUpdate) {
            const entry = currentAccessoriesByCode[accessoryInput.code]
            debug(
              `updateCart[${apiCart.guid}] - updating entry ${entry.product.code} (${entry.quantity} > ${accessoryInput.quantity})`
            )
            await context.dataSources.hybrisCart.updateCartEntry(entry.entryNumber, accessoryInput.quantity)
          }

          // PUT cart **adds** quantities
          payload.accessories = accessoriesToAdd.map((accessoryInput) => ({
            code: accessoryInput.code,
            quantity: accessoryInput.quantity,
          }))

          if (accessoriesToAdd.length > 0) {
            debug(
              `updateCart[${apiCart.guid}] - changed - new accessories: ${accessoriesToAdd
                .map((a) => a.code)
                .join(', ')}`
            )
            updateCart = true
          } else if (entriesToDelete.length > 0 || accessoriesToUpdate.length > 0) {
            if (!updateCart) {
              /**
               * cart is changed but does not need to call PUT cart - refresh its state
               * by priming carts loader.
               */
              apiCart = await context.dataSources.hybrisCart.getCart(apiCartId)
              apiCartUpdated = apiCart

              await Promise.all([
                context.loaders.carts.prime(apiCartId, apiCart),
                context.loaders.deliveryModes.clear(apiCartId),
                context.loaders.paymentMethodsByCarts.clear(apiCartId),
                context.loaders.checkPremiumProductsForCarts.clear(apiCartId),
              ])
            } else {
              apiCartUpdated = await context.dataSources.hybrisCart.getCart(apiCartId)
            }

            const shouldClearPaymentMethods =
              entriesToDelete.filter((entry) => entry.product.code !== CONST.product.premiumOption).length > 0

            if (shouldClearPaymentMethods) await context.loaders.paymentMethodsByCarts.clear(apiCartId)
          }
        }
      }
      apiCartUpdated = apiCartUpdated ?? apiCart

      const deliveryFeesId = await this._getABTestDeliveryFees(context, apiCartUpdated)
      updateDeliveryFees = deliveryFeesId && deliveryFeesId !== (apiCartUpdated?.deliveryMode?.code ?? null)

      if (updateCart || updateDeliveryFees) {
        //Fix: if we manually add itf+ or changes cart content from front nothing in the code above transmits the mourning message to the back, so it's deleted
        const mourningRibbon = accessories.find((entry) =>
          entry.code.startsWith(CONST.cart.accessoriesIds.MOURNING_RIBBON_ID)
        )
        if (mourningRibbon && mourningRibbon.customText) {
          payload.messageRubanDeuil = mourningRibbon.customText
        }
        // AB test delivery fees
        {
          const email = apiCart?.user?.uid
          const isAccountPremium = await this._isAccountPremium(email, context)
          if (
            !isAccountPremium &&
            !payload.accessories?.length &&
            !services.some((service) => service.code === CONST.product.premiumOption) &&
            deliveryFeesId
          ) {
            payload.deliveryFees = deliveryFeesId
          }
        }
        apiCart = await context.dataSources.hybrisCart.updateCart(payload)
        context.session.apiCart = {
          ..._.pick(apiCart, ['guid', 'code']),
          countryId: delivery?.address?.countryId || apiCart?.deliveryAddress?.country?.isocode || countryId,
        }

        const shouldClearPaymentMethods =
          accessoriesToAdd.filter((entry) => entry.code !== CONST.product.premiumOption).length > 0

        await Promise.all([
          context.session.save(),
          context.loaders.carts.prime(apiCartId, apiCart),
          context.loaders.deliveryModes.clear(apiCartId),
          shouldClearPaymentMethods ? context.loaders.paymentMethodsByCarts.clear(apiCartId) : Promise.resolve(),
          context.loaders.checkPremiumProductsForCarts.clear(apiCartId),
        ])
      }
    } else {
      payload.accessories = []
      for (const accessory of accessories || []) {
        // GGT-834 => remove the prenium delivery service when we create the cart
        // added by Hybris if the conditions are satisfied
        if (accessory.code !== CONST.product.premiumOption) {
          payload.accessories.push({
            code: accessory.code,
            quantity: accessory.quantity,
          })
        }

        if (typeof accessory.customText === 'string') {
          payload.messageRubanDeuil = accessory.customText
        }
      }

      if (products && products[0]) {
        payload.productCode = products[0].code
        payload.qty = products[0].quantity

        if (products[0].customText) {
          payload.messageRubanDeuil = products[0].customText
        }
      }
      // no cart yet - create one
      apiCart = await context.dataSources.hybrisCart.createCart(payload)
      this._assertValidProductPrice(apiCart)
      context.session.apiCart = {
        ..._.pick(apiCart, ['guid', 'code']),
        countryId: delivery?.address?.countryId || apiCart.deliveryAddress?.country?.isocode || countryId,
      }
      context.log.info(`cart ${apiCart.guid} created`)

      await Promise.all([context.session.save(), context.loaders.carts.prime(apiCart.guid, apiCart)])
    }

    if (delivery) {
      apiCart = await this._updateDelivery(delivery, context)
    }

    if (context.session.apiCart.countryId !== apiCart.deliveryAddress?.country?.isocode) {
      context.session.apiCart.countryId = apiCart.deliveryAddress?.country?.isocode || countryId
      await context.session.save()
    }

    return CartTransformer.toGQL(apiCart, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{cartDelivery: GQLInputCartDelivery}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateDeliveryCart(_parent, args, context) {
    const apiCart = await this._updateDelivery(args.cartDelivery, context)
    return CartTransformer.toGQL(apiCart, context)
  }

  /**
   *
   * @param {GQLCart} _cart
   * @param {GraphQLContext<{token: string}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<boolean>}
   */
  async verifyAge(_cart, _args, _context) {
    return false
  }

  /**
   * @param {APICart} apiCart
   * @returns {void}
   * @throws
   * @protected
   */
  _assertValidProductPrice(apiCart) {
    let validPrice = true
    for (const entry of apiCart?.entries ?? []) {
      if (entry.basePrice?.value === 0) {
        if (entry.product?.accessory) {
          const accessoryPrice = entry.product.price?.value || 0
          validPrice = accessoryPrice === 0
        } else {
          validPrice = false
        }
      }
    }
    if (!validPrice) throw new GraphQLApolloError('The cart contains a product with an invalid base price.', 409)
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _canPremium(context) {
    if (!context.session?.apiCart?.code) return false
    const [apiCart, mainApiCartProduct] = await Promise.all([
      context.loaders.carts.load(this._getContextCartId(context)),
      this._getContextCartMainProduct(context),
    ])
    const deliveryFeesId = await this._getABTestDeliveryFees(context, apiCart)

    const { customerEligibleToProlongation, eligibleToAddPremiumProduct } =
      await context.loaders.checkPremiumProductsForCarts.load({
        cartId: this._getContextCartId(context),
        deliveryFeesId,
        productId: _.get(mainApiCartProduct, 'product.code'),
      })

    return customerEligibleToProlongation || eligibleToAddPremiumProduct
  }

  /**
   * @param {GraphQLContext} context
   * @param {APICart} apiCart
   * @returns {Promise<String | null>}
   */
  async _getABTestDeliveryFees(context, apiCart) {
    return getAbElasticityDeliveryFees(apiCart, context, {
      isFuneral: apiCart.deuil,
      unleashFeatureKey: CONST.unleash.features.AB_SHIPPING_FEES,
    })
  }

  /**
   * @param {string} email
   * @param {GraphQLContext} context
   * @returns {Promise<Boolean>}
   */
  async _isAccountPremium(email, context) {
    const isAnonymous = _.get(context, 'auth.isAnonymous', true)

    if (isAnonymous) {
      if (!email) return false
      return context.loaders.isPremiumUser.load(email)
    }

    /** @todo optimize this call ? */
    const user = await context.dataSources.hybrisUser.getCurrent()

    return _.get(user, 'premium', false)
  }

  /**
   * @param {GQLInputCartDelivery} delivery
   * @param {GraphQLContext} context
   * @returns {Promise<APICart>}
   */
  async _updateDelivery(delivery, context) {
    this._assertHasCart(context)

    let apiCart = await context.loaders.carts.load(this._getContextCartId(context))
    // Not allowing delivery change if order is already paid but waiting to be validated (cko paypal)
    if (apiCart.orderStatus === CONST.hybris.cart.orderStatus.pending) return
    /** @type {APICreateCart} */
    const data = {
      dateNotificationIfloraSms: undefined,
      messageRubanDeuil: apiCart.messageRubanDeuil,
      referenceCommande: apiCart.referenceCommande,
    }

    switch (delivery.mode) {
      case CONST.cart.deliveryMode.ceremony:
      case CONST.cart.deliveryMode.funeralPlace:
      case CONST.cart.deliveryMode.grave:
      case CONST.cart.deliveryMode.tomb:
      case CONST.cart.deliveryMode.tombBundle:
        if (!data.deliveryAddress) {
          data.deliveryAddress = {}
        }
        data.deliveryAddress = await this._updateMourningDelivery(delivery, apiCart, context)
        break

      case CONST.cart.deliveryMode.noAddress:
        if (apiCart.deliveryAddress) {
          await context.dataSources.hybrisCart.deleteDeliveryAddress()
        }

        if (delivery.dateSmsNotification) {
          data.dateNotificationIfloraSms = dates.formatToAPI(delivery.dateSmsNotification)
        }

        data.orderType = CONST.cart.orderType.NO_ADDRESS
        await context.dataSources.hybrisCart.removeTombDelivery(apiCart)
        break

      case CONST.cart.deliveryMode.self:
        data.orderType = CONST.cart.orderType.SELF
        await context.dataSources.hybrisCart.removeTombDelivery(apiCart)
        break

      case CONST.cart.deliveryMode.standard:
        data.orderType = CONST.cart.orderType.STANDARD
        await context.dataSources.hybrisCart.removeTombDelivery(apiCart)
        if (apiCart?.deuil) _.set(data, 'deliveryAddress.typeCeremonie', CONST.cart.deliveryMode.domicile)
        break
    }

    // Shipping address
    if (delivery.address) {
      const typeCeremony = data.deliveryAddress?.typeCeremonie
      data.deliveryAddress = this._registerAddressPayload(delivery.address, true, context) ?? {}
      data.deliveryAddress.typeCeremonie = typeCeremony
    }

    if (delivery.contact) {
      data.deliveryContact = {
        firstName: delivery.contact.firstName,
        lastName: delivery.contact.lastName,
        phone: delivery.contact.phone ? PhoneTransformer.toAPI(delivery.contact.phone, context) : null,
        title: UserTransformer.toAPICivility(delivery.contact.civility),
        titleCode: UserTransformer.toAPICivility(delivery.contact.civility),
      }
    }

    if (delivery.date && delivery.mode !== CONST.cart.deliveryMode.noAddress) {
      data.deliveryDate = dates.formatToAPI(delivery.date)
    } else {
      data.deliveryDate = null
    }

    if (delivery.time) {
      data.deliveryHour = delivery.time.hour.toString().padStart(2, '0')
      data.deliveryMinute = delivery.time.minute ? delivery.time.minute.toString().padStart(2, '0') : '00'
    } else {
      data.deliveryHour = null
      data.deliveryMinute = null
    }

    data.moment = delivery.mode === CONST.cart.deliveryMode.noAddress ? null : delivery.rangeHourId

    // Billing address
    if (delivery.sender) {
      data.billingAddress = this._registerAddressPayload(delivery.sender, false, context)
    }

    // Refresh the cart if there are changes and save it
    if (Object.keys(data).length > 0) {
      apiCart = await context.dataSources.hybrisCart.updateCart(data)
      await context.loaders.carts.prime(this._getContextCartId(context), apiCart)

      if (
        _.get(apiCart, 'deliveryAddress.typeCeremonie') !== _.get(data, 'deliveryAddress.typeCeremonie') ||
        _.get(apiCart, 'orderType') !== _.get(data, 'orderType')
      ) {
        // Payment methods depends on cart delivery mode
        await context.loaders.paymentMethodsByCarts.clear(this._getContextCartId(context))
      }
    }

    return apiCart
  }

  /**
   * @param {GQLInputCartDelivery} delivery
   * @param {APICart} apiCart
   * @param {GraphQLContext} context
   * @returns {Promise<GQLInputCartDelivery>}
   */
  async _updateMourningDelivery(delivery, apiCart, context) {
    const deliveryAddress = {
      typeCeremonie: delivery.mode,
    }
    switch (delivery.mode) {
      case CONST.cart.deliveryMode.grave:
      case CONST.cart.deliveryMode.tomb:
        // Don't add tomb accessory if there is one in cart already.
        if (!apiCart.entries.some((x) => x.product?.code === config.products.tombDelivery.key)) {
          const isFromGoogleShopping = context.partner === CONST.context.partner.google
          await context.dataSources.hybrisCart.setTombDelivery(isFromGoogleShopping)
        }
        break
      case CONST.cart.deliveryMode.ceremony:
      case CONST.cart.deliveryMode.funeralPlace:
      case CONST.cart.deliveryMode.tombBundle:
        await context.dataSources.hybrisCart.removeTombDelivery(apiCart)
        break
    }

    return deliveryAddress
  }

  /**
   * Retrieves the payment methods associated with the current cart.
   * Disabled in this context - Used only by Sweden
   */
  async payments() {
    return []
  }
}

module.exports = CartResolver
