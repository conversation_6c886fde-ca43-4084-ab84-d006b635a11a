const BaseResolver = require('./Base')
const {
  GQLAvailabilityDateRanges,
  GQLProduct,
  GQLProductVariant,
  GQLSolrProduct,
  GQLSolrProductVariant,
} = require('../../models')
const {
  DateRangeTransformer,
  LinkedProductsTransformer,
  ProductTransformer,
  ProductListTransformer,
  ProductVariantTransformer,
} = require('./transformers')
const { GraphQLApolloError } = require('../../errors')
const config = require('../../../config')
const CONST = require('../../../const')
const { getFRContext } = require('../fr/shared')

/** @typedef {import('graphql').GraphQLResolveInfo} GraphQLResolveInfo */
/** @typedef {import("../../types/_typedef")}  */

class ProductResolver extends BaseResolver {
  /**
   * @param {GQLProduct} product
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct[]>}
   */
  async getLinkedProducts(product, _args, context) {
    if (!context.appConfig.showCrossSells) return []

    if (Array.isArray(product.linked)) {
      return product.linked
    }

    const categoryId = product.mainCategoryId || context.categoryId || CONST.product.categories.all

    if (!categoryId) {
      return []
    }

    const params = {
      fq: [
        ...context.dataSources.solr.isVisibleFilters,
        `category_string_mv:"${categoryId}"`,
        `-code_string:"${product.code.split('-')[0]}"`,
      ],
      rows: config.products.linked.desired,
      sort: [`${categoryId.replace(/_/g, '')}_sort_int asc`],
    }
    // catalog
    {
      const catalog = context.dataSources.solr.getCatalog()
      params.fq.push(`catalogId:"${catalog}" AND catalogVersion:Online`)
    }

    return LinkedProductsTransformer.toGQL(await context.loaders.solrSearches.load(params))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ productCode: string }>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLMessageCard[]>}
   */
  async getMessageCards(_parent, _args, _context) {
    return []
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<void>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<GQLProduct>}
   */
  async getPremiumAccessory(_parent, args, context, info) {
    return this.getProduct(null, Object.assign({ code: CONST.product.premiumOption }, args), context, info)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, slugUrl: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct>}
   */
  async getProduct(_parent, args, context) {
    return this._loadProduct(args, context)
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{city: string, code: string, countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDateRange[]>}
   */
  async getProductDeliveryDateRanges(parent, args, context) {
    return (await this.getProductDeliveryDates(parent, args, context)).ranges
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, postalCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAvailabilityDateRanges>}
   */
  async getProductDeliveryDates(_parent, args, context) {
    const deliveryDates = await context.loaders.deliveryDates.load({
      code: args.code,
      countryId: args.countryId || context.countryId,
      postalCode: args.postalCode,
    })

    return new GQLAvailabilityDateRanges(DateRangeTransformer.toGQL(deliveryDates))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{city: string, codes: string[], countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{
   *   code: string,
   *   availability: GQLAvailabilityDateRanges
   * }[]>}
   */
  async getProductsDeliveryDates(_parent, args, context) {
    const { codes } = args

    const ranges = await Promise.all(
      codes.map(async (code) => {
        const deliveryDates = await context.loaders.deliveryDates.load({
          code,
          countryId: args.countryId || context.countryId,
          postalCode: args.postalCode,
        })

        return new GQLAvailabilityDateRanges(DateRangeTransformer.toGQL(deliveryDates))
      })
    )

    return codes.map((code, index) => ({
      availability: ranges[index],
      code,
    }))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDateRange[]>}
   */
  async getDeliveryAvailabilities(_parent, _args, context) {
    const deliveryDates = await context.loaders.deliveryDates.load({ countryId: context.countryId })

    return DateRangeTransformer.toGQL(deliveryDates)
  }

  /**
   * Extend a partial product with the api
   *
   * @param {GQLProduct|GQLSolrProduct} product
   * @param {GraphQLContextArgs<void>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getProductProperty(product, args, context, info) {
    if (product?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      return frContext.resolvers.product.getProductProperty(product, args, frContext, info)
    }

    if (product.code === CONST.product.premiumOption) {
      if (info.fieldName === 'classification') {
        return CONST.product.productClassifications.ITFPLUS
      }
      if (info.fieldName === 'type') {
        return CONST.product.types.service
      }
    }

    if (info.fieldName === 'isGenericAccessory') {
      return true
    }

    {
      // dirty workaround to trick prettier into disabling safe negation (@see https://eslint.org/docs/rules/no-unsafe-negation)
      const isGQLProduct = product instanceof GQLProduct
      const isGQLSolrProduct = product instanceof GQLSolrProduct

      if (product[info.fieldName] !== undefined || (isGQLProduct && !isGQLSolrProduct)) {
        return product[info.fieldName]
      }
    }

    const fetchedProduct = await this._loadProduct(product, context)
    return fetchedProduct[info.fieldName]
  }

  /**
   * @param {GQLProduct|GQLSolrProduct|{ code: string }} parent
   * @param {GraphQLContextArgs<{
   *  filterFormats: GQLImageFormat[],
   *  filterTypes: GQLImageType[]
   * }>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {GQLImage[]}
   */
  async getProductImages(parent, args, context, info) {
    if (parent?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      return frContext.resolvers.product.getProductImages(parent, args, frContext, info)
    }

    const { filterFormats, filterTypes } = args

    if (!parent) return []

    const _product =
      typeof parent.getImages === 'function'
        ? parent
        : await this.getProduct(
            null,
            {
              context: {
                countryId: parent.countryId || context.countryId,
                language: context.language,
                siteId: context.siteId,
                zoneGeoId: context.zoneGeoId,
              },
              ...parent,
            },
            context,
            info
          )
    return _product.getImages(filterFormats, filterTypes).map((image) => {
      if (!image.url.startsWith('http')) {
        image.url = config.apps[context.siteId].mediaBaseUrl + (image.url.startsWith('/') ? '' : '/') + image.url
      }
      return image
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  filters: GQLInputProductFilter[],
   *  pagination: GQLInputPagination,
   *  gelluleSlug: string,
   *  search: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProductList>}
   */
  async getProductList(_parent, args, context) {
    const { filters, pagination, search } = args
    const categorySlug = args.gelluleSlug || config.apps[context.siteId].product.category.allSlug

    // queried zone if any to compute default variants
    let zone

    const params = {
      facet: true,
      'facet.field': [],
      'facet.query': [],
      fq: context.dataSources.solr.isVisibleFilters,
      sort: ['score desc'],
      stats: true,
      'stats.field': 'minPriceAfterDiscount_double',
    }

    // catalog
    {
      const catalog = context.dataSources.solr.getCatalog()
      params.fq.push(`catalogId:"${catalog}" AND catalogVersion:Online`)
    }

    // facets
    {
      for (const definition of Object.values(CONST.product.filters)) {
        params['facet.field'].push(`{!ex=${definition.tag} key=all_${definition.field}}${definition.field}`)
        params['facet.field'].push(definition.field)
      }

      // query facets
      for (const definition of Object.values(CONST.product.filterQueries)) {
        for (const { query } of definition.entries) {
          params['facet.query'].push(query)
        }
      }
    }

    // category
    const category = await context.dataSources.hybrisCatalog.getCategory(categorySlug)
    zone = category.geographicalArea

    let pageSize = (params.rows = Number.parseInt(category.maxOfProduct || config.categories.pageSize))
    if (pagination.limit !== 0) {
      pageSize = params.rows = pagination.limit
    }
    if (pagination.offset !== undefined) {
      params.start = pagination.offset
    } else {
      params.start = Math.max(pagination.page, 0) * params.rows
    }

    params.fq.push(`category_string_mv:"${category.code}"`)
    params.sort = [`${category.code.replace(/_+/g, '')}_sort_int asc`]

    // filters
    for (const filter of filters || []) {
      // is actually a sort
      if (filter.filterId === CONST.product.filterAsSort) {
        const sort = []

        for (const value of filter.values) {
          if (value === CONST.product.sorts.bestSales) {
            if (!category.code) {
              throw new GraphQLApolloError(`${value} sort cannot be used without a category`, 400)
            }

            sort.push(`${category.code.replace(/_+/g, '')}_sort_int asc`)
          } else if (value === CONST.product.sorts.priceAsc) {
            sort.push('minPriceAfterDiscount_double asc')
          } else if (value === CONST.product.sorts.priceDesc) {
            sort.push('minPriceAfterDiscount_double desc')
          }
        }

        if (sort[0]) {
          params.sort = sort
        }
      } else if (CONST.product.filterQueries[filter.filterId]) {
        // query facet
        for (const { query, zone: queriedZone } of filter.values
          .map((label) =>
            Object.values(CONST.product.filterQueries[filter.filterId].entries || []).find(
              (entry) => entry.label === label
            )
          )
          .filter(Boolean)) {
          params.fq.push(query)

          if (!zone) {
            zone = queriedZone
          }
        }
      } else if (filter.filterId === config.filters.date.id) {
        // we only consider one value
        if (new Date().toISOString().startsWith(filter.values[0])) {
          // ask for today - fleuriste only
          params.fq.push('deliveryMode_string: fleuriste')
        }
      } else if (filter.filterId === CONST.product.filterAsRange) {
        // operation on price range filter
        const index = params.fq.indexOf('minPriceAfterDiscount_double: [1 TO *]')
        params.fq[index] = `minPriceAfterDiscount_double: [${filter.values[0] / 100} TO ${filter.values[1] / 100}]`
      } else {
        // regular "real" filter
        const definition = CONST.product.filters[filter.filterId]

        if (definition) {
          params.fq.push(
            `{!tag=${definition.tag}}${definition.field}: (${filter.values
              .map((f) => `"${encodeURIComponent(f)}"`)
              .join(' OR ')})`
          )
        }
      }
    }

    if (search) {
      params.q = encodeURIComponent(search)
      params.qf = config.solr.catalogs.qf
    }

    const response = await context.dataSources.solr.search(params)

    return ProductListTransformer.toGQL(response, context, {
      category,
      categorySlug,
      pageSize,
      zone,
    })
  }

  /**
   * @param {GQLProductVariant|GQLSolrProductVariant|{code: string}} variant
   * @param {{filterFormats: string[], filterTypes: string[]}} args
   * @param {GraphQLContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductVariantImages(variant, args, context, info) {
    if (variant?.parent?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      return frContext.resolvers.product.getProductVariantImages(variant, args, frContext, info)
    }

    const { filterFormats, filterTypes } = args

    let images
    if (
      variant.images &&
      (variant instanceof GQLProductVariant || variant instanceof GQLSolrProductVariant || variant.images.length > 0)
    ) {
      images = variant.images
    } else {
      // images are set only on the main product, not on the variants
      const code = variant.code.replace(/-\d+-[A-Z-]+$/, '')
      const product = await this._loadProduct(
        variant?.parent?.code === code ? variant.parent : { code: code, countryId: variant.countryId },
        context
      )

      if (product.hasProductsAsVariant) {
        return product.getImages(filterFormats, filterTypes)
      } else {
        const _variant = product.variants.find((v) => v.code === variant.code)
        images = _variant?.images || []
      }
    }

    return images
      .filter(
        (image) =>
          (filterFormats.length === 0 || filterFormats.includes(image.format)) &&
          (filterTypes.length === 0 || filterTypes.includes(image.type))
      )
      .map((image) => {
        if (!image.url.startsWith('http')) {
          image.url = config.apps[context.siteId].mediaBaseUrl + (image.url.startsWith('/') ? '' : '/') + image.url
        }
        return image
      })
  }

  /**
   * Extends a partial variant from the API
   *
   * @param {GQLProductVariant|GQLSolrProductVariant} variant
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getProductVariantProperty(variant, args, context, info) {
    if (variant?.parent?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      return frContext.resolvers.product.getProductVariantProperty(variant, args, frContext, info)
    }

    if (
      info.fieldName === 'parent' &&
      (variant.parent instanceof GQLProduct || variant.parent instanceof GQLSolrProduct)
    ) {
      return variant[info.fieldName]
    } else if (
      info.fieldName !== 'parent' &&
      (variant[info.fieldName] !== undefined || variant instanceof GQLProductVariant)
    ) {
      return variant[info.fieldName]
    }

    const product = await this._loadProduct(
      variant?.parent?.code ? variant.parent : { code: variant.code, countryId: variant.countryId },
      context
    )

    let parentVariant = product.variants.find((v) => v.code === variant.code)

    if (!parentVariant) {
      // the variant may actually be a parent product
      if (variant?.parent?.code === variant.code) {
        const gqlVariant = await this._loadProductAsVariant(
          {
            code: variant.code,
            countryId: variant.countryId,
          },
          context
        )
        return gqlVariant && gqlVariant[info.fieldName]
      }

      // we did not find the variant in its parent variants.. strange but seems to be possible
      // @see https://team-1597926993778.atlassian.net/browse/ITFFR-2313
      const variantAsProduct = await this._loadProduct(
        {
          code: variant.code,
          countryId: variant.countryId,
        },
        context
      )
      parentVariant = await variantAsProduct.variants.find((v) => v.code === variant.code)
    }

    return parentVariant && parentVariant[info.fieldName]
  }

  /**
   * @param {GraphQLContextArgs<{code: string, slugUrl: string, countryId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct>}
   * @private
   */
  async _loadProduct(args, context) {
    let code = args.code
    if (!code) {
      code = await context.loaders.productCodesBySlugUrls.load({ countryId: args.countryId, slugUrl: args.slugUrl })
    }

    const apiProduct = await context.loaders.products.load({ code, countryId: args.countryId })

    return ProductTransformer.toGQL(apiProduct, context)
  }

  /**
   * @param {GraphQLContextArgs<{code: string, countryId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProductVariant>}
   * @private
   */
  async _loadProductAsVariant(args, context) {
    const apiProduct = await context.loaders.products.load({ code: args.code, countryId: args.countryId })

    const variant = ProductVariantTransformer.apiProductToGQL(apiProduct, context)
    variant.parent = ProductTransformer.toGQL(apiProduct, context)

    return variant
  }
}

module.exports = ProductResolver
