const { AddressTransformer } = require('./transformers')
const BaseResolver = require('./Base')

const HybrisTownTransformer = require('../fr-hybris/transformers/Town')
const { addressValidator } = require('../../../helpers/validator/address')

/** @typedef {import("../_typedef")} */

class AddressResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{twitterId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async checkTwitterId(_parent, args, context) {
    try {
      /**
       * **NB**: do not return the api call directly, because errors won't
       * be catched for some reason..
       */
      const response = await context.dataSources.hybrisInterflora.checkTwitterId(args.twitterId)

      return response
    } catch (error) {
      return false
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddress>}
   */
  async addAddress(_parent, args, context) {
    const { address } = args

    const payload = this._registerAddressPayload(address, undefined, context)
    const apiAddress = await context.dataSources.hybrisAddress.addAddress(payload)

    return AddressTransformer.toGQL(apiAddress, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddress>}
   */
  async updateAddress(_parent, args, context) {
    const { address } = args

    const payload = this._registerAddressPayload(address, undefined, context)
    const apiAddress = await context.dataSources.hybrisAddress.updateAddress(payload)

    return AddressTransformer.toGQL(apiAddress, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{addressId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAddress>}
   */
  async removeAddress(_parent, args, context) {
    const apiAddress = await context.dataSources.hybrisAddress.removeAddress(args.addressId)

    return AddressTransformer.toGQL(apiAddress, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{address: GQLInputAddress}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLValidationResult>}
   */
  async validateAddress(_parent, args, context) {
    return addressValidator(args.address, context.language, {
      townSearch: async (search, countryId) =>
        (
          await context.loaders.towns.load({
            countryIsoCode: countryId,
            search,
          })
        )
          .map(HybrisTownTransformer.toGQL)
          .map((town) => {
            town.label = town.label.replace(`(${town.postalCode})`, '').trim()
            return town
          }),
    })
  }
}

module.exports = AddressResolver
