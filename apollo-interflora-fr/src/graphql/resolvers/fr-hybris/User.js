const { createHash } = require('node:crypto')

const BaseResolver = require('./Base')
const { CountryTransformer, PhoneTransformer, UserTransformer } = require('./transformers')
const { CTOrderTransformer } = require('../../transformers/commerceTools')

const { getPaginatedItems } = require('../../../helpers')
const { GQLUser } = require('../../models')
const { getDistributionChannels } = require('../../../helpers/commercetools')

/** @typedef {import('graphql').GraphQLResolveInfo} GraphQLResolveInfo */
/** @typedef {import("../../types/_typedef")}  */

class UserResolver extends BaseResolver {
  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCountry>}
   */
  async getUserCountry(user, _args, context) {
    if (!user.countryId) {
      return
    }

    const apiCountries = await context.dataSources.hybrisGeo.getCountries()
    const userCountry = apiCountries.find((apiCountry) => apiCountry.isocode === user.countryId)

    if (!userCountry) {
      return
    }

    return CountryTransformer.toGQL(userCountry, context)
  }

  /**
   *
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async getUserLastOrder(user, _args, context) {
    // orders are sorted by date desc
    const { orders } = await this.getUserOrders(user, { pagination: { limit: 1, page: 0 } }, context)

    return orders[0]
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderList>}
   */
  async getUserOrders(user, args, context) {
    return this._getUserOrders(user, args, context)
  }

  /**
   * @param {CTOrder} ctOrder
   * @param {FRContext} context
   * @return {Promise<GQLOrder>}
   */
  async transformCTOrder(ctOrder, context) {
    return CTOrderTransformer.toGQL(ctOrder, context, {
      distributionChannels: await getDistributionChannels(context),
      skipProductAPI: true,
    })
  }

  /**
   *
   * @param {
   *   { id: string, email: string, order: GQLOrder } | GQLUser
   * } user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getUserProperty(user, _args, context, info) {
    if (!user.id) {
      return
    }

    if (info.fieldName === 'id') {
      return createHash('sha256')
        .update(user.id)
        .digest('hex')
        .slice(0, 30)
        .replace(/^(.{8})(.{4})(.{3})(.{3})(.{12})/, '$1-$2-4$3-a$4-$5')
    }

    if (user[info.fieldName] !== undefined || user instanceof GQLUser) {
      return user[info.fieldName]
    }

    const apiUser = await context.dataSources.hybrisUser.getUser(user.id)
    const _user = UserTransformer.toGQL(apiUser, context)

    return _user && _user[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{oldPassword: string, newPassword: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  async updatePassword(_parent, args, context) {
    const { oldPassword, newPassword } = args

    await context.dataSources.hybrisUser.updatePassword(oldPassword, newPassword)

    return this._getCurrentUser(context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{user: GQLInputUpdateUser}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser>}
   */
  async updateUser(_parent, args, context) {
    const { user } = args

    const apiUser = {
      additionalInfo: user.address2,
      codeLocalite: user.townId,
      companyName: user.company,
      displayUid: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      line1: user.address,
      line2: '',
      phone: user.phone && PhoneTransformer.toAPI(user.phone, context),
      postalCode: user.postalCode,
      titleCode: UserTransformer.toAPICivility(user.civility),
      town: user.townLabel,
    }

    if (user.countryId) {
      apiUser.country = {
        isocode: user.countryId,
      }
    }

    if (user.townId) {
      apiUser.region = {
        isocode: user.townId,
      }
    }

    await context.dataSources.hybrisUser.update(apiUser)

    return this._getCurrentUser(context)
  }

  /**
   * Resolve `deliveryAddresses` and `senderAddresses` from User type.
   * @param {GQLUser} parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination}>} args
   * @param {GraphQLContext} _context
   * @param {GraphQLResolveInfo} info
   * @returns {{addresses: GQLAddress[], total: number}}
   */
  getUserAddressList(parent, args, _context, info) {
    const { pagination } = args
    const fieldName = info.fieldName

    let page = 0
    let limit = 0

    if (pagination) {
      page = pagination.page
      limit = pagination.limit
    } else {
      limit = fieldName === 'deliveryAddresses' ? parent.deliveryAddresses.length : parent.senderAddresses.length
    }

    const addresses = fieldName === 'deliveryAddresses' ? parent.deliveryAddresses : parent.senderAddresses
    const paginatedItems = getPaginatedItems(addresses, page, limit)

    return {
      addresses: paginatedItems.data,
      total: paginatedItems.total,
    }
  }
}

module.exports = UserResolver
