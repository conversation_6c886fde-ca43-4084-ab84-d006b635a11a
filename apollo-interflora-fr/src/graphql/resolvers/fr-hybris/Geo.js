const BaseResolver = require('./Base')
const { CountryTransformer, DepartmentTransformer, TownTransformer } = require('./transformers')
const { toFR } = require('../fr/shared')
const CONST = require('../../../const')

/** @typedef {import('../../types/_typedef')}  */

class GeoResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{showAll: boolean, withCurrent: boolean}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCountry[]>}
   */
  async getCountries(_parent, args, context) {
    const apiCountries = await context.loaders.countries.load({
      allCountries: args.showAll,
      includeFR: args.withCurrent,
    })

    return apiCountries.map((apiCountry) => CountryTransformer.toGQL(apiCountry, context))
  }

  /**
   * @param {GQLDepartment} department
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<any>}
   */
  async getDepartmentProperty(department, _args, _context, info) {
    return department[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{includeAddress: (boolean|undefined)} >} args
   * @param {GraphQLContext} context
   * @returns {Promise<{id: string, label: string}[]>}
   */
  async getDepartments(_parent, args, context) {
    const departmentList = await context.loaders.departments.load({})
    const { includeAddress } = args
    if (includeAddress) {
      const { frContext, frResolverArgs } = toFR(arguments)
      const domtomDepatmentCodes =
        departmentList?.listDepartmentsDomtom?.map((depatment) => depatment.codeDepartment) ?? []
      const results = await frContext._resolvers.geo.getDepartments(...frResolverArgs)
      return results.map((department) => ({
        ...department,
        type: domtomDepatmentCodes.includes(department.code)
          ? CONST.geo.departmentTypes.DOM_TOM
          : CONST.geo.departmentTypes.METROPOLITAN,
      }))
    }

    return departmentList.listDepartmentsFr
      .map((department) => DepartmentTransformer.toGQL(department, context))
      .concat(
        departmentList.listDepartmentsDomtom.map((department) =>
          DepartmentTransformer.toGQL(department, context, { isDomtom: true })
        )
      )
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string, includeAddress: (boolean|undefined)}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{id: string, label: string}[]>}
   */
  async getDepartmentBySlug(_parent, args, context) {
    const { slugUrl, includeAddress } = args
    const department = await context.loaders.department.load(slugUrl)

    if (includeAddress) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext._resolvers.geo.getDepartmentBySlug(...frResolverArgs)
    }

    return DepartmentTransformer.toGQL(department, context, {
      isDomtom: department.isDomtom,
      type: 'floristDepartment',
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{countryId: string, search: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTown[]>}
   */
  async getTowns(_parent, args, context) {
    const localities = await context.loaders.towns.load({
      countryIsoCode: args.countryId || context.countryId,
      search: args.search,
    })

    return localities.map(TownTransformer.toGQL).map((town) => {
      town.label = town.label.replace(`(${town.postalCode})`, '').trim()
      return town
    })
  }
}

module.exports = GeoResolver
