const { randomUUID } = require('crypto')
const _ = require('lodash')

const CONST = require('../../../const')
const { UserTransformer, AddressTransformer } = require('./transformers')
const { GraphQLApolloError } = require('../../errors')
const { Session } = require('../../../plugins/session')

const { getUserOrders, toFR } = require('../fr/shared')

/** @typedef {import("../_typedef")} */

/**
 * @class BaseResolver
 */
class BaseResolver {
  /**
   * Returns the current authenticated user or returns undefined otherwise.
   *
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser | undefined>}
   * @protected
   */
  async _getCurrentUser(context) {
    if (context.auth && context.auth.isAnonymous) {
      return
    }

    const [apiUser, apiUserAddresses] = await Promise.all([
      context.dataSources.hybrisUser.getCurrent(),
      context.dataSources.hybrisAddress.getAll(),
    ])

    return UserTransformer.toGQL(apiUser, context, { addresses: apiUserAddresses })
  }

  /**
   * Mutates context to match login response and cart init or update
   *
   * @param {GraphQLContext} context
   * @param {APIUserToken} response
   * @param {string} siteId
   * @param {string} [sessionId]
   * @returns {Promise<void>}
   * @protected
   */
  async _setAuthContext(context, response, siteId, sessionId) {
    const currentSessionId = _.get(context, 'auth.sessionId', sessionId)
    const login = response.login || CONST.user.anonymousLogin

    context.auth = {
      isAnonymous: login === CONST.user.anonymousLogin,
      isPro: response.b2bCustomer || false,
      isRefreshToken: false,
      login: login,
      oauthToken: response.access_token,
      scope: response.scope,
      sessionId: currentSessionId || randomUUID(),
      siteId,
    }

    context.session = await Session.get(context.server.redis, context.auth.sessionId, {
      expiresAt: Math.ceil(Date.now() / 1000) + response.expires_in,
    })
  }

  /**
   * @param {GraphQLContext} context
   * @returns {void}
   * @throws
   * @protected
   */
  _assertHasCart(context) {
    if (!_.get(context, 'session.apiCart.code')) {
      throw new GraphQLApolloError('Cart not found', 400)
    }
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<APIOrderEntry|null>}
   */
  async _getContextCartMainProduct(context) {
    const apiCartid = this._getContextCartId(context)

    if (!apiCartid) return null

    const apiCart = await context.loaders.carts.load(apiCartid)

    // can happen if the cart is empty
    if (!_.get(apiCart, 'entries')) return null

    const entry = apiCart.entries.find((entry) => !entry.product.accessory)

    return entry || null
  }

  /**
   * @param {GraphQLContext} context
   * @returns {string|null}
   */
  _getContextCartId(context) {
    const {
      auth,
      session: { apiCart },
    } = context

    return apiCart ? (auth && auth.isAnonymous ? apiCart.guid : apiCart.code) : null
  }

  /**
   * @param {APICart} apiCart
   * @param {GraphQLContext} context
   * @returns {string|null}
   */
  _getCartId(apiCart, context) {
    const { auth } = context

    return apiCart ? (auth && auth.isAnonymous ? apiCart.guid : apiCart.code) : null
  }

  /**
   * used by Address & Cart
   *
   * @param {GQLInputAddress} addressPayload
   * @param {boolean} [isDeliveryAddress]
   * @param {GraphQLContext} context
   * @returns {APIAddress}
   * @see src/service/GraphQL/types/Address
   */
  _registerAddressPayload(addressPayload, isDeliveryAddress, context) {
    return AddressTransformer.toAPIAddress(addressPayload, isDeliveryAddress, context)
  }

  /**
   * @param {GraphQLContext} context
   * @param {GQLInputTrackingCampaign[]} campaigns
   */
  async _setTrackingCampaigns(context, campaigns) {
    if (!context.session) {
      const error = new GraphQLApolloError(`No session found`, 400)
      error.addError('ReferenceError', 'No session found')
      throw error
    }

    context.session.campaigns = campaigns
    await context.session?.save?.()
  }

  /**
   * @param {GQLUser} _user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLOrderList>}
   */
  async _getUserOrders(_user, _args, _context) {
    const { frResolverArgs } = toFR(arguments)
    return getUserOrders(...frResolverArgs)
  }
}

module.exports = BaseResolver
