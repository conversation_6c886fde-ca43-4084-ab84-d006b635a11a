const _ = require('lodash')
const apm = require('elastic-apm-node')
const { URL } = require('url')
const debug = require('debug')('itf:order')

const config = require('../../../config')
const CONST = require('../../../const')

const BaseResolver = require('./Base')

const {
  CartTransformer,
  DateRangeTransformer,
  DeliveryFeasibilityTransformer,
  DeliveryTimeRangesResultTransformer,
  PhoneTransformer,
  OrderTransformer,
  OrderResultTransformer,
  VoucherTransformer,
  TokenTransformer,
  OrderReasonTransformer,
  TownTransformer,
} = require('./transformers')
const { CTOrderTransformer, CTProductTransformer } = require('../../transformers/commerceTools')

const { GQLOrder, GQLPaymentMethod } = require('../../models')
const { GraphQLApolloError } = require('../../errors')

const { Session } = require('../../../plugins/session')
const { dates } = require('../../../helpers')
const { isUUID } = require('../../../helpers/string')
const { getFRContext, toFR } = require('../fr/shared')
const CtDatoOrderResolser = require('../ct-datocms/Order')
const { getDistributionChannels } = require('../../../helpers/commercetools')

/** @typedef {import('graphql').GraphQLResolveInfo} GraphQLResolveInfo */

/** @typedef {import('../../types/_typedef')}  */
/** @typedef {import('./_typedef')}  */

class OrderResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{token: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async confirmOrderByToken(_parent, args, context) {
    const apiOrder = await context.dataSources.hybrisOrder.updateIflOrder(args.token, {}, true)
    await context.loaders.orders.prime(apiOrder.guid, apiOrder)

    return OrderTransformer.toGQL(apiOrder, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ urlPath: string, urlQuery: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async confirmRedirectOrder(_parent, args, context) {
    const { urlPath, urlQuery } = args
    const u = new URL(`http://example.com${urlPath}?${urlQuery}`)

    const partnerType = u.searchParams.get('type')
    const cartId = u.searchParams.get('cartId')
    const isS2S = urlPath === 's2s'

    if (u.searchParams.has('cko-session-id') || partnerType === 'alma') {
      let paymentId = u.searchParams.get('cko-session-id')
      // TODO: to be deleted when Checkout.com provides the correct payment ID
      /**
       * [GGT-830]
       * checks whether the payment is redirected from Alma to retrieve the order from the cart ID
       * instead the payment ID provided by Checkout.com => partner payment ID (in the Alma system)
       */
      if (!paymentId && partnerType === 'alma' && cartId) {
        const { paymentIdentifier } = await context.dataSources.hybrisCart
          .getCart(cartId)
          .catch(() => context.dataSources.hybrisOrder.getOrder(cartId))
        paymentId = paymentIdentifier
        if (!paymentId) {
          throw new GraphQLApolloError('Payment id not found', 400)
        }
      }
      return this._confirmCheckoutPayment(paymentId, isS2S, context, urlPath)
    }

    if (urlPath.includes('/buybox/')) {
      return this._manageBuyboxPayment(
        urlPath,
        {
          cartId,
          isS2S,
          payerId: u.searchParams.get('PayerID'),
          token: u.searchParams.get('token'),
        },
        context
      )
    }

    let response

    try {
      this._assertHasCart(context)
      response = await context.dataSources.hybrisCart.verifyPaymentResponseAndPlaceOrder(args.urlQuery)
    } catch (e) {
      if (cartId) {
        const order = await context.dataSources.hybrisOrder.getOrder(cartId)
        if (order) {
          return OrderResultTransformer.toGQL(
            {
              order,
              paymentResult: 'SUCESS',
            },
            context
          )
        }
      }

      throw e
    }

    if (response.paymentResult === 'SUCESS') {
      await context.loaders.orders.prime(response.order.guid, response.order)
    }

    const result = OrderResultTransformer.toGQL(response, context)

    if (result.order) {
      const apiCartId = this._getContextCartId(context)

      // do not await response to avoid blocking is the tracking fails
      this._convertCampaigns(context, result.order)

      /**
       * Creating the order destroys the cart on hybris side
       */
      context.log.info(`delete cart ${apiCartId} following offline payment confirmation`)

      delete context.session.apiCart

      await Promise.all([context.loaders.carts.clear(apiCartId), context.session.save()])
    }

    return result
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<void | GQLOrderResult>}
   */
  async _managePreOrder(context) {
    const contextCartId = this._getContextCartId(context)
    const apiCart = await context.loaders.carts.load(contextCartId)

    // Check the mourning delivery mode and update it with the known value by the Hybris
    if (
      [
        CONST.cart.deliveryMode.grave,
        CONST.cart.deliveryMode.funeralPlace,
        CONST.cart.deliveryMode.tombBundle,
      ].includes(apiCart?.deliveryAddress?.natureAddress)
    ) {
      _.set(
        apiCart,
        'deliveryAddress.typeCeremonie',
        CONST.cart.ceremonyTypeMap.hybris[apiCart.deliveryAddress.natureAddress]
      )
      await context.dataSources.hybrisCart.updateCart({
        deliveryAddress: apiCart.deliveryAddress,
      })
    }

    if (!(apiCart && apiCart.user && apiCart.user.uid !== 'anonymous')) {
      throw new GraphQLApolloError(`Cannot pay anonymous cart`)
    }

    // Check if it is a free order
    if (apiCart.totalPrice.value === 0 && apiCart.appliedVouchers?.length > 0) {
      const payload = {
        authorizationOnly: false,
        code: apiCart.code,
        parameters: [
          {
            key: 'CONTRACT_NUMBER',
            type: 'string',
            value: '',
          },
          {
            key: 'CADDIE',
            type: 'string',
            value: '',
          },
          {
            key: 'CARD_TYP',
            type: 'string',
            value: 'FREE',
          },
          {
            key: 'RESULT_CODE',
            type: 'string',
            value: '',
          },
        ],
        reconciliationId: '',
        totalAmount: '0.0',
        transactionStatus: 'ACCEPTED',
        transactionStatusDetails: 'SUCCESFULL',
      }
      const gqlOrderResult = await this._doPlaceOder(contextCartId, payload, context)
      return gqlOrderResult
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderInfo: GQLInputOrderInfo}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<void | GQLOrderResult>}
   */
  async createOrder(_parent, args, context) {
    let result = await this._managePreOrder(context)
    if (result) {
      return result
    }

    const {
      cardCode,
      cardExpireMonth,
      cardExpireYear,
      cardNumber,
      cardOwner,
      cardType,
      dsp2,
      eCVV,
      mode,
      paymentToken,
      provider,
    } = args.orderInfo

    const createBuyboxPayment = async () => {
      const apiCart = await context.loaders.carts.load(this._getContextCartId(context))
      const OrderResolver = new CtDatoOrderResolser()
      switch (mode) {
        case CONST.payment.paymentMode.GIFT_CARD:
        case CONST.payment.paymentMode.BUYBOX:
          return OrderResolver._submitBuyboxPayment(
            {
              amount: apiCart.totalPrice.value,
              cardCode: cardNumber,
              cardPin: cardCode,
              cartId: this._getContextCartId(context),
            },
            context,
            // This syntax preserves the this context
            // of this Order.js class (hybris resolver)
            // in the Order.js class of ct-datocms resolver
            // for the method passed as parameter
            (...args) => this._validateCartForPayment.apply(this, args)
          )
        default:
          throw new Error('Not implemented')
      }
    }

    if (provider === CONST.payment.provider.CHECKOUT) {
      switch (mode) {
        case CONST.payment.paymentMode.CARD:
        case CONST.payment.paymentMode.PAYPAL:
        case CONST.payment.paymentMode.APPLEPAY:
        case CONST.payment.paymentMode.GOOGLEPAY:
        case CONST.payment.paymentMode.ALMA:
          return this._submitCheckoutPayment(args, context)
        case CONST.payment.paymentMode.BUYBOX:
          return createBuyboxPayment()
        default:
          throw new Error('Not implemented')
      }
    }

    this._assertHasCart(context)

    if (provider === CONST.payment.provider.BUYBOX) {
      return createBuyboxPayment()
    }

    const responseBaseUrl = context.req.headers.origin || config.apps[context.siteId]?.baseUrl
    const payload = { responseBaseUrl }

    if ([CONST.payment.paymentMode.AMEX, CONST.payment.paymentMode.CARD].includes(mode)) {
      payload.accountHolderName = cardOwner
      payload.cardNumber = cardNumber
      payload.cardToken = paymentToken
      payload.cardType = { code: CONST.payment.cardTypeAPIMap[cardType] || CONST.payment.cardType.CB }
      if (
        [CONST.payment.cardType.CB, CONST.payment.cardType.MASTERCARD, CONST.payment.cardType.VISA].includes(cardType)
      ) {
        payload.cardType.brandCode = cardType
      }
      payload.expiryMonth = cardExpireMonth
      payload.expiryYear = cardExpireYear
      payload.issueNumber = cardCode
      payload.issueNumberV = eCVV
    } else {
      payload.cardType = { code: _.invert(CONST.payment.paymentMode)[mode] }
    }

    /** @type {APIDSP2Params} */
    let dsp2Params
    if (dsp2) {
      dsp2Params = {
        browserColorDepth: dsp2.colorDepth,
        browserJavaEnabled: dsp2.javaEnabled,
        browserJavascriptEnabled: dsp2.javascriptEnabled,
        browserLanguage: context.language,
        browserScreenHeight: dsp2.screenHeight,
        browserScreenWidth: dsp2.screenWidth,
        browserTimeZone: dsp2.timeZoneOffset,
      }
    }

    const response = await context.dataSources.hybrisCart.doPaymentAndPlaceOrder(payload, dsp2Params)

    // we do not log the request to avoid leaking sensitive information
    debug('create order result: %O', response)

    result = OrderResultTransformer.toGQL(response, context)

    if (result.order) {
      const apiCartId = this._getContextCartId(context)

      // do not await response to avoid blocking is the tracking fails
      this._convertCampaigns(context, result.order)

      /**
       * Creating the order destroys the cart on hybris side
       */
      context.log.info(`delete cart ${apiCartId} following online payment`)

      delete context.session.apiCart

      await Promise.all([
        context.loaders.orders.prime(response.order.guid, response.order),
        context.loaders.carts.clear(apiCartId),
        context.session.save(),
      ])
    }

    return result
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{token: string}>} args
   * @param {GraphQLContext} context
   */
  async refuseOrderByToken(_parent, args, context) {
    const apiOrderId = await context.loaders.orderIdsByTokens.load(args.token)
    const apiOrder = await context.loaders.orders.load(apiOrderId)

    await context.dataSources.hybrisOrder.rejectIflOrder(args.token)

    return OrderTransformer.toGQL(apiOrder, context)
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{max: string, min: string}[]>}
   */
  async getAvailableDateRanges(delivery, _args, context) {
    const orderId = delivery.order?.id ?? delivery.orderId
    if (orderId && isUUID(orderId)) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.getAvailableDateRanges(...frResolverArgs)
    }

    /** @type {GQLOrder} */
    const order = delivery.order
    const entry = order.productItems[0]

    if (!entry) return []

    const deliveryDates = await context.loaders.deliveryDates.load({
      code: entry.variant.code,
      countryId: order.countryCode,
      postalCode: _.get(delivery, 'address.postalCode'),
    })

    return DateRangeTransformer.toGQL(deliveryDates)
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getMaxDeliveryDays(delivery, _args, context) {
    const orderId = delivery.order?.id ?? delivery.orderId
    if (orderId && isUUID(orderId)) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.getMaxDeliveryDays(...frResolverArgs)
    }

    // taking today from api to avoid having to deal with time zones
    const { lastDate, toDay } = await context.loaders.deliveryDates.load({
      code: delivery.order.productItems[0].variant.code,
      countryId: delivery.order.countryCode,
      postalCode: _.get(delivery, 'address.postalCode'),
    })

    if (!lastDate) return null

    return (dates.formatToGQL(lastDate).getTime() - dates.formatToGQL(toDay).getTime()) / 1000 / 3600 / 24
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async getOrder(_parent, args, context) {
    if (isUUID(args.orderId) || /^100\d+$/.test(args.orderId)) {
      // is a CT order
      const ctOrder = await context.loaders.commerceTools.orders.load(args.orderId)
      const order = CTOrderTransformer.toGQL(ctOrder, context, {
        distributionChannels: await getDistributionChannels(context),
      })

      return order
    }

    const apiOrder = await context.loaders.orders.load(args.orderId)

    const order = OrderTransformer.toGQL(apiOrder, context)

    if (order.user) {
      order.user.order = order
    }

    return order
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ email: string, fallback: boolean, orderNumber: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async getOrderByEmail(_parent, args, context) {
    try {
      const { email, orderNumber } = args

      const apiOrderId = await context.loaders.orderIdsByEmails.load({ email, orderNumber })
      const apiOrder = await context.loaders.orders.load(apiOrderId)

      const order = OrderTransformer.toGQL(apiOrder, context)

      if (order.user) {
        order.user.order = order
      }

      return order
    } catch (e) {
      if (args.fallback !== false) {
        const { frContext, frResolverArgs } = toFR(arguments)
        const [, frArgs] = frResolverArgs
        return frContext.resolvers.order.getOrderByEmail(
          _parent,
          {
            ...frArgs,
            fallback: false,
          },
          frContext,
          frArgs[3]
        )
      }
      throw e
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{token: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async getOrderByToken(_parent, args, context) {
    const apiOrderId = await context.loaders.orderIdsByTokens.load(args.token)
    const apiOrder = await context.loaders.orders.load(apiOrderId)

    if (!apiOrder) return

    return OrderTransformer.toGQL(apiOrder, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryFeasibility>}
   */
  async getOrderDeliveryFeasibility(_parent, args, context) {
    const feasibilityApi = await context.dataSources.hybrisInterflora.checkFeasibility(args.orderId)

    return DeliveryFeasibilityTransformer.toGQL(feasibilityApi)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{date: Date, orderId: string}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ endHour: Number, endMinute: Number, startHour: Number, startMinute: Number }[]>}
   */
  async getOrderDeliveryRangeFixHour(_parent, _args, context) {
    // @todo: check if product is elligible and if not, return empty
    // @todo: check possible params (not documented in swagger)

    /** @todo loader from loaders */
    const ranges = await context.dataSources.hybrisInterflora.getCeremonyHourRange(_args.date)

    return ranges ? [ranges] : []
  }

  /**
   * @param {null} _parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getOrderDeliveryTimeRanges(_parent, args, context) {
    const { date, orderId } = args

    if (isUUID(orderId)) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.getOrderDeliveryTimeRanges(...frResolverArgs)
    }

    const order = await this.getOrder(null, { context: args.context, orderId }, context)

    return DeliveryTimeRangesResultTransformer.toGQL(
      await context.loaders.deliveryTimeIntervals.load({
        date,
        occasion: order.codeOccasion,
        postalCode: _.get(order, 'delivery.address.postalCode'),
        productCode: order.productItems[0].variant.code,
      })
    )
  }

  /**
   * @param {{ id: string, number: string } | GQLOrder} order
   * @param {GraphQLContextArgs<{}>} args
   * @param {FRHybrisContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getOrderProperty(order, args, context, info) {
    if (order?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      const property = frContext.resolvers.order.getOrderProperty(order, args, frContext, info)

      if (['accessoryItems', 'productItems', 'serviceItems'].includes(info.fieldName)) {
        if (order?.isLoaded(CONST.dataSources.commerceToolsOrderProducts)) {
          return order[info.fieldName]
        }

        const entries = order[info.fieldName]
        return Promise.all(
          entries.map(async (entry) => {
            const ctProduct = await context.loaders.commerceTools.products.load({ code: entry.variant.parent.code })
            const product = CTProductTransformer.toGQL(ctProduct, frContext, {
              countryId: context.countryId,
              isAccessory: info.fieldName === 'accessoryItems',
            })

            const variant = product.variants.find((variant) => variant.code === entry.variant.code)
            variant.parent = product

            return {
              ...entry,
              variant,
            }
          })
        )
      }

      return property
    }

    if (order[info.fieldName] !== undefined || order instanceof GQLOrder) {
      return order[info.fieldName]
    }

    const _order = await this.getOrder(
      null,
      {
        context: { language: context.language, siteId: context.siteId },
        orderId: !context.auth || context.auth.isAnonymous ? order.id : order.number,
      },
      context,
      info
    )
    return _order && _order[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  async getOrders(_parent, args, context) {
    const user = await this._getCurrentUser(context)
    return this._getUserOrders(user, args, context)
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  getCanAttachOrder(order, _args, context) {
    if (order?.isLoaded?.(CONST.dataSources.commerceTools) || isUUID(order.id)) {
      // is a CT order
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.getCanAttachOrder(...frResolverArgs)
    }
    return context.dataSources.hybrisOrder.canAttachOrder(order.id)
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Boolean>}
   */
  getEmailHasAccountNoGuest(order, _args, context) {
    return context.dataSources.hybrisInterflora.verifyEmailHasRegisteredUser(
      order.user.email || (order.delivery && order.delivery.sender && order.delivery.sender.email)
    )
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {void|Promise<{
   *   code: string,
   *   endDate: string,
   *   name: string,
   *   description: string,
   *   startDate: string,
   *   type: string,
   *   value: Number
   * }>}
   */
  async getOrderVoucher(order, args, context, info) {
    if (order?.isLoaded?.(CONST.dataSources.commerceTools)) {
      const frContext = getFRContext(context)
      return frContext.resolvers.order.getOrderVoucher(order, args, frContext, info)
    }

    /** @todo load from loaders */
    const apiVoucher = await context.dataSources.hybrisOrder.getOrderConfirmationVoucher(order.id)

    if (!apiVoucher) {
      return
    }

    return VoucherTransformer.toGQL(apiVoucher)
  }

  /**
   * @param {GQLOrder} _order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ id: string, label: string }[]>}
   */
  async getReasons(_order, _args, context) {
    const reasons = await context.loaders.occasions.load({})

    return reasons.map(OrderReasonTransformer.toGQL)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ orderId: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getSuggestedMessages(_parent, args, context) {
    if (isUUID(args.orderId)) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.getSuggestedMessages(...frResolverArgs)
    }

    const apiOrder = await context.loaders.orders.load(args.orderId)

    if (!apiOrder.codeOccasion) return []

    return (await context.loaders.suggestedMessages.load(apiOrder.codeOccasion)).map((code) => code.value)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, orderId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: GQLTown, valid: boolean }>}
   */
  async getTownsForOrder(_parent, args, context) {
    const { countryId, orderId, search } = args

    const apiOrder = await context.loaders.orders.load(orderId)

    const entry = apiOrder.entries.find((entry) => !entry.product.accessory)
    if (!entry) return []

    const localities = await context.loaders.towns.load({
      countryIsoCode: countryId,
      productCode: entry.product.code,
      search,
    })

    return localities.map((apiTown) => ({ town: TownTransformer.toGQL(apiTown), valid: apiTown.valide }))
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @returns {Promise<null|Number>}
   */
  async getUseFixHour(delivery, _args) {
    return delivery.useFixHour
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{voucherCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {void|Promise<GQLVoucher>}
   */
  async getVoucher(_parent, args, context) {
    const { voucherCode } = args
    const apiVoucher = await context.dataSources.hybrisOrder.getVoucher(voucherCode)

    if (!apiVoucher) {
      return
    }

    return {
      ...VoucherTransformer.toGQL(apiVoucher),
      voucherCode,
    }
  }

  /**
   *
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ orderId: string, password: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async loginAndAttachOrder(_parent, args, context) {
    const { orderId, password } = args

    const apiToken = await context.dataSources.hybrisOrder.loginAndAttachOrder(orderId, password)

    await this._setAuthContext(context, apiToken, args.context.siteId)

    return TokenTransformer.toGQL(apiToken, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{order: GQLInputOrder, orderId: string, isIfl: boolean, iflToken: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async updateOrder(_parent, args, context) {
    if (isUUID(args.orderId)) {
      const { frContext, frResolverArgs } = toFR(arguments)
      return frContext.resolvers.order.updateOrder(...frResolverArgs)
    }

    const {
      iflToken, // Injected by updateOrderByToken only
      isIfl = false, // Injected by updateOrderByToken only
      order: { reasonId },
      orderId,
    } = args

    /** @type {APIOrder} */
    let apiOrder

    /** @type {APIUpdateOrder} */
    const payload = OrderTransformer.toAPIUpdateOrder(args.order, context)

    if (reasonId && !isIfl) {
      apiOrder = await context.dataSources.hybrisOrder.updateOrderOccasion(orderId, reasonId)
    }

    if (Object.keys(payload).length > 0) {
      if (isIfl) {
        apiOrder = await context.dataSources.hybrisOrder.updateIflOrder(iflToken, payload)
      } else {
        apiOrder = await context.dataSources.hybrisOrder.updateOrder(orderId, payload)
      }
    }

    if (apiOrder) {
      await Promise.all([
        context.loaders.orders.prime(apiOrder.guid, apiOrder),
        context.loaders.orders.prime(apiOrder.code, apiOrder),
      ])
      return OrderTransformer.toGQL(apiOrder, context)
    }

    if (isIfl) {
      return this.getOrderByToken(null, { context: args.context, token: iflToken }, context)
    } else {
      return this.getOrder(null, { context: args.context, orderId }, context)
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{order: GQLInputOrder, token: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrder>}
   */
  async updateOrderByToken(_parent, args, context) {
    return this.updateOrder(
      _parent,
      { context: args.context, iflToken: args.token, isIfl: true, order: args.order },
      context
    )
  }

  /**
   * @param {GraphQLContext} context
   * @param {GQLOrder} order
   */
  async _convertCampaigns(context, order) {
    const promises = []

    for (const campaign of context.session?.campaigns || []) {
      if (campaign.provider === CONST.tracking.provider.timeone) {
        for (const param of campaign.params) {
          if (param.name === 'subtracking') {
            context.log.info(
              `converting timeone campaign ${order.number}/${param.values[0]} ${order.subTotal.value / 100}`
            )
            // @todo: TimeOne is asking for amound excluded taxes & shipping but taxes are not
            //        handled in hybris. We send the total price, without shipping instead
            promises.push(
              context.dataSources.timeone.convertCampaign(order.number, order.subTotal.value / 100, param.values[0])
            )
          }
        }
      }
    }

    const results = await Promise.allSettled(promises)
    delete context.session?.campaigns

    return results.map((result) => {
      if (result.status === 'fulfilled') return result.value

      context.log.error(result.reason, 'Error converting campaign')
      return result.reason
    })
  }

  /**
   * @param {string} id
   * @param {boolean} isS2S
   * @param {GraphQLContext} context
   * @param {string} urlPath
   */
  async _confirmCheckoutPayment(id, isS2S = false, context, urlPath) {
    const OrderResolser = new CtDatoOrderResolser()
    const paymentInfo = await OrderResolser._checkXPaymentCapture(id, CONST.payment.provider.CHECKOUT, context)

    return context.lock(paymentInfo.id, async () => {
      if (isS2S) {
        const auth = await context.dataSources.hybrisAuth.login()
        await this._setAuthContext(context, auth, context.siteId)
      } else if (!(context.session instanceof Session)) {
        context.log.info(`[_confirmCheckoutPayment] session lost - restoring`)
        context.session = await Session.get(context.server.redis, paymentInfo.metadata.session_id)
        const auth = await context.dataSources.hybrisAuth.login()
        await this._setAuthContext(context, auth, context.siteId)
      }

      switch (paymentInfo.status) {
        case CONST.payment.checkout.paymentStatus.AUTHORIZED:
        case CONST.payment.checkout.paymentStatus.PENDING: {
          if (!['paypal', 'alma'].includes(paymentInfo.source?.type)) {
            try {
              await context.dataSources.checkout.capturePayment(paymentInfo.id, `${paymentInfo.reference}_capture`)
            } catch (e) {
              apm.captureError(e, { custom: e })

              return OrderResultTransformer.toGQL(
                {
                  paymentErrorCode: e.source?.http_code || '500',
                  paymentErrorMessage: e.source?.name || e.name || 'Unknown error',
                  paymentResult: CONST.order.statuses.error,
                },
                context
              )
            }
          } else if (
            /*
             * FIX for FAM-742 => to improve this hack => in case alma cancel
             * payment we manage this case by checking the url contains 'failure'
             * cause checkout still return 'PENDING' when alma is canceled
             */
            (paymentInfo.source?.type === 'alma' || paymentInfo.source?.type === 'paypal') &&
            urlPath.includes('failure')
          ) {
            return OrderResultTransformer.toGQL(
              {
                paymentErrorCode: '500',
                paymentErrorMessage: 'Paiement annulé',
                paymentResult: CONST.order.statuses.error,
              },
              context
            )
          }

          await this._pendingXPaymentAction(paymentInfo.metadata?.cart_id, context)
          break
        }
        case CONST.payment.checkout.paymentStatus.CAPTURED:
        case CONST.payment.checkout.paymentStatus.PAID:
          return this._transformXPayment(
            {
              amount: paymentInfo.amount / 100,
              cartId: paymentInfo.metadata.cart_id,
              id: paymentInfo.id,
              method: paymentInfo.metadata.method,
              scheme: paymentInfo.source?.scheme,
              sessionId: paymentInfo.metadata.session_id,
              status: paymentInfo.status,
              type: paymentInfo.source?.type,
            },
            isS2S,
            CONST.payment.provider.CHECKOUT,
            context
          )
        case CONST.payment.checkout.paymentStatus.DECLINED:
        case CONST.payment.checkout.paymentStatus.EXPIRED: {
          const lastAction = (paymentInfo.actions || []).at(-1)
          return OrderResultTransformer.toGQL(
            {
              paymentErrorCode: lastAction?.response_code,
              paymentErrorMessage: lastAction?.response_summary,
              paymentResult: CONST.order.statuses.error,
            },
            context
          )
        }
        default: {
          const error = new GraphQLApolloError(`Unhandled checkout payment status ${paymentInfo.status}`)
          apm.captureError(error, { custom: error })

          const lastAction = (paymentInfo.actions || []).at(-1)

          return OrderResultTransformer.toGQL(
            {
              paymentErrorCode: lastAction?.response_code || '500',
              paymentErrorMessage: lastAction?.response_summary || 'Unknown payment status received',
              paymentResult: CONST.order.statuses.error,
            },
            context
          )
        }
      }
    })
  }

  /**
   * @param {GraphQLContextArgs<{orderInfo: GQLInputOrderInfo}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _submitCheckoutPayment(args, context) {
    const { mode, cardType, paymentToken, paymentTokenType } = args.orderInfo

    const apiCart = await this._validateCartForPayment(
      {
        cardType,
        mode,
        provider: CONST.payment.provider.CHECKOUT,
      },
      context
    )

    const orderUser = await context.dataSources.hybrisUser.getUser(apiCart.user.uid)

    // If no billing address has been given, we take the default one.
    if (!apiCart.billingAddress && orderUser.defaultAddress) {
      apiCart.billingAddress = orderUser.defaultAddress
    }

    const mode2Type = {
      [CONST.payment.paymentMode.PAYPAL]: 'paypal',
      [CONST.payment.paymentMode.CARD]: 'token',
      [CONST.payment.paymentMode.APPLEPAY]: 'token',
      [CONST.payment.paymentMode.GOOGLEPAY]: 'token',
      [CONST.payment.paymentMode.ALMA]: 'alma',
    }

    debug('%O', apiCart)

    /** @type {CheckoutPaymentRequest} */
    const payload = {
      amount: Math.round(apiCart.totalPrice.value * 100),
      capture: true,
      currency: apiCart.totalPrice.currencyIso,
      customer: {
        email: apiCart.billingAddress?.email || orderUser.email,
        name: `${apiCart.billingAddress?.firstName || ''} ${apiCart.billingAddress?.lastName || ''}`.trim(),
      },
      description: apiCart.code,
      // 5 fields max, each max 255 chars long
      metadata: {
        cart_id: apiCart.guid,
        method: mode,
        session_id: context.session?.id,
      },
      reference: apiCart.code,
      source: {
        type: mode2Type[mode],
      },
    }

    //Sometimes we don't have delivery address (old account or iflora)
    if (apiCart.deliveryAddress?.line1 || apiCart.orderType !== CONST.cart.orderType.NO_ADDRESS) {
      payload.shipping = {
        address: {
          address_line1: apiCart.deliveryAddress?.line1 || '',
          address_line2: apiCart.deliveryAddress?.line2 || '',
          city: apiCart.deliveryAddress?.town || '',
          country: apiCart.deliveryAddress?.country?.isocode || '',
          zip: apiCart.deliveryAddress?.postalCode || '',
        },
        method: 'OtherAddress',
      }

      if (apiCart.deliveryAddress?.phone) {
        try {
          const shippingPhone = PhoneTransformer.toGQL(apiCart.deliveryAddress.phone, context, {
            countryCode: apiCart.deliveryAddress?.country?.isocode || context.countryId,
          })
          if (shippingPhone) {
            payload.shipping.phone = {
              country_code: shippingPhone.prefix,
              number: shippingPhone.number,
            }
          }
        } catch (e) {
          context.log.error(`${e.message}: delivery phone was '${apiCart.deliveryAddress.phone}'`)
        }
      }
    }

    if (apiCart.billingAddress?.phone) {
      try {
        const phone = PhoneTransformer.toGQL(apiCart.billingAddress.phone, context, {
          countryCode: apiCart.billingAddress?.country?.isocode || context.countryId,
        })
        if (phone) {
          payload.source.phone = {
            country_code: phone.prefix,
            number: phone.number,
          }
          payload.customer.phone = {
            country_code: phone.prefix,
            number: phone.number,
          }
        }
      } catch (e) {
        context.log.error(`${e.message}: billing phone was '${apiCart.deliveryAddress.phone}'`)
      }
    }

    if (
      mode === CONST.payment.paymentMode.CARD ||
      mode === CONST.payment.paymentMode.APPLEPAY ||
      mode === CONST.payment.paymentMode.GOOGLEPAY
    ) {
      payload.source = {
        ...payload.source,
        token: paymentToken,
      }

      //If we don't have a billing adress we skip this parameter completly
      {
        const addressParts = `${apiCart.billingAddress?.line1 || ''} ${apiCart.billingAddress?.line2 || ''}`
          .trim()
          .match(/.{1,200}/g)

        if (addressParts?.length) {
          payload.source.billing_address = {
            address_line1: addressParts[0] || '',
            address_line2: addressParts[1] || '',
            city: apiCart.billingAddress?.town,
            country: apiCart.billingAddress?.country?.isocode,
            zip: apiCart.billingAddress?.postalCode,
          }
        }
      }

      let preferred_scheme
      if (cardType && mode !== CONST.payment.paymentMode.APPLEPAY && mode !== CONST.payment.paymentMode.GOOGLEPAY) {
        switch (cardType) {
          case 'CB':
            preferred_scheme = 'cartes_bancaires'
            break
          case 'MASTERCARD':
            preferred_scheme = 'mastercard'
            break
          case 'VISA':
            preferred_scheme = 'visa'
            break
        }
        if (preferred_scheme) {
          payload.processing = {
            preferred_scheme: preferred_scheme,
          }
        }
      }
    } else if (mode === CONST.payment.paymentMode.PAYPAL) {
      /* Paypal is not taking discounts and such into account
        it's better to just put one item corresponding to the order with its total to avoid
        strange scenarios which trigger an error because total sum for items
        wouldn't match total order amount on paypal side.
      */

      payload.items = [
        {
          name: apiCart.code,
          quantity: 1,
          reference: apiCart.code,
          total_amount: Math.round(apiCart.totalPrice.value * 100),
          unit_price: Math.round(apiCart.totalPrice.value * 100),
        },
      ]
      payload.processing = {
        invoice_id: apiCart.code,
      }
      payload.source = {
        ...payload.source,
        invoice_number: apiCart.code,
        logo_url: null,
        plan: {
          skip_shipping_address: apiCart.orderType === CONST.cart.orderType.NO_ADDRESS,
          type: 'MERCHANT_INITIATED_BILLING', // skipping shipping address if iflora
        },
      }
      const recipientName = `${apiCart.deliveryAddress?.firstName || ''} ${
        apiCart.deliveryAddress?.lastName || ''
      }`.trim()
      if (recipientName.length > 0) {
        payload.shipping.name = recipientName
        payload.source.recipient_name = recipientName
      }
    }

    const paymentRequestOptions = {
      enabled: !(
        // no 3ds if ...
        (
          [CONST.payment.paymentMode.PAYPAL].includes(mode) || //.. we pay with paypal or ..
          ([CONST.payment.paymentMode.GOOGLEPAY, CONST.payment.paymentMode.APPLEPAY].includes(mode) &&
            paymentTokenType?.toLowerCase() === 'cryptogram_3ds')
        ) // .. we use web with google pay or apple pay
      ),
    }

    // If any valuable product is in the cart, we ask Checkout to trigger a 3DS challenge.
    const force3DSCondition = apiCart.entries?.some((entry) =>
      config.checkout?.forced3DSProducts?.includes(entry?.product?.code)
    )

    if (force3DSCondition) {
      paymentRequestOptions['challenge_indicator'] = 'challenge_requested'
    }

    // Exemption is causing errors with google/apple pay, Checkout feedback
    if (
      ![CONST.payment.paymentMode.GOOGLEPAY, CONST.payment.paymentMode.APPLEPAY].includes(mode) &&
      !force3DSCondition
    ) {
      paymentRequestOptions['exemption'] = 'transaction_risk_assessment'
    }

    const url = `${context.appConfig.baseUrl}/checkout/paiements/checkout`
    context.log.info(`[_submitCheckoutPayment - payload] %O`, {
      payload,
      paymentRequestOptions,
      url,
    })

    const paymentResult = await context.dataSources.checkout.requestPayment(payload, paymentRequestOptions, url)
    context.log.info('[_submitCheckoutPayment - response] %O', paymentResult)

    if (
      paymentResult.status === CONST.payment.checkout.paymentStatus.AUTHORIZED ||
      paymentResult.status === CONST.payment.checkout.paymentStatus.PAID
    ) {
      const paymentInfo = await context.dataSources.checkout.getPaymentDetails(paymentResult.id)
      return this._transformXPayment(
        {
          amount: paymentInfo.amount / 100,
          cartId: paymentInfo.metadata.cart_id,
          id: paymentInfo.id,
          method: paymentInfo.metadata.method,
          scheme: paymentInfo.source?.scheme,
          sessionId: paymentInfo.metadata.session_id,
          status: paymentInfo.status,
          type: paymentInfo.source?.type,
        },
        false,
        CONST.payment.provider.CHECKOUT,
        context
      )
    } else if (paymentResult.status === CONST.payment.checkout.paymentStatus.DECLINED) {
      return OrderResultTransformer.toGQL(
        {
          paymentErrorCode: paymentResult.response_code,
          paymentErrorMessage: paymentResult.response_summary,
          paymentResult: CONST.order.statuses.error,
        },
        context
      )
    } else if (paymentResult.status === CONST.payment.checkout.paymentStatus.PENDING) {
      const redirectUrl = paymentResult._links?.redirect?.href
      try {
        await context.dataSources.hybrisCart.setPaymentIdentifier(paymentResult.id, apiCart.guid)
      } catch {
        // No action required, mainly used for Alma payment in the staging environment.
      }
      return OrderResultTransformer.toGQL(
        {
          paymentResult: CONST.order.statuses.redirect,
          redirectUrl,
        },
        context
      )
    }

    const error = new GraphQLApolloError(
      `Unhandled payment response: ${paymentResult.status}`,
      'order-invalid-payment-response'
    )
    apm.captureError(error, { custom: error })
    context.log.error(`[_doPlaceOrder] invalid status received ${paymentResult.status}`)
    return OrderResultTransformer.toGQL(
      {
        paymentErrorCode: '500',
        paymentErrorMessage: error.message,
        paymentResult: CONST.order.statuses.error,
      },
      context
    )
  }

  /**
   * Manage Buybox payment following the returned url
   *
   * @param {string} urlPath
   * @param {BuyboxOrderPaymentInfo} paymentInfo
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _manageBuyboxPayment(urlPath, paymentInfo, context) {
    if (urlPath.includes('/buybox/success')) {
      return this._confirmBuyboxPayment(paymentInfo, context)
    } else if (urlPath.includes('/buybox/failure')) {
      return OrderResultTransformer.toGQL(
        {
          paymentErrorCode: '500',
          paymentErrorMessage: 'Erreur de paiement',
          paymentResult: CONST.order.statuses.error,
        },
        context
      )
    } else if (urlPath.includes('/buybox/cancel')) {
      return OrderResultTransformer.toGQL(
        {
          paymentErrorCode: '500',
          paymentErrorMessage: 'Paiement annulé',
          paymentResult: CONST.order.statuses.canceled,
        },
        context
      )
    }
  }

  /**
   * Capture the payment and transform the cart to order
   *
   * @param {BuyboxOrderPaymentInfo} paymentInfo
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmBuyboxPayment(paymentInfo, context) {
    const { cartId, isS2S, payerId, token } = paymentInfo
    const OrderResolver = new CtDatoOrderResolser()
    try {
      const apiCart = await context.dataSources.hybrisCart.getCart(cartId)
      return OrderResolver._confirmBuyboxPayment(
        {
          amount: apiCart?.totalPrice.value,
          cartId,
          isS2S,
          payerId,
          token,
        },
        context,
        // This syntax preserves the this context
        // of this Order.js class (hybris resolver)
        // in the Order.js class of ct-datocms resolver
        // for the methods passed as parameters
        (...args) => this._transformXPayment.apply(this, args),
        (...args) => this._pendingXPaymentAction.apply(this, args),
        () => {},
        () => {}
      )
    } catch (e) {
      apm.captureError(e, { custom: e })
      return OrderResultTransformer.toGQL(
        {
          paymentErrorCode: '500',
          paymentErrorMessage: 'Payment error',
          paymentResult: CONST.order.statuses.error,
        },
        context
      )
    }
  }

  /**
   * call Hybris doPlaceOder
   * @param {string} apiCartId
   * @param {undefined} payload
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _doPlaceOder(apiCartId, payload, context) {
    let response
    try {
      // Hybris returns a success wether the order has already been placed or not
      debug('doPlaceOrder payload: %O', payload)
      response = await context.dataSources.hybrisCart.doPlaceOder(apiCartId, payload)
    } catch (error) {
      if (
        error.errors?.[0]?.type === 'ModelSavingError' ||
        /(DuplicateKeyException|cart not found)/i.test(error.message)
      ) {
        const errors = []
        let attempts = 0
        while (attempts++ < 3 && !response) {
          try {
            const order = await context.dataSources.hybrisOrder.getOrder(apiCartId)
            response = {
              order,
              paymentResult: 'SUCESS',
            }
          } catch (e) {
            errors.push(e)
            await new Promise((resolve) => setTimeout(resolve, config.checkout.waitForOrderDelay))
          }
        }

        if (!response) {
          for (const e of errors) {
            apm.captureError(e, { custom: e })
          }
          context.log.error({ errors }, `[_doPlaceOrder] ${errors.length} errors received`)

          return OrderResultTransformer.toGQL({
            paymentErrorCode: '500',
            paymentErrorMessage: errors[0].message || 'Unknown error',
            paymentResult: CONST.order.statuses.error,
          })
        }
      } else {
        apm.captureError(error, { custom: error })
        context.log.error(error, `[_doPlaceOrder] unexpected error received from hybris %s`, error.message)

        return OrderResultTransformer.toGQL({
          paymentErrorCode: error.code || '500',
          paymentErrorMessage: error.message || 'Unknown error',
          paymentResult: CONST.order.statuses.error,
        })
      }
    }

    if (!response?.order?.guid) {
      const error = new GraphQLApolloError(`checkout doPlaceOrder`, 500)
      apm.captureError(error, { custom: error })
      context.log.error(`[_doPlaceOrder] no order guid found`)

      return OrderResultTransformer.toGQL(
        {
          paymentErrorCode: response?.paymentResult || '500',
          paymentErrorMessage: response?.paymentErrorMessage || 'Unknown error',
          paymentResult: CONST.order.statuses.error,
        },
        context
      )
    }

    const result = OrderResultTransformer.toGQL(response, context)

    this._convertCampaigns(context, result.order)

    delete context.session.apiCart

    await Promise.all([
      context.loaders.orders.prime(response.order.guid, response.order),
      context.loaders.carts.clear(apiCartId),
      context.session.save(),
    ])

    return result
  }

  /**
   * @param {{cardType: string, mode: string, provider: string}} paymentInfo
   * @param {GraphQLContext} context
   * @return {Promise<APICart>}
   */
  async _validateCartForPayment(paymentInfo, context) {
    const { mode, provider } = paymentInfo
    const apiCart = await context.loaders.carts.load(this._getContextCartId(context))

    if (!(apiCart && apiCart.user && apiCart.user.uid !== 'anonymous')) {
      throw new GraphQLApolloError(`Cannot pay anonymous cart`)
    }

    /*
      We check if the order is already placed or paid with this cart, if it is we don't pay another time.
      We shouldn't have to do this but idempotencyKey from Checkout is making any payment retry (even on a failed 3DS) result in a 404..
    */
    try {
      const order = await context.loaders.orders.load(apiCart.guid)
      const resp = {
        order: order,
        paymentResult: 'SUCESS',
      }
      delete context.session.apiCart
      context.session.save()
      return OrderResultTransformer.toGQL(resp, context)
    } catch (e) {
      // If we dont it means we need to pay the cart and continue through this code
    }

    if (
      !(
        // IFlora order should have at least a country and either phone, twitterId or email.
        (
          (apiCart.orderType === CONST.cart.orderType.NO_ADDRESS &&
            apiCart.deliveryAddress?.country &&
            (apiCart.deliveryAddress?.phone || apiCart.deliveryAddress?.twitterId || apiCart.deliveryAddress?.email)) || // Every other order type should have a deliveryAddress with a line, a country and a town
          (apiCart.deliveryAddress?.line1 && apiCart.deliveryAddress?.country && apiCart.deliveryAddress?.town)
        )
      )
    ) {
      throw new GraphQLApolloError(`Missing delivery address infos`)
    }

    if (apiCart.orderType !== CONST.cart.orderType.NO_ADDRESS && !apiCart.deliveryDate) {
      throw new GraphQLApolloError(`Missing delivery date`)
    }

    {
      const paymentMethod = new GQLPaymentMethod()
      paymentMethod.mode = mode
      paymentMethod.provider = provider

      if (!paymentMethod.matchesApiCart(apiCart)) {
        throw new GraphQLApolloError(`Price rules are not valid for this ${mode} payment mode`)
      }
    }
    return apiCart
  }

  /**
   * @param {string} cartId
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart | void>}
   */
  async _pendingXPaymentAction(cartId, context) {
    const promises = []
    if (cartId) {
      promises.push(context.loaders.carts.clear(cartId))

      try {
        const apiCart = await context.loaders.carts.load(cartId)
        await context.dataSources.hybrisCart.updateCart(
          CartTransformer.toHybrisPayload(
            {
              ...apiCart,
              orderStatus: CONST.hybris.cart.orderStatus.pending,
            },
            context
          )
        )
        const apiCartId = this._getContextCartId(context)
        if (apiCartId !== cartId) {
          promises.push(context.loaders.carts.clear(apiCartId))
        }
      } catch (e) {
        apm.captureError(e, { custom: e })
        // do not throw
        // not finding the cart does not prevent processing it on
        // receiving the payment confirmation
      }
    }

    delete context.session?.apiCart
    if (typeof context.session?.save !== 'function') context.session.save = () => {} // no session is created for real anonymous
    promises.push(context.session.save())

    await Promise.all(promises)

    return OrderResultTransformer.toGQL(
      {
        paymentResult: 'AWAITING_CAPTURE',
      },
      context
    )
  }

  /**
   * Generic template to handle payment transformation
   *
   * ⚠︝ Does not pay order!
   * ⚠︝ Can be called by webhook OR by confirm page
   *
   *
   * @param {TransformPaymentInfo} paymentInfo
   * @param {boolean} isS2S
   * @param {string} provider
   * @param {GraphQLContext} context
   * @return {Promise<GQLOrderResult>}
   */
  async _transformXPayment(paymentInfo, isS2S = false, provider, context) {
    const {
      cartId,
      sessionId,
      method: paymentMethod,
      type: _paymentType,
      scheme: paymentScheme,
      status: paymentStatus,
      id: paymentId,
      amount: paymentAmount,
    } = paymentInfo
    const providerWaitForOrderDelay = config[provider.toLowerCase()].waitForOrderDelay

    if (isS2S) {
      // if called from webhook, we don't have any session and need to log in as anonymous
      const auth = await context.dataSources.hybrisAuth.login()
      await this._setAuthContext(context, auth, context.siteId)

      if (!context.session?.campaigns && sessionId) {
        const userSession = await Session.get(context.server.redis, sessionId)
        context.session.campaigns = userSession.campaigns
      }
    }

    let apiCart

    try {
      apiCart = await context.loaders.carts.load(cartId)
    } catch (e) {
      // do not thrown now
    }

    if (!apiCart) {
      try {
        const order = await new Promise((resolve) => {
          setTimeout(() => resolve(context.loaders.orders.load(cartId)), providerWaitForOrderDelay)
        })
        const resp = {
          order: order,
          paymentResult: 'SUCESS',
        }
        context.log.info(`[${provider.toLowerCase()}] order retrieved from payment info: ${order.guid}`)

        delete context.session?.apiCart
        if (typeof context.session?.save === 'function') await context.session.save()

        return OrderResultTransformer.toGQL(resp, context)
      } catch (e) {
        // no cart and no order => error
        apm.captureError(e, { custom: e })
        return OrderResultTransformer.toGQL(
          {
            paymentErrorCode: e.extensions?.[0]?.code || '500',
            paymentErrorMessage: e.errors?.[0]?.message || e.message || 'Unknown error',
            paymentResult: CONST.order.statuses.error,
          },
          context
        )
      }
    }

    let paymentType

    if (paymentMethod === CONST.payment.paymentMode.APPLEPAY || paymentMethod === CONST.payment.paymentMode.GOOGLEPAY) {
      paymentType = paymentMethod
    } else {
      switch (_paymentType.toUpperCase()) {
        case CONST.payment.paymentMode.CARD:
          paymentType =
            paymentScheme === 'American Express'
              ? CONST.payment.cardTypeAPIMap.AMERICAN_EXPRESS
              : CONST.payment.cardTypeAPIMap.CB
          break
        case CONST.payment.paymentMode.PAYPAL:
        case CONST.payment.paymentMode.ALMA:
          paymentType = _paymentType.toUpperCase()
          break
        case CONST.payment.paymentMode.GIFT_CARD:
          paymentType = CONST.payment.cardTypeAPIMap.GIFT_CARD
          break
        default:
          paymentType = 'UNKNOWN'
      }
    }

    // Hybris returns a success wether the order has already been placed or not
    const pld = {
      authorizationOnly: false,
      checkout: true,
      code: apiCart.code,
      parameters: [
        // tmp: hybris requires hard-coded magic strings for the time being
        {
          key: 'CONTRACT_NUMBER',
          type: 'string',
          value: provider.toLowerCase(),
        },
        {
          key: 'CADDIE',
          type: 'string',
          value: '123',
        },
        {
          key: 'RESULT_CODE',
          type: 'string',
          value: paymentStatus,
        },
        {
          key: 'CARD_TYP',
          type: 'string',
          value: paymentType,
        },
      ],
      reconciliationId: paymentId,
      totalAmount: paymentAmount,
      transactionStatus: 'ACCEPTED',
    }

    const gqlOrderResult = await this._doPlaceOder(cartId, pld, context)
    return gqlOrderResult
  }
}

module.exports = OrderResolver
