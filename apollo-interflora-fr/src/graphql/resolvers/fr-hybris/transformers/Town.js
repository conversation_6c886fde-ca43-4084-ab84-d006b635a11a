const GQLTown = require('../../../models/Town')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APILocalite, GQLTown, {}>}
 */
class TownTransformer extends Transformer {
  /**
   * @param {APILocalite} apiData
   * @returns {GQLTown}
   */
  static toGQL(apiData) {
    return new TownTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const town = new GQLTown()

    town.id = this.apiData.isoCode
    town.label = this.apiData.label
    town.postalCode = this.apiData.codePostale

    return town
  }
}

module.exports = TownTransformer
