const _ = require('lodash')

const config = require('../../../../config')
const CONST = require('../../../../const')
const GQLProduct = require('../../../models/Product')

const AdditionalInfoTransformer = require('./AdditionalInfo')
const ClassificationTransformer = require('./Classification')
const ImageTransformer = require('./Image')
const PriceTransformer = require('./Price')
const ProductVariantTransformer = require('./ProductVariant')
const AccessoryCategoryTransformer = require('./AccessoryCategory')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIProduct, GQLProduct, {}>}
 */
class ProductTransformer extends Transformer {
  /**
   * @param {APIProduct} apiData
   * @param {GraphQLContext} context
   * @param {{
   *  variantCode: string
   * }} options
   * @returns {GQLProduct}
   */
  static toGQL(apiData, context, options = {}) {
    return new ProductTransformer(options).setContext(context).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLProduct}
   */
  transformToGQL() {
    const product = new GQLProduct()
    for (const accessory of this.apiData.accessoires || []) {
      // GGT-129: filter accessories with undefined prices.
      if (!accessory.price) {
        continue
      }
      if (accessory.isCardMessage) {
        product.cards.push(ProductTransformer.toGQL(accessory, this.context))
      } else {
        product.accessories.push(ProductTransformer.toGQL(accessory, this.context))
      }
    }

    if (this.apiData.accessory) {
      product.__typename = 'Accessory'

      product.isCardMessage = Boolean(this.apiData.isCardMessage)

      for (const category of this.apiData.categories || []) {
        product.categories.push(AccessoryCategoryTransformer.toGQL(category, this.context))
      }

      switch (this.apiData.addMode) {
        case 'AUTO': // ex code = UB
          product.addToCart = CONST.product.accessoryAddTypes.auto
          break
        case 'SUGGEST':
          product.addToCart = CONST.product.accessoryAddTypes.suggest
          break
        default:
          product.addToCart = CONST.product.accessoryAddTypes.standard
          break
      }

      product._skipAvailabilityOn = [CONST.country.fr]
    }

    product.isFromWorkshop = Boolean(this.apiData.bebloom)

    product.code = this.apiData.code
    product.name = this.apiData.name || this.apiData.description || ''

    product.classification = CONST.product.productClassifications.NONE
    product.classifications = (this.apiData.classifications || []).map((apiClassification) =>
      ClassificationTransformer.toGQL(apiClassification, this.context)
    )

    if (this.apiData.url) {
      // nb: subscription products have complex slug segments, i.e. /p/abonnement/abonnement-bouquet-fleuriste/trois-mois/tous-mois/1/fr
      const arr = this.apiData.url.split('/')

      // remove /p/ prefix
      arr.shift()
      arr.shift()

      let zone = arr.pop()
      let size = arr.pop()

      let slug = arr.join('/')

      // Subscription product url
      if (arr.length >= 4) {
        arr.shift() // remove the '/abonnement/' part
        slug = arr.shift()
      }

      if (slug === '') {
        // happens in case the product is out of stock, the url is then /p/<code>/<size>
        slug = size
        size = zone
        zone = config.apps[this.context.siteId].defaultZone
      }

      product.slugUrl = slug
      product._defaultVariantSize = size
      product._defaultVariantZone = zone
    }

    product.hasCustomText = this.apiData.isRubanDeuil || false
    product.isOnline = !this.apiData.currentStep || this.apiData.currentStep === CONST.product.steps.online
    product.discount = this.apiData.pricePromo && PriceTransformer.toGQL(this.apiData.pricePromo)

    product.description = this.apiData.description
    product.descriptionTitle = this.apiData.descriptionTitle
    product.externalUrl = this.apiData.externalProductUrl
    product.mainCategoryId = this.apiData.mainCategory

    if (this.apiData.productLabel) {
      product.highlightText = this.apiData.productLabel.code || this.apiData.productLabel.text
    }

    product.deliveryDetail = this.apiData.deliveryDetail

    const variantImages = []
    if (this.apiData.images) {
      for (const image of this.apiData.images) {
        if (!image.altText) {
          image.altText = product.name
        }
        if (image.format && /-(\d+)$/.test(image.format)) {
          // ex: mobile-2 ; we have a variant image
          variantImages.push(image)
        } else if (image.url) {
          product.addImage(ImageTransformer.toGQL(image, this.context))
        }
      }
    } else if (this.apiData.accessory && this.apiData.image) {
      // accessory images are just given as a url
      product.addImage(
        ImageTransformer.toGQL(
          {
            altText: product.name,
            format: 'mobile',
            imageType: 'PRIMARY',
            url: this.apiData.image,
          },
          this.context
        )
      )
    }

    product.legalNotice = this.apiData.legalNotices

    if (product.isOnline && this.apiData.price) {
      product.price = PriceTransformer.toGQL(this.apiData.price)
    }

    product.summary = this.apiData.summary
    product.type = _.invert(CONST.product.types)[this.apiData.productType]
      ? this.apiData.productType
      : CONST.product.types.interflora

    product.additionalInfo = this.apiData.additionalInfo && AdditionalInfoTransformer.toGQL(this.apiData.additionalInfo)

    for (const reinsurance of this.apiData.reassurancesInfo || []) {
      product.reinsurances.push({
        icon: reinsurance.icon,
        label: reinsurance.content,
      })
    }

    for (const variant of this.apiData.variants || []) {
      const gqlVariant = ProductVariantTransformer.apiVariantToGQL(variant, this.context, {
        parentProduct: product,
        product: this.apiData,
      })

      // if the variant does not share the same base product - force a reload of images
      if (gqlVariant.code.split('-')[0] !== product.code.split('-')[0]) {
        delete gqlVariant.images
      }

      if (!product.useQuantitySelector) {
        const qualifier = gqlVariant.qualifiers.find(
          (qualifier) => qualifier.type.value === CONST.product.qualifierTypes.priceVariantType
        )
        if (qualifier && Number.parseInt(qualifier.value) > 3) {
          // hard-coded
          product.useQuantitySelector = true
        }
      }

      product.variants.push(gqlVariant)
    }

    // no variant - set ourself as one to please the front
    if (product.variants.length === 0) {
      const variant = ProductVariantTransformer.apiProductToGQL(this.apiData, this.context, {
        parent: product,
      })
      product.variants.push(variant)
    }

    const variant = this.options.variantCode ? product.variants.find((v) => v.code === this.options.variantCode) : null
    product.accessories = _.get(variant ?? product.defaultVariantLowCost, 'accessories', [])
    product.cards = product.accessories.filter((x) => x.isCardMessage && !!x.price)
    if (product.variants[0].cards.length > 1) {
      product.card = product.variants[0].cards.find((card) => card.price?.value === 0)
    } else {
      product.card = product.variants[0].cards[0]
    }
    product.accessories = product.accessories.filter((x) => !x.isCardMessage)
    // add variant images
    for (const apiImage of variantImages) {
      const format = apiImage.format.split('-')[0]
      const size = apiImage.format.split('-').pop() // '2', '3', etc.

      // we can have several variants for the same size (i.e. CODE-2-FR, CODE-2-CORSE)
      for (const variant of product.variants.filter((v) => v.code.split('-')[1] === size)) {
        const image = ImageTransformer.toGQL({ ...apiImage, format }, this.context)
        // add specific image
        variant.images.push(image)

        // add common images
        for (const commonImage of product.getImages()) {
          if (commonImage.format !== image.format && commonImage.type !== image.type) {
            variant.images.push(commonImage)
          }
        }
      }
    }

    product.setLoaded(CONST.dataSources.hybris)

    return product
  }
}

module.exports = ProductTransformer
