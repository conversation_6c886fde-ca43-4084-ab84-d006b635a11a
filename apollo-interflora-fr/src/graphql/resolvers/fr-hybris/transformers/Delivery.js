const config = require('../../../../config')
const CONST = require('../../../../const')
const GQLDelivery = require('../../../models/Delivery')

const AddressTransformer = require('./Address')
const ContactTransformer = require('./Contact')
const PriceTransformer = require('./Price')
const Transformer = require('./Transformer')

const { addTZOffsetToDate } = require('../../../../helpers/dates')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APICart | APIOrder, GQLDelivery, {}>}
 */
class DeliveryTransformer extends Transformer {
  /**
   * @param {APICart | APIOrder} apiData
   * @param {GraphQLContext} context
   * @returns {GQLDelivery}
   */
  static toGQL(apiData, context) {
    return new DeliveryTransformer().setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLDelivery}
   */
  transformToGQL() {
    const delivery = new GQLDelivery()

    if (this.apiData.deliveryDiscounts && this.apiData.deliveryDiscounts.value > 0) {
      delivery.discount = PriceTransformer.toGQL(this.apiData.deliveryCost).substract(
        PriceTransformer.toGQL(this.apiData.deliveryDiscounts)
      )
    }

    // availableDateRanges has a custom resolver
    delivery.price = PriceTransformer.toGQL(this.apiData.deliveryCost)
    if (CONST.order.deliveryStatus[this.apiData.status]) {
      delivery.status = /^\d+$/.test(this.apiData.status) ? '_' + this.apiData.status : this.apiData.status
    } else {
      delivery.status = CONST.order.deliveryStatus.UNKNOWN
    }
    delivery.useFixHour = this.apiData.ceremony

    if (this.apiData.moment) {
      delivery.rangeHour = {
        available: true,
        id: this.apiData.moment,
        label: this.apiData.momentLabel,
      }
    }

    if (this.apiData.deliveryAddress) {
      delivery.address = AddressTransformer.toGQL(this.apiData.deliveryAddress, this.context, {
        type: CONST.address.addressType.delivery,
      })
    }

    if (this.apiData.billingAddress) {
      delivery.sender = AddressTransformer.toGQL(this.apiData.billingAddress, this.context, {
        type: CONST.address.addressType.sender,
      })
    } else if (this.apiData.customer && this.apiData.customer.defaultAddress) {
      delivery.sender = AddressTransformer.toGQL(this.apiData.customer.defaultAddress, this.context, {
        type: CONST.address.addressType.sender,
      })
      delivery.sender.email = this.apiData.customer.email
    } else if (this.apiData.customer) {
      // no billing address at all - use customer info to build one
      delivery.sender = AddressTransformer.toGQL(
        {
          defaultAddress: false,
          email: this.apiData.customer.email,
          lastName: this.apiData.customer.name,
          title: this.apiData.customer.titleCode,
          visibleInAddressBook: false,
        },
        this.context,
        { type: CONST.address.addressType.sender }
      )
    }

    if (this.apiData.contactLastName) {
      delivery.contact = ContactTransformer.toGQL(this.apiData, this.context)
    }

    if (this.apiData.deliveryDate) {
      const [day, month, year] = this.apiData.deliveryDate.split('/')
      delivery.date = `${year}-${month}-${day}`
      let localTimeString = '20:00'

      if (this.apiData.deliveryHour) {
        delivery.time = {
          hour: Number.parseInt(this.apiData.deliveryHour),
          minute: (this.apiData.deliveryMinute && Number.parseInt(this.apiData.deliveryMinute)) || 0,
        }
        localTimeString = [delivery.time.hour, delivery.time.minute].map((val) => `${val}`.padStart(2, '0')).join(':')
      } else if (this.apiData.moment) {
        const momentTimes = config.apps[config.apps.defaultSiteId].momentTimes?.[this.apiData.moment]
        if (momentTimes) {
          localTimeString = `${`${momentTimes[1]}`.padStart(2, '0')}:00`
        }
      }

      if (!this.apiData.status) {
        delivery.status = CONST.cart.deliveryStatus.OK

        // check if delivery date is expired
        const localeDateTime = addTZOffsetToDate(
          new Date(`${delivery.date}T${localTimeString}:00.000Z`),
          this.context.appConfig.timezone
        )
        if (localeDateTime < new Date(Date.now() + config.availability.minCutOffDuration)) {
          delivery.status = CONST.cart.deliveryStatus.EXPIRED
        }
      }
    }

    delivery.statusFallbackLabel = this.apiData.statusDisplay

    if (this.apiData.deuil) {
      delivery.mode = CONST.cart.mourningDeliveryModeMap[this.apiData.deliveryAddress?.natureAddress]
    }

    if (!delivery.mode) {
      switch (this.apiData.orderType) {
        case CONST.cart.orderType.NO_ADDRESS:
          delivery.mode = CONST.cart.deliveryMode.noAddress
          if (this.apiData.dateNotificationIfloraSms) {
            const [day, month, year] = this.apiData.dateNotificationIfloraSms.split('/')
            delivery.dateSmsNotification = `${year}-${month}-${day}`
          }
          break

        case CONST.cart.orderType.SELF:
          delivery.mode = CONST.cart.deliveryMode.self
          break

        case CONST.cart.orderType.STANDARD:
          delivery.mode = CONST.cart.deliveryMode.standard
          break

        default:
          delivery.mode = null
          break
      }
    }

    const consignmentWithTracking = (this.apiData.consignments || []).find((consignment) =>
      Boolean(consignment.trackingID)
    )

    if (consignmentWithTracking) {
      delivery.trackingId = consignmentWithTracking.trackingID
    }

    delivery.trackingUrl = this.apiData.trackingUrl

    return delivery
  }
}

module.exports = DeliveryTransformer
