const CONST = require('../../../../const')
const GQLPaymentMethod = require('../../../models/PaymentMethod')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIPaymentMethod, GQLPaymentMethod, { apiCart: APICart }>}
 */
class PaymentMethodTransformer extends Transformer {
  /**
   * @param {APIPaymentMethod} apiData
   * @param {{ apiCart: APICart }} options
   * @returns {GQLPaymentMethod}
   */
  static toGQL(apiData, options) {
    return new PaymentMethodTransformer(options).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLPaymentMethod}
   */
  transformToGQL() {
    const paymentMethod = new GQLPaymentMethod()
    paymentMethod.encrypted = this.apiData.paymentEncryptedData
    paymentMethod.mode = CONST.payment.paymentTypeMapping[this.apiData.code]
    paymentMethod.provider = this.apiData.paymentProvider
    paymentMethod.availableForCart = paymentMethod.matchesApiCart(this.options.apiCart)

    return paymentMethod
  }
}

module.exports = PaymentMethodTransformer
