const CONST = require('../../../../const')
const GQLStock = require('../../../models/Stock')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIStock, GQLStock, {}>}
 */
class StockTransformer extends Transformer {
  /**
   * @param {APIStock} apiData
   * @returns {GQLStock}
   */
  static toGQL(apiData) {
    return new StockTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @override - to allow undefined source data
   * @returns {GQLStock}
   */
  toGQL() {
    return this.transformToGQL()
  }

  transformToGQL() {
    const stock = new GQLStock()

    if (!this.apiData) {
      stock.status = CONST.product.stockStatuses.unlimited
      return stock
    }

    stock.status = CONST.product.stockStatuses[this.apiData.stockLevelStatus]
    stock.value = this.apiData.stockLevel

    if (stock.status === CONST.product.stockStatuses.inStock && !stock.value) {
      stock.status = CONST.product.stockStatuses.unlimited
    }

    return stock
  }
}

module.exports = StockTransformer
