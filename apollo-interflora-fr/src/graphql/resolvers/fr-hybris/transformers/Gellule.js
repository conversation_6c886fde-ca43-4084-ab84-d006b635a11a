const { GQLGellule } = require('../../../models')

const Transformer = require('./Transformer')
const UrlTransformer = require('./Url')

/**
 * @typedef {{
 * }} GelluleTransformerOptions
 */

/**
 * @extends {Transformer<APIMenuComponent[], GQLGelluleType, GelluleTransformerOptions>}
 */
class GelluleTransformer extends Transformer {
  transformToGQL() {
    const gellules = []

    for (const component of this.apiData) {
      if (component.navigationNode?.children) {
        for (const node of component.navigationNode?.children ?? []) {
          if (!node.link) {
            continue
          }

          const gellule = new GQLGellule()
          gellule.icon = node.link.cssClass
          gellule.label = node.link.linkName
          gellule.slug = node.link.url.replace(/^\/\w\//, '')
          gellule.url = UrlTransformer.toGQL(node.link)

          gellules.push(gellule)
        }
      }
    }

    return gellules
  }

  /**
   * @param {APIMenuComponent[]} apiData
   * @param {GraphQLContext} context
   * @param {GelluleTransformerOptions} options
   * @returns {GQLGellule[]}
   */
  static toGQL(apiData, context, options = {}) {
    return new GelluleTransformer(options).setAPIData(apiData).setContext(context).transformToGQL()
  }
}

module.exports = GelluleTransformer
