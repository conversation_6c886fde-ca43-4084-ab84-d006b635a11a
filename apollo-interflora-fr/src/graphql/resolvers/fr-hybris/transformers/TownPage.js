const _ = require('lodash')

const GQLTownPage = require('../../../models/TownPage')

const ImageTransformer = require('./Image')
const Transformer = require('./Transformer')

/** @typedef {import('../datasources')} */
/** @typedef {import('../types/_typedef')} */

/**
 * @extends {Transformer<APIFloraCity, GQLTownPage, {}>}
 */
class TownPageTransformer extends Transformer {
  /**
   * @param {APIFloraCity} apiData
   * @param {GraphQLContext} context
   * @returns {GQLTownPage}
   */
  static toGQL(apiData, context) {
    return new TownPageTransformer().setContext(context).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLTownPage}
   */
  transformToGQL() {
    const page = new GQLTownPage()

    const titledCityName = this.apiData.cityName
      .split(/\b/)
      .map((term) => _.upperFirst(term.toLowerCase()))
      .join(' ')
      .replace(/  +/g, ' ')

    page.description = this.apiData.contentBis
    page.detail = this.apiData.contentTer
    page.title = this.apiData.titleCity

    if (this.apiData.urlImage) {
      page.image = ImageTransformer.toGQL(
        {
          altText: titledCityName,
          format: 'mobile',
          imageType: 'PRIMARY',
          url: this.apiData.urlImage,
        },
        this.context
      )
    }

    return page
  }
}

module.exports = TownPageTransformer
