const GQLLink = require('../../../models/Link')
const GQLRoute = require('../../../models/Route')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APICMSLink | APIBreadcrumb, GQLUrlUnion, {countryId: string, isBreadcrumb: boolean}>}
 */
class UrlTransformer extends Transformer {
  /**
   * @param {APICMSLink | APIBreadcrumb} apiData
   * @param {GraphQLContext} context
   * @param {{countryId: string, isBreadcrumb: boolean}} [options]
   * @returns {GQLUrlUnion}
   */
  static toGQL(apiData, context, options = { isBreadcrumb: false }) {
    return new UrlTransformer(options).setContext(context).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLUrlUnion}
   */
  transformToGQL() {
    const { isBreadcrumb } = this.options

    const isRoute =
      Boolean(this.apiData.categoryCode) || Boolean(this.apiData.contentPageLabel) || Boolean(this.apiData.productCode)

    if (isRoute || isBreadcrumb) {
      const route = new GQLRoute()

      if (this.apiData.categoryCode) {
        if (this.options.countryId) {
          route.params.push({
            name: 'countryId',
            value: this.options.countryId,
          })
        }

        const parts = this.apiData.url.replace(/^(\/\w+)?\/c\//, '').split('/')

        if (this.apiData.categoryParentCode || parts[1]) {
          route.name = 'parent_category'

          route.params.push({
            name: 'slugCategory',
            value: parts.pop(),
          })

          route.params.push({
            name: 'slugParentCategory',
            value: parts.pop(),
          })
        } else {
          route.name = 'category'

          route.params.push({
            name: 'slugCategory',
            value: parts[0],
          })
        }
      } else if (this.apiData.contentPageLabel) {
        switch (this.apiData.contentPageLabel) {
          case 'index':
          case 'userMyAccount':
          case 'floristDepartments':
          case 'floristAddressDepartments':
            route.name = this.apiData.contentPageLabel
            break

          default:
            route.name = 'cms'
            route.params.push({ name: 'slug', value: this.apiData.contentPageLabel.replace(/^\//, '') })
            break
        }
      } else if (this.apiData.productCode) {
        const productSplit = this.apiData.url
          .replace(/^\/\w+\//, '')
          .split('/')
          .filter((word) => word !== '')

        route.name = 'product'
        route.params.push({
          name: 'slugProduct',
          value: productSplit[0],
        })

        for (let i = 1; i < productSplit.length; i++) {
          route.params.push({
            name: `variant${i}`,
            value: productSplit[i],
          })
        }
      } else if (this.apiData.departmentSlugUrl) {
        route.name = 'floristDepartment'
        route.params.push({
          name: 'slugUrl',
          value: this.apiData.departmentSlugUrl.replace(/^\//, ''),
        })
      }

      if (this.apiData.highlight) {
        route.params.push({
          name: 'highlighted',
          value:
            typeof this.apiData.highlight === 'boolean'
              ? this.apiData.highlight
              : `${this.apiData.highlight}`.toLowerCase() === 'true', // RouteParams values are strings
        })
      }
      if (this.apiData.linkName) {
        route.params.push({
          name: 'label',
          value: this.apiData.linkName,
        })
      }
      if (this.apiData.target === 'newWindow') {
        route.params.push({
          name: 'target',
          value: 'new',
        })
      }
      if (this.apiData.url) {
        route.params.push({
          name: 'url',
          value: this.apiData.url,
        })
      }

      return route.name ? route : null
    }

    const link = new GQLLink()

    link.href = this.apiData.url || '#'
    link.targetBlank = this.apiData.target === 'newWindow'

    return link
  }
}

module.exports = UrlTransformer
