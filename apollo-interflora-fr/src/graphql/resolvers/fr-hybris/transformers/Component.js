const CONST = require('../../../../const')

const GQLComponent = require('../../../models/Component')
const GQLScalarBoolean = require('../../../models/ScalarBoolean')
const GQLScalarString = require('../../../models/ScalarString')
const GQLSourceImages = require('../../../models/SourceImages')

const ImageTransformer = require('./Image')
const Transformer = require('./Transformer')
const UrlTransformer = require('./Url')

/** @typedef {import('../datasources')} */

/** @typedef {import('../types/_typedef')} */

/**
 * @extends {Transformer<APICMSComponent, GQLComponent, {mediaBaseUrl: string?}>}
 */
class ComponentTransformer extends Transformer {
  /**
   *
   * @param {APICMSComponent} component
   * @param {GraphQLContext} context
   * @param {{mediaBaseUrl: string?}} options
   * @returns {GQLComponent}
   */
  static toGQL(component, context, options = {}) {
    return new ComponentTransformer(options).setAPIData(component).setContext(context).toGQL()
  }

  transformToGQL() {
    const component = new GQLComponent()

    component.name = CONST.cms.apiPositions[this.apiData.template] || this.apiData.template

    // mutate component params
    this._parse(component)

    // merge component name "slider_product"
    if (
      [
        CONST.cms.positions.slider_product1,
        CONST.cms.positions.slider_product2,
        CONST.cms.positions.slider_product3,
      ].includes(component.name)
    ) {
      component.name = CONST.cms.positions.slider_product
    }

    return component
  }

  /**
   * @param {APICMSBanner} banner
   * @param {GQLComponent?} component
   * @returns {{
   *  description: string,
   *  icon: string,
   *  image: GQLSourceImages,
   *  link: GQLUrlUnion,
   *  subtitle: string,
   *  title: string,
   * }}
   * @private
   */
  _bannerToGQL(banner, component) {
    const result = {}

    if (banner.content) {
      result.description = new GQLScalarString(banner.content)
    }

    if (banner.cssClass) {
      result.icon = new GQLScalarString(banner.cssClass)
    }

    if (banner.image) {
      let imageUrl
      // The `image` field on hybris side can either be mapped to a String or a media object
      if (typeof banner.image === 'string') {
        imageUrl = banner.image
      } else if (banner.image.url) {
        imageUrl = banner.image.url
      }

      if (imageUrl) {
        result.image = new GQLSourceImages()
        result.image.sources = [
          ImageTransformer.toGQL(
            {
              altText: banner.title || banner.subTitle || '',
              format: 'mobile',
              imageType: component.name || 'PRIMARY',
              url: imageUrl,
            },
            this.context,
            { mediaBaseUrl: this.options.mediaBaseUrl }
          ),
        ]
      }
    }

    if (banner.link && banner.link.url) {
      result.link = UrlTransformer.toGQL(banner.link, this.context)
      result.link_label = new GQLScalarString(banner.link.linkName)
    }

    if (banner.subTitle) {
      result.subtitle = new GQLScalarString(banner.subTitle)
    }

    if (banner.title) {
      result.title = new GQLScalarString(banner.title)
    }

    return result
  }

  /**
   * @param {{
   *  description: string,
   *  icon: string,
   *  image: GQLImage,
   *  link: GQLUrlUnion,
   *  subtitle: string,
   *  title: string,
   * }[]} banners
   * @returns {GQLComponentParamType[]}
   * @private
   */
  _bannersToGQL(banners) {
    const acc = {
      description: [],
      icon: [],
      image: [],
      link: [],
      link_label: [],
      subtitle: [],
      title: [],
    }

    for (const banner of banners) {
      for (const key of Object.keys(acc)) {
        acc[key].push(banner[key] === undefined ? null : banner[key])
      }
    }

    const params = []
    for (const [key, values] of Object.entries(acc)) {
      params.push({
        name: `${key}s`,
        value: values,
      })
    }

    return params
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate} apiData
   * @private
   */
  _bannerContainerToGQL(component, apiData = this.apiData) {
    if (apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(apiData.title)],
      })
    }
    if (apiData.content) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(apiData.content)],
      })
    }
    if (apiData.link && apiData.link.url) {
      component.params.push({
        name: 'button_label',
        value: [new GQLScalarString(apiData.link.linkName || apiData.title)],
      })
      component.params.push({
        name: 'button_link',
        value: [UrlTransformer.toGQL(apiData.link, this.context)],
      })
    }

    if (apiData.image) {
      let imageUrl
      // The `image` field on hybris side can either be mapped to a String or a media object
      if (typeof apiData.image === 'string') {
        imageUrl = apiData.image
      } else if (apiData.image.url) {
        imageUrl = apiData.image.url
      }

      if (imageUrl) {
        let image = new GQLSourceImages()
        image.sources = [
          ImageTransformer.toGQL(
            {
              altText: apiData.title || apiData.subTitle || '',
              format: 'mobile',
              imageType: component.name || 'PRIMARY',
              order: 0,
              url: imageUrl,
            },
            this.context
          ),
        ]

        component.params.push({
          name: 'mainImage',
          value: [image],
        })
      }
    }

    for (const param of this._bannersToGQL(
      (apiData.components || []).map((banner) => this._bannerToGQL(banner, component))
    )) {
      component.params.push(param)
    }
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate} apiData
   * @private
   */
  _parse(component, apiData = this.apiData) {
    switch (apiData.typeCode) {
      case CONST.cms.apiComponentTypes.BannerComponent:
        for (const [name, value] of Object.entries(this._bannerToGQL(apiData, component))) {
          component.params.push({
            name,
            value: [value],
          })
        }
        break
      case CONST.cms.apiComponentTypes.CMSImageComponent:
        if (!component.name) {
          component.name = 'image'
        }
        this._imageToGQL(component, apiData)
        break
      case CONST.cms.apiComponentTypes.CMSLinkComponent:
        if (!component.name) {
          component.name = 'link'
        }
        component.params.push({
          name: 'link',
          value: [UrlTransformer.toGQL(apiData, this.context)],
        })
        break
      case CONST.cms.apiComponentTypes.CMSParagraphComponent:
        if (!component.name) {
          component.name = 'content'
        }
        this._wysiwygToGQL(component, apiData)
        break
      case CONST.cms.apiComponentTypes.IframeComponent:
        component.name = 'iframe'

        component.params.push({
          name: 'resized',
          value: [new GQLScalarBoolean(Boolean(apiData.resized))],
        })
        component.params.push({
          name: 'url',
          value: [new GQLScalarString(apiData.url)],
        })

        break
      case CONST.cms.apiComponentTypes.MomentFirstBannerComponent:
        if (
          ![CONST.cms.positions.home_block, CONST.cms.positions.hero, CONST.cms.positions.moment_first].includes(
            component.name
          )
        ) {
          component.name = CONST.cms.positions.hero
        }
        this._momentFirstToGQL(component)
        break
      case CONST.cms.apiComponentTypes.PopupComponent:
        component.name = 'popup'

        // PopupComponent extends BannerComponent
        for (const [name, value] of Object.entries(this._bannerToGQL(apiData))) {
          component.params.push({
            name,
            value: [value],
          })
        }
        if (apiData.closeLabel) {
          component.params.push({
            name: 'close_label',
            value: [new GQLScalarString(apiData.closeLabel)],
          })
        }
        break
      case CONST.cms.apiComponentTypes.CategoryFeatureComponent:
      case CONST.cms.apiComponentTypes.ProductCarouselComponent:
        if (!component.name) {
          component.name = 'slider_product'
        }
        this._productCarouselToGQL(component)
        break
      case CONST.cms.apiComponentTypes.RotatingImagesComponent:
        this._bannerContainerToGQL(component)
        break
      case CONST.cms.apiComponentTypes.NavigationBarComponent:
        component.params = this._navigationBarToGQL(component, apiData)
        break
    }
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSImage} apiData
   * @private
   */
  _imageToGQL(component, apiData) {
    const imageSet = new GQLSourceImages()
    imageSet.sources.push(
      ImageTransformer.toGQL(
        {
          altText: apiData.title || apiData.subTitle || '',
          format: 'mobile',
          imageType: CONST.cms.positions[component.name] || 'PRIMARY',
          url: apiData.url,
        },
        this.context
      )
    )

    component.params.push({
      name: 'image',
      value: [imageSet],
    })
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate<APICMSMomentFirst>} apiData
   * @private
   */
  async _momentFirstToGQL(component, apiData = this.apiData) {
    for (const [name, value] of Object.entries(this._bannerToGQL(apiData, component))) {
      component.params.push({
        name,
        value: [value],
      })
    }

    if (Array.isArray(apiData.links)) {
      const acc = {
        link_labels: [],
        links: [],
      }

      for (const apiLink of apiData.links) {
        acc.link_labels.push(apiLink.linkName ? new GQLScalarString(apiLink.linkName, this.context) : undefined)
        acc.links.push(UrlTransformer.toGQL(apiLink, this.context))
      }

      for (const [name, values] of Object.entries(acc)) {
        component.params.push({
          name,
          value: values,
        })
      }
    }
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate<{ navigationNode: APINavigationNode }>} apiData
   * @returns {GQLComponentType[]}
   * @private
   */
  _navigationBarToGQL(component, apiData = this.apiData) {
    const node = apiData.navigationNode || {}

    const params = []

    if (node.title) {
      params.push({
        name: 'title',
        value: [new GQLScalarString(node.title)],
      })
    }

    if (node.link && node.link.url) {
      params.push({
        name: 'link',
        value: [UrlTransformer.toGQL(node.link, this.context)],
      })
    }

    return params
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate<APICMSProductCarousel>} apiData
   * @returns {GQLComponent}
   * @private
   */
  _productCarouselToGQL(component, apiData = this.apiData) {
    if (apiData.link) {
      component.params.push({
        name: 'button_label',
        value: [new GQLScalarString(apiData.link.linkName)],
      })
      component.params.push({
        name: 'button_link',
        value: [UrlTransformer.toGQL(apiData.link, this.context)],
      })
    }

    // do NOT use the product transformer to force using product attributes resolvers
    component.params.push({
      name: 'products',
      value: (apiData.products || []).map((apiProduct) => ({ ...apiProduct, __typename: 'Product' })),
    })

    if (apiData.content) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(apiData.content)],
      })
    }
    if (apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(apiData.title)],
      })
    }

    return component
  }

  /**
   * @param {GQLComponent} component
   * @param {APICMSTemplate<APICMSParagraph>} apiData
   * @private
   */
  _wysiwygToGQL(component, apiData = this.apiData) {
    if (apiData.apiName) {
      component.params.push({
        name: 'apiName',
        value: [new GQLScalarString(apiData.apiName)],
      })
    }

    // for the time being, highlight is returned as a 'false'|'true' string
    component.params.push({
      name: 'highlight',
      value: [
        new GQLScalarBoolean(
          typeof apiData.highlight === 'boolean' ? apiData.highlight : `${apiData.highlight}`.toLowerCase() === 'true'
        ),
      ],
    })

    if (apiData.title) {
      component.params.push({
        name: 'title',
        value: [new GQLScalarString(apiData.title)],
      })
    }
    if (apiData.subTitle) {
      component.params.push({
        name: 'subtitle',
        value: [new GQLScalarString(apiData.subTitle)],
      })
    }
    if (apiData.content) {
      component.params.push({
        name: 'content',
        value: [new GQLScalarString(apiData.content)],
      })
    }
  }
}

module.exports = ComponentTransformer
