const config = require('../../../../config')

const SolrProductTransformer = require('./SolrProduct')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<null, GQLProduct[], {}>}
 */
class LinkedProductsTransformer extends Transformer {
  /**
   * @param {{
   *  responseHeader: object,
   *  response: {
   *    numFound: Number,
   *    start: Number,
   *    docs: object[]
   *  }
   * }} solrData
   * @returns {GQLProduct[]}
   */
  static toGQL(solrData) {
    return new LinkedProductsTransformer().setSolrData(solrData).toGQL()
  }

  /**
   * @returns {GQLLinkedProducts}
   */
  transformToGQL() {
    const {
      response: { docs: solrProducts },
    } = this.solrData

    if (solrProducts.length < config.products.linked.min) {
      return []
    }

    return solrProducts.map(SolrProductTransformer.toGQL)
  }
}

module.exports = LinkedProductsTransformer
