const { GQLBreadcrumb } = require('../../../models')

const Transformer = require('./Transformer')
const UrlTransformer = require('./Url')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIBreadcrumb, GQLBreadcrumb, { productCategory: APICategory, type: string }>}
 */
class BreadcrumbTransformer extends Transformer {
  /**
   * @param {APIBreadcrumb} apiData
   * @param {GraphQLContext} context
   * @param {{ productCategory: APICategory, type: string }} options
   * @returns {GQLBreadcrumb}
   */
  static toGQL(apiData, context, options) {
    return new BreadcrumbTransformer(options).setContext(context).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLBreadcrumb}
   */
  transformToGQL() {
    const breadcrumb = new GQLBreadcrumb()

    breadcrumb.label = this.apiData.label

    let countryId
    if (this.apiData.url) {
      const matches = this.apiData.url.match(/^\/([a-z]{2}|[A-Z]{2})\//)

      if (matches) {
        countryId = matches[1]
      }
    }

    if (this.apiData.categoryCode && this.options.type === 'product' && this.options.productCategory) {
      breadcrumb.label = this.options.productCategory.name

      breadcrumb.link = UrlTransformer.toGQL(
        {
          ...this.apiData,
          categoryCode: this.options.productCategory.code,
          url: this.options.productCategory.url,
        },
        this.context,
        { countryId, isBreadcrumb: true }
      )
    } else {
      breadcrumb.link = UrlTransformer.toGQL(this.apiData, this.context, { countryId, isBreadcrumb: true })
    }

    return breadcrumb
  }
}

module.exports = BreadcrumbTransformer
