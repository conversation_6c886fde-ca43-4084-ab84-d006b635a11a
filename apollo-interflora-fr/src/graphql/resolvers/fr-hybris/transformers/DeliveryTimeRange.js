const { GQLDeliveryTimeRange } = require('../../../models')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIMomentLivraison, GQLDeliveryTimeRange, {}>}
 */
class DeliveryTimeRangeTransformer extends Transformer {
  /**
   * @param {APIMomentLivraison} apiData
   * @param {{isDefault:boolean}} options
   * @returns {GQLBreadcrumb}
   */
  static toGQL(apiData, options) {
    return new DeliveryTimeRangeTransformer(options).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GraphQLDeliveryTimeRange}
   */
  transformToGQL() {
    const deliveryTimeRange = new GQLDeliveryTimeRange()

    deliveryTimeRange.available = this.apiData.enabled
    deliveryTimeRange.id = this.apiData.code
    deliveryTimeRange.label = this.apiData.label
    deliveryTimeRange.isDefault = this.options.isDefault

    return deliveryTimeRange
  }
}

module.exports = DeliveryTimeRangeTransformer
