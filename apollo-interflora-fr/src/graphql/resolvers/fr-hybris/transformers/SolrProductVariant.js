const _ = require('lodash')

const CONST = require('../../../../const')
const { GQLSolrProductVariant } = require('../../../models')

const PriceTransformer = require('./Price')
const StockTransformer = require('./Stock')
const Transformer = require('./Transformer')

/**
 * @extends {Transformer<null, GQLSolrProductVariant, {doc: object, zone: string?}>}
 */
class SolrProductVariantTransformer extends Transformer {
  /**
   * @param {object} solrData
   * @param {{ doc: object, zone: string }} options
   * @returns {GQLSolrProductVariant}
   */
  static toGQL(solrData, options) {
    return new SolrProductVariantTransformer(options).setSolrData(solrData).toGQL()
  }

  transformToGQL() {
    const variant = new GQLSolrProductVariant()

    variant.code = this.solrData.productVariantCode
    variant.label = this.solrData.productVariantName

    variant.delivery = {
      available: true,
      express: Boolean(this.options.doc.deliveryExpress_boolean),
      price: {
        currencyIso: 'N/A',
        value: 0,
      },
      type:
        this.options.doc.deliveryMode_string === 'fleuriste'
          ? CONST.product.deliveryTypes.florist
          : CONST.product.deliveryTypes.carrier,
    }

    variant.discount = null // must be !== undefined to avoid fetching the production from the api
    if (this.solrData.discountMin) {
      variant.discount = PriceTransformer.toGQL({
        currencyIso: 'EUR',
        value: this.solrData.discountMin,
      })
    }

    if (this.solrData.priceValue) {
      variant.price = PriceTransformer.toGQL({
        currencyIso: 'EUR',
        value: this.solrData.priceValue,
      })
    }

    variant.minQuantity = this.solrData.volumePrice

    {
      // Example: /p/bouquet-fleuriste-multicolore/1/CORSE
      const arr = this.solrData.productSlugUrl.split('/')
      arr.pop() // zone
      const size = arr.pop() // size

      // drop /p/ prefix
      arr.shift()
      arr.shift()

      const slug = arr.join('/')

      variant.qualifiers = [
        {
          orderSlug: 0,
          slugUrlPart: slug,
          type: {
            name: CONST.product.qualifierTypes.slug,
            value: CONST.product.qualifierTypes.slug,
          },
          value: slug,
        },
        {
          orderSlug: 1,
          slugUrlPart: size,
          type: {
            name: 'Niveau prix', // @todo
            value: CONST.product.qualifierTypes.priceVariantType,
          },
          value: size,
        },
        {
          orderSlug: 2,
          slugUrlPart: _.toUpper(this.options.zone),
          type: {
            name: 'Zone Géographique ?', // @todo
            value: CONST.product.qualifierTypes.zoneGeographique,
          },
          value: _.toUpper(this.options.zone),
        },
      ]
    }

    variant.stock = StockTransformer.toGQL(this.solrData.stock)

    return variant
  }
}

module.exports = SolrProductVariantTransformer
