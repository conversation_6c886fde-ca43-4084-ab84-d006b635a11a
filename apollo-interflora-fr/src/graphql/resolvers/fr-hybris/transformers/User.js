const { find, findKey, get, isEmpty } = require('lodash')

const CONST = require('../../../../const')
const GQLUser = require('../../../models/User')

const AddressTransformer = require('./Address')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIUser, GQLUser, {addresses: APIAddress[]}>}
 */
class UserTransformer extends Transformer {
  /**
   * @param {APIUser} apiData
   * @param {GraphQLContext} context
   * @param {{addresses: APIAddress[]}} [options]
   * @returns {GQLUser}
   */
  static toGQL(apiData, context, options) {
    return new UserTransformer(options).setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @param {string} apiCivility
   * @returns {string}
   */
  static toGQLCivility(apiCivility) {
    return find(CONST.user.civility, (_civ, key) => key === apiCivility)
  }

  /**
   * @param {string} toGQLCivility
   * @returns {string}
   */
  static toAPICivility(toGQLCivility) {
    return findKey(CONST.user.civility, (civ) => civ === toGQLCivility)
  }

  /**
   * @param {string} gqlType
   * @returns {string}
   */
  static toAPIType(gqlType) {
    return findKey(CONST.user.userType, (type) => type === gqlType)
  }

  /**
   * @returns {GQLUser}
   */
  transformToGQL() {
    const { addresses } = this.options

    const user = new GQLUser()

    // hide uid = email behind a false reproduceable guid
    user.id = this.apiData.uid
    user.email = this.apiData.email || this.apiData.uid.split('|').pop()

    const isProfessionnal = this.apiData.b2bCustomer || false
    user.type = isProfessionnal ? CONST.user.userType.b2b : CONST.user.userType.b2c
    user.civility = UserTransformer.toGQLCivility(this.apiData.titleCode)

    /** @type {APIAddress} */
    const defaultAddress = get(this.apiData, 'defaultAddress')

    if (defaultAddress) {
      const userAddress = AddressTransformer.toGQL(defaultAddress, this.context)

      user.addressId = userAddress.id

      for (const field of ['address', 'address2', 'company', 'country', 'phone', 'postalCode', 'town']) {
        user[field] = userAddress[field]
      }

      // use User.country resolver to get additional params - @tbc if needed
      user.countryId = defaultAddress.country && defaultAddress.country.isocode
    }

    user.firstName = this.apiData.firstName
    user.lastName = this.apiData.lastName
    user.premium = this.apiData.itfPlusExpirationDate && new Date(this.apiData.itfPlusExpirationDate)
    user.username = this.apiData.displayUid

    if (!isEmpty(addresses)) {
      user.deliveryAddresses = addresses
        .filter((address) => address.shippingAddress)
        .map((address) => AddressTransformer.toGQL(address, this.context, { type: 'DELIVERY' }))

      user.senderAddresses = addresses
        .filter((address) => !address.shippingAddress)
        .map((address) => AddressTransformer.toGQL(address, this.context, { type: 'SENDER' }))
    }

    return user
  }
}

module.exports = UserTransformer
