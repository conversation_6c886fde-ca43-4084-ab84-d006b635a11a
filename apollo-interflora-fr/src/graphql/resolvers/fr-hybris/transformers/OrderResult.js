const apm = require('elastic-apm-node')

const { GraphQLApolloError } = require('../../../errors')

const GQLOrderResult = require('../../../models/OrderResult')

const OrderTransformer = require('./Order')
const TokenTransformer = require('./Token')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIPaymentResult, GQLOrderResult, {}>}
 */
class OrderResultTransformer extends Transformer {
  /**
   * @param {APIPaymentResult} apiData
   * @param {GraphQLContext} context
   * @returns {GQLOrderResult}
   */
  static toGQL(apiData, context) {
    return new OrderResultTransformer().setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLOrderResult}
   */
  transformToGQL() {
    const orderResult = new GQLOrderResult()

    if (this.apiData.paymentResult === 'SUCESS') {
      orderResult.status = 'SUCCESS'
      orderResult.statusCode = 'SUCCESS'

      orderResult.order = OrderTransformer.toGQL(this.apiData.order, this.context)

      if (orderResult.order.user) {
        orderResult.order.user.order = orderResult.order
      }
    }

    if (this.apiData.paymentResult === 'REDIRECT') {
      orderResult.status = 'REDIRECT'
      orderResult.statusCode = 'REDIRECT'

      if (this.apiData.redirectPostParams) {
        orderResult.queryPostParams = new URLSearchParams(
          this.apiData.redirectPostParams.map(({ key, value }) => [key, value])
        ).toString()
      }
      orderResult.redirectHtml = this.apiData.redirectHTML
      orderResult.redirectUrl = this.apiData.redirectPostUrl || this.apiData.redirectUrl
      orderResult.usePost = Boolean(this.apiData.redirectPostUrl)
    }

    if (this.apiData.token) {
      orderResult.userToken = TokenTransformer.toGQL(this.apiData.token, this.context, {
        user: this.apiData.order.customer ? this.apiData.order.customer : undefined,
      })
    }

    if (this.apiData.paymentResult === 'ERROR') {
      orderResult.status = 'ERROR'
      orderResult.statusCode = this.apiData.paymentErrorCode
      orderResult.statusMessage = this.apiData.paymentErrorMessage
    }

    if (this.apiData.paymentResult === 'AWAITING_CAPTURE') {
      orderResult.status = 'AWAITING_CAPTURE'
      orderResult.statusCode = 'AWAITING_CAPTURE'
    }

    if (this.apiData.paymentResult === 'CANCELED') {
      orderResult.status = 'CANCELED'
      orderResult.statusCode = 'CANCELED'
      orderResult.statusMessage = this.apiData.paymentErrorMessage
    }

    if (!orderResult.status) {
      const error = new GraphQLApolloError('[OrderResult] Unknown error - no status found', 500)
      apm.captureError(error, { custom: error })
      this.context?.log?.error?.(this.apiData, error.message)

      orderResult.status = 'ERROR'
      if (!orderResult.statusCode) orderResult.statusCode = '500'
      if (!orderResult.statusMessage) orderResult.statusMessage = 'Unexpected error'
    }

    return orderResult
  }
}

module.exports = OrderResultTransformer
