const CONST = require('../../../../const')
const GQLTownPageTab = require('../../../models/TownPageTab')

const TemplateTransformer = require('./Template')
const Transformer = require('./Transformer')

/** @typedef {import('../datasources')} */
/** @typedef {import('../types/_typedef')} */

/**
 * @extends {Transformer<APICmsTownPageTab, GQLTownPageTab, {}>}
 */
class TownPageTabTransformer extends Transformer {
  /**
   * @param {APICmsTownPageTab} apiData
   * @returns {GQLTownPageTab}
   */
  static toGQL(apiData) {
    return new TownPageTabTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLTownPageTab}
   */
  transformToGQL() {
    const tab = new GQLTownPageTab()

    tab.icon = this.apiData.icon
    tab.label = this.apiData.label
    tab.products = this.apiData.products || []

    if (this.apiData.content) {
      tab[this.apiData.uid === 'infoLivraisonHtmlTabComponentCity' ? 'componentsBottom' : 'componentsPromo'].push(
        TemplateTransformer.toGQL(
          {
            content: this.apiData.content,
            template: CONST.cms.positions.wysiwyg,
            typeCode: CONST.cms.apiComponentTypes.CMSParagraphComponent,
          },
          this.context
        )
      )
    }

    return tab
  }
}

module.exports = TownPageTabTransformer
