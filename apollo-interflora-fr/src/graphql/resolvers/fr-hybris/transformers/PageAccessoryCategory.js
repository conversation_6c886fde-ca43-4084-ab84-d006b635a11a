const GQLPageAccessoryCategory = require('../../../models/PageAccessoryCategory')

const Transformer = require('./Transformer')

/**
 * @typedef {{}} PageAccessoryCategoryTransformerOptions
 */

/**
 * @extends {Transformer<APIMenuComponent[], GQLPageAccessoryCategory[], PageAccessoryCategoryTransformerOptions>}
 */
class PageAccessoryCategoryTransformer extends Transformer {
  transformToGQL() {
    const accessoryCategories = []
    for (const component of this.apiData) {
      if (component.navigationNode?.children) {
        for (const node of component.navigationNode?.children ?? []) {
          if (!node.link) {
            continue
          }

          const accessoryCategory = new GQLPageAccessoryCategory()

          accessoryCategory.code = node.link.categoryFilter
          accessoryCategory.icon = node.link.cssClass
          accessoryCategory.label = node.link.linkName
          accessoryCategory.navigationCategoryCode = node.link.categoryCode

          accessoryCategories.push(accessoryCategory)
        }
      }
    }

    return accessoryCategories
  }

  /**
   * @param {APIMenuComponent[]} apiData
   * @param {GraphQLContext} context
   * @param {PageAccessoryCategoryTransformerOptions} options
   * @returns {GQLPageAccessoryCategory[]}
   */
  static toGQL(apiData, context, options = {}) {
    return new PageAccessoryCategoryTransformer(options).setAPIData(apiData).setContext(context).toGQL()
  }
}

module.exports = PageAccessoryCategoryTransformer
