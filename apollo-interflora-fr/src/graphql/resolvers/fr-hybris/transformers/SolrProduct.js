const CONST = require('../../../../const')
const { GQLSolrProduct } = require('../../../models')

const PriceTransformer = require('./Price')
const SolrProductVariantTransformer = require('./SolrProductVariant')
const Transformer = require('./Transformer')

/**
 * @extends {Transformer<null, GQLSolrProduct, { zone: string? }>}
 */
class SolrProductTransformer extends Transformer {
  /**
   * @param {object} solrData - solr document
   * @param {{ zone: string? }} options
   * @returns {GQLSolrProduct}
   */
  static toGQL(solrData, options) {
    return new SolrProductTransformer(options).setSolrData(solrData).toGQL()
  }

  transformToGQL() {
    const product = new GQLSolrProduct()

    product.code = this.solrData.code_string || this.solrData.codeText_text
    product.description = this.solrData.description_text_fr
    product.name = this.solrData.name_text_fr
    product.highlightText = this.solrData.productLabelCode_fr_string
    if (this.solrData.minPriceAfterDiscount_double !== this.solrData.prixMin_double) {
      product.discount = PriceTransformer.toGQL({
        currencyIso: 'EUR',
        value: this.solrData.minPrinceAfterDiscount_double,
      })
    }
    product.price = PriceTransformer.toGQL({
      currencyIso: 'EUR',
      value: this.solrData.prixMin_double,
    })

    // @dirty - prevent nested calls to solr
    product.linked = []

    {
      // nb: subscription products have complex slug segments, i.e. /p/abonnement/abonnement-bouquet-fleuriste/trois-mois/tous-mois/1/fr
      const arr = this.solrData.url_fr_string.split('/')
      const zone = arr.pop()
      const size = arr.pop()
      // remove /p/ prefix
      arr.shift()
      arr.shift()
      const slug = arr.join('/')

      product.slugUrl = slug
      product._defaultVariantSize = size
      product._defaultVariantZone = this.options.zone || zone
    }

    {
      const solrVariants = this.solrData.priceVariant_string && JSON.parse(this.solrData.priceVariant_string)
      if (solrVariants.length === 0) {
        // no variant set - use main product
        solrVariants.push({
          variantsPricesData: [
            {
              levelPriceCode: 0,
              priceValue: this.solrData.minPriceAfterDiscount_double,
              productSlugUrl: this.solrData.url_fr_string,
              productVariantCode: product.code,
              productVariantName: product.name,
              urlProductVariant: this.solrData.url_fr_string,
            },
          ],
          zoneGeoCode: this.options.zone,
        })
      }

      for (const { zoneGeoCode, variantsPricesData } of solrVariants) {
        for (const solrVariant of variantsPricesData || []) {
          const variant = SolrProductVariantTransformer.toGQL(solrVariant, { doc: this.solrData, zone: zoneGeoCode })
          variant.parent = product
          product.variants.push(variant)
        }
      }
    }

    for (const [key, url] of Object.entries(this.solrData).filter(
      ([key]) => key.startsWith('img-') || key.startsWith('image_PRIMARY')
    )) {
      for (const type of ['GALLERY', 'PRIMARY', 'PRIMARY_THUMBNAIL']) {
        let order = 0

        if (!CONST.images.solrSize2Format[key]) {
          console.error(`missing solr image mapping for ${key}`)
          continue
        }

        for (const format of CONST.images.solrSize2Format[key][type] || []) {
          product._images.push({
            altText: this.solrData.altImg_string || '',
            format: CONST.images.formats[format],
            order: order++,
            type,
            url,
          })
        }
      }
    }

    product.type = this.solrData.typeProduit_string

    return product
  }
}

module.exports = SolrProductTransformer
