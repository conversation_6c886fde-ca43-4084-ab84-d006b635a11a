const { GQLInvalidDestination, GQLRoute } = require('../../../models')
const PriceTransformer = require('./Price')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIValidateLocalite, GQLInvalidDestination, {cart: APICart}>}
 */
class InvalidDestinationTransformer extends Transformer {
  /**
   * @param {APIValidateLocalite} apiData
   * @param {{cart: APICart}} options
   * @returns {GQLInvalidDestination}
   */
  static toGQL(apiData, options) {
    return new InvalidDestinationTransformer(options).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLInvalidDestination}
   */
  transformToGQL() {
    const { cart } = this.options

    const invalidDestination = new GQLInvalidDestination()

    let categoryRoute
    if (this.apiData.alternativeCategoryUrl) {
      const categoryUrl = new URL(`http://domain/${this.apiData.alternativeCategoryUrl.replace(/^\/+/, '')}`)

      categoryRoute = new GQLRoute()
      categoryRoute.name = 'category'
      categoryRoute.params = [
        {
          name: 'slugCategory',
          value: categoryUrl.pathname.replace(/^\/+?c\//, ''),
        },
      ]

      for (const [name, value] of categoryUrl.searchParams) {
        categoryRoute.query.push({
          name,
          value,
        })
      }

      invalidDestination.alternativeCategoryRoute = categoryRoute
    }

    for (const accessory of this.apiData.alternativeAccessories || []) {
      invalidDestination.alternativeAccessories.push({
        customText: cart.messageRubanDeuil,
        quantity: accessory.value || 1,
        removable: true,
        variant: { code: accessory.key },
        /**
         * Price is not available here, so if requested by GraphQL, it will crash (as it is mandatory).
         * Even a field resolver won't do the job because GraphQL cannot guarantee that the variant will be
         * resolved (along with the price). The only thing we could do, is to get the
         * full variant from Hybris, but that's useless for an unused field..
         *
         * So we're leaving as is for now, even if GraphQL is incorrect,
         * this field is never called, so there shouldn't be a problem.
         */
      })
    }

    if (this.apiData.alternativeProduct) {
      invalidDestination.alternativeProducts.push({
        customText: cart.messageRubanDeuil,
        discount:
          this.apiData.alternativeProduct.discount && PriceTransformer.toGQL(this.apiData.alternativeProduct.discount),
        price: PriceTransformer.toGQL(this.apiData.alternativeProduct.price),
        quantity: 1 /** @todo mono product api for now, so we're safe */,
        removable: true,
        variant: { code: this.apiData.alternativeProduct.productVariantCode },
      })
    }

    invalidDestination.isValid = this.apiData.isValid
    invalidDestination.zoneGeo = this.apiData.postalCodeZoneGeo

    return invalidDestination
  }
}

module.exports = InvalidDestinationTransformer
