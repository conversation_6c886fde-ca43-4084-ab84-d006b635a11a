const GQLOrderReason = require('../../../models/OrderReason')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIOrderReason, GQLOrderReason, {}>}
 */
class OrderReasonTransformer extends Transformer {
  /**
   * @param {APIOrderReason} apiData
   * @returns {GQLOrderReason}
   */
  static toGQL(apiData) {
    return new OrderReasonTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const orderReason = new GQLOrderReason()

    orderReason.id = this.apiData.key
    orderReason.label = this.apiData.value

    return orderReason
  }
}

module.exports = OrderReasonTransformer
