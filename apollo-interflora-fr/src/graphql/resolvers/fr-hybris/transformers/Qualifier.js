const _ = require('lodash')

const CONST = require('../../../../const')
const GQLQualifier = require('../../../models/Qualifier')

const Transformer = require('./Transformer')

const excludedQualifiers = []

{
  const qualifierTypesByValue = _.invert(CONST.product.qualifierTypes)

  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.dureeAbonnement])
  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.frequenceAbonnement])
  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.priceVariantType])
  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.slug])
  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.tailleVariantProduct])
  excludedQualifiers.push(qualifierTypesByValue[CONST.product.qualifierTypes.zoneGeographique])
}

/** @typedef {import('../../../_typedef')} */

/**
 * @extends {Transformer<string, GQLQualifier[], {optionQualifiers: APIVariantOptionQualifier[]}>}
 */
class QualifierTransformer extends Transformer {
  /**
   * @param {APIProductVariantData|APIProduct} variant
   * @param {{}} options
   * @returns {GQLQualifier}
   */
  static toGQL(variant, options = {}) {
    return new QualifierTransformer(options).setAPIData(variant).toGQL()
  }

  /**
   * URL examples:
   * - Product: /p/AITF1/1/FR
   * - Subscription: /p/abonnement/abonnement-bouquet-fleuriste/un-an/toutes-semaines/3/fr
   * - Accessory: /p/AITF1/{product-level1}
   *
   * @returns {GQLQualifier[]}
   */
  transformToGQL() {
    const _qualifiersByKey = (this.apiData.variantOptionQualifiers || []).reduce(
      (o, qualifier) => ({
        ...o,
        [CONST.product.qualifierTypes[qualifier.qualifier] || qualifier.qualifier]: qualifier,
      }),
      {}
    )

    const arr = this.apiData.url.split('/')

    // drop /p/ prefix
    arr.shift()
    arr.shift()

    /**
     * Most likely an accessory, their urls look like:
     * - /p/AITF1/{product-level1}
     */
    if (arr.length === 2) {
      return []
    }

    /** @type {GQLQualifier[]} */
    const qualifiers = []

    for (const [index, part] of arr.entries()) {
      const qualifier = new GQLQualifier()

      /**
       * Don't know if its the right way to do it ?
       */
      const isSubscription = arr.length >= 4

      // Remove '/abonnement/' part (not mapped)
      if (isSubscription && index === 0) continue

      qualifier.slugUrlPart = part
      qualifier.value = part

      if (isSubscription) {
        switch (index) {
          case 1:
            // Slug
            qualifier.orderSlug = 0
            qualifier.type = {
              name: 'Slug ?',
              value: CONST.product.qualifierTypes.slug,
            }
            break

          case 2:
            // Duration
            qualifier.orderSlug = 1
            qualifier.type = {
              name: "Durée d'abonnement ?",
              value: CONST.product.qualifierTypes.dureeAbonnement,
            }
            break

          case 3:
            // Frequency
            qualifier.orderSlug = 2
            qualifier.type = {
              name: "Fréquence d'abonnement ?",
              value: CONST.product.qualifierTypes.frequenceAbonnement,
            }
            break
          case 4:
            // Price variant
            qualifier.orderSlug = 4
            qualifier.type = {
              name: 'Niveau prix',
              value: CONST.product.qualifierTypes.priceVariantType,
            }
            break
          case 5:
            // Zone geo
            qualifier.orderSlug = 4

            qualifier.slugUrlPart = part.toUpperCase()
            qualifier.value = part.toUpperCase()

            qualifier.type = {
              name: 'Zone Géographique ?',
              value: CONST.product.qualifierTypes.zoneGeographique,
            }
            break
        }
      } else {
        switch (index) {
          case 0:
            // Slug
            qualifier.orderSlug = 0
            qualifier.type = {
              name: 'Slug ?',
              value: CONST.product.qualifierTypes.slug,
            }
            break
          case 1:
            // Price variant
            qualifier.orderSlug = 1
            qualifier.type = {
              name: 'Niveau prix ?',
              value: CONST.product.qualifierTypes.priceVariantType,
            }
            break
          case 2:
            // Zone geo
            qualifier.orderSlug = 2

            qualifier.slugUrlPart = part.toUpperCase()
            qualifier.value = part.toUpperCase()

            qualifier.type = {
              name: 'Zone Géographique ?',
              value: CONST.product.qualifierTypes.zoneGeographique,
            }
            break
        }
      }

      if (qualifier.type.value === CONST.product.qualifierTypes.slug) {
        qualifier.label = this.apiData.name
      } else if (qualifier.type.value === CONST.product.qualifierTypes.priceVariantType) {
        // hybris alternatively returns a priceVariant or a size one depending on the product
        qualifier.label = (
          _qualifiersByKey[CONST.product.qualifierTypes.priceVariantType] ||
          _qualifiersByKey[CONST.product.qualifierTypes.tailleVariantProduct] ||
          {}
        ).label
      } else {
        qualifier.label = _.get(_qualifiersByKey, `${qualifier.type.value}.label`)
      }

      qualifiers.push(qualifier)
    }

    ;(this.apiData.variantOptionQualifiers || [])
      .filter((q) => !excludedQualifiers.includes(q.qualifier))
      .map((q) => {
        const _qualifier = new GQLQualifier()

        _qualifier.label = q.label
        _qualifier.value = q.value
        _qualifier.type = {
          name: q.name || q.qualifier,
          value: CONST.product.qualifierTypes[q.qualifier],
        }

        qualifiers.push(_qualifier)
      })

    return qualifiers
  }
}

module.exports = QualifierTransformer
