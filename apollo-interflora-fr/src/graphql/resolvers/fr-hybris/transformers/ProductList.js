const CONST = require('../../../../const')
const config = require('../../../../config')
const { GQLProductList } = require('../../../models')

const SolrProductTransformer = require('./SolrProduct')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<null, GQLProductList, {}>}
 */
class ProductListTransformer extends Transformer {
  /**
   * @param {SolrResult} solrData
   * @param {GraphQLContext} context
   * @param {{
   *   category: ApiCategory,
   *   pageSize: Number,
   *   slug: string,
   *   zone: string?
   * }} options
   * @returns {GQLProductList}
   */
  static toGQL(solrData, context, options) {
    return new ProductListTransformer(options).setContext(context).setSolrData(solrData).toGQL()
  }

  transformToGQL() {
    const productList = new GQLProductList()

    productList.categoryId = this.options.category.code
    productList.categoryDescription = this.options.category.description
    productList.categoryName = this.options.category.name
    productList.categorySlug = this.options.categorySlug
    productList.categoryTitle = this.options.category.title
    productList.categoryType = this.options.category.type
    productList.pageSize = this.options.pageSize

    const showFilters = this.options.category.showFiltre !== false
    const showSort = this.options.category.showSort !== false

    const sortFiler = {
      id: CONST.product.filterAsSort,
      label: 'Trier par', // @todo i18n
      multiple: false,
      type: CONST.product.filterAsSort,
      values: (
        (this.options.category && [
          {
            defaultValue: true,
            label: 'Meilleures ventes', // @todo i18n
            value: CONST.product.sorts.bestSales,
          },
        ]) ||
        []
      ).concat([
        {
          defaultValue: false,
          label: 'Prix croissant', // @todo i18n
          value: CONST.product.sorts.priceAsc,
        },
        {
          defaultValue: false,
          label: 'Prix décroissant', // @todo i18n
          value: CONST.product.sorts.priceDesc,
        },
      ]),
    }

    // filters
    if (showFilters) {
      // products price range
      const solrPriceData = this.solrData.stats?.stats_fields?.minPriceAfterDiscount_double
      const max = Math.round(solrPriceData?.max * 100) || Number.MAX_SAFE_INTEGER
      const min = Math.round(solrPriceData?.min * 100) || 0

      for (const apiFilterField of this.options.category.visibleFilters) {
        const filterDefinition = Object.entries(CONST.product.filters).find(([, def]) =>
          def.field.startsWith(`${apiFilterField}_`)
        )

        if (!filterDefinition) continue

        const [id, definition] = filterDefinition
        const [, facetValues] = Object.entries(this.solrData.facet_counts.facet_fields)
          // override price range values by stats values min/max
          .map(([key, value]) => {
            if (key.startsWith(`all_${CONST.product.filters[CONST.product.filterAsRange].field}`)) {
              return [key, ['Minimum', `${min}`, 'Maximum', `${max}`]]
            }
            return [key, value]
          })
          .find(([key]) => key.startsWith(`all_${apiFilterField}_`))
        const filter = {
          id,
          label: definition.label,
          multiple: true,
          type: ['COLOR', 'DELIVERY'].includes(id) ? id : 'LIST',
          values: [],
        }

        if (id === CONST.product.filterAsRange) {
          for (let i = 0; i < facetValues.length; i += 2) {
            filter.values.push({
              defaultValue: false,
              label: facetValues[i],
              value: facetValues[i + 1],
            })
          }
        } else {
          for (let i = 0; i < facetValues.length; i += 2) {
            filter.values.push({
              defaultValue: false,
              label: facetValues[i],
              value: facetValues[i],
            })
          }
        }

        if (filter.values[0]) {
          productList.filters.push(filter)
        }
      }

      if (this.context.isDefaultCountry()) {
        // special delivery date filter
        productList.filters.push({
          id: config.filters.date.id,
          label: config.filters.date.label,
          multiple: false,
          type: config.filters.date.id,
          values: [], // not used by front and requested to be empty
        })
      }

      if (showSort) productList.filters.push(sortFiler)

      // query facets
      for (const [id, definition] of Object.entries(CONST.product.filterQueries)) {
        if (definition.entries.some((entry) => this.solrData.facet_counts.facet_queries[entry.query])) {
          productList.filters.push({
            id,
            label: definition.label,
            multiple: true,
            type: 'LIST',
            values: definition.entries
              .filter((entry) => this.solrData.facet_counts.facet_queries[entry.query] > 0)
              .map((entry) => ({
                defaultValue: false,
                label: entry.label,
                value: entry.label,
              })),
          })
        }
      }
    } else if (showSort) {
      productList.filters.push(sortFiler)
    }

    // products
    for (const doc of this.solrData.response.docs) {
      productList.products.push(SolrProductTransformer.toGQL(doc, { zone: this.options.zone }))
    }

    productList.total = this.solrData.response.numFound

    return productList
  }
}

module.exports = ProductListTransformer
