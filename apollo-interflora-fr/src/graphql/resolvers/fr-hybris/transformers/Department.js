const config = require('../../../../config')
const CONST = require('../../../../const')
const GQLDepartment = require('../../../models/Department')

const Transformer = require('./Transformer')
const ImageTransfomer = require('./Image')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIFloraDepartment, GQLDepartment, {isDomtom: boolean, mediaBaseUrl: string?}>}
 */
class DepartmentTransformer extends Transformer {
  /**
   * @param {APIFloraDepartment} apiData
   * @param {GraphQLContext} context
   * @param {{isDomtom: boolean, mediaBaseUrl: string?}} options
   * @returns {GQLDepartment}
   */
  static toGQL(apiData, context, options) {
    return new DepartmentTransformer(options).setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLDepartment}
   */
  transformToGQL() {
    const department = new GQLDepartment()

    department.slugUrl = this.apiData.slugUrl
    department.label = this.apiData.nomDepartment
    department.indice = this.apiData.codeDepartment
    department.description = this.apiData.content
    department.type = this.options.isDomtom ? CONST.geo.departmentTypes.DOM_TOM : CONST.geo.departmentTypes.METROPOLITAN
    department.mainTowns = this.apiData.contentTopVilles

    {
      const image = ImageTransfomer.toGQL(
        {
          altText: this.apiData.nomDepartment,
          format: 'mobile', // ??
          imageType: 'PRIMARY', // ??
          url: this.apiData.urlImage,
        },
        this.context,
        { mediaBaseUrl: this.options.mediaBaseUrl }
      )

      // transition - image => images
      department.image = image
      department.images = [image]
    }

    department.mainTown = {
      label: this.apiData.chefLieu.cityName,
      slugUrl: this.apiData.chefLieu.slugUrl,
    }

    /**
     * `listCity` is empty when fetching the list of departments (for
     * performance reasons).
     */
    department.towns = (this.apiData.listCity || []).map((city) => ({
      label: city.cityName,
      slugUrl: city.slugUrl,
    }))

    department.others = (this.apiData.nearestDepartments || []).map((department) =>
      DepartmentTransformer.toGQL(department, this.context, {
        isDomtom: this.options.isDomtom4,
        mediaBaseUrl: this.options.mediaBaseUrl,
      })
    )

    const tpls = config.apps[this.context.siteId].seo.floristDepartment || config.apps[this.context.siteId].seo.default
    department.title = tpls?.title
      ? tpls.title.replace(/%s/, `${department.label} - ${department.indice}`)
      : `${department.label} - ${department.indice}`

    return department
  }
}

module.exports = DepartmentTransformer
