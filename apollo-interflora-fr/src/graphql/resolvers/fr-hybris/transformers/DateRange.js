const { dateRangeIntervals } = require('../../../../helpers/dates')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIExcludedDateList, GQLDateRange[], {}>}
 */
class DateRangeTransformer extends Transformer {
  /**
   * @param {APIExcludedDateList} apiData
   * @returns {GQLDateRange[]}
   */
  static toGQL(apiData) {
    return new DateRangeTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLDateRange[]}
   */
  transformToGQL() {
    return dateRangeIntervals(this.apiData.firstDate, this.apiData.lastDate, this.apiData.excludedDates)
  }
}

module.exports = DateRangeTransformer
