const GQLCurrency = require('../../../models/Currency')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APICurrency, GQLCurrency, {}>}
 */
class CurrencyTransformer extends Transformer {
  /**
   * @param {APICurrency} apiData
   * @returns {GQLCurrency}
   */
  static toGQL(apiData) {
    return new CurrencyTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const currency = new GQLCurrency()

    currency.active = this.apiData.active
    currency.isocode = this.apiData.isocode
    currency.name = this.apiData.name
    currency.symbol = this.apiData.symbol

    return currency
  }
}

module.exports = CurrencyTransformer
