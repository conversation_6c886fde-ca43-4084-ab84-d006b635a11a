const GQLAdditionalInfo = require('../../../models/AdditionalInfo')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIMessageInfo, GQLAdditionalInfo, {}>}
 */
class AdditionalInfoTransformer extends Transformer {
  /**
   * @param {APIMessageInfo} apiData
   * @returns {GQLAdditionalInfo}
   */
  static toGQL(apiData) {
    return new AdditionalInfoTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLAdditionalInfo}
   */
  transformToGQL() {
    const additionalInfo = new GQLAdditionalInfo()

    additionalInfo.content = this.apiData.content
    additionalInfo.type = this.apiData.type

    return additionalInfo
  }
}

module.exports = AdditionalInfoTransformer
