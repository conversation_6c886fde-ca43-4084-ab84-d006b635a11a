const _ = require('lodash')

const CONST = require('../../../../const')
const GQLProductVariant = require('../../../models/ProductVariant')

const AdditionalInfoTransformer = require('./AdditionalInfo')
const ImageTransformer = require('./Image')
const PriceTransformer = require('./Price')
const QualifierTransformer = require('./Qualifier')
const StockTransformer = require('./Stock')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIProduct | APIProductVariantData | APIVariantOption, GQLProductVariant, { parentProduct: GQLProduct, product: GQLProduct }>}
 */
class ProductVariantTransformer extends Transformer {
  /**
   * @param {APIProductVariantData} apiData
   * @param {GraphQLContext} context
   * @param {{ parent: GQLProduct }} options
   * @returns {GQLProductVariant}
   */
  static apiProductToGQL(apiData, context, options = {}) {
    return new ProductVariantTransformer(options).setContext(context).setAPIData(apiData).apiProductToGQL()
  }

  /**
   * @param {APIProductVariantData} apiData
   * @param {GraphQLContext} context
   * @param {{ parentProduct: GQLProduct, product: GQLProduct }} options
   * @returns {GQLProductVariant}
   */
  static apiVariantToGQL(apiData, context, options) {
    return new ProductVariantTransformer(options).setContext(context).setAPIData(apiData).toGQL()
  }

  apiProductToGQL() {
    const variant = new GQLProductVariant()

    variant.code = this.apiData.code
    const _isOnline = !this.apiData.currentStep || this.apiData.currentStep === CONST.product.steps.online

    variant.delivery = {
      available: Boolean(!this.apiData.expressDelivery || this.apiData.expressDeliveryAvailable),
      express: Boolean(this.apiData.expressDeliveryAvailable),
      price:
        (this.apiData.deliveryCost && PriceTransformer.toGQL(this.apiData.deliveryCost)) ||
        PriceTransformer.toGQL({
          currencyIso: 'EUR',
          value: 0,
        }),
      type: this._deliveryType(this.apiData),
    }

    variant.hasCustomText = Boolean(this.apiData.isRubanDeuil)

    variant.maxQuantity = this.apiData.maxOrderQuantity
    variant.minQuantity = null

    if (this.options.parentProduct?.isFromWorkshop) {
      variant.isFromWorkshop = true
    }

    if (_isOnline && this.apiData.price) {
      variant.price = PriceTransformer.toGQL(this.apiData.price)
    }
    if (_isOnline && this.apiData.pricePromo) {
      variant.discount = PriceTransformer.toGQL(this.apiData.pricePromo)
    }

    for (const reinsurance of this.apiData.reassurancesInfo || []) {
      variant.reinsurances.push({
        icon: reinsurance.icon,
        label: reinsurance.content,
      })
    }

    if (this.apiData.url) {
      variant.qualifiers = QualifierTransformer.toGQL(this.apiData)
    }

    if (this.apiData.images) {
      const variantSize = variant.qualifiers.find(
        (qualifier) => qualifier.type.value === CONST.product.qualifierTypes.priceVariantType
      )?.value

      if (variantSize !== undefined) {
        const _byType = {}
        const _formats = new Set()

        for (const image of this.apiData.images) {
          const format = image.format?.replace(/-\d+$/, '')
          _formats.add(format)

          if (!_byType[image.type]) {
            _byType[image.type] = {}
          }

          if (!_byType[image.type][image.format]) {
            _byType[image.type][image.format] = []
          }

          _byType[image.type][image.format].push(
            ImageTransformer.toGQL(
              {
                ...image,
                format,
              },
              this.context
            )
          )
        }

        for (const [_type, images] of Object.entries(_byType)) {
          for (const format of _formats) {
            variant.images = variant.images.concat(images[`${format}-${variantSize}`] || images[format])
          }
        }
      } else {
        for (const image of this.apiData.images.filter((img) => Boolean(CONST.images.formats[img.format]))) {
          if (image.url) {
            variant.images.push(ImageTransformer.toGQL(image, this.context))
          }
        }
      }
    }

    variant.stock = StockTransformer.toGQL(this.apiData.stock)
    variant.parent = this.options.parent

    return variant
  }

  /**
   * @returns {GQLProductVariant}
   */
  transformToGQL() {
    // here because of circular dependency
    let ProductTransformer = require('./Product')
    if (!ProductTransformer.toGQL) {
      delete require.cache[require.resolve('./Product')]
      ProductTransformer = require('./Product')
    }

    const variant = new GQLProductVariant()

    if (!_.isEqual(_.sortBy(this.options.product.accessoires || []), _.sortBy(this.apiData.accessoires || []))) {
      // feed accessories only if different from the parent product
      for (const accessory of this.apiData.accessoires || []) {
        // GGT-129: filter accessories with undefined prices.
        if (!accessory.price) {
          continue
        }
        if (accessory.isCardMessage) {
          variant.cards.push(ProductTransformer.toGQL(accessory, this.context))
        } else {
          variant.accessories.push(ProductTransformer.toGQL(accessory, this.context))
        }
      }
    }

    if (this.options.parentProduct?.isFromWorkshop) {
      variant.isFromWorkshop = true
    }

    variant.additionalInfo =
      this.options.product.additionalInfo && AdditionalInfoTransformer.toGQL(this.options.product.additionalInfo)

    variant.code = this.apiData.code

    variant.delivery = {
      available: Boolean(!this.options.product.expressDelivery || this.options.product.expressDeliveryAvailable),
      express: Boolean(this.options.product.expressDelivery),
      price: this.apiData.deliveryCost && PriceTransformer.toGQL(this.apiData.deliveryCost),
      type: this._deliveryType(this.options.parentProduct),
    }

    variant.label = this.apiData.variantLabel
    variant.maxQuantity = this.options.product.maxOrderQuantity
    variant.minQuantity = null
    variant.parent = this.options.parentProduct
    variant.variationCode =
      CONST.product.variationCodes[this.apiData.variantLabelIcon?.trim().toUpperCase()] ||
      CONST.product.variationCodes[this.apiData.variantLabelCode] ||
      CONST.product.variationCodes.unknown

    if (this.options.parentProduct.isOnline) {
      variant.price = PriceTransformer.toGQL(this.apiData.price)
    }
    if (this.options.parentProduct.isOnline && this.apiData.pricePromo) {
      variant.discount = PriceTransformer.toGQL(this.apiData.pricePromo)
    }

    if (this.apiData.url) {
      variant.qualifiers = QualifierTransformer.toGQL(this.apiData)
    }

    variant.stock = StockTransformer.toGQL(this.apiData.stock)

    return variant
  }

  /**
   * @param {APIProduct} product
   * @returns {string}
   * @private
   */
  _deliveryType(product) {
    if (
      [
        CONST.product.types.gift,
        CONST.product.types.mixed,
        CONST.product.types.plants,
        CONST.product.types.prestige,
      ].includes(product.type)
    ) {
      return CONST.product.deliveryTypes.carrier
    }

    return CONST.product.deliveryTypes.florist
  }
}

module.exports = ProductVariantTransformer
