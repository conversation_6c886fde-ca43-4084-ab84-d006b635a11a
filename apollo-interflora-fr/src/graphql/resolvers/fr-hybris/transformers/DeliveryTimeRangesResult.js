const { GQLDeliveryTimeRangesResult } = require('../../../models')

const DeliveryTimeRangeTransformer = require('./DeliveryTimeRange')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIMomentLivraisonList, GQLDeliveryTimeRangesResult, {}>}
 */
class DeliveryTimeRangesResultTransformer extends Transformer {
  /**
   * @param {APIMomentLivraisonList} apiData
   * @returns {GQLDeliveryTimeRangesResult}
   */
  static toGQL(apiData) {
    return new DeliveryTimeRangesResultTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLDeliveryTimeRangesResult}
   */
  transformToGQL() {
    const result = new GQLDeliveryTimeRangesResult()

    result.message = this.apiData.messageSpecifique

    const hasWholedayMoment = this.apiData.moments?.some((range) => range.code === 'J') || false

    result.ranges = (this.apiData.moments || []).map((moment, index) => {
      let isDefault = false
      if ((hasWholedayMoment && moment.code === 'J') || (!hasWholedayMoment && index === 0)) isDefault = true
      return DeliveryTimeRangeTransformer.toGQL(moment, { isDefault })
    })

    return result
  }
}

module.exports = DeliveryTimeRangesResultTransformer
