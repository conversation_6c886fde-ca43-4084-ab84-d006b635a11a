const AdditionalInfoTransformer = require('./AdditionalInfo')
const AddressTransformer = require('./Address')
const BreadcrumbTransformer = require('./Breadcrumb')
const CartTransformer = require('./Cart')
const Classification = require('./Classification')
const ClassificationFeature = require('./ClassificationFeature')
const ContactSubjectTransformer = require('./ContactSubject')
const ComponentTransformer = require('./Component')
const ContactTransformer = require('./Contact')
const CountryTransformer = require('./Country')
const DateRangeTransformer = require('./DateRange')
const DeliveryFeasibilityTransformer = require('./DeliveryFeasibility')
const DeliveryModesTransformer = require('./DeliveryModes')
const DeliveryTransformer = require('./Delivery')
const DeliveryTimeRangeTransformer = require('./DeliveryTimeRange')
const DeliveryTimeRangesResultTransformer = require('./DeliveryTimeRangesResult')
const FestModeTransformer = require('./FestMode')
const FooterTransformer = require('./Footer')
const ImageTransfomer = require('./Image')
const InvalidDestinationTransformer = require('./InvalidDestination')
const LinkedProductsTransformer = require('./LinkedProducts')
const MenuTransformer = require('./Menu')
const MetaDataTransformer = require('./MetaData')
const OrderListTransformer = require('./OrderList')
const OrderReasonTransformer = require('./OrderReason')
const OrderResultTransformer = require('./OrderResult')
const OrderTransformer = require('./Order')
const PageAccessoryCategoryTransformer = require('./PageAccessoryCategory')
const PageTransformer = require('./Page')
const PaymentMethodTransformer = require('./PaymenMethod')
const PhoneTransformer = require('../../../transformers/Phone')
const PriceTransformer = require('./Price')
const ProductTransformer = require('./Product')
const ProductListTransformer = require('./ProductList')
const ProductVariantTransformer = require('./ProductVariant')
const QualifierTransformer = require('./Qualifier')
const SolrProductTransformer = require('./SolrProduct')
const SolrProductVariantTransformer = require('./SolrProductVariant')
const StockTransformer = require('./Stock')
const TemplateTransformer = require('./Template')
const TokenTransformer = require('./Token')
const TownPageTransformer = require('./TownPage')
const TownPageTabTransformer = require('./TownPageTab')
const TownTransformer = require('./Town')
const UrlTransformer = require('./Url')
const UserTransformer = require('./User')
const DepartmentTransformer = require('./Department')
const VoucherTransformer = require('./Voucher')

module.exports = {
  AdditionalInfoTransformer,
  AddressTransformer,
  BreadcrumbTransformer,
  CartTransformer,
  Classification,
  ClassificationFeature,
  ComponentTransformer,
  ContactSubjectTransformer,
  ContactTransformer,
  CountryTransformer,
  DateRangeTransformer,
  DeliveryFeasibilityTransformer,
  DeliveryModesTransformer,
  DeliveryTimeRangeTransformer,
  DeliveryTimeRangesResultTransformer,
  DeliveryTransformer,
  DepartmentTransformer,
  FestModeTransformer,
  FooterTransformer,
  ImageTransfomer,
  InvalidDestinationTransformer,
  LinkedProductsTransformer,
  MenuTransformer,
  MetaDataTransformer,
  OrderListTransformer,
  OrderReasonTransformer,
  OrderResultTransformer,
  OrderTransformer,
  PageAccessoryCategoryTransformer,
  PageTransformer,
  PaymentMethodTransformer,
  PhoneTransformer,
  PriceTransformer,
  ProductListTransformer,
  ProductTransformer,
  ProductVariantTransformer,
  QualifierTransformer,
  SolrProductTransformer,
  SolrProductVariantTransformer,
  StockTransformer,
  TemplateTransformer,
  TokenTransformer,
  TownPageTabTransformer,
  TownPageTransformer,
  TownTransformer,
  UrlTransformer,
  UserTransformer,
  VoucherTransformer,
}
