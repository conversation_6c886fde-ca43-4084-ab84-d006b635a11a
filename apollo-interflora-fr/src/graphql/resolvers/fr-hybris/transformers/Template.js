const _ = require('lodash')

const CONST = require('../../../../const')
const GQLTemplate = require('../../../models/Template')

const ComponentTransformer = require('./Component')
const Transformer = require('./Transformer')

/** @typedef {import('../datasources')} */
/** @typedef {import('../types/_typedef')} */

/**
 * @extends {Transformer<APICMSTemplate, GQLTemplate, {}>}
 */
class TemplateTransformer extends Transformer {
  /**
   * @param {APICMSTemplate} template
   * @param {GraphQLContext} context
   * @param {{mediaBaseUrl: string?}} options
   * @returns {GQLTemplate}
   */
  static toGQL(template, context, options = {}) {
    return new TemplateTransformer(options).setAPIData(template).setContext(context).toGQL()
  }

  transformToGQL() {
    const template = new GQLTemplate()

    const apiData =
      this.apiData.typeCode === CONST.cms.apiComponentTypes.CMSTemplateContainer
        ? this.apiData
        : {
            components: [this.apiData],
            name: CONST.cms.defaultTemplateName,
          }

    template.background = _.invert(CONST.cms.templateBackground)[apiData.background]
      ? apiData.background
      : CONST.cms.templateBackground.none

    template.wrapper = _.invert(CONST.cms.templateWrapper)[apiData.wrapper]
      ? apiData.wrapper
      : CONST.cms.templateWrapper.default

    for (const apiComponent of apiData.components || []) {
      template.components.push(
        ComponentTransformer.toGQL(apiComponent, this.context, { mediaBaseUrl: this.options.mediaBaseUrl })
      )
    }

    template.name = _.invert(CONST.cms.templates)[apiData.template] || CONST.cms.defaultTemplateName

    return template
  }
}

module.exports = TemplateTransformer
