const CONST = require('../../../../const')
const GQLPage = require('../../../models/Page')

const BreadcrumbTransformer = require('./Breadcrumb')
const FooterTransformer = require('./Footer')
const GelluleTransformer = require('./Gellule')
const MenuTransformer = require('./Menu')
const MetaDataTransformer = require('./MetaData')
const PageAccessoryCategoryTransformer = require('./PageAccessoryCategory')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @typedef {{
 *  components: Object<string, APIComponent>
 *  customLink: Object<string, GQLRoute>|undefined
 *  pageType: string
 *  productCategory: APICategory,
 *  type: string,
 *  zones: string[]
 * }} PageTransformerOptions
 */

/**
 * @extends {Transformer<APICMSPage, GQLPage, PageTransformerOptions>}
 */
class PageTransformer extends Transformer {
  /**
   *
   * @param {APICMSPage} apiPage
   * @param {GraphQLContext} context
   * @param {PageTransformerOptions} options
   * @returns {GQLPage}
   */
  static toGQL(apiPage, context, options = {}) {
    return new PageTransformer(options).setAPIData(apiPage).setContext(context).toGQL()
  }

  /**
   * @returns {GQLPage}
   */
  transformToGQL() {
    const page = new GQLPage()

    page.type = this.options.type
    page.breadcrumbs = (this.apiData.breadcrumb || [])
      .filter((breadcrumb) => Boolean(breadcrumb.label))
      .map((breadcrumb) => {
        if (breadcrumb.contentPageLabel === 'index') {
          breadcrumb.label = this.context.appConfig.breadcrumbsHome ?? breadcrumb.label
        }
        return BreadcrumbTransformer.toGQL(breadcrumb, this.context, {
          productCategory: this.options.productCategory,
          type: this.options.type,
        })
      })
    page.menus = []
    page.meta = (this.apiData.metaData || []).map(MetaDataTransformer.toGQL)
    page.pro = Boolean(this.apiData.isPro)
    page.title = this.apiData.title
    page.version = this.apiData.version
      ? CONST.cms.templateVersion[this.apiData.version]
      : CONST.cms.templateVersion.old
    if (!page.version) {
      this.context.log.warn(`unknown page version ${this.apiData.version}`)
      page.version = CONST.cms.templateVersion.unknown
    }

    if (!this.apiData.contentSlots.contentSlot) {
      // empty page
      return page
    }

    const _zones = {}
    for (const slot of this.apiData.contentSlots.contentSlot) {
      if (!slot.components || !slot.components.component || slot.components.component.length === 0) {
        // no component to add
        continue
      }

      if (slot.position === CONST.cms.positions.footer) {
        const components = slot.components.component.map((c) => this.options.components[c.uid]).filter(Boolean)
        page.footers = components
          .map((component) =>
            FooterTransformer.toGQL(component, this.context, {
              route: this.options.pageType,
            })
          )
          .flat()
      } else if (slot.position === CONST.cms.positions.gellules) {
        const components = slot.components.component.map((c) => this.options.components[c.uid]).filter(Boolean)
        page.gellules = GelluleTransformer.toGQL(components, this.context)
      } else if (slot.position === CONST.cms.positions.menu) {
        if (slot.components.component[0]) {
          const component = this.options.components[slot.components.component[0].uid]
          page.menus = MenuTransformer.toGQL(component, this.context, {
            pageType: this.options.pageType,
          })
        }
      } else if (slot.position === CONST.cms.zones.menu_card) {
        const components = slot.components.component.map((c) => this.options.components[c.uid]).filter(Boolean)
        page.accessoryCategories = PageAccessoryCategoryTransformer.toGQL(components, this.context).filter(Boolean)
      } else {
        let zone = CONST.cms.zones[slot.position] || CONST.cms.zones.unknown
        // for the time being, hybris puts all product page components in the content zone
        // @todo: remove when fixed on hybris side
        if (this.options.type === 'product' && zone === 'content') {
          zone = CONST.cms.zones.after_info
        }
        // idem for categories
        // @todo: remove when fixed on hybris side
        if (this.options.type === 'category' && zone === 'content') {
          zone = CONST.cms.zones.after_productlist
        }

        if (Array.isArray(this.options.zones) && this.options.zones.length > 0 && !this.options.zones.includes(zone)) {
          // current zone is not requested - skip
          continue
        }

        if (!_zones[zone]) {
          _zones[zone] = {
            name: zone,
            uids: slot.components.component.map((c) => c.uid),
          }
        } else {
          for (const c of slot.components.component) {
            _zones[zone].uids.push(c.uid)
          }
        }
      }
    }

    for (const zone of Object.values(_zones)) {
      page.componentZones.push(zone)
    }

    for (const breadcrumb of page.breadcrumbs) {
      if (breadcrumb.link?.name && this.options.customLink?.[breadcrumb.link.name]) {
        const link = this.options.customLink[breadcrumb.link.name]
        breadcrumb.link = link
      }
    }

    return page
  }
}

module.exports = PageTransformer
