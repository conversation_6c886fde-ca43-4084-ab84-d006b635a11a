const CONST = require('../../../../const')
const config = require('../../../../config')
const { createJwt } = require('../../../../helpers')
const GQLToken = require('../../../models/Token')

const Transformer = require('./Transformer')
const UserTransformer = require('./User')

/** @typedef {import('../_typedef')} */
/** @typedef {import('../../Authentication')} AuthenticationService */

/**
 * @extends {Transformer<APIToken, GQLToken, {user: APIUser}>}
 */
class TokenTransformer extends Transformer {
  /**
   * @param {APIToken} apiData
   * @param {GraphQLContext} context
   * @param {{user: APIUser}} options
   * @returns {GQLToken}
   */
  static toGQL(apiData, context, options) {
    return new TokenTransformer(options).setAPIData(apiData).setContext(context).toGQL()
  }

  transformToGQL() {
    const { user: apiUser } = this.options

    const token = new GQLToken()

    const { token: accessToken, expiresAt: accessExpiresAt } = createJwt(
      this.apiData,
      CONST.user.tokenType.access,
      this.context.auth.sessionId,
      this.context.siteId
    )

    token.value = accessToken
    token.lifetime = accessExpiresAt

    const { token: refreshToken, expiresAt: refreshExpiresAt } = createJwt(
      this.apiData,
      CONST.user.tokenType.refresh,
      this.context.auth.sessionId,
      this.context.siteId
    )

    token.refreshToken = refreshToken
    token.refreshLifetime = refreshExpiresAt

    token.state = this.options.state || CONST.user.userRegistrationStates.CREATED

    token.createdAt = String(Date.now() + this.apiData.expires_in * 1000 - config.interflora.accessTokenLifeTime)

    if (apiUser) {
      token.user = UserTransformer.toGQL(apiUser, this.context)
    }

    return token
  }
}

module.exports = TokenTransformer
