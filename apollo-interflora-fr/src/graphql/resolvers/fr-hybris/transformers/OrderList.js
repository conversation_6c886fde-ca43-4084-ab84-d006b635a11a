const GQLOrderList = require('../../../models/OrderList')
const Transformer = require('./Transformer')
const { isOrderArchived } = require('../../../../helpers/orders')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIOrderList, GQLOrderList, {}>}
 */
class OrderListTransformer extends Transformer {
  /**
   * @param {APIOrderList} apiData
   * @returns {GQLOrderList}
   */
  static toGQL(apiData) {
    return new OrderListTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const orderList = new GQLOrderList()

    if (!this.apiData.orders) {
      return orderList
    }

    orderList.orders = this.apiData.orders.map((apiOrderHistory) => {
      const createdAt = apiOrderHistory.placed && new Date(apiOrderHistory.placed)
      return {
        archived: isOrderArchived(createdAt, apiOrderHistory.status),
        createdAt,
        id: apiOrderHistory.guid,
        number: apiOrderHistory.code,
      }
    })
    orderList.total = this.apiData.pagination.totalResults

    return orderList
  }
}

module.exports = OrderListTransformer
