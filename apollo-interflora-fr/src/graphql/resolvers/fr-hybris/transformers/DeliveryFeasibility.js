const { GQLDeliveryFeasibility, GQLRoute } = require('../../../models')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIFeasibility, GQLDeliveryFeasibility, {}>}
 */
class DeliveryFeasibilityTransformer extends Transformer {
  /**
   * @param {APIFeasibility} apiData
   * @returns {GQLDeliveryFeasibility}
   */
  static toGQL(apiData) {
    return new DeliveryFeasibilityTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLDeliveryFeasibility}
   */
  transformToGQL() {
    const deliveryFeasibility = new GQLDeliveryFeasibility()

    deliveryFeasibility.feasible = this.apiData.result

    if (this.apiData.alternativeCategoryUrl) {
      const categoryUrl = new URL(`http://domain/${this.apiData.alternativeCategoryUrl.replace(/^\/+/, '')}`)

      const categoryRoute = new GQLRoute()
      categoryRoute.name = 'category'

      categoryRoute.params = [
        {
          name: 'slugCategory',
          value: categoryUrl.pathname.replace(/^\/+?c\//, ''),
        },
      ]

      for (const [name, value] of categoryUrl.searchParams.entries()) {
        categoryRoute.query.push({ name, value })
      }

      deliveryFeasibility.alternativeCategoryRoute = categoryRoute
    }

    return deliveryFeasibility
  }
}

module.exports = DeliveryFeasibilityTransformer
