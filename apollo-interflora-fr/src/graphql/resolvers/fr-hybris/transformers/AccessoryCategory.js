const GQLAccessoryCategory = require('../../../models/AccessoryCategory')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIFilter, GQLAccessoryCategory, {}>}
 */
class AccessoryCategoryTransformer extends Transformer {
  /**
   * @param {APIFilter} apiData
   * @returns {GQLAccessoryCategory}
   */
  static toGQL(apiData) {
    return new AccessoryCategoryTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const accessoryCategory = new GQLAccessoryCategory()

    accessoryCategory.code = this.apiData.code
    accessoryCategory.name = this.apiData.name

    return accessoryCategory
  }
}

module.exports = AccessoryCategoryTransformer
