const GQLContactSubject = require('../../../models/ContactSubject')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APITicketSubject, GQLContactSubject, {parent: GQLContactSubject}>}
 */
class ContactSubjectTransformer extends Transformer {
  /**
   *
   * @param {APITicketSubject} apiData
   * @param {Object} [options]
   * @param {GQLContactSubject} [options.parent]
   * @returns {GQLContactSubject}
   */
  static toGQL(apiData, options) {
    return new ContactSubjectTransformer(options).setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const { parent } = this.options

    const contactSubject = new GQLContactSubject()

    contactSubject.displayDeliveryDate = false
    contactSubject.id = this.apiData.code
    contactSubject.label = this.apiData.name
    contactSubject.type = this.apiData.type
    contactSubject.mandatoryDeliveryAddress = Boolean(this.apiData.mandatoryDeliveryAddress)
    contactSubject.mandatoryDeliveryDate = Boolean(this.apiData.mandatoryDeliveryDate)
    contactSubject.mandatoryFile = Boolean(this.apiData.mandatoryDoc)
    contactSubject.mandatoryMessage = Boolean(this.apiData.mandatoryMessage)
    contactSubject.mandatoryOrderNumber = Boolean(this.apiData.mandatoryOrderId)
    contactSubject.type = this.apiData.type

    if (parent instanceof GQLContactSubject) {
      contactSubject.parent = parent
    }

    for (const apiSubSubject of this.apiData.ticketSubSubject || []) {
      const subSubject = ContactSubjectTransformer.toGQL(apiSubSubject, { parent: contactSubject })
      contactSubject.subSubject.push(subSubject)
    }

    return contactSubject
  }
}

module.exports = ContactSubjectTransformer
