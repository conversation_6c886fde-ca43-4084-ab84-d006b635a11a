/** @typedef {import('../../../_typedef')} */

/**
 * @template A,G,O
 * @class Transformer
 * @abstract
 */
class Transformer {
  /**
   * @param {O} [options]
   */
  constructor(options = {}) {
    /**
     * @protected
     */
    this.options = options

    /**
     * @protected
     * @type {A}
     */
    this.apiData

    /**
     * @protected
     * @type {G}
     */
    this.gqlData

    /**
     * @protected
     * @type {SolrResult}
     */
    this.solrData

    /**
     * @protected
     * @type {GraphQLContext}
     */
    this.context
  }

  /**
   * @param {A} data
   * @returns {this}
   */
  setAPIData(data) {
    this.apiData = data
    return this
  }

  /**
   * @param {G} data
   * @returns {this}
   */
  setGQLData(data) {
    this.gqlData = data
    return this
  }

  /**
   * @param {SolrResult} data
   * @returns {this}
   */
  setSolrData(data) {
    this.solrData = data
    return this
  }

  /**
   * @param {GraphQLContext} context
   * @returns {this}
   */
  setContext(context) {
    this.context = context
    return this
  }

  /**
   * @returns {A}
   */
  toAPI() {
    if (this.gqlData === undefined) {
      throw new Error(`No GQL data to transform into API data.`)
    }

    this.apiData = this.transformToAPI()

    return this.apiData
  }

  /**
   * @returns {G}
   */
  toGQL() {
    if (this.apiData === undefined && this.setSolrData === undefined) {
      throw new Error(`No API nor Solr data to transform into GQL data.`)
    }

    this.gqlData = this.transformToGQL()

    return this.gqlData
  }

  /**
   * @protected
   * @abstract
   * @returns {G}
   */
  transformToGQL() {
    throw new Error('Method not implemented')
  }

  /**
   * @protected
   * @abstract
   * @returns {A}
   */
  transformToAPI() {
    throw new Error('Method not implemented')
  }
}

module.exports = Transformer
