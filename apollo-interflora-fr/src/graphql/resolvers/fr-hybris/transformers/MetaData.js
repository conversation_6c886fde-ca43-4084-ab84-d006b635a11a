const CONST = require('../../../../const')
const { GQLMetaData } = require('../../../models')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIMetaData, GQLMetaData, {}>}
 */
class MetaDataTransformer extends Transformer {
  /**
   * @param {APIMetaData} apiData
   * @returns {GQLMetaData}
   */
  static toGQL(apiData) {
    return new MetaDataTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLMetaData}
   */
  transformToGQL() {
    const metadata = new GQLMetaData()

    metadata.tag = CONST.cms.metaDataTypes[this.apiData.type]
    metadata.params = [{ name: this.apiData.name, value: this.apiData.value }]

    return metadata
  }
}

module.exports = MetaDataTransformer
