const _ = require('lodash')
const CONST = require('../../../../const')
const GQLClassification = require('../../../models/Classification')

const ClassificationFeatureTransformer = require('./ClassificationFeature')
const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIClassification, GQLClassification, {}>}
 */
class ClassificationTransformer extends Transformer {
  /**
   * @param {APIProductClassification} apiData
   * @param {GraphQLContext} context
   * @returns {GQLProductClassification}
   */
  static toGQL(apiData, context) {
    return new ClassificationTransformer().setContext(context).setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLProductClassification}
   */
  transformToGQL() {
    const classification = new GQLClassification()

    classification.code = _.invert(CONST.product.classificationTypes)[this.apiData.code]
    if (!classification.code) {
      this.context.log.warn(`uknown classification code received: ${this.apiData.code}`)
      classification.code = CONST.product.classificationTypes.UNKNOWN
    }
    classification.features = (this.apiData.features || []).map(ClassificationFeatureTransformer.toGQL)
    classification.icon = this.apiData.icon
    classification.name = this.apiData.name

    return classification
  }
}

module.exports = ClassificationTransformer
