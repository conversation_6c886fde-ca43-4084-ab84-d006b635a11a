const GQLFooter = require('../../../models/Footer')

const Transformer = require('./Transformer')
const UrlTransformer = require('./Url')

/** @typedef {import('../datasources')} */
/** @typedef {import('../types/_typedef')} */

/**
 * @typedef {{
 *  pageType: string
 * }} FooterTransformerOptions
 */

/**
 * @extends {Transformer<APINavigationNode, GQLFooter, FooterTransformerOptions>}
 */
class FooterTransformer extends Transformer {
  /**
   * @param {APINavigationNode} node
   * @param {GraphQLContext} context
   * @param {FooterTransformerOptions} options
   * @returns {GQLFooter}
   */
  static toGQL(node, context, options = {}) {
    return new FooterTransformer(options).setAPIData(node).setContext(context).toGQL()
  }

  transformToGQL() {
    return (this.apiData.navigationNode?.children || []).map((apiNode) => this._nodeToFooter(apiNode))
  }

  /**
   * @param {APINavigationNode} node
   * @returns {GQLFooter}
   * @private
   */
  _nodeToFooter(node) {
    const footer = new GQLFooter()
    footer.label = node.name
    if (node.link) {
      // Yes some hybris backend boolean is string...
      footer.highlight = node.link.highlight === 'true' || false
      footer.label = node.link.linkName
      footer.mentions = node.comments || []
      footer.multiple = node.multiple || false
      const obfuscationExclusions = node.link?.obfuscateLink?.pages?.split(',') || []
      const obfuscated = node.link?.obfuscateLink?.obfuscated === 'true'
      footer.obfuscated =
        (obfuscated && !obfuscationExclusions?.includes(this.options.pageType)) ||
        (!obfuscated && obfuscationExclusions?.includes(this.options.pageType))

      if (node.link.url) {
        footer.href = UrlTransformer.toGQL(node.link, this.context)
      }
    }

    footer.children = (node.children || []).map((apiNode) => this._nodeToFooter(apiNode))
    return footer
  }
}

module.exports = FooterTransformer
