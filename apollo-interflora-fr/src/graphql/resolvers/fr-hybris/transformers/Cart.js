const _ = require('lodash')

const GQLCart = require('../../../models/Cart')

const PriceTransformer = require('./Price')
const Transformer = require('./Transformer')
const DeliveryTransformer = require('./Delivery')
const AccessoryCategoryTransformer = require('./AccessoryCategory')

const CONST = require('../../../../const')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APICart, GQLCart, {}>}
 */
class CartTransformer extends Transformer {
  /**
   * @param {APICart} apiData
   * @param {GraphQLContext} context
   * @returns {GQLCart}
   */
  static toGQL(apiData, context) {
    return new CartTransformer().setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @param {APICart} apiData
   * @param {GraphQLContext} _context
   * @returns {object}
   */
  static toHybrisPayload(apiData, _context) {
    const payload = {}

    payload.accessories = (apiData.accessories || []).map((accessory) => ({
      code: accessory.code,
      quantity: accessory.quantity,
    }))
    payload.author = apiData.author
    if (apiData.billingAddress) payload.billingAddress = JSON.parse(JSON.stringify(apiData))
    payload.comment = apiData.comment
    payload.customPrice = apiData.customPrice
    payload.dateNotificationIfloraSms = apiData.dateNotificationIfloraSms
    if (apiData.deliveryAddress) payload.deliveryAddress = JSON.parse(JSON.stringify(apiData.deliveryAddress))
    if (apiData.deliveryContact) payload.deliveryContact = JSON.parse(JSON.stringify(apiData.deliveryContact))
    payload.deliveryDate = apiData.deliveryDate
    payload.deliveryHour = apiData.deliveryHour
    payload.deliveryMinute = apiData.deliveryMinute
    payload.message = apiData.message
    payload.messageRubanDeuil = apiData.messageRubanDeuil
    payload.moment = apiData.moment
    payload.momentLibre = apiData.momentLibre
    payload.occasion = apiData.occasion
    payload.orderStatus = apiData.orderStatus
    payload.orderType = apiData.orderType
    payload.productCode = apiData.productCode
    payload.qty = apiData.qty
    payload.referenceCommande = apiData.referenceCommande
    payload.signature = apiData.signature
    payload.templateMessage = apiData.templateMessage

    return payload
  }

  /**
   * @returns {GQLCart}
   */
  transformToGQL() {
    const cart = new GQLCart()

    let mainProductEntry

    cart.id = this.apiData.guid
    cart.number = this.apiData.code
    cart.categoryCode = this.apiData.navigationCategory

    cart.delivery = DeliveryTransformer.toGQL(this.apiData, this.context)
    const countryId = _.get(cart, 'delivery.address.country.id', this.apiData.countryCode)

    for (const entry of this.apiData.entries || []) {
      const price = PriceTransformer.toGQL(entry.basePrice || entry.totalPrice)
      const total = PriceTransformer.toGQL(entry.totalPrice || price.multiply(entry.quantity))

      const item = {
        categories: entry.product.categories?.map((cat) => AccessoryCategoryTransformer.toGQL(cat, this.context)) || [],
        customText: entry.product.isRubanDeuil ? this.apiData.messageRubanDeuil : undefined,
        customizations: [],
        isCardMessage: entry.product.isCardMessage || false,
        isFromWorkshop: entry.product.bebloom || false,
        price,
        quantity: entry.quantity,
        removable: !entry.isNotRemovable,
        total,
        variant: {
          code: entry.product.code,
          countryId,
          hasCustomText: entry.product.isRubanDeuil,
          parent: { code: entry.product.baseProduct, countryId },
        },
      }

      if (entry.basePrice && entry.basePrice.value * entry.quantity !== entry.totalPrice) {
        // in hybris, totalPrice = qty * discounted price
        item.discount = PriceTransformer.toGQL(entry.totalPrice)
      }

      if (entry.product.code === CONST.product.premiumOption) {
        cart.serviceItems.push(item)
      } else if (entry.product.accessory) {
        item.variant.parent = {
          __typename: 'Accessory',
          code: item.variant.code,
        }

        cart.accessoryItems.push(item)
      } else {
        mainProductEntry = item
        cart.productItems.push(item)
      }
    }

    // accessory.isFor - in hybris, we have only one product
    for (const item of cart.accessoryItems) {
      item.isFor = mainProductEntry?.variant.code
    }

    for (const voucher of this.apiData.appliedVouchers || []) {
      cart.discountCodes.push(voucher.voucherCode)
    }

    cart.externalReference = this.apiData.referenceCommande

    if (this.apiData.message || this.apiData.signature) {
      cart.joinMessage = {
        message: this.apiData.message,
        signature: this.apiData.signature,
      }
    }

    /**
     * Hybris:
     * - totalPrice = Order total with discount applied
     * - orderDiscounts = Total of discounts that come from vouchers
     * - productDiscounts = Total of discounts that come from products
     * - totalDiscounts = orderDiscounts + productDiscounts
     */
    const totalPrice = PriceTransformer.toGQL(this.apiData.totalPrice)
    const orderDiscounts = PriceTransformer.toGQL(this.apiData.orderDiscounts)
    const totalDiscounts = PriceTransformer.toGQL(this.apiData.totalDiscounts)

    cart.discountCodesPrice = orderDiscounts
    if (totalDiscounts.value > 0) cart.discountedTotal = totalPrice

    // products only, ITF+ excluded
    cart.subTotal = PriceTransformer.toGQL(this.apiData.subTotal)
    if (orderDiscounts.value > 0) cart.discountedSubTotal = cart.subTotal.substract(orderDiscounts)

    cart.total = totalPrice.add(totalDiscounts)
    cart.hasDeliveryMethodSelected = true
    return cart
  }
}

module.exports = CartTransformer
