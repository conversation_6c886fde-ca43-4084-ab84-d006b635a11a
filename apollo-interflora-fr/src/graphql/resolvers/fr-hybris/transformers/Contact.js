const _ = require('lodash')

const config = require('../../../../config')
const GQLContact = require('../../../models/Contact')

const PhoneTransformer = require('../../../transformers/Phone')
const Transformer = require('./Transformer')
const UserTransformer = require('./User')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIOrder, GQLContact, {}>}
 */
class ContactTransformer extends Transformer {
  /**
   *
   * @param {APIOrder} apiData
   * @param {GraphQLContext} context
   * @returns {GQLContact}
   */
  static toGQL(apiData, context) {
    return new ContactTransformer().setContext(context).setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const contact = new GQLContact()

    contact.civility = UserTransformer.toGQLCivility(this.apiData.contactTitle)
    contact.firstName = this.apiData.contactFirstName
    contact.lastName = this.apiData.contactLastName
    if (this.apiData.contactPhone) {
      contact.phone = PhoneTransformer.toGQL(this.apiData.contactPhone, {
        countryCode:
          _.get(this.apiData, 'deliveryAddress.country.isocode') ||
          this.context.countryId ||
          config.apps[this.context.siteId].country,
      })
    }

    return contact
  }
}

module.exports = ContactTransformer
