const CONST = require('../../../../const')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<string[], string[], {}>}
 */
class DeliveryModesTransformer extends Transformer {
  /**
   * @param {string[]} apiData
   * @returns {string[]}
   */
  static toGQL(apiData) {
    return new DeliveryModesTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    let isMourning = false
    let includeStandard = false

    const modes = []

    for (const mode of this.apiData) {
      const gqlMode = CONST.cart.deliveryModeMap[mode]

      if (gqlMode === CONST.cart.deliveryMode.ceremony) {
        isMourning = true
      } else if (gqlMode === CONST.cart.deliveryMode.standard) {
        includeStandard = true
      }

      modes.push(gqlMode)
    }

    if (!isMourning && !includeStandard) {
      modes.push(CONST.cart.deliveryMode.standard)
    }

    return modes
  }
}

module.exports = DeliveryModesTransformer
