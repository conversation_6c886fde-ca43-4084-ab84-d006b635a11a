const CONST = require('../../../../const')
const GQLImage = require('../../../models/Image')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIImage, GQLImage[], {mediaBaseUrl: string?}>}
 */
class ImageTransformer extends Transformer {
  /**
   * @param {APIImage} image
   * @param {GraphQLContext} context
   * @param {{mediaBaseUrl: string?}} options
   * @returns {GQLImage}
   */
  static toGQL(image, context, options) {
    return new ImageTransformer(options).setAPIData(image).setContext(context).toGQL()
  }

  transformToGQL() {
    const image = new GQLImage()

    image.altText = this.apiData.altText
    image.format = this.apiData.format && CONST.images.formats[this.apiData.format]
    image.type = this.apiData.imageType
    image.order = this.apiData.galleryIndex || 0
    image.info = this.apiData.description

    if (this.apiData.url) {
      const baseUrl = this.options.mediaBaseUrl || this.context.appConfig.mediaBaseUrl || '/'
      image.url = this.apiData.url.startsWith('http')
        ? this.apiData.url
        : baseUrl + (this.apiData.url.startsWith('/') ? '' : '/') + this.apiData.url
    } else {
      this.context.log.warn(`no url for image ${JSON.stringify(this.apiData)}`)
      image.url = '#'
    }

    if (!image.altText) {
      this.context.log.debug(`no alt for image ${JSON.stringify(this.apiData)}`)
      image.altText = ''
    }
    if (!image.format) {
      this.context.log.debug(`no format found for image ${JSON.stringify(this.apiData)}`)
      image.format = CONST.images.formats.mobile
    }

    return image
  }
}

module.exports = ImageTransformer
