const CONST = require('../../../../const')
const GQLVoucher = require('../../../models/Voucher')
const CurrencyTransformer = require('./Currency')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIVoucher, GQLVoucher, {}>}
 */
class VoucherTransformer extends Transformer {
  /**
   * @param {APIVoucher} apiData
   * @returns {GQLVoucher}
   */
  static toGQL(apiData) {
    return new VoucherTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const voucher = new GQLVoucher()

    voucher.code = this.apiData.voucherCode
    voucher.description = this.apiData.description
    voucher.endDate = this.apiData.endDate && this.apiData.endDate.slice(0, 10)
    voucher.name = this.apiData.name
    voucher.startDate = this.apiData.startDate && this.apiData.startDate.slice(0, 10)
    if (this.apiData.freeShipping) {
      voucher.type = CONST.cart.discountType.freeShipping
    } else if (this.apiData.currency) {
      voucher.type = CONST.cart.discountType.amount
      voucher.currency = CurrencyTransformer.toGQL(this.apiData.currency)
    } else {
      voucher.type = CONST.cart.discountType.percent
    }
    voucher.value = this.apiData.value !== undefined && Math.round(this.apiData.value * 100)

    return voucher
  }
}

module.exports = VoucherTransformer
