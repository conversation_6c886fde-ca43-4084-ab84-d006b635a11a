const _ = require('lodash')

const CONST = require('../../../../const')
const GQLOrder = require('../../../models/Order')

const AddressTransformer = require('./Address')
const CartTransformer = require('./Cart')
const PhoneTransformer = require('../../../transformers/Phone')
const Transformer = require('./Transformer')
const { dates } = require('../../../../helpers')
const UserTransformer = require('./User')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIOrder, GQLOrder, {}>}
 */
class OrderTransformer extends Transformer {
  /**
   * @param {APIOrder} apiData
   * @param {GraphQLContext} context
   * @returns {GQLOrder}
   */
  static toGQL(apiData, context) {
    return new OrderTransformer().setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLOrder}
   */
  transformToGQL() {
    const order = new GQLOrder()

    const cart = CartTransformer.toGQL(this.apiData, this.context)

    order.id = this.apiData.guid
    order.accessoryItems = cart.accessoryItems
    order.availableEdit = this.apiData.isValidSelfCare || false
    order.billUrl = this.apiData.urlFacture

    if (this.apiData.created) {
      order.createdAt = new Date(this.apiData.created)
    }
    order.countryCode = _.get(cart.delivery, 'address.country.id', this.apiData.countryCode)

    order.delivery = { ...cart.delivery, order }
    if (!Object.values(CONST.order.deliveryStatus).includes(order.delivery.status)) {
      order.delivery.status = CONST.order.deliveryStatus.UNKNOWN
    }

    // Order specific delivery property (bubble up)
    order.delivery.country = order.delivery.address && order.delivery.address.country
    order.discountCodes = (this.apiData.appliedVouchers || []).map((voucher) => voucher.voucherCode)
    order.discountCodesPrice = cart.discountCodesPrice
    order.discountedTotal = cart.discountedTotal
    order.emailHasRegisteredUser = this.apiData.guestCustomer
    order.externalReference = cart.externalReference
    order.joinMessage = cart.joinMessage
    order.number = this.apiData.code

    if (this.apiData.paymentMethod) {
      order.paymentMode = CONST.payment.paymentTypeMapping[this.apiData.paymentMethod]
    }
    // not returned for now but is the model definition
    else if (this.apiData.paymentInfo) {
      if (this.apiData.paymentInfo.payementMode) {
        order.paymentMode = CONST.payment.paymentTypeMapping[this.apiData.paymentInfo.payementMode]
      } else if (this.apiData.paymentInfo.cardType) {
        order.paymentMode =
          CONST.payment.paymentTypeMapping[this.apiData.paymentInfo.cardType.code.toUpperCase()] ||
          CONST.payment.paymentTypeMapping.CB
      }
    }

    order.productItems = cart.productItems
    order.serviceItems = cart.serviceItems

    if (this.apiData.codeOccasion) {
      order.reason = {
        id: this.apiData.codeOccasion,
        label: this.apiData.codeOccasionLabel,
      }
    }

    // reasons uses a custom resolver
    order.subTotal = cart.subTotal
    order.total = cart.total
    order.signature = this.apiData.signature

    if (this.apiData.customer && this.apiData.customer.uid !== 'anonymous') {
      order.user = UserTransformer.toGQL(this.apiData.customer, this.context)
    } else if (this.apiData.user && this.apiData.user.uid !== 'anonymous') {
      order.user = { email: this.apiData.user.uid.split('|').pop(), id: this.apiData.user.uid }
    }

    // voucher has its own resolver

    return order
  }

  /**
   * @param {GQLInputOrder} inputOrder
   * @param {GraphQLContext} context
   * @returns {APIUpdateOrder}
   */
  static toAPIUpdateOrder(inputOrder, context) {
    const { address, contact, date, joinMessage, rangeHourId, sender, time } = inputOrder

    /** @type {APIUpdateOrder} */
    const payload = {}

    if (address) {
      payload.deliveryAddress = AddressTransformer.toAPIAddress(address, true, context)
    }

    if (contact) {
      payload.deliveryContact = {
        firstName: contact.firstName,
        lastName: contact.lastName,
        phone: PhoneTransformer.toAPI(contact.phone, context),
        titleCode: UserTransformer.toAPICivility(contact.civility),
      }
    }

    if (date) {
      payload.deliveryDate = dates.formatToAPI(date)
    }

    if (joinMessage) {
      payload.message = joinMessage.message
      payload.signature = joinMessage.signature
    }

    if (rangeHourId) {
      payload.moment = rangeHourId
    }

    if (sender) {
      payload.billingAddress = AddressTransformer.toAPIAddress(sender, false, context)
    }

    if (time) {
      payload.deliveryHour = time.hour.toString().padStart(2, '0')
      payload.deliveryMinute = time.minute ? time.minute.toString().padStart(2, '0') : '00'
    }

    return payload
  }
}

module.exports = OrderTransformer
