const GQLClassificationFeature = require('../../../models/ClassificationFeature')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIClassificationFeature, GQLClassificationFeature, {}>}
 */
class ClassificationFeatureTransformer extends Transformer {
  /**
   * @param {APIProductClassificationFeature} apiData
   * @returns {GQLProductClassificationFeature}
   */
  static toGQL(apiData) {
    return new ClassificationFeatureTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const feature = new GQLClassificationFeature()

    feature.code = this.apiData.code
    feature.icon = this.apiData.icon
    feature.name = this.apiData.name
    feature.values = this.apiData.featureValues || []

    return feature
  }
}

module.exports = ClassificationFeatureTransformer
