const CONST = require('../../../../const')
const GQLMenu = require('../../../models/Menu')

const Transformer = require('./Transformer')
const UrlTransformer = require('./Url')

/** @typedef {import('../datasources')} */
/** @typedef {import('../types/_typedef')} */

/**
 * @typedef {{
 *  pageType: string
 * }} MenuTransformerOptions
 */

/**
 * @extends {Transformer<APIMenuComponent, GQLMenu[], MenuTransformerOptions>}
 */
class MenuTransformer extends Transformer {
  /**
   * @param {APIMenuComponent} menu
   * @param {GraphQLContext} context
   * @param {MenuTransformerOptions} options
   * @returns {GQLMenu}
   */
  static toGQL(menu, context, options) {
    return new MenuTransformer(options).setAPIData(menu).setContext(context).toGQL()
  }

  transformToGQL() {
    const primaryItems = (this.apiData.navigationNode?.children || []).map((apiNode) =>
      this._nodeToMenu(apiNode, CONST.cms.blockMenuType.PRIMARY)
    )

    const secondaryItems = (this.apiData.navigationNodeSecondary?.children || []).map((apiNode) =>
      this._nodeToMenu(apiNode, CONST.cms.blockMenuType.SECONDARY)
    )

    const otherItems = (this.apiData.navigationNodeOthers?.children || []).map((apiNode) =>
      this._nodeToMenu(apiNode, CONST.cms.blockMenuType.OTHERS)
    )

    return [...primaryItems, ...secondaryItems, ...otherItems]
  }

  /**
   * @param {APINavigationNode} node
   * @param {GQLBlockMenuTypeEnum} type
   * @returns {GQLMenu}
   * @private
   */
  _nodeToMenu(node, type = CONST.cms.blockMenuType.OTHERS) {
    const menu = new GQLMenu()

    menu.blockType = type
    menu.link = false

    if (node.link) {
      menu.categoryCode = node.link.categoryCode || null
      menu.devices = node.visibleOnlyInMobile ? [CONST.devices.mobile] : [CONST.devices.all]
      menu.highlight = `${node.link.highlight}`.toLowerCase() === 'true'

      menu.icon = node.link.cssClass
      menu.label = node.link.linkName
      menu.promote = `${node.link.promote}`.toLowerCase() === 'true'
      const obfuscationExclusions = node.link?.obfuscateLink?.pages?.split(',') || []
      const obfuscated = node.link?.obfuscateLink?.obfuscated === 'true'
      menu.obfuscated =
        (obfuscated && !obfuscationExclusions?.includes(this.options.pageType)) ||
        (!obfuscated && obfuscationExclusions?.includes(this.options.pageType))

      if (node.link.url) {
        menu.href = UrlTransformer.toGQL(node.link, this.context)
      }
    }

    if (!menu.label) {
      menu.label = node.name
    }

    menu.children = (node.children || []).map((apiNode) => this._nodeToMenu(apiNode, type))

    return menu
  }
}

module.exports = MenuTransformer
