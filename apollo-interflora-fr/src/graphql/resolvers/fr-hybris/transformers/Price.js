const GQLPrice = require('../../../models/Price')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIPrice, GQLPrice, {}>}
 */
class PriceTransformer extends Transformer {
  /**
   * @param {APIPrice} apiData
   * @returns {GQLPrice}
   */
  static toGQL(apiData) {
    return new PriceTransformer().setAPIData(apiData).toGQL()
  }

  /**
   * @returns {GQLPrice}
   */
  transformToGQL() {
    const price = new GQLPrice()

    price.value = Math.max(Math.round(this.apiData.value * 100), 0)
    price.currencyIso = this.apiData.currencyIso
    price.tiers = []

    return price
  }
}

module.exports = PriceTransformer
