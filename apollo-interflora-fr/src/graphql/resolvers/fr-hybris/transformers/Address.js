const _ = require('lodash')

const config = require('../../../../config')
const CONST = require('../../../../const')
const GQLAddress = require('../../../models/Address')

const Transformer = require('./Transformer')
const CountryTransformer = require('./Country')
const PhoneTransformer = require('../../../transformers/Phone')
const TownTransformer = require('./Town')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIAddress, GQLAddress, {type: GQLAddressTypeEnum, isDefaultAddress: boolean}>}
 */
class AddressTransformer extends Transformer {
  /**
   * @param {APIAddress} apiData
   * @param {GraphQLContext} context
   * @param {{type: GQLAddressTypeEnum, isDefaultAddress: boolean}} [options]
   * @returns {GQLAddress}
   */
  static toGQL(apiData, context, options) {
    return new AddressTransformer(options).setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLAddress}
   */
  transformToGQL() {
    /** Cyclic imports */
    const UserTransformer = require('./User')

    const { type, isDefaultAddress } = this.options

    const address = new GQLAddress()

    address.id = this.apiData.id
    address.email = this.apiData.email
    address.address = this.apiData.line1
    if (this.apiData.additionalInfo && this.apiData.line2 && this.apiData.additionalInfo !== this.apiData.line2) {
      address.address2 = `${this.apiData.additionalInfo} ${this.apiData.line2}`
    } else {
      address.address2 = this.apiData.additionalInfo || this.apiData.line2
    }
    address.civility = UserTransformer.toGQLCivility(this.apiData.titleCode)
    address.country = this.apiData.country && CountryTransformer.toGQL(this.apiData.country, this.context)
    address.firstName = this.apiData.firstName
    address.lastName = this.apiData.lastName
    address.company = this.apiData.companyName
    address.postalCode = this.apiData.postalCode
    address.stored = Boolean(this.apiData.visibleInAddressBook)

    {
      const codeLocalite = this.apiData.codeLocalite || (this.apiData.region && this.apiData.region.isocode)
      const codePostal = this.apiData.postalCode || (codeLocalite && codeLocalite.replace(/_([^_]+)_[A-Z]{2}$/, '$1'))
      const label =
        this.apiData.town ||
        (this.apiData.region && this.apiData.region.name) ||
        (codeLocalite && codeLocalite.replace(/^(.*?)_[^_]+_[A-Z]{2}$/, '$1'))

      if (codeLocalite) {
        address.town = TownTransformer.toGQL({
          codePostale: codePostal,
          isoCode: codeLocalite,
          label,
        })
      } else if (this.apiData.town) {
        address.town = TownTransformer.toGQL({
          codePostale: codePostal,
          label,
        })
      }
    }

    if (this.apiData.phone) {
      address.phone = PhoneTransformer.toGQL(this.apiData.phone, {
        countryCode:
          _.get(address, 'country.isocode') ||
          _.get(address, 'country.id') ||
          this.context.countryId ||
          config.apps[this.context.siteId].country,
      })
    }

    address.type = this.apiData.shippingAddress ? CONST.address.addressType.delivery : CONST.address.addressType.sender
    address.twitterId = this.apiData.twitterId

    if (type) {
      address.type = type
    }

    if (typeof isDefaultAddress === 'boolean') {
      address.default = isDefaultAddress
    }

    return address
  }

  /**
   * @param {GQLInputAddress} inputAddress
   * @param {boolean} isDeliveryAddress
   * @param {GraphQLContext} context
   * @returns {APIAddress}
   */
  static toAPIAddress(inputAddress, isDeliveryAddress, context) {
    /** Cyclic imports */
    const UserTransformer = require('./User')

    /** @type {APIAddress} */
    const address = {
      additionalInfo: inputAddress.address2,
      companyName: inputAddress.company,
      country: null,
      email: inputAddress.email,
      firstName: inputAddress.firstName,
      id: null,
      lastName: inputAddress.lastName,
      line1: inputAddress.address,
      phone: null,
      postalCode: inputAddress.postalCode,
      region: null,
      shippingAddress: inputAddress.type === CONST.address.addressType.delivery,
      title: null,
      titleCode: null,
      town: null,
      twitterId: inputAddress.twitterId,
    }

    if (inputAddress.civility) {
      address.title = UserTransformer.toAPICivility(inputAddress.civility)
      address.titleCode = UserTransformer.toAPICivility(inputAddress.civility)
    }

    if (inputAddress.phone) {
      address.phone = PhoneTransformer.toAPI(inputAddress.phone, context)
    }

    if (typeof inputAddress.markDefault === 'boolean') {
      address.defaultAddress = inputAddress.markDefault
    }

    if (typeof inputAddress.store === 'boolean' && !inputAddress.id) {
      address.visibleInAddressBook = inputAddress.store
    }

    if (typeof isDeliveryAddress === 'boolean') {
      address.shippingAddress = isDeliveryAddress
    }

    if (inputAddress.countryId) {
      address.country = { isocode: inputAddress.countryId }
    }

    if (inputAddress.townId) {
      address.region = { isocode: inputAddress.townId }
    } else if (inputAddress.townLabel) {
      address.town = inputAddress.townLabel
    }

    if (inputAddress.id) {
      address.id = inputAddress.id
    }

    return address
  }
}

module.exports = AddressTransformer
