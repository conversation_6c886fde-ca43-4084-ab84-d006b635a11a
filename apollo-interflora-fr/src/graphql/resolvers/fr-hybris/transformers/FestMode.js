const GQLFestMode = require('../../../models/FestMode')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APIFestMode, GQLFestMode, {}>}
 */
class FestModeTransformer extends Transformer {
  /**
   * @param {APIFestMode} apiData
   * @returns {GQLFestMode}
   */
  static toGQL(apiData) {
    return new FestModeTransformer().setAPIData(apiData).toGQL()
  }

  transformToGQL() {
    const festMode = new GQLFestMode()

    festMode.id = this.apiData.id
    festMode.date = new Date(this.apiData.date)
    festMode.label = this.apiData.label

    return festMode
  }
}

module.exports = FestModeTransformer
