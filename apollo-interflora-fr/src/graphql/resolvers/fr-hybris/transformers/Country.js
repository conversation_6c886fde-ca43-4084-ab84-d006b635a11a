const i18nCountries = require('i18n-iso-countries')

const GQLCountry = require('../../../models/Country')

const Transformer = require('./Transformer')

/** @typedef {import('../_typedef')} */

/**
 * @extends {Transformer<APICountry, GQLCountry, {}>}
 */
class CountryTransformer extends Transformer {
  /**
   * @param {APICountry} apiData
   * @param {GraphQLContext} context
   * @returns {GQLCountry}
   */
  static toGQL(apiData, context) {
    return new CountryTransformer().setAPIData(apiData).setContext(context).toGQL()
  }

  /**
   * @returns {GQLCountry}
   */
  transformToGQL() {
    const country = new GQLCountry()

    country.id = this.apiData.isocode.toUpperCase() // Ensure it's ISO 3166-2 compliant
    country.label = i18nCountries.getName(this.apiData.isocode, this.context.language) || this.apiData.name // for now, hybris returns some non ISO-3166-2 codes
    country.default = this.apiData.isDefault || false
    country.highlighted = this.apiData.topCountriesIndex
    country.knownPostalCode = this.apiData.isocode === 'FR'

    return country
  }
}

module.exports = CountryTransformer
