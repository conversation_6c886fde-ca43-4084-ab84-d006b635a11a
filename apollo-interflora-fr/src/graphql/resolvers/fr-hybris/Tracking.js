const BaseResolver = require('../ct-datocms/Tracking')

class TrackingResolver extends BaseResolver {
  /**
   * @param {string} email
   * @param {boolean} consent
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _updateAbandonedCartEligibility(email, consent, context) {
    const partialCartDto = {
      billingAddress: {
        email,
      },
      statutRelance: consent ? 'ACCEPTED_MAIL_AUTO' : null,
    }
    const apiCart = await context.dataSources.hybrisCart.updateCart(partialCartDto)
    {
      const { auth } = context
      const apiCartId = apiCart ? (auth && auth.isAnonymous ? apiCart.guid : apiCart.code) : null
      await context.loaders.carts.prime(apiCartId, apiCart)
    }
    return !!apiCart
  }
}

module.exports = TrackingResolver
