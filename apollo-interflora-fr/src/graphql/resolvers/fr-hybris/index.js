const { createCTLoaders } = require('../../dataloaders/commercetools')
const DataLoaders = require('./dataloaders')

const AddressResolver = require('./Address')
const AuthenticationResolver = require('./Authentication')
const CartResolver = require('./Cart')
const CmsResolver = require('./Cms')
const ContactResolver = require('./Contact')
const ForgotPasswordResolver = require('./ForgotPassword')
const GeoResolver = require('./Geo')
const NewsletterResolver = require('./Newsletter')
const OrderResolver = require('./Order')
const ProductResolver = require('./Product')
const RootResolver = require('./Root')
const UserResolver = require('./User')
const TrackingResolver = require('./Tracking')
const { createDatoLoaders } = require('../../dataloaders/datocms')

class FRLegacyResolvers {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context

    this.address = new AddressResolver()
    this.authentication = new AuthenticationResolver()
    this.cart = new CartResolver()
    this.cms = new CmsResolver()
    this.contact = new ContactResolver()
    this.forgotPassword = new ForgotPasswordResolver()
    this.geo = new GeoResolver()
    this.newsletter = new NewsletterResolver()
    this.order = new OrderResolver()
    this.product = new ProductResolver()
    this.root = new RootResolver()
    this.tracking = new TrackingResolver()
    this.user = new UserResolver()

    this._dataloaders
  }

  get dataloaders() {
    if (!this._dataloaders) {
      this._dataloaders = new DataLoaders(this.context)
      this._dataloaders.commerceTools = createCTLoaders(this.context)
      this._dataloaders.datocms = createDatoLoaders(this.context)
    }

    return this._dataloaders
  }
}

/**
 * @param {GraphQLContext} context
 * @returns {FRLegacyResolvers | FRLegacyResolvers}
 */
const createResolvers = (context) => {
  return new FRLegacyResolvers(context)
}

module.exports = {
  createResolvers,
}
