const config = require('../../../config')
const BaseGeoResolver = require(`../${config.site}/Geo`)

class GeoResolver extends BaseGeoResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{countryId: string, search: string}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLTown[]>}
   */
  async getTowns(_parent, _args, _context) {
    // getTowns not supported for GFS
    return []
  }
}
module.exports = GeoResolver
