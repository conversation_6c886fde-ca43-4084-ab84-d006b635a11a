const AddressResolver = require('./Address')
const AuthenticationResolver = require('./Authentication')
const CartResolver = require('./Cart')
const CmsResolver = require('./Cms')
const ContactResolver = require('./Contact')
const ForgotPasswordResolver = require('./ForgotPassword')
const GeoResolver = require('./Geo')
const OrderResolver = require('./Order')
const ProductResolver = require('./Product')
const RootResolver = require('./Root')
const UserResolver = require('./User')
const TrackingResolver = require('./Tracking')

class GFSResolvers {
  /**
   * @param {GraphQLContext} context
   */
  constructor(context) {
    this.context = context

    this.address = new AddressResolver(this.context)
    this.authentication = new AuthenticationResolver(this.context)
    this.cart = new CartResolver(this.context)
    this.cms = new CmsResolver(this.context)
    this.contact = new ContactResolver(this.context)
    this.forgotPassword = new ForgotPasswordResolver(this.context)
    this.geo = new GeoResolver(this.context)
    this.order = new OrderResolver(this.context)
    this.product = new ProductResolver(this.context)
    this.root = new RootResolver(this.context)
    this.user = new UserResolver(this.context)
    this.tracking = new TrackingResolver()
  }
}

/**
 * @param {GraphQLContext} context
 * @returns {GFSResolvers}
 */
const createResolvers = (context) => {
  return new GFSResolvers(context)
}

module.exports = {
  createResolvers,
}
