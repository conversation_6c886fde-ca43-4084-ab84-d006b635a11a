/** @typedef {import('../../datasources/commerceTools/_typedef')} */
const debug = require('debug')('itf:cart:it')

const config = require('../../../config')
const CONST = require('../../../const')

/** @typedef {import('../ct-datocms/Cart')} CartResolver */

const BaseCartResolver = require(`../${config.site}/Cart`)

const { GraphQLApolloError } = require('../../errors')

const {
  AvailabilityDeliveryTimeRangeResultTransformer,
  AvailabilityTownTransformer,
} = require('../../transformers/availability')
const { CTCartTransformer } = require('../../transformers/commerceTools')
const { GFSDateRangeTransformer } = require('../../transformers/gfs')

const { loadGFSProduct, loadProduct } = require('./shared')
const {
  getMarketingFeesAmount,
  gfsHolidays,
  splitGFSCode: splitCode,
  gfsAllAvailabilities,
} = require('../../../helpers/gfs')
const { commonDateRanges } = require('../../../helpers/dates')

const { isInterfloraPlus, itfPlusDiscountIdFromCart } = require('../../../helpers/commercetools/interfloraplus')
const { getDistributionChannels } = require('../../../helpers/commercetools')

class CartResolver extends BaseCartResolver {
  /**
   * @param {GQLCart} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {boolean}
   */
  canPremium(_parent, _args, _context) {
    return false
  }

  /**
   * @param {GQLDelivery} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(_parent, _args, context) {
    const cart = await this._getApiCart(context)
    if (!cart) return []

    if (this._isMainCountryCart(cart, context)) {
      return []
    }
    if (!cart.customLineItems?.length) {
      return []
    }

    // Getting context country as we cannot mix products from different countries
    const countryId = context.countryId
    const [country, holidays] = await Promise.allSettled([
      context.loaders.gfs.countries.load(countryId),
      context.loaders.gfs.holidays.load(countryId),
    ])
    if (![country, holidays].every((i) => i.status === 'fulfilled')) {
      return []
    }

    const products = await Promise.allSettled(
      cart.customLineItems.map((p) => loadGFSProduct(countryId, p.slug, context))
    )
    if (!products.every((i) => i.status === 'fulfilled')) {
      return []
    }
    let availabilities = []
    products.forEach((product) => {
      availabilities = [...availabilities, ...gfsAllAvailabilities(country?.value, product?.value, true)]
    })

    if (products.length > 1) {
      availabilities = commonDateRanges(availabilities)
    }

    if (!availabilities?.length) {
      return []
    }
    const excludeHolidays = gfsHolidays(country?.value, holidays?.value)
    return GFSDateRangeTransformer.toGQL(
      {
        availabilities,
        excluded: excludeHolidays,
      },
      context
    )
  }

  /**
   * @param {null} _parent
   * @param {GraphQLContextArgs<{date: Date}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getCartDeliveryTimeRanges(_parent, _args, context) {
    return AvailabilityDeliveryTimeRangeResultTransformer.toGQL([CONST.commerceTools.moment.Wholeday], context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   */
  async getDeliveryFeasibility(_parent, _args, _context) {
    return { feasible: true }
  }

  /**
   * @returns {string[]}
   */
  getDeliveryModes() {
    return [CONST.cart.deliveryMode.standard]
  }

  /**
   * @param {GraphQLContext} context
   * @returns {CTExternalTaxRateDraft}
   */
  getExternalTaxRate(context) {
    return {
      amount: 0,
      country: context.countryId,
      includedInPrice: true,
      name: 'gfs 0',
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: GQLTown, valid: boolean }[]>}
   */
  async getTownsForCart(_parent, args, context) {
    // @todo: remove when hybris is not used anymore
    if (config.site === CONST.site.fr) {
      return super.getTownsForCart(...arguments)
    }

    const places = await context.loaders.availability.towns.load({
      country: args.countryId || context.countryId,
      search: args.search,
    })

    return places
      .slice(0, config.apps[context.siteId].towns.max)
      .map((place) => ({ town: AvailabilityTownTransformer.toGQL(place, context), valid: true }))
  }

  /**
   * @param {{action: string}[]} actions
   * @param {GraphQLContext} context
   */
  setExternalTaxRateActions(actions, context) {
    actions
      .filter(
        ({ action, shippingMethod, externalTaxRate }) =>
          action === 'setShippingMethod' && !!shippingMethod && !externalTaxRate
      )
      .forEach((action) => {
        action.externalTaxRate = this.getExternalTaxRate(context)
      })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLCartProductItemType[]>}
   */
  suggestedAccessories(_parent, _args, _context) {
    return []
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateCart(_parent, args, context) {
    /**
     * @todo
     * - [ ] business rules
     */
    const {
      cart: { accessories = [], products = [] },
    } = args

    /** @type {CTCart} */
    let apiCart = await this._getApiCart(context)

    let actions = []

    // switch country
    let isCountryChanged = false
    const cartCountry = apiCart?.shippingAddress?.country
    if (cartCountry) {
      isCountryChanged = cartCountry !== context.countryId

      const inputCountry = args.cart.delivery?.address?.countryId
      if (inputCountry && inputCountry !== cartCountry) {
        isCountryChanged |= true
      }
    }

    if (isCountryChanged) {
      if (apiCart?.lineItems.length > 0 || apiCart?.customLineItems.length > 0) {
        if (apiCart.customLineItems.some((item) => args.cart.products.some((product) => product.code === item.slug))) {
          const err = new GraphQLApolloError(`cannot mix international products from different countries`, 400)
          err.addError('mix-destination-international', 'cannot mix international products from different countries')
          throw err
        }

        if (
          apiCart.lineItems.some((item) =>
            args.cart.products.some((product) => product.code === `${item.productId}#${item.variant.id}`)
          )
        ) {
          const err = new GraphQLApolloError(`cannot mix international and local products`, 400)
          err.addError('mix-destination', 'cannot mix international and local products')
          throw err
        }
      }

      // the cart is empty. We can allow switching
      context.log.warn(args, `[gfs updatecart] mutating input args to switch to ${context.countryId}`)
      args.cart.delivery = {
        countryId: context.countryId,
      }
    }

    const externalTaxRate = this.getExternalTaxRate(context)

    let entriesToAdd = []
    let entriesToDelete = []
    let entriesToUpdate = []

    if (apiCart) {
      // update
      // ----------------------------------------------------------------------
      for (const lineItem of apiCart.lineItems) {
        actions.push({
          action: 'removeLineItem',
          lineItemId: lineItem.id,
        })
      }

      actions = actions.concat(this._updateShippingCostsActions(apiCart, context))
      const { actions: updateActions, adminActions } = await this._updateCartActionsOutsideProducts(args, context)
      actions = actions.concat(updateActions)

      if (adminActions.length > 0) {
        debug(`update cart (admin): %O`, adminActions)
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: adminActions,
          version: apiCart.version,
        })
      }

      const entries = await this._updateCartSplitEntries(args, 'customLineItems', context)
      entriesToAdd = entries.entriesToAdd
      entriesToDelete = entries.entriesToDelete
      entriesToUpdate = entries.entriesToUpdate
    } else {
      // create
      // ----------------------------------------------------------------------
      // call parent with no product
      await super.updateCart(
        _parent,
        {
          ...args,
          cart: {
            ...args.cart,
            accessories: [],
            products: [],
          },
        },
        context
      )

      apiCart = await this._getApiCart(context)

      apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
        actions: this._updateShippingCostsActions(apiCart, context),
        version: apiCart.version,
      })

      for (const entry of [...accessories, ...products]) {
        const { code, size } = splitCode(entry.code)
        entriesToAdd.push({
          ...entry,
          productId: code,
          variantId: size,
        })
      }
    }

    const [categories, marketingFees] = await Promise.all([
      context.loaders.gfs.allCategories.load(true),
      context.loaders.commerceTools.allGFSMarketingFees.load(true),
    ])
    const sizeMap = {
      1: 'small',
      2: 'medium',
      3: 'large',
    }

    for (const lineItem of apiCart.customLineItems) {
      if (!lineItem.taxRate) {
        actions.push({
          action: 'setCustomLineItemTaxRate',
          customLineItemId: lineItem.id,
          externalTaxRate,
        })
      }
    }

    for (const entry of entriesToAdd) {
      const product = await loadProduct(context.countryId, entry.productId, context)
      const variant = product.variants.find((v) => v.code === entry.code)

      const marketingFee = {
        centAmount: getMarketingFeesAmount({
          categories,
          marketingFees,
          product: await loadGFSProduct(context.countryId, entry.productId, context),
        }),
        currencyCode: variant.price.currencyIso,
        fractionDigits: 2,
        type: 'centPrecision',
      }
      const size = sizeMap[entry.code.split('#').pop()]

      actions.push({
        action: 'addCustomLineItem',
        custom: {
          fields: {
            description: variant.label,
            marketingFee,
            size,
            sku: variant.code,
          },
          type: { key: CONST.commerceTools.customTypes.customLineItem },
        },
        externalTaxRate,
        money: {
          centAmount: variant.price.value,
          currencyCode: variant.price.currencyIso,
        },
        name: {
          [context.language]: product.name,
        },
        quantity: entry.quantity,
        slug: entry.code,
      })
    }
    for (const entry of entriesToDelete) {
      actions.push({
        action: 'removeCustomLineItem',
        customLineItemId: entry.lineItemId,
      })
    }
    for (const entry of entriesToUpdate) {
      actions.push({
        action: 'changeCustomLineItemQuantity',
        customLineItemId: entry.lineItemId,
        quantity: entry.quantity,
      })
    }

    this.setExternalTaxRateActions(actions, context)
    debug(`update cart: %O`, actions)
    apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
      actions,
      version: apiCart.version,
    })

    // actions that need actual cart state after the update of items / customer
    {
      const actions = []
      const interfloraPlus = await isInterfloraPlus(apiCart, context)
      if (apiCart.custom?.fields?.interfloraplus !== interfloraPlus) {
        actions.push({
          action: 'setCustomField',
          name: 'interfloraplus',
          value: interfloraPlus,
        })
      }
      debug(`update cart: %O`, actions)
      apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
        actions,
        version: apiCart.version,
      })
    }

    context.session.apiCart = { id: apiCart.id }

    await Promise.all([
      context.session?.save?.(),
      context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
    ])

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @override
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ cartDelivery: GQLInputCartDelivery }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateDeliveryCart(_parent, args, context) {
    const actions = await this._updateDeliveryActions(args.cartDelivery, context)

    let apiCart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)

    if (actions.length) {
      apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
        actions,
        version: apiCart.version++,
      })
      if (actions.find(({ action }) => action === 'setShippingMethod')) {
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: [
            {
              action: 'setShippingMethodTaxRate',
              externalTaxRate: this.getExternalTaxRate(context),
            },
          ],
          version: apiCart.version,
        })
      }

      if (context.session.apiCart?.id !== apiCart.id) {
        context.log.info(`updateDeliveryCart - set cart id in session ${apiCart.id}`)
        context.session.apiCart = { id: apiCart.id }
      }

      await Promise.all([
        context.session?.save?.(),
        context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
        context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
      ])
    }

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @returns {{ action: string }[]}
   */
  _updateShippingCostsActions(apiCart, context) {
    const actions = []

    if (apiCart.taxMode !== 'External') {
      actions.push({
        action: 'changeTaxMode',
        taxMode: 'External',
      })
      actions.push({
        action: 'setCustomShippingMethod',
        externalTaxRate: {
          amount: config.gfs.customShipping.externalTaxRate.amount,
          country: context.countryId.toUpperCase(),
          name: `tax for ${config.site}`,
        },
        shippingMethodName: config.gfs.customShipping.name,
        shippingRate: {
          price: {
            centAmount: config.gfs.customShipping.shippingCosts,
            currencyCode: config.apps[config.apps.defaultSiteId].currency,
          },
        },
      })
    }

    return actions
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {boolean}
   */
  _isMainCountryCart(cart, context) {
    const cartCountry = cart.shippingAddress?.country ?? ''

    return cartCountry.toLowerCase() === context.appConfig.country.toLowerCase()
  }
}

module.exports = CartResolver
