const config = require('../../../config')
const CONST = require('../../../const')

const BaseProductResolver = require(`../${config.site}/Product`)

const { GQLAvailabilityDateRanges, GQLProduct, GQLProductVariant } = require('../../models')

const { CTCentPrecisionMoneyTransformer } = require('../../transformers/commerceTools')
const { GFSDateRangeTransformer, GFSProductTransformer, GFSProductListTransformer } = require('../../transformers/gfs')

const { loadGFSProduct, loadProduct, loadProductVariant } = require('./shared')
const { gfsAvailability, splitGFSCode: splitCode, getConfigCategoriesBySlug } = require('../../../helpers/gfs')
const { isInterfloraPlusProduct } = require('../../../helpers/commercetools/interfloraplus')
const PriceTransformer = require('../../transformers/gfs/Price')

class ProductResolver extends BaseProductResolver {
  /**
   * @param {GQLProduct} product
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{code: string}>}
   */
  async getLinkedProducts(product, _args, context) {
    if (Array.isArray(product.linked)) return product.linked

    product.linked = []
    const { code } = splitCode(product.code)

    const [categories, country, marketingFees] = await Promise.all([
      context.loaders.gfs.allCategories.load(true),
      context.loaders.gfs.countries.load(context.countryId),
      context.loaders.commerceTools.allGFSMarketingFees.load(true),
    ])

    const products = await context.loaders.gfs.allProducts.load(context.countryId)
    for (const p of products) {
      if (p.IntercatCode === code) continue
      const linkedProduct = GFSProductTransformer.toGQL(p, context, { categories, country, marketingFees })
      if (linkedProduct) product.linked.push(linkedProduct)
      if (product.linked.length === config.products.linked.desired) break
    }

    return product.linked
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, slugUrl: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct>}
   */
  async getProduct(_parent, args, context) {
    return loadProduct(context.countryId, args.code || args.slugUrl, context)
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{city: string, code: string, countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDateRange[]>}
   */
  async getProductDeliveryDateRanges(parent, args, context) {
    return (await this.getProductDeliveryDates(parent, args, context)).ranges
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{city: string, code: string, countryId, postalCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAvailabilityDateRanges>}
   */
  async getProductDeliveryDates(_parent, args, context) {
    const country = await context.loaders.gfs.countries.load(context.countryId)
    const product = await loadGFSProduct(context.countryId, args.code, context)

    const availability = gfsAvailability(country, product)
    if (!availability) return new GQLAvailabilityDateRanges()

    return new GQLAvailabilityDateRanges(GFSDateRangeTransformer.toGQL({ availabilities: [availability] }, context))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{city: string, codes: string[], countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{
   *   code: string,
   *   availability: GQLAvailabilityDateRanges
   * }[]>}
   */
  async getProductsDeliveryDates(_parent, args, context) {
    const { codes } = args

    const ranges = await Promise.all(
      codes.map(async (code) => {
        const country = await context.loaders.gfs.countries.load(context.countryId)
        const product = await loadGFSProduct(context.countryId, code, context)

        const availability = gfsAvailability(country, product)
        if (!availability) return new GQLAvailabilityDateRanges()

        return new GQLAvailabilityDateRanges(GFSDateRangeTransformer.toGQL(availability, context))
      })
    )

    return codes.map((code, index) => ({
      availability: ranges[index],
      code,
    }))
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} args
   * @param {GraphQLContext} context
   * @param {Object} _info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductImages(product, args, context, _info) {
    const { filterFormats, filterTypes } = args

    let _product = product
    if (!(_product instanceof GQLProduct))
      _product = await loadProduct(product.countryId || context.countryId, product.code, context)

    return _product.getImages(filterFormats, filterTypes)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  filters: GQLInputProductFilter[],
   *  pagination: GQLInputPagination,
   *  gelluleSlug: string,
   *  search: string
   * }>} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLProductList>}
   */
  async getProductList(_parent, args, context) {
    const { gelluleSlug, filters = [] } = args

    /** @type {GQLInputProductFilter[]} */
    let filtering = []
    if (filters.length) {
      filtering = filters
    } else if (['0', CONST.gfs.categories.slugs.all_occasions].includes(gelluleSlug)) {
      filtering = [
        {
          filterId: CONST.product.filterAsSort,
          values: [
            CONST.product.sorts.priceAsc,
            CONST.product.sorts.categoryFlowerFirst,
            CONST.product.sorts.categoryFuneralLast,
          ],
        },
      ]
    }

    const [products, country, categories, category, marketingFees] = await Promise.all([
      context.loaders.gfs.allProducts.load(context.countryId),
      context.loaders.gfs.countries.load(context.countryId),
      context.loaders.gfs.allCategories.load(true),
      context.loaders.gfs.categoryPage.load({
        locale: context.language,
        slug: context.countryId?.toLowerCase(),
      }),
      context.loaders.commerceTools.allGFSMarketingFees.load(true),
    ])
    /** @type {GFSUnit | null} */
    let unit = null
    if (country.UnitId) {
      unit = await context.loaders.gfs.units.load(country.UnitId)
    }

    const categoriesMappedBySlug = getConfigCategoriesBySlug()

    const categoryIds = categoriesMappedBySlug.get(gelluleSlug)
    let availableProducts = products.filter((p) => {
      if (p.IsAddOn) return false

      if (categoryIds && categoryIds.length && !p.ProductCategories.some((c) => categoryIds.includes(c))) {
        return false
      }

      const availability = gfsAvailability(country, p)
      return Boolean(availability)
    })

    const priceFilter = filtering.find((f) => f.filterId === CONST.product.filterAsRange)
    if (priceFilter) {
      const [min, max] = priceFilter.values
      availableProducts = availableProducts.filter((p) => {
        let { MinPrice, MaxPrice } = gfsAvailability(country, p).apiData
        MinPrice = PriceTransformer.toGQL(MinPrice, context, {
          categories,
          marketingFees,
          product: p,
        })
        MaxPrice = PriceTransformer.toGQL(MaxPrice, context, {
          categories,
          marketingFees,
          product: p,
        })

        return MaxPrice.value >= min && MinPrice.value <= max
      })
    }

    const orderFilter = filtering.find((f) => f.filterId === CONST.product.filterAsSort)
    if (orderFilter) {
      const orderedProducts = availableProducts.filter((p) => p.Ordering > 0).sort((a, b) => a.Ordering - b.Ordering)
      const unorderedProducts = availableProducts
        .filter((p) => p.Ordering <= 0)
        .sort((aProduct, bProduct) =>
          this._sortProductsByOrderFilter(aProduct, bProduct, orderFilter, country, categoriesMappedBySlug)
        )
      availableProducts = [...orderedProducts, ...unorderedProducts]
    }

    const limit = config.categories.pageSize
    const offset =
      args.pagination.offset !== undefined ? args.pagination.offset : limit * Math.max(args.pagination.page, 0)

    let categoryOption = {
      id: categoriesMappedBySlug.has(gelluleSlug) ? gelluleSlug : undefined,
      title: category?.title ?? country.Name,
    }
    if (config.site === CONST.site.fr) {
      const hybrisCategory = await context.loaders.hybris.categories.load('0')

      if (hybrisCategory?.description) {
        categoryOption.description = hybrisCategory.description
      }
      if (hybrisCategory?.name) {
        categoryOption.name = hybrisCategory.name
      }
      if (hybrisCategory?.title) {
        categoryOption.title = hybrisCategory.title
      }
    }

    return GFSProductListTransformer.toGQL(availableProducts, context, {
      categories,
      category: categoryOption,
      country,
      marketingFees,
      offset,
      unit,
    })
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {*}
   */
  async getProductProperty(product, _args, context, info) {
    if (this._isFromMainCountry(product)) return super.getProductProperty(...arguments)

    let _product = product
    if (!(_product instanceof GQLProduct))
      _product = await loadProduct(product.countryId || context.countryId, product.code, context)

    return _product[info.fieldName]
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} args
   * @param {GraphQLContext} context
   * @returns {GQLImage[]}
   */
  async getProductVariantImages(variant, args, context) {
    if (this._isFromMainCountry(variant)) return super.getProductVariantImages(...arguments)

    let _variant = variant
    if (!(_variant instanceof GQLProductVariant))
      _variant = await loadProductVariant(variant.countryId || context.countryId, variant.code, context)

    const { filterFormats, filterTypes } = args

    return (
      _variant?.images.filter(
        (image) =>
          (filterFormats.length === 0 || filterFormats.includes(image.format)) &&
          (filterTypes.length === 0 || filterTypes.includes(image.type))
      ) ?? []
    )
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getProductVariantProperty(variant, _args, context, info) {
    if (this._isFromMainCountry(variant)) return super.getProductVariantProperty(...arguments)

    let _variant = variant
    if (!(_variant instanceof GQLProductVariant))
      _variant = await loadProductVariant(variant.countryId || context.countryId, variant.code, context)

    if (_variant && info.fieldName === 'delivery') {
      const delivery = { ..._variant.delivery }

      const price = {
        centAmount: config.gfs.customShipping.shippingCosts,
        currencyCode: config.apps[config.apps.defaultSiteId].currency,
        fractionDigits: config.gfs.customShipping.fractionDigits,
        type: 'centPrecision',
      }

      delivery.price = CTCentPrecisionMoneyTransformer.toGQL(price, context)
      return delivery
    }
    return _variant[info.fieldName]
  }

  /**
   * @param {{ countryId: string }} productOrVariant
   * @returns {boolean}
   */
  _isFromMainCountry(productOrVariant) {
    return (
      isInterfloraPlusProduct(productOrVariant) ||
      productOrVariant.countryId === config.apps[config.apps.defaultSiteId].country
    )
  }

  /**
   * @param {GFSProduct} aProduct
   * @param {GFSProduct} bProduct
   * @param {GQLInputProductFilter} orderFilter
   * @param {GFSCountry} country
   * @param {Map<string, number[]>} categoriesMappedBySlug
   * @returns {number}
   */
  _sortProductsByOrderFilter(aProduct, bProduct, orderFilter, country, categoriesMappedBySlug) {
    const getFirstPrice = (product) => {
      return gfsAvailability(country, product).firstPrice
    }
    const getSlugPosition = (product, slugName) => {
      const slugIds = categoriesMappedBySlug.get(slugName)
      const categoryIds = product.ProductCategories ?? []
      return categoryIds.some((id) => slugIds.includes(id)) ? 1 : 0
    }

    return orderFilter.values.reduce((position, order) => {
      let move
      switch (order) {
        case CONST.product.sorts.priceAsc:
          move = getFirstPrice(aProduct) - getFirstPrice(bProduct)
          break
        case CONST.product.sorts.priceDesc:
          move = getFirstPrice(bProduct) - getFirstPrice(aProduct)
          break
        case CONST.product.sorts.categoryFlowerFirst:
          move =
            getSlugPosition(bProduct, CONST.gfs.categories.slugs.flower) -
            getSlugPosition(aProduct, CONST.gfs.categories.slugs.flower)
          break
        case CONST.product.sorts.categoryFuneralLast:
          move =
            getSlugPosition(aProduct, CONST.gfs.categories.slugs.funeral) -
            getSlugPosition(bProduct, CONST.gfs.categories.slugs.funeral)
          break
        default:
          move = 0
      }
      return move !== 0 ? move : position
    }, 0)
  }
}

module.exports = ProductResolver
