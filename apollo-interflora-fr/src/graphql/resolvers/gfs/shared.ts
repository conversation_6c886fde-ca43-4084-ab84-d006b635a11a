import { GFSProductTransformer } from '../../transformers/gfs'
import { splitGFSCode } from '../../../helpers/gfs'

export const loadGFSProduct = async (countryId: string, code: string, context: CTDatocmsContext) => {
  const { code: productCode } = splitGFSCode(code)
  const product = await context.loaders.gfs.products.load({
    code: productCode,
    countryId,
  })
  if (!product) return null

  return product
}

export const loadProduct = async (countryId: string, code: string, context: CTDatocmsContext) => {
  const product = await loadGFSProduct(countryId, code, context)
  if (!product) return null

  const [country, categories, marketingFees] = await Promise.all([
    context.loaders.gfs.countries.load(countryId),
    context.loaders.gfs.allCategories.load(true),
    context.loaders.commerceTools.allGFSMarketingFees.load(true),
  ])

  /** @type {GFSUnit} */
  let unit = null
  if (country.UnitId) {
    unit = await context.loaders.gfs.units.load(country.UnitId)
  }

  const gfsProduct = GFSProductTransformer.toGQL(product, context, { categories, country, marketingFees, unit })
  gfsProduct.countryId = countryId
  for (const variant of gfsProduct.variants || []) {
    variant.countryId = countryId
  }
  return gfsProduct
}

export const loadProductVariant = async (countryId: string, code: string, context: CTDatocmsContext) => {
  const { code: productCode } = splitGFSCode(code)
  const product = await loadProduct(countryId, productCode, context)
  if (!product) return null

  let variant = product.variants.find((v) => v.code === code)
  if (!variant && code.startsWith(`${countryId}-${countryId}-`)) {
    // may occur from PFS - country id is prepended multiple times
    const strippedCode = code.replace(new RegExp(`^(${countryId}-)+`), '$1')
    variant = product.variants.find((v) => v.code === strippedCode)
  }
  return variant ?? null
}
