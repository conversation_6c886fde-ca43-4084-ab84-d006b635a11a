const config = require('../../../config')
const CONST = require('../../../const')
const BaseCmsResolver = require(`../${config.site}/Cms`)
const { loadGFSProduct } = require('./shared')

const { GraphQLApolloError } = require('../../errors')

const { GFSPageCategoryTransformer, GFSPageProductTransformer } = require('../../transformers/gfs')
const DatoCmsPageTransformer = require('../../transformers/datocms/Page')

const { splitGFSCode: splitCode, gfsAvailability } = require('../../../helpers/gfs')
const { getFRHybrisContext } = require('../fr/shared')

class CmsResolver extends BaseCmsResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *   route: GQLInputRoute,
   *   previousRoute: GQLInputRoute,
   *   zoneNames: string[]
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLPage>}
   */
  async getPage(_parent, args, context) {
    const { route } = args

    if (!['category', 'parent_category', 'product'].includes(route.name)) {
      return super.getPage(_parent, args, context)
    }

    if (config.site === CONST.site.fr && route.name === 'product') {
      const frHybrisContext = getFRHybrisContext(context)
      frHybrisContext.categoryId = '0'
      return context.resolvers.hybris.cms.getPage(_parent, args, frHybrisContext)
    }

    let canonical = null

    switch (route.name) {
      case 'category':
      case 'parent_category': {
        /** @typedef {[GFSProduct[], GFSCountry, GFSCategory[], DatoInternationalCategoryPage]} PromiseAllGFS */
        /** @type {PromiseAllGFS} */
        const [products = [], country, categories = [], categoryPage] = await Promise.all([
          context.loaders.gfs.allProducts.load(context.countryId),
          context.loaders.gfs.countries.load(context.countryId),
          context.loaders.gfs.allCategories.load(true),
          context.loaders.gfs.categoryPage.load({
            locale: context.language,
            slug: context.countryId?.toLowerCase(),
          }),
          context.loaders.commerceTools.allGFSMarketingFees.load(true),
        ])

        /** @type {GFSProduct[]} */
        const availableProducts = products.filter((p) => {
          if (p.IsAddOn) return false

          const availability = gfsAvailability(country, p)
          return Boolean(availability)
        })
        /** @type {GFSCategory[]} */
        const availableCategories = categories.filter((category) =>
          availableProducts.some((product) =>
            product.ProductCategories?.some((pcId) => [category.Id, category.ParentId].includes(pcId))
          )
        )

        let _categoryPage
        let page
        if (categoryPage) {
          _categoryPage = JSON.parse(JSON.stringify(categoryPage))
          _categoryPage.name = country.Name
          _categoryPage.title = country.Name
          _categoryPage._seoMetaTags.forEach((metaTag) => {
            if (/title/.test(metaTag.attributes?.property) || /title/.test(metaTag.attributes?.name)) {
              metaTag.attributes.content = country.Name
            }
          })

          page = DatoCmsPageTransformer.toGQL(_categoryPage, context, {
            canonical,
            type: 'category',
            zones: args.zones || [],
          })
        }

        const gqlPage = GFSPageCategoryTransformer.toGQL(country, context, {
          canonical,
          categories: availableCategories,
          page,
          type: 'category',
        })

        if (config.site === CONST.site.fr) {
          /** @type {GQLPage} */
          const hybrisPage = await context.resolvers.hybris.cms.getPage(_parent, args, getFRHybrisContext(context))
          hybrisPage.gellules = gqlPage.gellules
          return hybrisPage
        }

        return gqlPage
      }

      case 'product': {
        const param = route.params.find((p) => p.name === 'slugProduct')
        const { countryId, code } = splitCode(param.value)

        const product = await loadGFSProduct(countryId, code, context)
        if (!product) {
          throw new GraphQLApolloError('Not found', 404)
        }

        let productPage = await context.loaders.gfs.productPage.load(context.language)
        if (productPage) {
          productPage = JSON.parse(JSON.stringify(productPage))
          productPage.name = product.Name
          productPage.title = product.Name
          productPage._seoMetaTags.forEach((metaTag) => {
            if (/title/.test(metaTag.attributes?.property) || /title/.test(metaTag.attributes?.name)) {
              metaTag.attributes.content = product.Name
            }
          })
        }

        /** @type {GFSCountry} */
        const gfsCountry = await context.loaders.gfs.countries.load(context.countryId)

        return GFSPageProductTransformer.toGQL(product, context, {
          canonical,
          categoryTitle: gfsCountry.Name,
          cmsPage: productPage,
          type: 'product',
        })
      }
    }
  }
}

module.exports = CmsResolver
