/** @typedef {import('./es')} ESResolvers */
/** @typedef {import('./fr')} FRResolvers */
/** @typedef {import('./fr-hybris')} FRHybrisResolvers */
/** @typedef {import('./gfs')} GFSResolvers */
/** @typedef {import('./it')} ITResolvers */
/** @typedef {import('./se')} SEResolvers */
/** @typedef {import('./pt')} PTResolvers */
/** @typedef {import('./dk')} DKResolvers */

/**
 * @typedef {ESResolvers | FRResolvers | FRHybrisResolvers | GFSResolvers | ITResolvers | PTResolvers | SEResolvers | DKResolvers  } Resolvers
 */
