/** @typedef {import('../../datasources/_typedef').DataSources} DataSources */

/** @typedef {import('../../dataloaders/availability').loadersDefinition} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/bloomreach').loadersDefinition} BloomreachDataLoaders */
/** @typedef {import('../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs').loadersDefinition} GFSDataLoaders */
/** @typedef {import('../../datasources/signaturgruppen').loadersDefinition: SignaturgruppenLoaders} */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: DataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    bloomreach: BloomreachDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *    signaturgruppen: SignaturgruppenLoaders
 *  }
 * }} DKContext
 */
