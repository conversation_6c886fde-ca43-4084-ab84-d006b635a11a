const CONST = require('../../../const')
const { addTZOffsetToDate } = require('../../../helpers/dates')
const { getAvailablePaymentMethods } = require('../../../helpers/features')
const BaseResolver = require('../ct-datocms/Cart')
const config = require('../../../config')
const { verifyJwtToken } = require('../../../helpers/jws')
const jwt = require('jsonwebtoken')
const { variantIds } = require('../../../helpers/commercetools')

const VERIFICATION_AGE = 18

class CartResolver extends BaseResolver {
  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<null|number>}
   */
  async ageVerification(cart, _args, context) {
    const countryId = cart.delivery?.address?.country?.id
    if (countryId && countryId !== context.appConfig.country) {
      return null
    }

    const variants = await Promise.all(
      (cart?.allItems ?? [])
        .map((product) => variantIds(product.variant.code))
        .map(async (variant) => {
          const product = await context.loaders.commerceTools.products.load({
            channel: variant.channel,
            code: variant.productId,
          })
          return [product.masterVariant, ...product.variants].find(
            (productVariant) => productVariant.id === variant.variantId
          )
        })
    )
    const hasAlcoholProduct = variants
      .map((variant) => variant.attributes.find((attribute) => attribute.name === 'alcohol_product')?.value ?? false)
      .some((value) => value)
    if (hasAlcoholProduct) {
      return VERIFICATION_AGE
    }
    return null
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async ageVerified(cart, _args, context) {
    const apiCart = await context.loaders.commerceTools.carts.load(cart.id)
    const ageVerificationToken = apiCart?.custom?.fields?.ageVerificationToken
    if (!ageVerificationToken) {
      return false
    }
    const { idbrokerdk_age_verified = '' } = jwt.decode(ageVerificationToken)
    return Boolean(idbrokerdk_age_verified) && /^18:true$/.test(idbrokerdk_age_verified)
  }

  /**
   * @param {string} token
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   * @protected
   */
  async _verifyAgeTokenValidation(token, context) {
    let ageVerified = false
    try {
      const { idbrokerdk_age_verified = '' } = await verifyJwtToken(token, async (kid) =>
        context.loaders.signaturgruppen.sigBrokerJwks.load(kid)
      )
      ageVerified = Boolean(idbrokerdk_age_verified) && /^18:true$/.test(idbrokerdk_age_verified)
    } catch (error) {
      console.error(error)
      context.log.error(error)
    }
    return ageVerified
  }

  async paymentMethods(_parent, _args, context) {
    const cart = await this._getApiCart(context)

    /** @type {GQLPaymentMethod[]} */
    let paymentMethods = await getAvailablePaymentMethods(cart, context)
    const mobilePayIndex = paymentMethods.findIndex((pm) => pm.mode === CONST.payment.paymentMode.MOBILE_PAY)
    if (mobilePayIndex !== -1) {
      const stringDeliveryDate = cart.shippingAddress.custom?.fields?.date
      if (stringDeliveryDate) {
        if (
          // Disable Mobile Pay if the order delivery is more than X (disableIfDeliveryDateAndOrderDateDifferenceIs amount of) days after the day of the order
          new Date(stringDeliveryDate).getTime() - addTZOffsetToDate(new Date(), context.appConfig.timezone).getTime() >
          86400000 *
            config.payment.rules[CONST.payment.paymentMode.MOBILE_PAY].disableIfDeliveryDateAndOrderDateDifferenceIs
        ) {
          paymentMethods[mobilePayIndex].availableForCart = false
        }
      }
    }
    return paymentMethods
  }
}

module.exports = CartResolver
