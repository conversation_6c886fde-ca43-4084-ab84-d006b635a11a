const apm = require('elastic-apm-node')

const CONST = require('../../../const')

const GQLDepartment = require('../../models/Department')

const CountryTransformer = require('../../transformers/gfs/Country')
const { DatoFloristRegionTransformer } = require('../../transformers/datocms')
const { AvailabilityTownTransformer } = require('../../transformers/availability')

const { getValidCountries } = require('../../../helpers/countries')

class GeoResolver {
  getCadastralCities() {
    throw new Error('Not implemented')
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  showAll: boolean,
   *  withCurrent: boolean
   * }>} args
   * @param {GraphQLContext} context
   * @returns {GQLCountry[]}
   */
  async getCountries(_parent, args, context) {
    const { showAll, withCurrent } = args
    try {
      const results = await context.loaders.gfs.allCountries.load(true)

      /** @type {{ id: string, label: string }[]} */
      const countries = getValidCountries(
        context.language,
        results.filter((country) => showAll || country.Enabled).map((country) => country.CountryCode)
      )

      return countries
        .filter((country) => {
          if (context.countryId === country.id && !withCurrent) return false
          return true
        })
        .map(({ id }) => CountryTransformer.toGQL(id, context))
    } catch (error) {
      apm.captureError(error, { custom: error })
      return []
    }
  }

  /**
   * @param {GQLDepartment|{ slugUrl: string }} department
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<any>}
   */
  async getDepartmentProperty(department, _args, context, info) {
    if (info.fieldName === 'type') return CONST.geo.departmentTypes.METROPOLITAN

    const value = department[info.fieldName]
    if (value || value === null) return value

    if (!(department instanceof GQLDepartment)) {
      const region = await context.loaders.datocms.floristRegion.load({
        locale: context.language,
        slug: department.slugUrl,
      })
      if (!region) return null
      const gqlDepartment = DatoFloristRegionTransformer.toGQL(region, context, { type: 'floristDepartment' })
      return gqlDepartment[info.fieldName]
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLDepartment}[]>}
   */
  async getDepartments(_parent, _args, context) {
    const regionsList = await context.loaders.datocms.floristRegions.load(context.language)
    return regionsList.sort((a, b) => a.label?.localeCompare(b.label))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{slugUrl: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLDepartment}>}
   */
  async getDepartmentBySlug(_parent, args, context) {
    const { slugUrl, includeAddress } = args
    const department = await context.loaders.datocms.floristRegion.load({
      locale: context.language,
      slug: slugUrl.toLocaleLowerCase(),
    })
    if (!department) return null

    return DatoFloristRegionTransformer.toGQL(department, context, {
      customPath: includeAddress ? context.appConfig.storeLocator.customPath : undefined,
      type: 'floristDepartment',
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{countryId: string, search: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLTown[]>}
   */
  async getTowns(_parent, args, context) {
    const places = await context.loaders.availability.towns.load({
      country: args.countryId || context.countryId,
      language: context.language,
      search: args.search,
    })

    return places
      .slice(0, context.appConfig.towns.max)
      .map((place) => AvailabilityTownTransformer.toGQL(place, context))
  }
}

module.exports = GeoResolver
