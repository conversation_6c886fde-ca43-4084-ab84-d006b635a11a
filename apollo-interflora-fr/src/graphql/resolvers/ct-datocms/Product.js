const _ = require('lodash')
const apm = require('elastic-apm-node')

const config = require('../../../config')
const CONST = require('../../../const')
const { variantIds } = require('../../../helpers/commercetools')
const { priceChannelKeyFromSlug, seaKeyFromSlug, seaKeyFromVariantCode } = require('../../../helpers/sea')

const BaseResolver = require('./Base')
const {
  GQLAvailabilityDateRanges,
  GQLProduct,
  GQLQualifier,
  GQLProductVariant,
  GQLSearchSuggestions,
} = require('../../models')
const {
  CTCentPrecisionMoneyTransformer,
  CTPriceTransformer,
  CTProductListTransformer,
  CTProductListFiltersTransformer,
  CTProductTransformer,
} = require('../../transformers/commerceTools')

const { DatoProductListTransformer } = require('../../transformers/datocms')
const { AvailabilityDateRangeTransformer } = require('../../transformers/availability')
const BloomreachSearchSuggestionsTransformer = require('../../transformers/bloomreach/SearchSuggestions')

const { GraphQLDatoError, GraphQLApolloError } = require('../../errors')

const { isInterfloraPlusProduct } = require('../../../helpers/commercetools/interfloraplus')
const { getCategoryTree } = require('../../../helpers/categoryTree')
const { getCeremonyTimeRange } = require('../../../helpers/dates')
const CTFacetService = require('../../../helpers/commercetools/facet/FacetService')
const { getBloomreachTracking } = require('../../../helpers/tracking')
const LegalText = require('../../../helpers/dato/LegalText')

/**
 * @param {GQLProduct} product
 * @param {{}} _args
 * @param {GraphQLContext} context
 * @returns {Promise<GQLProduct>}
 */
class ProductResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLProduct[]}
   */
  async getRoundUpProducts(_parent, _args, context) {
    const datoConfiguration = await context.loaders.datocms.configuration.load(context.language)
    const configurationRoundUpProducts = datoConfiguration?.roundUpProducts || []
    return Promise.all(
      configurationRoundUpProducts.map((product) =>
        this._loadProductFromCT({ code: product.commercetoolsProduct, countryId: context.countryId }, context)
      )
    )
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLDateRange[]}
   */
  async getDeliveryAvailabilities(_parent, _args, context) {
    const days = (await context.loaders.availability.undeliverableDays.load({ country: context.countryId })) || []
    return AvailabilityDateRangeTransformer.toGQL(days, context)
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{code: string}[]>}
   */
  async getLinkedProducts(product, _args, context) {
    // Linked products aren't display on the digital catalog
    if (context.siteId === CONST.siteIds.catalog) {
      return []
    }

    if (this._isFromGfs(product)) return context.resolvers.gfs.product.getLinkedProducts(...arguments)

    if (!Array.isArray(product.linked)) {
      if (!product.isLoaded(CONST.dataSources.datocms)) {
        const _product = await this._loadProductFromDato({ code: product.code }, context)
        product.merge(_product)
      }

      let linkedProducts = []
      if (product.mainCategoryId) {
        linkedProducts =
          (
            await context.loaders.commerceTools.productLists.load({
              id: product.mainCategoryId,
              opts: {
                filters: [],
                limit: config.products.linked.desired + 1,
                locale: context.language,
              },
            })
          )?.results ?? []
      }
      // if products fetch from productLists loader
      // product projection search filters do not have any "!=" or "not in" operator
      product.linked = linkedProducts
        .filter((ctProduct) => ctProduct.id !== product.code)
        .slice(0, config.products.linked.desired)
        .map((ctProduct) => CTProductTransformer.toGQL(ctProduct, context))
    }

    return product.linked
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ productCode: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLMessageCard[]>}
   */
  async getMessageCards(_parent, args, context) {
    const { productCode } = args
    // Set the warehouse type by product code
    let warehouseType
    {
      const ctProduct = await context.loaders.commerceTools.products.load({
        code: productCode,
      })
      if (!ctProduct?.productType?.id) return []
      // Some types of warehouse correspond to the type of product delivery
      const deliveryType = ctProduct.masterVariant?.attributes?.find((attr) => attr.name === 'delivery_type')?.value
      warehouseType = deliveryType?.key
      const productType = await context.loaders.commerceTools.productTypes.load(ctProduct.productType.id)
      // For mourning we check the product type in CT
      if (productType?.key?.toUpperCase() === CONST.product.warehouseType.MOURNING) {
        warehouseType = CONST.product.warehouseType.MOURNING
      } else if (!deliveryType) {
        return []
      }
    }
    const messageCards = []
    /** @type {DatoMessageCardRecord} */
    const datoMessageCards = await context.loaders.datocms.messageCards.load({
      locale: args.locale || context.language,
      warehouseType,
    })

    for (const { category, exclude, icon, id, label, products } of datoMessageCards?.gellules ?? []) {
      if (exclude) continue
      const messageCard = {
        cards: [],
        categoryCode: category?.commercetoolsCategory,
        gellule: { icon, id, label },
      }
      for (const product of products) {
        const productMessageCard = await this._loadProductFromCT(
          { code: product.commercetoolsProduct, countryId: context.countryId, isAccessory: true },
          context
        )
        messageCard.cards.push(productMessageCard)
      }
      messageCards.push(messageCard)
    }

    return messageCards
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct|null>}
   */
  async getPremiumAccessory(_parent, _args, context) {
    if (!config.products?.interfloraplus?.key) return null
    return this._loadProductFromCT(
      {
        code: config.products.interfloraplus.key,
        countryId: context.countryId,
      },
      context
    )
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{code: string, slugUrl: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct>}
   */
  async getProduct(_parent, args, context) {
    const { code, slugUrl: slug } = args

    const product = await this._loadProductFromDato(
      {
        code,
        seaKey: seaKeyFromSlug(slug),
        slug,
      },
      context
    )

    if (!product) throw new GraphQLApolloError('Not Found', 404)

    return product
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{city: string, code: string, countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDateRange[]>}
   */
  async getProductDeliveryDateRanges(parent, args, context) {
    return (await this.getProductDeliveryDates(parent, args, context)).ranges
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{city: string, code: string, countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAvailabilityDateRanges>}
   */
  async getProductDeliveryDates(_parent, args, context) {
    const { city, code, countryId, postalCode, province } = args

    return this._getProductDeliveryDates(
      {
        city,
        code,
        countryId: countryId ?? context.countryId,
        postalCode,
        province,
      },
      context
    )
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{city: string, codes: string[], countryId, postalCode: string, province: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{
   *   code: string,
   *   availability: GQLAvailabilityDateRanges
   * }[]>}
   */
  async getProductsDeliveryDates(_parent, args, context) {
    const { city, codes, countryId, postalCode, province } = args

    const ranges = await Promise.all(
      codes.map((code) =>
        this._getProductDeliveryDates(
          {
            city,
            code,
            countryId: countryId ?? context.countryId,
            postalCode,
            province,
          },
          context
        )
      )
    )

    return codes.map((code, index) => ({
      availability: ranges[index],
      code,
    }))
  }

  /**
   * @param {GQLProduct} product
   * @param {{}} args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductImages(product, args, context, info) {
    if (this._isFromGfs(product)) return context.resolvers.gfs.product.getProductImages(...arguments)

    if (!(product instanceof GQLProduct) || !product.isLoaded(CONST.dataSources.datocms)) {
      let seaKey
      if (product instanceof GQLProduct) {
        seaKey = product.seaKey
      }
      const _product = await this._loadProductFromDato(
        {
          code: product.code,
          isAccessory: Boolean(product.isAccessory),
          seaKey,
        },
        context
      )

      if (product instanceof GQLProduct) {
        product.merge(_product)
      } else {
        return this.getProductImages(_product, args, context, info)
      }
    }

    if (!product.isLoaded(CONST.dataSources.commerceTools)) {
      const _product = await this._loadProductFromCT(
        {
          channel: priceChannelKeyFromSlug(product.slugUrl),
          code: product.code,
          countryId: product.countryId || context.countryId,
          isAccessory: Boolean(product.isAccessory),
          locale: 'en',
        },
        context
      )
      product.merge(_product)
    }

    const { filterFormats, filterTypes } = args

    return [
      ...product.getImages(filterFormats, filterTypes),
      ...(await this.getProductVariantImages(product.defaultVariant, args, context, info)),
    ]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  filters: GQLInputProductFilter[],
   *  pagination: GQLInputPagination,
   *  gelluleSlug: string,
   *  search: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProductList>}
   */
  async getProductList(_parent, args, context) {
    const list = await this.loadProductListFromDato(args, context)
    const sortFilter = args.filters?.find?.((filter) => filter.filterId === CONST.product.filterAsSort)

    const offset = args.pagination?.offset ?? Math.max(0, args.pagination.page) * list.pageSize
    const limit = list.pageSize

    if (config.bloomreach.enabled && (!sortFilter || args.search) && list.bloomreachEnabled) {
      const batchLimit = 50
      const { uid } = await getBloomreachTracking(context)

      const productKeysOpts = {
        id: list.categoryId,
        limit: batchLimit,
        search: args.search,
        slug: list.categorySlug,
        uid,
      }

      let bloomreachProductKeys = {
        keys: [],
        keywordRedirect: null,
        numFound: 0,
      }

      try {
        bloomreachProductKeys = await context.loaders.bloomreach.searchProductKeys.load(productKeysOpts)
      } catch (e) {
        apm.captureError(e, { custom: e })
      }

      const { keys: bloomreachKeys, keywordRedirect, numFound } = bloomreachProductKeys
      if (keywordRedirect?.['redirected url']) {
        list.redirectUrl = keywordRedirect['redirected url']
      }

      if (numFound) {
        let batchKeys = bloomreachKeys ?? []
        let batchOffset = batchKeys.length
        let rounds = 0
        const keys = []

        while (batchOffset <= numFound) {
          for (const product of await context.loaders.commerceTools.productsKeysIn.load({
            filters: CTFacetService.instance(context.language).getCtFilters(args.filters ?? []),
            keys: batchKeys,
            limit: batchLimit,
            offset: 0,
          })) {
            keys.push(product.key)
          }
          if (batchOffset < numFound) {
            try {
              const batch = await context.loaders.bloomreach.searchProductKeys.load({
                ...productKeysOpts,
                offset: batchOffset,
              })
              batchKeys = batch.keys ?? []
              batchOffset += batchKeys.length
            } catch (e) {
              apm.captureError(e, { custom: e })
              break
            }
          } else {
            break
          }

          if (++rounds > 20) {
            // should not happen!
            const err = new GraphQLApolloError(`getProductList - bloomreach - too many iterations`)
            err.extensions.args = args
            err.extensions.numFound = numFound
            apm.captureError(err, { custom: err })
            break
          }
        }

        // * Functionally, will hit the redis cache as long as the list of products (keys) and filters are the same
        // ! If Bloomreach is down, keys === [] and there will not be any products
        const products = await context.loaders.commerceTools.productsKeysIn.load({
          filters: CTFacetService.instance(context.language).getCtFilters(args.filters ?? []),
          keys: keys.slice(offset, offset + limit),
          limit,
          offset: 0,
        })

        list.filters = []
        if (list.categoryId) {
          const { filters } = await this.loadProductListFromCT(args, context, {
            categoryId: list.categoryId,
            pageSize: 0,
          })
          list.filters = filters
        }
        list.products = products.map((p) => CTProductTransformer.toGQL(p, context))
        list.total = keys.length

        return list
      }
    }

    if (list.categoryId) {
      if (config.categories.categoryTitle.exclude?.includes('defaultCTCategory')) {
        const category = await context.loaders.commerceTools.categories.load(list.categoryId)
        if (category.key === config.categories.defaultCategoryKey) {
          list.categoryTitle = null
        }
      }

      let results
      let total

      // price sort
      if (sortFilter && [CONST.product.sorts.priceAsc, CONST.product.sorts.priceDesc].includes(sortFilter.values[0])) {
        const keyprices = await context.loaders.commerceTools.productKeysPriceSorted.load({
          categoryId: list.categoryId,
          options: {
            filters: args.filters,
          },
        })
        if (sortFilter.values[0] === CONST.product.sorts.priceDesc) {
          keyprices.reverse()
        }

        total = keyprices.length
        results = await context.loaders.commerceTools.productsKeysIn.load({
          filters: CTFacetService.instance(context.language).getCtFilters(args.filters ?? []),
          keys: keyprices.slice(offset, offset + limit).map((k) => k.key),
          limit,
          offset: 0,
        })
      }

      const response = await this.loadProductListFromCT(args, context, {
        categoryId: list.categoryId,
        pageSize: list.pageSize,
        results,
        total,
      })
      list.products = response.products
      list.filters = response.filters
      list.total = response.total
    }

    return list
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  variantCode: string,
   *  customizations: GQLInputProductCustomizationLine[],
   * }>} _args
   * @param {CTDatocmsContext} _context
   * @returns {Promise<GQLProductCustomizationPreview>}
   */
  async getProductPreview(_parent, _args, _context) {
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLProduct} product
   * @param {*} args
   * @param {CTDatocmsContext} context
   * @param {GraphGQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getProductProperty(product, args, context, info) {
    if (this._isFromGfs(product)) return context.resolvers.gfs.product.getProductProperty(...arguments)
    if (
      (product[info.fieldName] !== undefined && !Array.isArray(product[info.fieldName])) ||
      (product instanceof GQLProduct &&
        product.isLoaded(CONST.dataSources.commerceTools) &&
        product.isLoaded(CONST.dataSources.datocms))
    ) {
      return product[info.fieldName]
    }

    if (!(product instanceof GQLProduct)) {
      let seaKey
      if (product.slug) {
        seaKey = seaKeyFromSlug(product.slug)
      }
      const gqlProduct = await this._loadProductFromDato(
        { code: product.code, isAccessory: Boolean(product.isAccessory), seaKey },
        context
      )
      gqlProduct.countryId = product.countryId
      return this.getProductProperty(gqlProduct, args, context, info)
    }

    if (
      !product.isLoaded(CONST.dataSources.commerceTools) &&
      [
        'classification',
        'defaultVariant',
        'defaultVariantLowCost',
        'defaultVariants',
        'discount',
        'externalUrl',
        'hasCustomText',
        'images',
        'isFromWorkshop',
        'isRubanDeuil',
        'key',
        'legalNotice',
        'price',
        'subtitle',
        'type',
        'variants',
        'isGenericAccessory',
      ].includes(info.fieldName)
    ) {
      const _product = await this._loadProductFromCT(
        {
          channel: product.seaKey
            ? config.sea[product.seaKey].priceChannelKey
            : priceChannelKeyFromSlug(product.slugUrl),
          code: product.code,
          countryId: product.countryId,
          isAccessory: Boolean(product.isAccessory),
        },
        context
      )
      if (!_product) throw new GraphQLApolloError('Not Found', 404)

      product.merge(_product)
    }
    if (
      !product.isLoaded(CONST.dataSources.datocms) &&
      [
        'accessories',
        'additionalInfo',
        'pictureText',
        'badge',
        'card',
        'cards',
        'deliveryDetail',
        'deliveryTypeText',
        'description',
        'highlightText',
        'name',
        'reinsurances',
        'slugUrl',
        'useQuantitySelector',
      ].includes(info.fieldName)
    ) {
      const _product = await this._loadProductFromDato(
        { code: product.code, isAccessory: Boolean(product.isAccessory), seaKey: product.seaKey },
        context
      )
      if (!_product) throw new GraphQLApolloError('Not Found', 404)

      product.merge(_product)
    }

    if (['pictureText', 'description', 'deliveryTypeText'].includes(info.fieldName)) {
      let legalText = await LegalText.load(product.productType, context)
      product.description = `${product._datoDescription ?? ''} ${legalText.description}`.trim()
      product.pictureText = legalText.pictureText
      product.deliveryTypeText = legalText.deliveryTypeText
    }

    if (['accessories', 'card', 'cards'].includes(info.fieldName)) {
      product.countryId = product.countryId || config.apps[config.apps.defaultSiteId].country
      const accessoryCollections = await this._loadProductAccessories(product, context)
      return this._filterAccessories(accessoryCollections, args, info)
    }

    if (info.fieldName === 'addToCart') {
      return this.getAccessoryAddMode(product)
    }

    const accessoryInfoFields = ['legalNotice', 'hasCustomText', 'useQuantitySelector', 'isGenericAccessory']
    if (product.isAccessory && accessoryInfoFields.includes(info.fieldName)) {
      const accessoryInfo = await context.loaders.datocms.accessoryInfos.load(product.code)
      product.hasCustomText = accessoryInfo?.hasCustomText ?? false
      product.legalNotice = accessoryInfo?.legalNotice || product._ctLegalNotice
      const maxQuantity = accessoryInfo?.maxQuantity ?? product.variants?.[0]?.maxQuantity ?? 0
      product.useQuantitySelector = maxQuantity > 1
      product.isGenericAccessory = this._isGenericAccessory(
        product._skipAvailabilityOn,
        product.countryId || context.countryId
      )

      return product[info.fieldName]
    } else if (info.fieldName === 'hasCustomText') {
      if (!product.type) {
        product.type = await this._loadProductType({ code: product.code }, context)
      }

      if (product.type === CONST.product.types.bundle) {
        product.hasCustomText = (
          await Promise.allSettled(
            product.bundledProducts.map((code) => context.loaders.datocms.accessoryInfos.load(code))
          )
        ).some((info) => info.status === 'fulfilled' && info.value?.hasCustomText)
      }

      product.hasCustomText = product.hasCustomText ?? false
    } else if (info.fieldName === 'legalNotice') {
      product.legalNotice = product._ctLegalNotice
    }

    if (info.fieldName === 'type') {
      return this._loadProductType({ code: product.code }, context)
    }

    return product[info.fieldName]
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<GQLImage[]>}
   */
  async getProductVariantImages(variant, args, context, info) {
    if (this._isFromGfs(variant)) return context.resolvers.gfs.product.getProductVariantImages(...arguments)

    const { filterFormats, filterTypes } = args

    /** @type {GQLProduct} */
    let product
    if (variant.parent instanceof GQLProduct && variant.parent.isLoaded(CONST.dataSources.datocms)) {
      product = variant.parent
    } else {
      const ctProduct = await this._loadCTProductFromVariant(variant, context)
      product = await this._loadProductFromDato(
        { code: ctProduct.id, seaKey: seaKeyFromVariantCode(variant.code) },
        context
      )
    }
    product.countryId = variant.countryId
    const productImages = product.getImages(filterFormats, filterTypes)
    const variantImages = await this.getProductVariantProperty(variant, args, context, info)

    return [
      ...productImages,
      ...variantImages.filter(
        (image) =>
          (filterFormats.length === 0 || filterFormats.includes(image.format)) &&
          (filterTypes.length === 0 || filterTypes.includes(image.type))
      ),
    ]
  }

  /**
   * @param {GQLProductVariant} variant
   * @param {{}} args
   * @param {CTDatocmsContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getProductVariantProperty(variant, args, context, info) {
    if (this._isFromGfs(variant)) return context.resolvers.gfs.product.getProductVariantProperty(...arguments)

    if (['code', 'discount', 'price'].includes(info.fieldName) && variant[info.fieldName] !== undefined)
      return variant[info.fieldName]

    if (info.fieldName === 'code') {
      if (variant.priceChannelId) {
        const priceChannel = await context.loaders.commerceTools.channels.load(variant.priceChannelId)
        let seaChannelKey
        for (const [key, entry] of Object.entries(config.sea)) {
          if (entry.priceChannelKey === priceChannel.key && priceChannel.key) {
            seaChannelKey = key
            break
          }
        }
        if (seaChannelKey) {
          const { productId, variantId } = variantIds(variant.code)
          return `${productId}#${seaChannelKey}#${variantId}`
        }
      }

      return variant.code
    }

    if (variant instanceof GQLProductVariant) {
      const accessoryInfoFields = ['maxQuantity']
      if (variant.parent.isAccessory && accessoryInfoFields.includes(info.fieldName)) {
        const accessoryInfo = await context.loaders.datocms.accessoryInfos.load(variant.parent.code)
        variant.maxQuantity = accessoryInfo?.maxQuantity ?? variant.maxQuantity

        return variant[info.fieldName]
      }

      if (info.fieldName === 'delivery') {
        const delivery = { ...variant.delivery }
        if (Number.isFinite(variant.shippingFee?.value)) {
          delivery.price = variant.shippingFee
          return delivery
        }

        const appConfig = config.apps[context.siteId]

        const productType = await this._loadProductType({ code: variant.parent.code }, context)
        /** @type {CTShippingMethod} */
        let apiShippingMethod
        if (productType === CONST.product.types.mourning) {
          apiShippingMethod = await context.loaders.commerceTools.shippingMethodByKey.load(
            CONST.commerceTools.productTypes.mourning
          )
        } else {
          apiShippingMethod = await context.loaders.commerceTools.defaultShippingMethod.load(true)
        }

        for (const zoneRate of apiShippingMethod.zoneRates) {
          /** @type {CTZone} */
          const zone = await context.loaders.commerceTools.zones.load(zoneRate.zone.id)
          if (zone.locations.some((location) => location.country === appConfig.country)) {
            const rate = zoneRate.shippingRates.find((rate) => rate.price.currencyCode === appConfig.currency)
            if (rate) {
              delivery.price = CTCentPrecisionMoneyTransformer.toGQL(rate.price, context)
            }
            break
          }
        }

        return delivery
      }

      if (['discount', 'price'].includes(info.fieldName)) {
        const { variantId } = variantIds(variant.code)

        const ctProduct = await this._loadCTProductFromVariant(variant, context)
        if (!ctProduct) {
          context.log.warn(`[variant.${info.fieldName}] no product found in ct for ${variant.code}`)
          return null
        }
        const ctVariant = [ctProduct.masterVariant, ...ctProduct.variants].find((v) => v?.id === variantId)
        if (!ctVariant) return null

        let taxCategory
        if (ctProduct.taxCategory) {
          taxCategory = await context.loaders.commerceTools.taxCategories.load(ctProduct.taxCategory.id)
        } else {
          apm.captureError(new GraphQLApolloError(`[non blocking] no tax category on product ${ctProduct.key}`))
        }

        let priceEntry = ctVariant.price ?? ctVariant.prices?.[0]
        if (info.fieldName === 'discount') {
          if (priceEntry.value === null || !priceEntry.discounted) {
            variant.discounted = null
            return null
          }

          if (priceEntry.discounted) {
            priceEntry = priceEntry.discounted
          }
        }

        variant[info.fieldName] = CTPriceTransformer.toGQL(priceEntry, context, { taxCategory })
        return variant[info.fieldName]
      }

      if (['qualifiers', 'reinsurances'].includes(info.fieldName)) {
        const product = variant?.parent
        if (!product?.isLoaded(CONST.dataSources.datocms)) {
          const { productId } = variantIds(variant.code)

          const _product = await this._loadProductFromDato(
            {
              code: productId,
              seaKey: seaKeyFromVariantCode(variant.code),
            },
            context
          )
          product.merge(_product)
        }

        if (info.fieldName === 'qualifiers') {
          let slugQualifier = new GQLQualifier()
          slugQualifier.label = product.name
          slugQualifier.orderSlug = 0
          slugQualifier.slugUrlPart = product.slugUrl
          slugQualifier.type = {
            name: 'slug',
            value: CONST.product.qualifierTypes.slug,
          }
          slugQualifier.value = product.slugUrl

          try {
            const ctProduct = await this._loadCTProductFromVariant(variant, context)
            const { variantId } = variantIds(variant.code)
            const ctVariant = [ctProduct.masterVariant, ...ctProduct.variants].find((v) => v?.id === variantId)
            const linkedProducts = ctVariant.attributes?.find((attr) => attr.name === 'linked_products')?.value
            /* If the lazy-loaded variant's parent isn't the real ct variant's parent and the real ct variant has it in its linkedProducts
              it means we are trying to sell it as a variant of the lazy-loaded parent and we have to replace the qualifier
            */
            if (linkedProducts?.some((p) => p.id === product.code) && ctProduct.code !== product.code) {
              const realParentProduct = await this._loadProductFromDato(
                {
                  code: ctProduct.id,
                  seaKey: seaKeyFromVariantCode(variant.code),
                },
                context
              )

              if (realParentProduct) {
                slugQualifier.label = realParentProduct.name
                slugQualifier.orderSlug = 0
                slugQualifier.slugUrlPart = realParentProduct.slugUrl
                slugQualifier.type = {
                  name: 'slug',
                  value: CONST.product.qualifierTypes.slug,
                }
                slugQualifier.value = realParentProduct.slugUrl
              }
            }
          } catch (e) {
            context.log.warn(`[variant.${info.fieldName}] for ${variant.code}, couldn't check real parent: ${e}`)
          }
          return [slugQualifier, ...variant.qualifiers]
        } else {
          return product?.reinsurances ?? []
        }
      }

      if (['accessories', 'cards'].includes(info.fieldName)) {
        const variantAccessories = await this._loadProductVariantAccessories(variant, context)
        return this._filterAccessories(variantAccessories, args, info)
      }

      return variant[info.fieldName]
    }

    const { channel, variantId } = variantIds(variant.code)

    const ctProduct = await this._loadCTProductFromVariant(variant, context)
    if (!ctProduct) {
      // do not throw as other needed attributes may have been set upfront
      const err = new GraphQLApolloError(`[variant property] could not load product from variant ${variant.code}`)
      apm.captureError(err, { custom: err })
      return null
    }
    const product = CTProductTransformer.toGQL(ctProduct, context, {
      countryId: variant.countryId,
      isAccessory: Boolean(variant.parent?.isAccessory),
      isCardMessage: Boolean(variant.parent?.isCardMessage),
      priceChannelKey: channel,
    })

    const _variant = product?.variants.find((v) => v.id === variantId)
    if (_variant) _variant.countryId = variant.countryId

    if (!_variant) return null

    return this.getProductVariantProperty(_variant, args, context, info)
  }

  /**
   * @param {{
   *  filters: GQLInputProductFilter[],
   *  pagination: GQLInputPagination,
   *  search: string,
   * }} args
   * @param {CTDatocmsContext} context
   * @param {{
   *   categoryId: string,
   *   pageSize: number,
   *   results?: CTProduct[],
   *   total?: number
   * }} options
   * @returns {Promise<{
   *  filters: GQLProductFilterType[],
   *  products: GQLProduct[],
   *  total: number
   * }>}
   */
  async loadProductListFromCT(args, context, options) {
    const locale = context.language || 'en'
    const { filters: requestFilters, pagination, search } = args
    const { categoryId, pageSize } = options

    let offset
    if (pagination.offset !== undefined) {
      offset = pagination.offset
    } else {
      offset = Math.max(pagination.page, 0) * pageSize
    }

    let filters = []
    let { results, total } = options

    if (!results || !total) {
      const response = await context.loaders.commerceTools.productLists.load({
        id: categoryId,
        opts: {
          filters: requestFilters,
          limit: config.categories.pageSize,
          locale,
          offset,
          search,
          siteId: context.siteId,
        },
      })
      results = response.results
      total = response.total
      filters = CTProductListFiltersTransformer.toGQL(response.facets, context, { locale })
    } else {
      ;({ filters } = await this.loadProductListFromCT(args, context, {
        categoryId,
        pageSize: 0,
      }))
    }

    const geoFilter = await this._detectGeographicalFilter(categoryId, requestFilters, context)
    if (geoFilter) {
      requestFilters.push(geoFilter)
    }

    // linked products as variants
    const linkedProductIds = new Set()
    const linkedProductIdsByProductId = {}
    for (const ctProduct of results) {
      const linkedIds = (ctProduct.masterVariant.attributes.find((a) => a.name === 'linked_products')?.value ?? []).map(
        ({ id }) => id
      )
      for (const id of linkedIds) {
        linkedProductIds.add(id)
      }
      linkedProductIdsByProductId[ctProduct.id] = linkedIds
    }

    const allLinkedProductsById = new Map()
    for (const product of await context.loaders.commerceTools.products.loadMany(
      [...linkedProductIds].map((id) => ({
        code: id,
      }))
    )) {
      allLinkedProductsById.set(product.id, product)
    }

    const linkedProducts = {}
    for (const [productId, linkedProductIds] of Object.entries(linkedProductIdsByProductId)) {
      linkedProducts[productId] = linkedProductIds.map((id) => allLinkedProductsById.get(id))
    }

    return {
      filters,
      products: CTProductListTransformer.toGQL(results, context, { filters: requestFilters, linkedProducts }),
      total,
    }
  }

  /**
   * @param {{
   *  gelluleSlug: string
   * }} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProductList>}
   */
  async loadProductListFromDato(args, context) {
    const locale = context.language || 'en'
    const categorySlug = args.gelluleSlug?.toLowerCase() || config.apps[context.siteId].product.category.allSlug

    const response = await context.loaders.datocms.categories.load({ locale, slug: categorySlug })
    if (!response) {
      throw new GraphQLDatoError('not found', categorySlug, 404)
    }
    return DatoProductListTransformer.toGQL(response, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ term: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLSearchSuggestions>}
   */
  async searchSuggest(_parent, args, context) {
    /** @type {BloomreachAutoSuggestResponse} */
    let response
    try {
      response = await context.dataSources.bloomreach.autosuggest(args.term)
    } catch (e) {
      apm.captureError(e, { custom: { error: e } })
      return new GQLSearchSuggestions()
    }

    const categoryTree = await getCategoryTree(context.language, context)

    const bloomreachKeys = new Set()
    for (const group of response.suggestionGroups ?? []) {
      for (const searchSuggestion of group.searchSuggestions ?? []) {
        bloomreachKeys.add(searchSuggestion.pid)
      }
    }

    const products = await context.loaders.commerceTools.productsKeysIn.load({
      keys: [...bloomreachKeys],
      limit: bloomreachKeys.size,
      offset: 0,
    })
    const productKeys = new Set(products.map((product) => product.key))

    /** @type {BloomreachAutoSuggestResponse} */
    const filteredResponse = {
      ...response,
      suggestionGroups: response.suggestionGroups.map((group) => ({
        ...group,
        searchSuggestions: (group.searchSuggestions ?? []).filter((searchSuggestion) =>
          productKeys.has(searchSuggestion.pid)
        ),
      })),
    }

    const categorySuggestions = (response.suggestionGroups ?? []).reduce(
      (result, group) => [
        ...result,
        ...(group.attributeSuggestions ?? []).filter((s) => s.attributeType === 'category'),
      ],
      []
    )

    const categorySlugs = new Map(
      await Promise.all(
        categorySuggestions.map(async (s) => {
          const slug = await context.loaders.datocms.categorySlugsById.load({
            id: s.value,
            locale: context.language,
          })

          return [s.value, slug]
        })
      )
    )

    return BloomreachSearchSuggestionsTransformer.toGQL(filteredResponse, context, {
      categorySlugs,
      categoryTree,
    })
  }

  /**
   * @param {*} accessoryCollection
   * @param {{type: (string|undefined), types: (string[]|undefined)}} args
   * @param {GraphGQLResolveInfo} info
   * @returns {*}
   */
  _filterAccessories(accessoryCollection, args, info) {
    if (!['accessories', 'cards'].includes(info.fieldName)) {
      return accessoryCollection[info.fieldName]
    }

    if (args.type || args.types) {
      const types = _.uniq([args.type ?? null, ...(args.types ?? [])]).filter((type) => Boolean(type))
      return accessoryCollection[info.fieldName].filter((accessory) =>
        accessory.accessoryTypes.some((type) => types.includes(type))
      )
    }

    // If no type filter defined, by default we filter out suggested accessories
    return accessoryCollection[info.fieldName].filter(
      (accessory) => !accessory.accessoryTypes.some((type) => type === CONST.product.accessoryTypes.suggested)
    )
  }

  /**
   * @param {GQLProduct|GQLProductVariant} productOrVariant
   * @returns {boolean}
   */
  _isFromGfs(productOrVariant) {
    const isMainCountry = productOrVariant.countryId === config.apps[config.apps.defaultSiteId].country

    return !isInterfloraPlusProduct(productOrVariant) && productOrVariant.countryId && !isMainCountry
  }

  /**
   * @param {string[]} skipAvailabilityOn
   * @param {string} countryId
   * @returns {boolean}
   */
  _isGenericAccessory(skipAvailabilityOn = [], countryId) {
    return skipAvailabilityOn.includes(countryId)
  }

  /**
   * @param {{
   *   code: string,
   *   countryId: string,
   *   city: string,
   *   postalCode: string,
   *   province: string,
   * }} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLAvailabilityDateRanges>}
   */
  async _getProductDeliveryDates(args, context) {
    const { code, countryId, city, postalCode, province } = args
    const product = await this._loadProductFromCT({ code }, context)

    if (!product) {
      return new GQLAvailabilityDateRanges()
    }

    const productType = await this._loadProductType({ code }, context)

    try {
      const unavailabilities = await context.loaders.availability.unavailabilities.load({
        city: city,
        code: product.key,
        country: countryId,
        postalCode: postalCode,
        province: province,
      })

      const result = new GQLAvailabilityDateRanges(AvailabilityDateRangeTransformer.toGQL(unavailabilities, context))
      if (productType === CONST.product.types.mourning && getCeremonyTimeRange(new Date(), context).length === 0) {
        result.removeToday()
      }

      return result
    } catch (e) {
      apm.captureError(e, { custom: e })
      return new GQLAvailabilityDateRanges()
    }
  }

  /**
   * FR only, detects if there is a geographical zone in the give category and return the corresponding filter for variant processings
   * @param {string} categoryId
   * @param {GQLInputProductFilter[]} requestFilters
   * @param {GraphQLContext} context
   * @returns {GQLInputProductFilter | null}
   */
  async _detectGeographicalFilter(categoryId, requestFilters, context) {
    if (config.site !== CONST.site.fr) {
      return null
    }
    /** @type {CTCategory} */
    const category = await context.loaders.commerceTools.categories.load(categoryId)

    // If the selected category has a geographical zone we need to have default variants set to it, so we return the filter to be used.
    if (
      category?.custom?.fields?.zone &&
      !requestFilters?.find((filter) => filter.filterId === CONST.product.qualifierTypes.zoneGeographique)
    ) {
      return { filterId: CONST.product.qualifierTypes.zoneGeographique, values: [category?.custom?.fields?.zone] }
    }
  }
}

module.exports = ProductResolver
