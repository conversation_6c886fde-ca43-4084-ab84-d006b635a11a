/** @typedef {import('../../datasources/checkout/_typedef')} */
/** @typedef {import('../../datasources/paycomet/_typedef')} */
/** @typedef {import('./_typedef')}  */

const apm = require('elastic-apm-node')
const debug = require('debug')('itf:order')
const { URL } = require('url')

const config = require('../../../config')
const CONST = require('../../../const')

const { GraphQLError } = require('graphql')
const { GraphQLApolloError } = require('../../errors')
const { GQLOrderResult } = require('../../models')
const { PaycometOperationState, PaycometPaymentMethod } = require('../../datasources/paycomet/const')

const {
  AvailabilityDateRangeTransformer,
  AvailabilityDeliveryTimeRangeResultTransformer,
} = require('../../transformers/availability')
const { CTOrderTransformer, CTVoucherTransformer } = require('../../transformers/commerceTools')

const BaseResolver = require('./Base')

const { getPaymentMode } = require('../../../helpers/payment')
const { addTZOffsetToDate } = require('../../../helpers/dates')
const { orderInputToCtUpdate } = require('../../../helpers/commercetools/orders')
const { stripHTMLRootTag, concat, hash } = require('../../../helpers/string')
const { checkout: checkoutHelper } = require('../../../helpers/payment')
const PhoneTransformer = require('../../transformers/Phone')
const { getInterfloraPlus } = require('../../../helpers/commercetools/interfloraplus')
const { getAvailablePaymentMethods } = require('../../../helpers/features')
const { clearTagCache } = require('../../../helpers/cache')
const { getDistributionChannels } = require('../../../helpers/commercetools')

class OrderResolver extends BaseResolver {
  confirmOrderByToken() {
    throw new Error('not implemented')
  }

  /**
   * @see https://interflora.127.0.0.1.nip.io/dev/amazonPay on local env for manual testing
   *
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ urlPath: string, urlQuery: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async confirmRedirectOrder(_parent, args, context) {
    const orderResult = new GQLOrderResult()

    const { urlPath, urlQuery } = args
    // domain is not used - we only need the query string params
    const u = new URL(`https://domain.com/${urlPath}?${urlQuery}`)

    const partnerType = u.searchParams.get('type')
    const cartId = u.searchParams.get('cartId')
    const isS2S = urlPath === 's2s'

    if (u.searchParams.has('cko-session-id') || u.searchParams.has('pid')) {
      let paymentId
      // TODO: to be deleted when Checkout.com provides the correct payment ID
      /**
       * [GGT-830]
       * checks whether the payment is redirected from Alma to retrieve the order from the cart ID
       * instead the payment ID provided by Checkout.com => partner payment ID (in the Alma system)
       */
      if (partnerType === 'alma' && cartId) {
        const partnerPaymentId = u.searchParams.get('cko-session-id') || u.searchParams.get('pid')
        paymentId = await this._getCheckoutPaymentId(cartId, CONST.payment.paymentMode.ALMA, partnerPaymentId, context)
        if (!paymentId) {
          throw new GraphQLApolloError('Payment id not found', 400)
        }
      } else {
        paymentId = u.searchParams.get('cko-session-id') || u.searchParams.get('pid')
      }
      return this._confirmCheckoutPayment(paymentId, isS2S, context, urlPath)
    }

    if (urlPath.includes('/buybox/success')) {
      const cart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
      try {
        const orderAmount = (cart?.taxedPrice?.totalGross.centAmount || cart?.totalPrice.centAmount) / 100
        return this._confirmBuyboxPayment(
          {
            amount: orderAmount,
            cartId,
            isS2S,
            payerId: u.searchParams.get('PayerID'),
            token: u.searchParams.get('token'),
          },
          context,
          (...args) => this._transformXPayment.apply(this, args)
        )
      } catch (e) {
        apm.captureError(e, { custom: e })
        orderResult.status = 'ERROR'
        orderResult.statusCode = '500'
        orderResult.statusMessage = 'Payment error'
        return orderResult
      }
    } else if (urlPath.includes('/buybox/failure')) {
      orderResult.status = 'ERROR'
      orderResult.statusCode = '500'
      orderResult.statusMessage = 'Payment error'
      return orderResult
    } else if (urlPath.includes('/buybox/cancel')) {
      orderResult.status = 'CANCELED'
      orderResult.statusCode = '500'
      orderResult.statusMessage = 'Payment cancelled'
      return orderResult
    }

    if (urlPath.includes('/paycomet/success')) {
      return this._confirmPaycometPayment(context)
    }

    if (urlPath.includes('/paycomet/failure')) {
      orderResult.status = 'ERROR'
      orderResult.statusCode = '418' // Custom teapot code for paycoment error
      orderResult.statusMessage = 'Payment error'
      return orderResult
    }

    if (u.searchParams.has('amazonCheckoutSessionId')) {
      const sessionId = u.searchParams.get('amazonCheckoutSessionId')
      let cart = await this._getApiCart(context)

      if (!cart && !context.session?.apiCart?.id) {
        const session = await context.dataSources.amazonPay.getCheckoutSession(sessionId)
        const cartId = session?.merchantMetadata?.merchantReferenceId
        if (cartId) {
          cart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
          if (cart?.cartState === CONST.commerceTools.cartStates.active)
            context.session.apiCart = { countryId: cart.shippingAddress?.country, id: cart.id }
        }
      }
      this._assertHasCart(context)

      // amazon pay
      const amount = (cart.taxedPrice.totalGross.centAmount / 100).toFixed(2)
      const currency = cart.taxedPrice.totalGross.currencyCode

      const response = await context.dataSources.amazonPay.completeCheckoutSession(
        u.searchParams.get('amazonCheckoutSessionId'),
        amount,
        currency
      )

      if (response?.statusDetails?.state !== 'Completed') {
        const err = new GraphQLApolloError(
          response?.statusDetails?.reasonCode
            ? `[${response.statusDetails.reasonCode}] ${response.statusDetails.resasonDescription}`
            : `[Unknown] ${JSON.stringify(response)}`
        )
        err.addError('PaymentError', CONST.payment.paymentMode.AMAZON)
        apm.captureError(err, { custom: err })

        await this._addPayment(
          {
            cart,
            details: response,
            mode: CONST.payment.paymentMode.AMAZON,
            provider: CONST.payment.provider.AMAZON,
            state: CONST.commerceTools.paymentStates.failed,
            transactionId: response?.chargeId,
          },
          context
        )

        orderResult.status = CONST.order.statuses.error
        orderResult.statusCode = response?.statusDetails?.reasonCode || '500'
        orderResult.statusMessage = response?.statusDetails?.reasonDescription || 'Unknown error'

        return orderResult
      }

      const order = await this._convertCart(
        {
          cart,
          details: response,
          mode: CONST.payment.paymentMode.AMAZON,
          provider: CONST.payment.provider.AMAZON,
          state: CONST.commerceTools.paymentStates.paid,
          transactionId: response.chargeId,
        },
        context
      )

      orderResult.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
        skipProductAPI: false,
      })
      orderResult.status = CONST.order.statuses.success
      return orderResult
    } else if (u.searchParams.has('a') && u.searchParams.has('b')) {
      const paymentToken = u.searchParams.get('b')

      // axerve - s2s or paypal
      return context.lock(paymentToken, () =>
        this._confirmAxervePayment(undefined, paymentToken, context, u.pathname === '/s2s')
      )
    }

    if (urlPath.includes(config.frisbii.checkout.urlPath)) {
      return this._handleFrisbiiPayment(context, urlPath, urlQuery)
    }

    orderResult.status = 'ERROR'
    orderResult.statusCode = '500'
    orderResult.statusMessage = 'Unknown error'

    return orderResult
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderInfo: GQLInputOrderInfo}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<void | GQLOrderResult>}
   */
  async createOrder(_parent, args, context) {
    const { mode, paymentToken, cardNumber, cardCode } = args.orderInfo
    let provider = args.orderInfo.provider

    /** @type {CTCart} */
    let apiCart
    try {
      this._assertHasCart(context)
      apiCart = await context.loaders.commerceTools.cartsAsRead.load(context.session.apiCart.id)
      await this._assertCartReady(context, apiCart)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return this._error2OrderResult(e)
    }

    context.dataSources.meta
      .registerInitiateCheckout(context, new Date(), apiCart)
      .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

    const orderResult = new GQLOrderResult()

    if (!apiCart) {
      const error = new GraphQLApolloError(`no cart found ${context.session.apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No cart found ${context.session.apiCart.id}`

      return orderResult
    }
    if (apiCart.cartState !== CONST.commerceTools.cartStates.active) {
      const order = await new Promise((resolve) =>
        setTimeout(
          () => resolve(context.dataSources.orders.getByCartId(context.session.apiCart.id)),
          config.commerceTools.waitForOrderDelay
        )
      )

      if (order) {
        context.log.info(`[createOrder] order retrieved from payment info: ${order.id}`)
        orderResult.order = CTOrderTransformer.toGQL(order, context, {
          distributionChannels: await getDistributionChannels(context),
        })
        orderResult.status = CONST.order.statuses.success
        return orderResult
      }

      const error = new GraphQLApolloError(`no order found for cart ${apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No order found for cart ${apiCart.id}`

      return orderResult
    }

    if (provider === CONST.payment.provider.PAYCOMET) {
      switch (mode) {
        case CONST.payment.paymentMode.CARD:
        case CONST.payment.paymentMode.PAYPAL: {
          const result = await this._createPaycometOrder(args, context)
          return result
        }
        default:
          throw new Error('not implemented')
      }
    }

    if (provider === CONST.payment.provider.CHECKOUT) {
      switch (mode) {
        case CONST.payment.paymentMode.APPLEPAY:
        case CONST.payment.paymentMode.CARD:
        case CONST.payment.paymentMode.GOOGLEPAY:
        case CONST.payment.paymentMode.MBWAY:
        case CONST.payment.paymentMode.MULTIBANCO:
        case CONST.payment.paymentMode.PAYPAL:
        case CONST.payment.paymentMode.ALMA:
          return this._submitCheckoutPayment(args, context)
        default:
          throw new Error('Not implemented')
      }
    }

    if (provider === CONST.payment.provider.BUYBOX) {
      switch (mode) {
        case CONST.payment.paymentMode.GIFT_CARD: {
          const orderAmount = (apiCart.taxedPrice?.totalGross.centAmount || apiCart.totalPrice.centAmount) / 100
          return this._submitBuyboxPayment(
            {
              amount: orderAmount,
              cardCode: cardNumber,
              cardPin: cardCode,
              cartId: apiCart?.id,
              cartType: args.cartType,
            },
            context,
            (...args) => this._initCartForPayment.apply(this, args)
          )
        }
        default:
          throw new Error('Not implemented')
      }
    }

    if (mode === CONST.payment.paymentMode.FACTURE) {
      return this._createInvoiceOrder(apiCart, context)
    }

    if ([CONST.payment.paymentMode.CARD, CONST.payment.paymentMode.PAYPAL].includes(mode)) {
      if (!provider) {
        provider = (await getAvailablePaymentMethods(apiCart, context)).find((method) => method.mode === mode)?.provider
      }

      await this._addPayment(
        {
          cart: apiCart,
          mode,
          provider,
          state: CONST.commerceTools.paymentStates.pending,
          transactionId: `${context.session.apiCart?.id}-init-${mode.toLowerCase()}`,
        },
        context
      )
    }

    if (mode === CONST.payment.paymentMode.CARD) {
      return this._confirmAxervePayment(mode, paymentToken, context)
    }

    if (mode === CONST.payment.paymentMode.PAYPAL) {
      if (provider === CONST.payment.provider.PAYPAL) {
        return await context.lock(paymentToken, async () => {
          const captureResponse = await context.dataSources.paypal.captureOrder(paymentToken)
          return this._transformPaypalPayment(context, paymentToken, captureResponse)
        })
      }
      await this._getApiCart(context) // loading is required to assert
      this._assertHasCart(context)
      await this._assertCartReady(context)

      const response = await context.dataSources.axerve.encryptPaypal(await this._axerveEncryptPayload(context))

      const encrypted = response?.[0]?.EncryptResult?.GestPayCryptDecrypt?.CryptDecryptString
      const status = response?.[0]?.EncryptResult?.GestPayCryptDecrypt?.TransactionResult
      if (status !== 'OK') {
        const code = response?.[0]?.EncryptResult?.GestPayCryptDecrypt?.ErrorCode || '400'
        const message = response?.[0]?.EncryptResult?.GestPayCryptDecrypt?.ErrorDescription || 'Unknown error'

        throw new GraphQLApolloError(message, code)
      }

      const url = new URL(config.axerve.redirectPaymentUrl)
      url.searchParams.set('a', config.axerve.shopLogin)
      url.searchParams.set('b', encrypted)

      orderResult.redirectUrl = url.toString()
      orderResult.status = CONST.order.statuses.redirect
      orderResult.statusCode = null // is considered as error code by the front - keep it null in case of success
      orderResult.usePost = false
      return orderResult
    }

    if (mode === CONST.payment.paymentMode.NONE) {
      // TODO if this code/algo is correct, it's mostly duplicated from _transformPaycometPayment and should be refactored
      try {
        const order = await context.dataSources.orders.getByCartId(apiCart.id)
        if (order) {
          debug('createOrder, cart already transformed: %0', {
            order,
            paymentResult: 'SUCCESS',
          })

          await this._clearSessionCart(context)
          if (!context.session || !context.session.apiCart) {
            await this._clearCartLoaders(apiCart.id, context)
          }

          const result = new GQLOrderResult()
          result.status = CONST.order.statuses.success
          result.order = CTOrderTransformer.toGQL(order, context)

          return result
        }
      } catch (e) {
        // Do nothing
      }

      const order = await this._convertCart(
        {
          cardType: CONST.payment.paymentMode.NONE,
          cart: apiCart,
          state: CONST.commerceTools.paymentStates.paid,
        },
        context
      )

      if (!order) {
        const error = new GraphQLApolloError('transform free cart', 500)
        apm.captureError(error, { custom: error })

        orderResult.status = CONST.order.statuses.error
        orderResult.statusCode = 500
        orderResult.statusMessage = 'Error converting cart to order'
      } else {
        orderResult.status = CONST.order.statuses.success
        orderResult.order = CTOrderTransformer.toGQL(order, context)
      }

      await this._clearSessionCart(context)
      if (!context.session || !context.session.apiCart) {
        await this._clearCartLoaders(apiCart.id, context)
      }

      return orderResult
    }

    throw new Error('Not implemented')
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(delivery, _args, context) {
    if (delivery.availableDateRanges) return delivery.availableDateRanges
    let ctOrder
    try {
      ctOrder = await context.loaders.commerceTools.orders.load(delivery.parentId)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return []
    }
    const productVariantSku = this._getProductVariantCodes(ctOrder.cart)
    const opts = {
      city: ctOrder.shippingAddress?.city,
      country: ctOrder.shippingAddress?.country || context.countryId,
      postalCode: ctOrder.shippingAddress?.postalCode,
      productVariantSku: productVariantSku.at(0),
      province: ctOrder.shippingAddress?.region,
    }

    const responses = await Promise.allSettled(
      ctOrder.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return context.loaders.availability.unavailabilities.load({
          ...opts,
          code: product.key,
        })
      })
    )

    if (responses.some((r) => r.status === 'rejected')) return []

    const unavailabilities = [...new Set(responses.map((r) => r.value).flat())]

    return AvailabilityDateRangeTransformer.toGQL(unavailabilities, context)
  }

  /**
   * @todo
   *
   * @param {GQLOrder} _order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<boolean>}
   */
  async getCanAttachOrder(_order, _args, _context) {
    return false
  }

  /**
   * Returns true if the order is anonymous and the billing address email is matching an existing user
   *
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {boolean}
   */
  async getEmailHasAccountNoGuest(order, _args, context) {
    if (order.emailHasRegisteredUser) return order.emailHasRegisteredUser
    let ctOrder
    try {
      /** @type {CTOrder} ctOrder */
      ctOrder = await context.loaders.commerceTools.orders.load(order.number)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return false
    }
    const email = ctOrder?.billingAddress?.email

    // customerEmail is set only if user is logged in
    if (ctOrder.customerEmail || !email) {
      return false
    }

    return !!(await context.loaders.itUser.userByEmail.load(email))
  }

  /**
   * @param {GQLDelivery} _delivery
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getMaxDeliveryDays(_delivery, args, context) {
    if (_delivery.maxDayDelivery) return _delivery.maxDayDelivery
    let ranges
    try {
      ranges = await this.getAvailableDateRanges(_delivery, args, context)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return 0
    }

    const lastDay = ranges
      .map(({ max }) => max)
      .sort((a, b) => a.getTime() - b.getTime())
      .pop()

    if (!lastDay) return

    // lastDay is a pseudo UTC representation of the date
    // as our now may not be the backend now, we switch to locale dates to compute the diff
    const localeLastDay = addTZOffsetToDate(lastDay, config.apps[context.siteId].timezone)
    const localeNow = addTZOffsetToDate(new Date(), config.apps[context.siteId].timezone)

    return Math.ceil((localeLastDay.getTime() - localeNow.getTime()) / 1000 / 3600 / 24)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrder}
   */
  async getOrder(_parent, args, context) {
    const ctOrder = await context.loaders.commerceTools.orders.load(args.orderId)
    if (!ctOrder) {
      const user = await this._getCurrentUser(context)

      if (user?.lastOrder?.id === args.orderId) return user.lastOrder
      for (const order of user?.orders?.orders || []) {
        if (order.number === args.orderId) return order
      }
    }

    if (!ctOrder) return null

    const order = CTOrderTransformer.toGQL(ctOrder, context, {
      distributionChannels: await getDistributionChannels(context),
    })

    if (order.delivery) {
      order.delivery.ctOrder = ctOrder
    }

    return order
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string, orderNumber: string}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrder}
   */
  async getOrderByEmail(_parent, args, context) {
    const { email, orderNumber } = args
    const ctOrder = await context.dataSources.orders.getByNumberAndEmail(orderNumber, email)
    if (!ctOrder) {
      return null
    }

    return CTOrderTransformer.toGQL(ctOrder, context, {
      distributionChannels: await getDistributionChannels(context),
      skipProductAPI: true,
    })
  }

  getOrderByToken() {
    throw new Error('not implemented')
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   */
  async getOrderDeliveryFeasibility(_parent, args, context) {
    const ctOrder = await context.dataSources.orders.getOrder(args.orderId)

    if (!ctOrder) {
      throw new GraphQLError('404 not found')
    }

    const codes = []
    let isFuneral = false

    for (const product of await Promise.all(
      ctOrder.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return product
      })
    )) {
      // TODO FAM-800 => Back should make the product with code 'ITFPLUS' exist and always available. Then delete this filter.
      if (product.key !== config.products.interfloraplus.key && product.key !== config.products.tombDelivery.key)
        codes.push(product.key)
      const productType = await this._loadProductType({ code: product.code }, context)
      if (productType === CONST.product.types.mourning) {
        isFuneral = true
      }
    }

    /** @type {CTCart} */
    const cart = await context.loaders.commerceTools.cartsAsRead.load(ctOrder.cart.id)

    const response = await context.dataSources.availability.checkAvailability({
      city: ctOrder.shippingAddress?.city,
      codes,
      country: ctOrder.shippingAddress?.country || context.countryId,
      date: ctOrder.shippingAddress?.custom?.fields?.date,
      hasRibbon: false, // @todo
      isFuneral,
      moment: ctOrder.shippingAddress?.custom?.fields?.moment,
      orderId: args.orderId,
      postalCode: ctOrder.shippingAddress?.postalCode,
      productVariantSKUs: this._getProductVariantCodes(cart),
      province: ctOrder.shippingAddress?.region,
      street: ctOrder.shippingAddress?.streetName,
    })

    return {
      feasible: response.Result,
    }
  }

  getOrderDeliveryRangeFixHour() {
    throw new Error('not implemented')
  }

  /**
   * @param {object} _parent
   * @param {GraphQLContextArgs<{orderId: string ,date: Date}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getOrderDeliveryTimeRanges(_parent, args, context) {
    const { orderId, date } = args
    try {
      const ctOrder = await context.loaders.commerceTools.orders.load(orderId)

      const codes = await this._getLineItemsCodes(ctOrder, context)

      const apiMoments = await context.loaders.availability.moments.load({
        codes,
        country: ctOrder.shippingAddress?.country || context.countryId,
        date,
        province: ctOrder.shippingAddress?.region || undefined,
      })

      return AvailabilityDeliveryTimeRangeResultTransformer.toGQL(apiMoments || [], context)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return { ranges: [] }
    }
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise<*>}
   */
  async getOrderProperty(order, args, context, info) {
    if (info.fieldName === 'discountCodes') {
      if (Array.isArray(order.discountCodes)) return order.discountCodes

      const ctOrder = await context.loaders.commerceTools.orders.load(order.id)
      const codes = (
        await Promise.all(
          (ctOrder.discountCodes || []).map(async (info) => {
            if (info.state !== CONST.commerceTools.discountCodeState.matchesCart) return

            const discountCode = await context.loaders.commerceTools.discounts.load(info.discountCode.id)
            return discountCode.code
          })
        )
      ).filter(Boolean)

      order.discountCodes = codes

      return codes
    } else if (info.fieldName === 'reason') {
      if (!order.reason) return

      return this._getReasons().find((reason) => reason.id === order.reason.id)
    } else if (info.fieldName === 'paymentMode') {
      return this._getPaymentMode(order, args, context)
    }

    return order[info.fieldName]
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  async getOrders(_parent, args, context) {
    const user = await this._getCurrentUser(context)
    return this._getUserOrders(user, args, context)
  }

  /**
   *
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @return {Promise<GQLVoucher|null>}
   */
  async getOrderVoucher(order, args, context) {
    const ctOrder = await context.loaders.commerceTools.orders.load(order.id)
    const voucherCode = ctOrder?.custom?.fields?.voucher
    if (!voucherCode) {
      return null
    }
    const discount = await context.dataSources.carts.getDiscountCodeByCode(voucherCode)
    if (!discount.cartDiscounts?.length) {
      return null
    }

    const currency = config.apps[context.siteId].currency
    const currencyName = config.apps[context.siteId].currencyName
    const currencySymbole = config.apps[context.siteId].currencySymbole

    const ctCartDiscount = await context.dataSources.carts.getCartDiscount(discount.cartDiscounts[0].id)
    if (!ctCartDiscount) {
      return null
    }
    ctCartDiscount.code = voucherCode

    return CTVoucherTransformer.toGQL(ctCartDiscount, context, {
      cartDiscount: ctCartDiscount,
      currency,
      currencyName,
      currencySymbole,
    })
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string>}
   */
  async _getPaymentMode(order, _args, context) {
    if (order.paymentMode) {
      return order.paymentMode
    }

    /** @type {CTOrder} */
    const ctOrder = await context.loaders.commerceTools.orders.load(order.id)
    if (!ctOrder?.paymentInfo) {
      return null
    }

    if (!ctOrder.paymentInfo) {
      return null
    }

    const paymentIds = ctOrder.paymentInfo.payments.map((payment) => payment.id)
    const payments = await Promise.all(paymentIds.map((id) => context.loaders.commerceTools.payments.load(id)))
    const payment = payments
      .filter((p) =>
        [CONST.commerceTools.paymentStates.paid, CONST.commerceTools.paymentStates.pending].includes(
          p.paymentStatus?.interfaceCode
        )
      )
      .at(0)

    return payment?.paymentMethodInfo?.method ?? null
  }

  // @todo
  getReasons() {
    return this._getReasons()
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getSuggestedMessages(_parent, args, context) {
    if (!args.orderId) return []

    const ctOrder = await context.loaders.commerceTools.orders.load(args.orderId)
    const slug = await context.loaders.datocms.categorySlugsById.load({
      id: ctOrder.custom.fields.occasionCode,
      locale: context.language,
    })
    if (!slug) return []

    const datoCategory = await context.loaders.datocms.categories.load({ locale: context.language, slug })
    return (datoCategory.cardSuggestions || []).map((s) => stripHTMLRootTag(s.message))
  }

  getTownsForOrder() {
    throw new Error('not implemented')
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getUseFixHour(delivery, _args, context) {
    if (delivery.useFixHour) return delivery.useFixHour
    let ctOrder
    try {
      ctOrder = await context.loaders.commerceTools.orders.load(delivery.parentId)
    } catch (e) {
      apm.captureError(e, { custom: e })
      return false
    }

    if (!ctOrder) return false

    let isFuneral = true

    for (const productType of await Promise.all(
      ctOrder.lineItems.map(async (lineItem) => {
        const productType = await this._loadProductType({ code: lineItem.productId }, context)
        return productType
      })
    )) {
      if (productType === CONST.product.types.mourning) {
        isFuneral = true
      }
    }

    return isFuneral
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{voucherCode: string}>} args
   * @param {GraphQLContext} context
   * @returns {void|Promise<GQLVoucher>}
   */
  async getVoucher(_parent, args, context) {
    const discountCode = await context.dataSources.orders.getDiscountCode(args.voucherCode)
    if (!discountCode) {
      return
    }
    const cartDiscountId = discountCode.cartDiscounts[0].id
    const cartDiscount = await context.dataSources.orders.getCartDiscount(cartDiscountId)
    const currency = config.apps[context.siteId].currency
    const currencyName = config.apps[context.siteId].currencyName
    const currencySymbole = config.apps[context.siteId].currencySymbole
    return CTVoucherTransformer.toGQL(discountCode, context, {
      cartDiscount,
      currency,
      currencyName,
      currencySymbole,
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string, password: string}>} args
   * @param {GraphQLContext} context
   */
  async loginAndAttachOrder(_parent, args, context) {
    /** @type {CTOrder} ctOrder */
    const ctOrder = await context.dataSources.orders.getOrder(args.orderId)
    const email = ctOrder?.billingAddress?.email

    if (!email) {
      throw new GraphQLApolloError('Not found', 404)
    }

    /** @type {GQLToken} token */
    const token = await context.resolvers.authentication.login(
      undefined,
      {
        password: args.password,
        username: email,
      },
      context
    )

    if (!context.auth.isAnonymous) {
      const userId = context.session.itUser.userId
      context.dataSources.orders.attachOrderAsAdmin(ctOrder, userId, email)
    }

    return token
  }

  refuseOrderByToken() {
    throw new Error('not implemented')
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{orderId: string, order: GQLInputOrder}>} args
   * @param {GraphQLContext} context
   * @return {Promise<GQLOrder>}
   */
  async updateOrder(_parent, args, context) {
    let ctOrder = await context.dataSources.orders.getOrder(args.orderId)
    const newOrderData = orderInputToCtUpdate(ctOrder, args.order, context)
    ctOrder = await context.dataSources.orders.updateOrder(ctOrder, newOrderData)
    await context.loaders.commerceTools.orders.prime(ctOrder.id, ctOrder)

    return CTOrderTransformer.toGQL(ctOrder, context, {
      distributionChannels: await getDistributionChannels(context),
    })
  }

  updateOrderByToken() {
    throw new Error('not implemented')
  }

  /**
   * @param {CTOrder} order
   * @param {CTDatocmsContext} context
   * @return {Promise<void>}
   * @protected
   */
  async _afterCartConverted(order, context) {
    const promises = []

    if (order.customerEmail) {
      promises.push(context.loaders.commerceTools.ordersByCustomerEmail.clear({ email: order.customerEmail }))
    }

    if (promises.length === 0) {
      return
    }

    for (const result of await Promise.allSettled(promises)) {
      if (result.status === 'rejected') {
        apm.captureError(result.reason, { custom: result.reason })
      }
    }
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>}
   */
  async _beforeConvertingCart(cart, context) {
    if (
      [CONST.site.es, CONST.site.it, CONST.site.pt].includes(config.site) &&
      cart.lineItems.some((lineItem) => lineItem.productKey === config.products?.interfloraplus?.key)
    ) {
      const customerEmail = cart.customerEmail?.toLowerCase()
      const todayDate = new Date()
      const interfloraPlus = await getInterfloraPlus(customerEmail, context)
      const registrationDate = interfloraPlus.expirationDate ?? todayDate
      const expirationDate = new Date(
        new Date(registrationDate.getTime()).setFullYear(registrationDate.getFullYear() + 1)
      )

      context.log.info(`registering interfloraplus subscription => [${cart.id}] ${expirationDate.toISOString()}`)

      const { country, timezone } = context.appConfig
      const strRegistrationDate = addTZOffsetToDate(registrationDate, timezone).toISOString().slice(0, 10)
      const strExpirationDate = addTZOffsetToDate(expirationDate, timezone).toISOString().slice(0, 10)
      const minExtendAvailableDate = new Date(
        new Date(registrationDate.getTime() - config.products.interfloraplus.subscriptionRenewTimeLimit)
      )

      /** @type {CTCustomer} */
      let ctUser = await context.loaders.commerceTools.customers.load(customerEmail)

      if (!ctUser) {
        /** @type {CTCustomerDraft} */
        const payload = {
          anonymousCart: {
            id: cart.id,
            typeId: 'cart',
          },
          authenticationMode: 'ExternalAuth',
          custom: {
            fields: {
              [`interfloraplusSubcriptionDate${country}`]: strRegistrationDate,
              [`interfloraplusExpirationDate${country}`]: strExpirationDate,
            },
            type: {
              key: 'customer-custom',
              typeId: 'type',
            },
          },
          customerGroup: {
            key: 'interflora-plus',
            typeId: 'customer-group',
          },
          email: customerEmail,
        }

        try {
          /** @type {CTCustomerSignInResult} */
          await context.dataSources.customers.createCustomer(payload)
          await context.loaders.commerceTools.customers.clear(customerEmail)
        } catch (error) {
          // Most probably user creationg collision, logging while investigating about why
          context.log.error(`[_beforeConvertingCart] user ${customerEmail} couldn't be created: ${error.message}`)
          apm.captureError(error, {
            custom: error, // needed to get the graphql errors details in apm
          })
        }
      } else if (
        ctUser.custom?.field?.[`interfloraplusExpirationDate${country}`] !== strExpirationDate &&
        todayDate > minExtendAvailableDate
      ) {
        const nextYearExpirationDate = new Date().setFullYear(todayDate.getFullYear() + 1)
        const newExpirationDate = expirationDate > nextYearExpirationDate ? expirationDate : nextYearExpirationDate
        const strNewExpirationDate = addTZOffsetToDate(newExpirationDate, timezone).toISOString().slice(0, 10)

        await context.dataSources.customers.updateCustomer(ctUser.id, {
          actions: [
            {
              action: 'setCustomField',
              name: `interfloraplusExpirationDate${country}`,
              value: strNewExpirationDate,
            },
          ],
          version: ctUser.version,
        })
        await context.loaders.commerceTools.customers.clear(ctUser.email)
      }
    }

    if (cart.custom?.fields?.deliveryMode === CONST.cart.deliveryMode.tombBundle) {
      cart = await context.dataSources.carts.updateCartAsAdmin(cart.id, {
        actions: [
          {
            action: 'setCustomField',
            name: 'deliveryMode',
            value: CONST.cart.ceremonyTypeMap.ct[cart.custom.fields.deliveryMode],
          },
        ],
        version: cart.version,
      })
    }

    return cart
  }

  /**
   * @param {string} inputMode
   * @param {string} encrypted - encrypted string received form Axerve
   * @param {GraphQLContext} context
   * @param {boolean} _isS2S
   * @returns {Promise<CTOrder>}
   */
  async _confirmAxervePayment(inputMode, encrypted, context, _isS2S = false) {
    const orderResult = new GQLOrderResult()
    const paymentDetails = await context.dataSources.axerve.decrypt(encrypted)

    if (!paymentDetails) {
      const err = new GraphQLApolloError('Could not decrypt axerve payload', 500)
      err.addError('PaymentError', mode)
      throw err
    }

    let cardType
    let mode = inputMode
    // nb: PaymentMethod is set on production only
    if (paymentDetails.PaymentMethod) ({ cardType, mode } = getPaymentMode(paymentDetails.PaymentMethod))

    /** @type {CTCart} */
    let cart

    try {
      const apiCart = await context.loaders.commerceTools.cartsAsRead.load(paymentDetails.ShopTransactionID)

      if (apiCart?.cartState === CONST.commerceTools.cartStates.active) {
        cart = apiCart
        context.session.apiCart = { countryId: cart.shippingAddress?.country, id: cart.id }
      } else {
        const order = await new Promise((resolve) =>
          setTimeout(
            () => resolve(context.dataSources.orders.getByCartId(paymentDetails.ShopTransactionID)),
            config.commerceTools.waitForOrderDelay
          )
        )

        if (order) {
          context.log.info(`[confirmAxerve] order retrieved from payment info: ${order.id}`)
          orderResult.order = CTOrderTransformer.toGQL(order, context, {
            distributionChannels: await getDistributionChannels(context),
          })
          orderResult.status = CONST.order.statuses.success
          return orderResult
        }
      }
    } catch (e) {
      context.log.error(
        e,
        `[confirmAxerve] error retrieving cart ${paymentDetails.ShopTransactionID} from payment info`
      )
      // do not throw here, will be caught later
    }

    // error and no cart => throw withouth registering a failed payment
    if (!cart && paymentDetails.TransactionResult !== 'OK') {
      const err = new GraphQLApolloError(
        paymentDetails.ErrorDescription || 'Unknown error received from Axerve',
        paymentDetails.ErrorCode || 500
      )
      err.addError('PaymentError', mode)
      err.addError('NoCartError', 'cart')
      apm.captureError(err, { custom: err })

      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = paymentDetails.ErrorCode || 500
      orderResult.statusMessage = paymentDetails.ErrorDescription
      return orderResult
    }

    // we did our best...
    if (!cart) {
      const err = new GraphQLApolloError(`no cart found ${paymentDetails?.ShopTransactionID}`, 404)
      apm.captureError(err, { custom: err })

      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = 404
      orderResult.statusMessage = err.message
      return orderResult
    }

    // retrieve payment mode - i.e. confirmRedirectOrder on non-prod env
    if (!mode) {
      let lastPaymentMode

      const lastPaymentInfo = cart.paymentInfo?.payments?.at(-1)
      if (lastPaymentInfo) {
        const payment = await context.loaders.commerceTools.payments.load(lastPaymentInfo.id)
        lastPaymentMode = payment.paymentMethodInfo.method
      }

      // default: trust last payment mode
      //
      // if no previous payment found, we did not go through createOrder
      // and thus can only be a card payment
      mode = lastPaymentMode ?? CONST.payment.paymentMode.CARD
    }

    if (paymentDetails.TransactionResult !== 'OK') {
      const err = new GraphQLApolloError(paymentDetails.ErrorDescription)
      err.addError('PaymentError', mode)
      apm.captureError(err, { custom: err })

      cart = this._addPayment(
        {
          cardType: cardType || mode,
          cart,
          details: paymentDetails,
          mode,
          provider: CONST.payment.provider.AXERVE,
          state: CONST.commerceTools.paymentStates.failed,
          transactionId: paymentDetails?.BankTransactionID,
        },
        context
      )

      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = paymentDetails.ErrorCode
      orderResult.statusMessage = paymentDetails.ErrorDescription
      return orderResult
    }

    // create payment
    const order = await this._convertCart(
      {
        cart,
        details: paymentDetails,
        mode,
        provider: CONST.payment.provider.AXERVE,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: paymentDetails.BankTransactionID,
      },
      context
    )
    await this._clearSessionCart(context)

    if (order) {
      orderResult.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
      orderResult.status = CONST.order.statuses.success
    } else {
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = 500
      orderResult.statusMessage = 'Error converting cart to order'
    }

    return orderResult
  }

  /**
   * @param {GQLOrder} order
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async _getLineItemsCodes(order, context) {
    const codes = await Promise.all(
      order.lineItems.map(async (lineItem) => {
        try {
          const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
          return product.key
        } catch (e) {
          apm.captureError(e, { custom: e })
          return null
        }
      })
    )

    return codes.filter((code) => !!code)
  }

  /**
   * @param {GraphQLContextArgs<{orderInfo: GQLInputOrderInfo}>} args
   * @param {GraphQLContext} context
   */
  async _createPaycometOrder(args, context) {
    const apiCartId = args.context.cartId || context.session.apiCart.id
    if (!apiCartId) {
      const error = new GraphQLApolloError(`no cart found`)
      apm.captureError(error, { custom: error })
    }
    const orderResult = new GQLOrderResult()
    let apiCart = await context.loaders.commerceTools.cartsAsRead.load(apiCartId)
    if (!apiCart) {
      const error = new GraphQLApolloError(`no cart found ${apiCartId || ''}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `Cart not found ${apiCartId || ''}`

      return orderResult
    }

    if (apiCart.cartState !== CONST.commerceTools.cartStates.active) {
      const order = await new Promise((resolve) =>
        setTimeout(
          () => resolve(context.dataSources.orders.getByCartId(apiCartId)),
          config.commerceTools.waitForOrderDelay
        )
      )

      if (order) {
        context.log.info(`[confirm Paycomet] order retrieved from payment info: ${order.id}`)
        orderResult.order = CTOrderTransformer.toGQL(order, context, {
          distributionChannels: await getDistributionChannels(context),
        })
        orderResult.status = CONST.order.statuses.success
        return orderResult
      }

      const error = new GraphQLApolloError(`no order found for cart ${apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No order found for cart ${apiCart.id}`

      return orderResult
    }

    try {
      const { mode, paymentToken } = args.orderInfo
      /** @type {PaycometOperationInfoResponse} */
      const { payment: operationInfo } = await context.dataSources.paycomet.operationInfo(apiCart.id)
      if (`${operationInfo.paycometId}` !== `${paymentToken}`) {
        throw new GraphQLApolloError(`paycometId mismatch`)
      }

      context.session.apiCart = {
        id: apiCart.id,
      }

      apiCart = await this._addPayment(
        {
          cart: apiCart,
          details: operationInfo,
          mode,
          provider: CONST.payment.provider.PAYCOMET,
          state: PaycometOperationState.translateValue('ct', operationInfo.state),
          transactionId: `${apiCart.id}#${apiCart.version}`,
        },
        context
      )

      switch (operationInfo.state) {
        case PaycometOperationState.Pending: {
          const result = new GQLOrderResult()
          result.status = 'AWAITING_CAPTURE'
          result.statusCode = 'AWAITING_CAPTURE'

          return result
        }

        case PaycometOperationState.Error: {
          const result = new GQLOrderResult()
          result.status = CONST.order.statuses.error
          result.statusCode = operationInfo.errorCode
          result.statusMessage = operationInfo.errorDescription

          return result
        }

        case PaycometOperationState.Success: {
          try {
            return this._transformPaycometPayment(operationInfo, context)
          } catch (e) {
            const result = new GQLOrderResult()
            result.status = 'ERROR'
            result.statusCode = '404'
            result.statusMessage = e.message
            return result
          }
        }
        default: {
          throw new Error(`Unknonw operation state ${operationInfo.state}`)
        }
      }
    } catch (error) {
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = error.message

      return orderResult
    }
  }

  /**
   * @param {GraphQLContextArgs<{orderInfo: GQLInputOrderInfo}>} args
   * @param {GraphQLContext} context
   */
  async _submitCheckoutPayment(args, context) {
    const { mode, cardType, paymentToken, paymentTokenType } = args.orderInfo

    const apiCart = await this._initCartForPayment(
      {
        cardType,
        mode,
        provider: CONST.payment.provider.CHECKOUT,
      },
      context
    )
    if (apiCart instanceof GQLOrderResult) {
      return apiCart
    }

    const mode2Type = {
      [CONST.payment.paymentMode.PAYPAL]: 'paypal',
      [CONST.payment.paymentMode.CARD]: 'token',
      [CONST.payment.paymentMode.APPLEPAY]: 'token',
      [CONST.payment.paymentMode.MBWAY]: 'mbway',
      [CONST.payment.paymentMode.MULTIBANCO]: 'multibanco',
      [CONST.payment.paymentMode.GOOGLEPAY]: 'token',
      [CONST.payment.paymentMode.ALMA]: 'alma',
    }

    const customerEmail = apiCart.billingAddress?.email || apiCart.customerEmail
    const customerName = concat(apiCart.billingAddress?.firstName, apiCart.billingAddress?.lastName)
    const customerPhone = apiCart.billingAddress?.mobile
      ? PhoneTransformer.toGQL(apiCart.billingAddress.mobile, context, {
          countryCode: apiCart.billingAddress.country,
        })
      : undefined
    const shippingPhone = apiCart.shippingAddress?.mobile
      ? PhoneTransformer.toGQL(apiCart.shippingAddress.mobile, context, {
          countryCode: apiCart.shippingAddress.country,
        })
      : undefined
    /**
     * Transform the address to checkout address format
     * @param {CTAddress} address
     * @returns {CheckoutPaymentRequest.shipping.address|undefined}
     */
    const toCkoAddress = (address) =>
      address
        ? {
            address_line1: concat(address.streetName, address.streetNumber, address.additionalStreetInfo),
            address_line2: address.additionAddressInfo || '',
            city: address.city,
            country: address.country,
            zip: address.postalCode,
          }
        : undefined

    /** @type {CheckoutPaymentRequest} */
    const payload = {
      amount: apiCart.taxedPrice?.totalGross.centAmount || apiCart.totalPrice.centAmount,
      capture: true,
      currency: apiCart.totalPrice.currencyCode,
      customer: {
        email: customerEmail,
        name: customerName,
      },
      description: apiCart.id,
      metadata: {
        cart_id: apiCart.id,
        method: mode,
        session_id: context.session?.id,
      },
      reference: apiCart.id,
      shipping: {
        address: toCkoAddress(apiCart.shippingAddress),
        method: 'OtherAddress',
      },
      source: {
        billing_address: toCkoAddress(apiCart.billingAddress),
        email: customerEmail,
        name: customerName,
        type: mode2Type[mode],
      },
    }

    if (shippingPhone) {
      payload.shipping.phone = {
        country_code: shippingPhone.prefix,
        number: shippingPhone.number,
      }
    }

    if (customerPhone) {
      payload.source.phone = {
        country_code: customerPhone.prefix,
        number: customerPhone.number,
      }
      payload.customer.phone = {
        country_code: customerPhone.prefix,
        number: customerPhone.number,
      }
    }

    if (
      mode === CONST.payment.paymentMode.CARD ||
      mode === CONST.payment.paymentMode.APPLEPAY ||
      mode === CONST.payment.paymentMode.GOOGLEPAY
    ) {
      payload.source = {
        ...payload.source,
        token: paymentToken,
      }

      if ((mode !== mode) === CONST.payment.paymentMode.APPLEPAY) {
        const preferredScheme = checkoutHelper.preferredCardScheme(cardType)
        if (preferredScheme) {
          payload.processing = {
            preferred_scheme: preferredScheme,
          }
        }
      }
    } else if (mode === CONST.payment.paymentMode.PAYPAL) {
      const amount = apiCart.taxedPrice?.totalGross.centAmount || apiCart.totalPrice.centAmount
      payload.items = [
        {
          name: apiCart.id,
          quantity: 1,
          reference: apiCart.id,
          total_amount: amount,
          unit_price: amount,
        },
      ]
      payload.processing = {
        invoice_id: apiCart.id,
      }
      payload.source = {
        ...payload.source,
        invoice_number: apiCart.id,
        logo_url: null,
        plan: {
          skip_shipping_address: false, // TODO: iflora?,
          type: 'MERCHANT_INITIATED_BILLING',
        },
        recipient_name: concat(apiCart.shippingAddress.firstName, apiCart.shippingAddress.lastName),
      }
    } else if (mode === CONST.payment.paymentMode.MBWAY) {
      const isTestMode = config.checkout.mbway?.testMode && Boolean(paymentToken?.trim())

      let phone
      try {
        const { prefix, number } = JSON.parse(Buffer.from(paymentToken?.trim(), 'base64').toString('ascii'))
        if (prefix && number && (isTestMode || prefix !== '777')) {
          phone = { number, prefix }
        }
      } catch {
        // use billing address phone
      }

      if (!phone) {
        phone = customerPhone
      }

      payload.customer.phone = {
        country_code: phone?.prefix,
        number: phone?.number,
      }
    } else if (mode === CONST.payment.paymentMode.MULTIBANCO) {
      const amount = apiCart.taxedPrice?.totalGross.centAmount || apiCart.totalPrice.centAmount
      payload.items = [
        {
          name: apiCart.id,
          quantity: 1,
          reference: apiCart.id,
          total_amount: amount,
          unit_price: amount,
        },
      ]
      payload.processing = {
        invoice_id: apiCart.id,
      }
      payload.source = {
        ...payload.source,
        account_holder_name: concat(apiCart.shippingAddress.firstName, apiCart.shippingAddress.lastName),
        payment_country: 'PT',
      }
    }

    const paymentRequestOptions = {
      enabled: !(
        // no 3ds if ...
        (
          [CONST.payment.paymentMode.PAYPAL].includes(mode) || //.. we pay with paypal or ..
          ([CONST.payment.paymentMode.GOOGLEPAY, CONST.payment.paymentMode.APPLEPAY].includes(mode) &&
            paymentTokenType?.toLowerCase() === 'cryptogram_3ds')
        ) // .. we use web with google pay or apple pay
      ),
    }

    // If any valuable product is in the cart, we ask Checkout to trigger a 3DS challenge.
    const force3DSCondition = apiCart.entries?.some((entry) =>
      config.checkout?.forced3DSProducts?.includes(entry?.product?.code)
    )

    if (force3DSCondition) {
      paymentRequestOptions['challenge_indicator'] = 'challenge_requested'
    }

    // Exemption is causing errors with google pay/apple pay, Checkout feedback
    if (![CONST.payment.paymentMode.GOOGLEPAY, CONST.payment.paymentMode.APPLEPAY].includes(mode)) {
      paymentRequestOptions['exemption'] = 'transaction_risk_assessment'
    }

    /** @type {CheckoutPaymentRequestResponse} */
    let paymentResult
    try {
      const url = `${config.apps[context.siteId].baseUrl}/checkout/paiements/checkout`
      paymentResult = await context.dataSources.checkout.requestPayment(payload, paymentRequestOptions, url)
    } catch (e) {
      const orderResult = new GQLOrderResult()
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = e.body.error_codes?.join('|') || CONST.order.statuses.error
      orderResult.statusMessage = e.body.error_type || 'Unknown error'
      return orderResult
    }

    const orderResult = new GQLOrderResult()
    switch (paymentResult.status) {
      case CONST.payment.checkout.paymentStatus.AUTHORIZED:
      case CONST.payment.checkout.paymentStatus.PAID: {
        const paymentInfo = await context.dataSources.checkout.getPaymentDetails(paymentResult.id)
        return this._transformXPayment(
          {
            amount: paymentInfo.amount,
            cartId: paymentInfo.metadata.cart_id,
            id: paymentInfo.id,
            method: paymentInfo.metadata.method,
            scheme: paymentInfo.source?.scheme,
            sessionId: paymentInfo.metadata.session_id,
            status: paymentInfo.status,
            type: paymentInfo.source?.type,
          },
          false,
          CONST.payment.provider.CHECKOUT,
          context
        )
      }
      case CONST.payment.checkout.paymentStatus.DECLINED: {
        const apiCart = await this._getApiCart(context)
        const { cardType } = getPaymentMode(paymentResult.source?.scheme || paymentResult.source?.type)
        const paymentState =
          paymentResult.status === CONST.payment.checkout.paymentStatus.DECLINED
            ? CONST.commerceTools.paymentStates.failed
            : CONST.commerceTools.paymentStates.pending
        // do not block
        this._addPayment(
          {
            cardType,
            cart: apiCart,
            details: paymentResult,
            mode,
            provider: CONST.payment.provider.CHECKOUT,
            state: paymentState,
            transactionId: paymentResult.id,
          },
          context
        ).catch((e) => apm.captureError(e, { custom: e }))
        orderResult.status = CONST.order.statuses.error
        orderResult.statusCode = paymentResult.response_code || CONST.order.statuses.error
        orderResult.statusMessage = paymentResult.response_summary || 'Unknown error'
        return orderResult
      }
      case CONST.payment.checkout.paymentStatus.PENDING:
        orderResult.status = CONST.order.statuses.redirect
        orderResult.statusCode = 'REDIRECT'
        orderResult.usePost = false
        if (paymentResult._links.redirect) {
          orderResult.redirectUrl = paymentResult._links.redirect.href
        } else {
          orderResult.redirectUrl = `${context.appConfig.baseUrl}/checkout/paiements/checkout/success?cko-session-id=${paymentResult.id}`
        }
        return orderResult
    }

    throw new GraphQLApolloError(
      `Unhandled payment response: ${paymentResult.status}`,
      'order-invalid-payment-response'
    )
  }

  /**
   * @param {string} id
   * @param {boolean} isS2S
   * @param {GraphQLContext} context
   * @param {string} urlPath
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmCheckoutPayment(id, isS2S, context, urlPath) {
    /** @type {CheckoutPaymentDetails} */
    const paymentInfo = await this._checkXPaymentCapture(id, CONST.payment.provider.CHECKOUT, context)

    if (!paymentInfo) {
      const error = new GraphQLApolloError('[checkout] no payment info')
      apm.captureError(error, { custom: error })

      const result = new GQLOrderResult()
      result.status = CONST.order.statuses.error
      result.statusCode = 'ERROR'
      result.statusMessage = 'Payment info not found'

      return result
    }

    return context.lock(paymentInfo.id, async () => {
      if (!paymentInfo?.source?.type) {
        const error = new GraphQLApolloError('[checkout] no source type found')
        apm.captureError(error, { custom: error })

        const result = new GQLOrderResult()
        result.status = CONST.order.statuses.error
        result.statusCode = 'ERROR'
        result.statusMessage = 'Source type missing'

        return result
      }

      switch (paymentInfo?.status) {
        case CONST.payment.checkout.paymentStatus.AUTHORIZED:
        case CONST.payment.checkout.paymentStatus.PENDING: {
          if (!['paypal', 'mbway'].includes(paymentInfo.source?.type)) {
            try {
              await context.dataSources.checkout.capturePayment(paymentInfo.id, `${paymentInfo.reference}_capture`)
            } catch (e) {
              apm.captureError(e, { custom: e })

              const { cardType, mode } = getPaymentMode(paymentInfo.source?.scheme || paymentInfo.metadata.method)
              // do not wait
              this._addPayment(
                {
                  cardType,
                  details: paymentInfo,
                  mode,
                  provider: CONST.payment.provider.CHECKOUT,
                  state: CONST.commerceTools.paymentStates.failed,
                  transactionId: paymentInfo.actions?.at(-1)?.id || `${paymentInfo.id}-failed-${Date.now()}`,
                },
                context
              ).catch((e) => apm.captureError(e, { custom: e }))

              const orderResult = new GQLOrderResult()
              orderResult.status = CONST.order.statuses.error
              orderResult.statusCode = e.source?.http_code || 500
              orderResult.statusMessage = e.source?.name || e.message || 'Unknown error'
              return orderResult
            }
          } else if (['alma', 'paypal', 'mbway'].includes(paymentInfo.source?.type) && urlPath.includes('failure')) {
            context.log.warn(`[checkout] ${paymentInfo.source?.type} cancelled`)

            const { cardType, mode } = getPaymentMode(paymentInfo.source?.scheme || paymentInfo.metadata.method)
            this._addPayment(
              {
                cardType,
                details: paymentInfo,
                mode,
                provider: CONST.payment.provider.CHECKOUT,
                state: CONST.commerceTools.paymentStates.failed,
                transactionId: paymentInfo.actions?.at(-1)?.id || `${paymentInfo.id}-failed-${Date.now()}`,
              },
              context
            ).catch((e) => apm.captureError(e, { custom: e }))

            const orderResult = new GQLOrderResult()
            orderResult.status = CONST.order.statuses.error
            orderResult.statusCode = 4004 // cancellation code for paypal
            orderResult.statusMessage = 'Payment cancelled'
            return orderResult
          }

          return this._pendingXPaymentAction(
            paymentInfo.metadata?.cart_id,
            paymentInfo.source?.scheme || paymentInfo.source?.type || paymentInfo.metadata?.method,
            paymentInfo,
            CONST.payment.provider.CHECKOUT,
            context
          )
        }
        case CONST.payment.checkout.paymentStatus.CAPTURED:
        case CONST.payment.checkout.paymentStatus.PAID:
          return this._transformXPayment(
            {
              amount: paymentInfo.amount,
              cartId: paymentInfo.metadata.cart_id,
              id: paymentInfo.id,
              method: paymentInfo.metadata.method,
              scheme: paymentInfo.source?.scheme,
              sessionId: paymentInfo.metadata.session_id,
              status: paymentInfo.status,
              type: paymentInfo.source?.type,
            },
            isS2S,
            CONST.payment.provider.CHECKOUT,
            context
          )
        case CONST.payment.checkout.paymentStatus.DECLINED:
        case CONST.payment.checkout.paymentStatus.EXPIRED: {
          const lastAction = (paymentInfo.actions || []).at(-1)
          const orderResult = new GQLOrderResult()
          orderResult.status = CONST.order.statuses.error
          orderResult.statusCode = lastAction?.response_code || CONST.order.statuses.error
          orderResult.statusMessage = lastAction?.response_summary || 'Unknown error'

          return orderResult
        }
        default:
          throw new GraphQLApolloError(`Unhandled checkout payment status ${paymentInfo.status}`)
      }
    })
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmPaycometPayment(context) {
    const cartId = context.session?.apiCart?.id
    if (!cartId) {
      throw new GraphQLApolloError('Cart not found', 400)
    }

    /** @type {PaycometOperationInfoResponse} */
    let { payment: operationInfo } = await context.dataSources.paycomet.operationInfo(cartId)
    if (!operationInfo) {
      throw new GraphQLApolloError('Payment not found', 400)
    }

    {
      let attempts = 0
      while (
        attempts < config.paycomet.maxPendingChecks &&
        PaycometOperationState.is(operationInfo?.history.at(-1), 'Pending')
      ) {
        await new Promise((resolve, reject) =>
          setTimeout(async () => {
            try {
              operationInfo = await context.dataSources.paycomet.operationInfo(cartId)
              attempts++
              const lastOperation = operationInfo?.history.at(-1)
              if (!PaycometOperationState.is(lastOperation.status, 'Pending')) {
                context.log.info(`_confirmPaycometPayment - status changed from Pending to ${lastOperation.status}`)
              }
              resolve()
            } catch (e) {
              attempts = Number.MAX_SAFE_INTEGER
              reject(e)
            }
          }, config.paycomet.pendingCheckInterval)
        )
      }

      const lastOperation = operationInfo?.history.at(-1)
      const paymentMode = PaycometPaymentMethod.is(lastOperation.methodId, 'Cards')
        ? lastOperation.cardBrand
        : PaycometPaymentMethod.fromValue(lastOperation.methodId)

      switch (lastOperation.state) {
        case PaycometOperationState.Pending: {
          let apiCart = await context.loaders.commerceTools.cartsAsRead.load(operationInfo.order)
          if (!apiCart) {
            throw new GraphQLApolloError('Cart not found', 400)
          }

          if (apiCart.cartState === CONST.commerceTools.cartStates.active) {
            context.session.apiCart = { id: operationInfo.order }
          }

          const { cardType, mode } = getPaymentMode(paymentMode)
          apiCart = await this._addPayment(
            {
              cardType: cardType || mode,
              cart: apiCart,
              mode,
              provider: CONST.payment.provider.paycomet,
              state: CONST.commerceTools.paymentStates.pending,
            },
            context
          )

          await this._clearSessionCart(context)

          const result = new GQLOrderResult()
          result.status = 'AWAITING_CAPTURE'
          result.statusCode = 'AWAITING_CAPTURE'

          return result
        }

        case PaycometOperationState.Success:
          return this._transformPaycometPayment(operationInfo, context)

        case PaycometOperationState.Error: {
          const result = new GQLOrderResult()
          result.status = CONST.order.statuses.error
          result.statusCode = lastOperation.errorCode
          result.statusMessage = lastOperation.errorDescription

          return result
        }

        default:
          throw new GraphQLApolloError(`Unhandled paycomet payment state ${lastOperation.state}`)
      }
    }
  }

  /**
   * @param {Error} error
   * @param {{
   *  code: string,
   *  message: string
   * }} opts
   * @returns {GQLOrderResult}
   */
  _error2OrderResult(error, opts = {}) {
    apm.captureError(error, { custom: error })

    const result = new GQLOrderResult()
    result.status = CONST.order.statuses.error
    result.statusCode = opts.code || error.status || error.statusCode || '500'
    result.statusMessage = opts.message || error.message

    return result
  }

  /**
   * @param {PaycometOperationInfo} operationInfo
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOrderResult>}
   */
  async _transformPaycometPayment(operationInfo, context) {
    const cartId = operationInfo.order
    try {
      const order = await context.dataSources.orders.getByCartId(cartId)
      if (order) {
        debug('transformPaycometPayment, cart already transformed: %0', {
          order,
          paymentResult: 'SUCCESS',
        })

        await this._clearSessionCart(context)
        if (!context.session || !context.session.apiCart) {
          await this._clearCartLoaders(cartId, context)
        }

        const result = new GQLOrderResult()
        result.status = CONST.order.statuses.success
        result.order = CTOrderTransformer.toGQL(order, context, {
          distributionChannels: await getDistributionChannels(context),
        })

        return result
      }
    } catch (e) {
      // Do nothing
    }

    const apiCart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
    const lastOperation = operationInfo.history.at(-1)
    const paymentMode = PaycometPaymentMethod.is(lastOperation.methodId, 'Cards')
      ? lastOperation.cardBrand
      : PaycometPaymentMethod.fromValue(lastOperation.methodId)
    const { cardType, mode } = getPaymentMode(paymentMode)
    const order = await this._convertCart(
      {
        cardType: cardType || mode,
        cart: apiCart,
        details: operationInfo,
        provider: CONST.payment.provider.PAYCOMET,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: operationInfo.paycometId,
      },
      context
    )

    const result = new GQLOrderResult()
    if (!order) {
      const error = new GraphQLApolloError('paycomet transform cart', 500)
      apm.captureError(error, { custom: error })

      result.status = CONST.order.statuses.error
      result.statusCode = 500
      result.statusMessage = 'Error converting cart to order'
    } else {
      result.status = CONST.order.statuses.success
      result.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
    }

    await this._clearSessionCart(context)
    if (!context.session || !context.session.apiCart) {
      await this._clearCartLoaders(cartId, context)
    }

    return result
  }

  /**
   * @param {GraphQLContext} context
   * @param {string} paypalOrderId
   * @param {PaypalCaptureOrderResponse} captureResponse
   */
  async _transformPaypalPayment(context, paypalOrderId, captureResponse) {
    /** @type {PaypalOrderDetails} */
    const paymentDetails = await context.dataSources.paypal.getOrder(paypalOrderId)

    if (paymentDetails.status !== 'COMPLETED') {
      const error = new GraphQLApolloError('Paypal capture is not completed')
      context.log.error(`[paypal] capture is not completed %O`, paymentDetails)
      apm.captureError(error, { custom: paymentDetails })

      const result = new GQLOrderResult()
      result.status = CONST.order.statuses.error
      result.statusCode = '500'
      result.statusMessage = 'Payment failed'
      return result
    }

    const cartId = paymentDetails.purchase_units[0].reference_id

    /** @type {CTCart} */
    let apiCart
    try {
      apiCart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
    } catch (e) {
      return this._error2OrderResult(e, {
        code: '404',
        message: 'Cart not found',
      })
    }

    if (apiCart.cartState !== CONST.commerceTools.cartStates.active) {
      try {
        const order = await new Promise((resolve) =>
          setTimeout(() => resolve(context.dataSources.orders.getByCartId(cartId)))
        )
        context.log.info(`[paypal] order retrieved from payment info: ${order.id}`)

        if (context.session?.apiCart) {
          delete context.session.apiCart
          await context.session?.save?.()
        }

        const result = new GQLOrderResult()
        result.status = CONST.order.statuses.success
        result.order = order
        return result
      } catch (e) {
        return this._error2OrderResult(e, {
          code: '404',
          message: 'Cart not found',
        })
      }
    }

    /** @type {PaypalCapture} */
    const capture = paymentDetails.purchase_units
      .map((purchaseUnit) => purchaseUnit.payments.captures)
      .flat()
      .sort((a, b) => b.create_time.localeCompare(a.create_time))[0]

    if (!capture || capture.status !== 'COMPLETED') {
      let transactionId = capture.id

      if (!capture) {
        const error = new GraphQLApolloError(`[paypal] no capture found`)
        apm.captureError(error, { custom: error })
        transactionId = captureResponse.id
      }

      // authorized but not captured yet => pending
      await this._addPayment(
        {
          cardType: CONST.payment.paymentMode.PAYPAL,
          cart: apiCart,
          details: captureResponse,
          mode: CONST.payment.paymentMode.PAYPAL,
          provider: CONST.payment.provider.PAYPAL,
          state: CONST.commerceTools.paymentStates.paid,
          transactionId: `${transactionId}-paypal-pending-review`,
        },
        context
      )

      const result = new GQLOrderResult()
      result.status = 'AWAITING_CAPTURE'
      result.statusCode = 'AWAITING_CAPTURE'

      return result
    }

    const order = await this._convertCart(
      {
        cardType: CONST.payment.paymentMode.PAYPAL,
        cart: apiCart,
        details: captureResponse,
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.PAYPAL,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: capture.id,
      },
      context
    )

    const result = new GQLOrderResult()
    if (!order) {
      const error = new GraphQLApolloError(`_transformPaypalPayment`, 500)
      apm.captureError(error, { custom: error })

      result.status = CONST.order.statuses.error
      result.statusCode = 500
      result.statusMessage = 'Error converting cart to order'
    } else {
      result.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
      result.status = CONST.order.statuses.success
    }

    await Promise.all([this._clearSessionCart(context), context.loaders.commerceTools.orders.prime(order.id, order)])

    return result
  }

  /**
   * Attempts to recover a captured payment for a maximum number of iterations
   *
   * @param {string} paymentId
   * @param {string} provider
   * @param {GraphQLContext} context
   * @return {Promise<CheckoutPaymentDetails | BuyboxGetTransactionDetailsResponse | undefined>}
   */
  async _checkXPaymentCapture(paymentId, provider, context) {
    const providerPendingStatus = CONST.payment[provider.toLowerCase()].paymentStatus.PENDING
    const providerPendingCheckInterval = config[provider.toLowerCase()].pendingCheckInterval
    const providerMaxPendingChecks = config[provider.toLowerCase()].maxPendingChecks

    /** @type {CheckoutPaymentDetails | BuyboxGetTransactionDetailsResponse} */
    let paymentInfo
    let status

    const setPaymentDetails = async () => {
      switch (provider) {
        case CONST.payment.provider.CHECKOUT:
          paymentInfo = await context.dataSources.checkout.getPaymentDetails(paymentId)
          status = paymentInfo?.status
          break
        case CONST.payment.provider.BUYBOX:
          paymentInfo = await context.dataSources.buybox.getTransactionDetails(paymentId)
          status = paymentInfo?.PAYMENTSTATUS
          break
        default:
          throw new Error('not implemented')
      }
    }

    try {
      await setPaymentDetails()
    } catch (e) {
      apm.captureError(e, { custom: e })
      const orderResult = new GQLOrderResult()
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = e.http_code || '500'
      orderResult.statusMessage = e.message || e.name || 'Unknown error'
      return orderResult
    }

    {
      // For pending orders, try to refresh a couple of time to catch captured
      // payments received in a reasonable amount of time
      // on dev, is too low to match paypal orders (captured time is ~20s)
      let attempts = 0
      while (attempts < providerMaxPendingChecks && status === providerPendingStatus) {
        await new Promise((resolve, reject) =>
          setTimeout(async () => {
            try {
              await setPaymentDetails()
              attempts++

              if (status !== providerPendingStatus)
                context.log.info(`_confirm${provider}Payment - status changeed from Pending to ${status}`)
              resolve()
            } catch (e) {
              attempts = Number.MAX_SAFE_INTEGER // force exit
              reject(e)
            }
          }, providerPendingCheckInterval)
        )
      }
    }

    context.log.info(`[_confirm${provider}Payment] paymentInfo %O`, paymentInfo)
    debug(`${provider.toLowerCase()} payment info: %O`, paymentInfo)

    return paymentInfo
  }

  /**
   * @returns {GQLOrderReason[]}
   */
  _getReasons() {
    return config.order.reasons
  }

  /**
   * Called once we are sure we received a payment
   * Reflect the payment in commerce tools and transform the cart into an order
   *
   * @param {{
   *  cart: CTCart,
   *  details: object,
   *  custom: CTCustomFields?,
   *  mode: string,
   *  provider: string,
   *  state: string,
   *  transactionId: string,
   *  transactionType: string
   * }} input
   * @param {GraphQLContext} context
   * @return {Promise<CTOrder>}
   */
  async _convertCart(input, context) {
    let cart = await this._addPayment(input, context)
    cart = await this._beforeConvertingCart(cart, context)

    const cartId = cart.id

    let orderNumber
    try {
      const orderNumberPrefix = await this._computeOrderNumberPrefix(cart, context)
      orderNumber = await context.dataSources.sequenceGenerator.next(orderNumberPrefix)
      if (!orderNumber) {
        throw new Error(`/GenerateNextSequence returned => ${orderNumber}`)
      }
    } catch (e) {
      context.log.error(`[_convertCart] cart ${cartId} was unable to generate an order number: ${e}`)
      // tmp workaround - do not throw but keep error visible
      apm.captureError(e, { custom: e })
    }
    if (orderNumber && typeof orderNumber !== 'string') {
      orderNumber = `${orderNumber}`
    }

    let order
    try {
      const custom = input.custom ?? undefined

      /** @type {CTOrder} */
      order = await context.dataSources.carts.createOrder({
        cart: {
          id: cartId,
          type: 'Cart',
        },
        custom: {
          ...(custom ?? {}),
          fields: {
            src: CONST.commerceTools.order.comingFromSystem.Commercetools,
          },
          type: { key: CONST.commerceTools.customTypes.cart },
        },
        orderNumber,
        paymentState: CONST.commerceTools.paymentStates.paid,
        version: cart?.version || 1,
      })

      if (order.customerEmail) {
        // In case user visits his orders the list will be cached and new order
        // won't be visible on it. This clears all user order list pages cache.
        await clearTagCache(`uol:${hash(order.customerEmail)}`, context.server.redis)
      }
    } catch (e) {
      apm.captureError(e, { custom: e })
      const error = new GraphQLApolloError(
        `createOrder failed - fallback to get order from cart id ${cartId}`,
        e.statusCode || 500
      )
      error.originalError = e
      apm.captureError(error, { custom: error })
      order = await context.dataSources.orders.getByCartId(cartId)
    }

    try {
      await this._afterCartConverted(order, context)
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    await this._decrementStock(input.cart, order.id, context)

    await Promise.all([
      context.loaders.commerceTools.carts.clear(cartId),
      context.loaders.commerceTools.cartsAsRead.clear(cartId),
    ])
    await this._clearSessionCart(context)
    return order
  }

  /**
   * Decrement stock using a dedicated dataSource
   *
   * @param {CTCart} _cart
   * @param {string} _orderId
   * @param {GraphQLContext} _context
   * @return {Promise<void>}
   */
  async _decrementStock(_cart, _orderId, _context) {
    return null
    // Only implemented in the FR resolvers for now
  }

  /**
   * Post payment actions in the cart
   *
   * @param {{cardType: string, mode: string, provider: string}} paymentInfo
   * @param {GraphQLContext} context
   * @return {Promise<CTCart>}
   */
  async _initCartForPayment(paymentInfo, context) {
    const { cardType, mode, provider } = paymentInfo
    await this._assertHasCart(context)

    /** @type {CTCart} */
    const apiCart = await context.loaders.commerceTools.cartsAsRead.load(context.session.apiCart.id)
    if (!apiCart) {
      const error = new GraphQLApolloError(`no cart found ${context.session.apiCart.id}`)
      apm.captureError(error, { custom: error })

      const orderResult = new GQLOrderResult()
      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No cart found ${context.session.apiCart.id}`

      return orderResult
    }

    if (apiCart.cartState !== CONST.commerceTools.cartStates.active) {
      const orderResult = new GQLOrderResult()
      const order = await new Promise((resolve) =>
        setTimeout(
          () => resolve(context.dataSources.orders.getByCartId(context.session.apiCart.id)),
          config.commerceTools.waitForOrderDelay
        )
      )

      if (order) {
        context.log.info(`[createOrder ${provider.toLowerCase()}] order retrieved for payment info: ${order.id}`)
        orderResult.order = CTOrderTransformer.toGQL(order, context, {
          distributionChannels: await getDistributionChannels(context),
        })
        orderResult.status = CONST.order.statuses.success
        return orderResult
      }

      const error = new GraphQLApolloError(`no order found for cart ${apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No order found for cart ${apiCart.id}`

      return orderResult
    }

    await this._addPayment(
      {
        cardType,
        cart: apiCart,
        mode,
        provider,
        state: CONST.commerceTools.paymentStates.pending,
        transactionId: `${apiCart.id}#${apiCart.version}`,
      },
      context
    )
    return apiCart
  }

  /**
   * Get the Checkout payment ID
   *
   * @param {string} cartId
   * @param {string} paymentMode
   * @param {string} partnerPaymentId
   * @param {GraphQLContext} context
   */
  async _getCheckoutPaymentId(cartId, paymentMode, partnerPaymentId, context) {
    const cart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
    const paymentIds = cart?.paymentInfo?.payments?.map((payment) => payment.id) ?? []
    /** @type {CTPayment[]} */
    const payments = (await context.loaders.commerceTools.payments.loadMany(paymentIds)) ?? []
    // reverse the array, as the payment we're interested in is more likely to be among the last
    for (const payment of payments.reverse()) {
      const interactionPaymentIds =
        payment.interfaceInteractions?.map((interaction) => {
          try {
            const data = JSON.parse(interaction.fields?.serialized)
            let partnerPaymentId
            /**
             * the access path to the value may
             * differ depending on the method used
             */
            switch (paymentMode) {
              case CONST.payment.paymentMode.ALMA:
                partnerPaymentId = data?.processing?.partner_payment_id
            }
            return {
              checkoutId: data?.id,
              partnerPaymentId,
            }
          } catch {
            return null
          }
        }) ?? []
      const checkoutId = interactionPaymentIds.find((pay) => pay?.partnerPaymentId === partnerPaymentId)?.checkoutId
      if (checkoutId) return checkoutId
    }
    return partnerPaymentId
  }

  /**
   * Setting up the order configuration
   * and obtaining the redirect Url with the token
   *
   * @param {BuyboxCartPaymentInfo} paymentInfo
   * @param {GraphQLContext} context
   * @param {Promise<postPaymentCallback>} [postPaymentHook]
   * @returns {Promise<GQLOrderResult>}
   */
  async _submitBuyboxPayment(paymentInfo, context, postPaymentHook) {
    const { amount, cardCode, cardPin, cartId } = paymentInfo
    if (postPaymentHook) await postPaymentHook({ ...paymentInfo, provider: CONST.payment.provider.BUYBOX }, context)
    const orderResult = new GQLOrderResult()
    try {
      const { TOKEN: token } = await context.dataSources.buybox.setExpressCheckout({
        action: CONST.payment.buybox.paymentAction.SALE,
        amount,
        cardCode,
        cardPin,
        cartId,
        solutionType: CONST.payment.buybox.solutionType.SOLE,
      })
      orderResult.status = CONST.order.statuses.redirect
      orderResult.statusCode = 'REDIRECT'
      orderResult.usePost = false
      orderResult.redirectUrl = `${config.buybox.redirectUrl}?useraction=commit&token=${token}`
      return orderResult
    } catch (e) {
      apm.captureError(e, { custom: e })
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = 500
      orderResult.statusMessage = e.message || 'Unknown error'
      return orderResult
    }
  }

  /**
   * Capture the payment and transform the cart to order
   *
   * @param {BuyboxOrderPaymentInfo} paymentInfo
   * @param {GraphQLContext} context
   * @param {Promise<transformOrderCallback>} transformOrderHook
   * @param {Promise<pendingOrderCallback>} [pendingOrderHook]
   * @param {Promise<cancelledOrderCallback>} [cancelledOrderHook]
   * @param {Promise<errorOrderCallback>} [errorOrderHook]
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmBuyboxPayment(
    paymentInfo,
    context,
    transformOrderHook,
    pendingOrderHook,
    cancelledOrderHook,
    errorOrderHook
  ) {
    const { amount, payerId, token, cartId, isS2S } = paymentInfo
    const orderResult = new GQLOrderResult()
    try {
      let _paymentInfo
      _paymentInfo = await context.dataSources.buybox.doExpressCheckoutPayment({
        action: CONST.payment.buybox.paymentAction.SALE,
        amount,
        payerId,
        token,
      })

      /** @type {BuyboxGetTransactionDetailsResponse} */
      if (_paymentInfo?.PAYMENTSTATUS === CONST.payment.buybox.paymentStatus.AWAITING_CAPTURE) {
        _paymentInfo =
          (await this._checkXPaymentCapture(_paymentInfo.TRANSACTIONID, CONST.payment.provider.BUYBOX, context)) ??
          _paymentInfo
      }

      const updatePaymentStatus = (state, transactionId) => {
        this._addPayment(
          {
            cardType: CONST.payment.paymentMode.GIFT_CARD,
            details: _paymentInfo,
            mode: CONST.payment.paymentMode.GIFT_CARD,
            provider: CONST.payment.provider.BUYBOX,
            state,
            transactionId,
          },
          context
        ).catch((e) => apm.captureError(e, { custom: e }))
      }

      switch (_paymentInfo?.PAYMENTSTATUS) {
        case CONST.payment.buybox.paymentStatus.SUCCESS:
          return transformOrderHook(
            {
              amount: _paymentInfo.AMT,
              cartId,
              id: _paymentInfo.TRANSACTIONID,
              method: null,
              scheme: null,
              sessionId: null,
              status: CONST.payment.paymentStatus.SUCCESS,
              type: CONST.payment.paymentMode.GIFT_CARD,
            },
            isS2S,
            CONST.payment.provider.BUYBOX,
            context
          )
        case CONST.payment.buybox.paymentStatus.AWAITING_CAPTURE:
          if (pendingOrderHook) {
            await pendingOrderHook(cartId, context)
          } else {
            await this._pendingXPaymentAction(
              cartId,
              CONST.payment.paymentMode.GIFT_CARD,
              _paymentInfo,
              CONST.payment.provider.BUYBOX,
              context
            )
          }
          orderResult.status = 'AWAITING_CAPTURE'
          orderResult.statusCode = 'AWAITING_CAPTURE'
          break
        case CONST.payment.buybox.paymentStatus.VOIDED:
          context.log.warn(`[buybox] ${CONST.payment.cardType.GIFT_CARD} cancelled`)
          if (cancelledOrderHook) {
            await cancelledOrderHook(context)
          } else {
            updatePaymentStatus(CONST.commerceTools.paymentStates.failed, _paymentInfo.TRANSACTIONID)
          }
          orderResult.status = CONST.order.statuses.canceled
          orderResult.statusCode = 500
          orderResult.statusMessage = 'Payment cancelled'
          break
        case CONST.payment.buybox.paymentStatus.ERROR:
          if (errorOrderHook) {
            await errorOrderHook(context)
          } else {
            updatePaymentStatus(CONST.commerceTools.paymentStates.failed, _paymentInfo.TRANSACTIONID)
          }
          orderResult.status = CONST.order.statuses.error
          orderResult.statusCode = 500
          orderResult.statusMessage = 'Transaction failed'
          break
        default:
          orderResult.status = CONST.order.statuses.error
          orderResult.statusCode = 500
          orderResult.statusMessage = 'Unknown error'
      }
      return orderResult
    } catch (e) {
      apm.captureError(e, { custom: e })
      orderResult.status = CONST.order.statuses.error
      orderResult.statusCode = 500
      orderResult.statusMessage = e.message || 'Unknown error'
      return orderResult
    }
  }

  /**
   * In some countries specific orders (such as b2b for France) need a prefix in front of their order number
   * this method computes it with country-specific logic and returns a prefix if needed
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {string | undefined}
   */
  async _computeOrderNumberPrefix(_cart, _context) {
    return undefined
  }

  /**
   * @param {string} cartId
   * @param {string} paymentMode
   * @param {*} details
   * @param {string} provider
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart | void>}
   */
  async _pendingXPaymentAction(cartId, paymentMode, details, provider, context) {
    if (cartId) {
      let apiCart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
      if (!apiCart) {
        throw new GraphQLApolloError('Cart not found', 400)
      }

      if (apiCart.cartState === CONST.commerceTools.cartStates.active) {
        context.session.apiCart = {
          countryId: apiCart.shippingAddress?.country,
          id: cartId,
        }
      }

      const { cardType, mode } = getPaymentMode(paymentMode)

      await this._addPayment(
        {
          cardType: cardType || mode,
          cart: apiCart,
          details,
          mode,
          provider,
          state: CONST.commerceTools.paymentStates.pending,
        },
        context
      )
    }
    await this._clearSessionCart(context)
    const orderResult = new GQLOrderResult()
    orderResult.status = 'AWAITING_CAPTURE'
    orderResult.statusCode = 'AWAITING_CAPTURE'

    return orderResult
  }

  /**
   * Generic template to handle payment transformation
   *
   * @param {TransformPaymentInfo} paymentInfo
   * @param {boolean} isS2S
   * @param {string} provider
   * @param {GraphQLContext} context
   * @return {Promise<GQLOrderResult>}
   */
  async _transformXPayment(paymentInfo, isS2S, provider, context) {
    const {
      cartId,
      scheme: paymentScheme,
      method: paymentMethod,
      type: paymentType,
      id: paymentId,
      details: paymentDetails,
    } = paymentInfo
    const providerWaitForOrderDelay = config[provider.toLowerCase()].waitForOrderDelay

    let apiCart
    try {
      apiCart = await context.loaders.commerceTools.cartsAsRead.load(cartId)
    } catch (e) {
      // do not throw yet
    }

    let order

    if (!apiCart) {
      try {
        order = await new Promise((resolve) =>
          setTimeout(() => resolve(context.dataSources.orders.getByCartId(cartId), providerWaitForOrderDelay))
        )
      } catch (e) {
        // no cart and no order => error
        apm.captureError(e, { custom: e })

        const result = new GQLOrderResult()
        result.status = CONST.order.statuses.error
        result.statusCode = '404'
        result.statusMessage = 'Cart not found'

        return result
      }
    }

    if (apiCart?.cartState === CONST.commerceTools.cartStates.ordered) {
      order = await new Promise((resolve) =>
        setTimeout(() => resolve(context.dataSources.orders.getByCartId(cartId), providerWaitForOrderDelay))
      )
    }

    if (order) {
      context.log.info(`[${provider.toLowerCase()}] order retrieved from payment info: ${order.id}`)

      if (context.session?.apiCart) {
        delete context.session.apiCart
        await context.session?.save?.()
      }

      const result = new GQLOrderResult()
      result.status = CONST.order.statuses.success
      result.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
      context.dataSources.meta
        .registerOrderPaid(context, new Date(), result.order, apiCart)
        .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

      return result
    }

    if (apiCart?.cartState === CONST.commerceTools.cartStates.active) {
      context.session.apiCart = { countryId: apiCart.shippingAddress?.country, id: cartId }
    }

    const { cardType, mode } = getPaymentMode(paymentScheme || paymentType || paymentMethod)
    order = await this._convertCart(
      {
        cardType: cardType || mode,
        cart: apiCart,
        details: paymentDetails,
        mode,
        provider,
        state: CONST.commerceTools.paymentStates.paid,
        transactionId: paymentId,
      },
      context
    )

    const result = new GQLOrderResult()

    if (order) {
      result.order = CTOrderTransformer.toGQL(order, context, {
        distributionChannels: await getDistributionChannels(context),
      })
      result.status = CONST.order.statuses.success
    } else {
      const error = new GraphQLApolloError(`${provider.toLowerCase()} doPlaceOrder`, 500)
      apm.captureError(error, { custom: error })

      result.status = CONST.order.statuses.error
      result.statusCode = 500
      result.statusMessage = 'Error converting cart to order'
    }
    await this._clearSessionCart(context)
    context.dataSources.meta
      .registerOrderPaid(context, new Date(), result.order, apiCart)
      .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))
    return result
  }

  // Invoice order only implemented in CT FR for now
  async _createInvoiceOrder() {
    throw new Error('not implemented')
  }

  /**
   * @param {GraphQLContext} context
   * @param {string} urlPath
   * @param {string} urlQuery
   * @returns {Promise<GQLOrderResult>}
   */
  async _handleFrisbiiPayment(context, urlPath, urlQuery) {
    const orderResult = new GQLOrderResult()
    orderResult.status = 'ERROR'
    orderResult.statusCode = '500'
    orderResult.statusMessage = 'Unknown error'

    const url = new URL(`https://domain.com/${urlPath}?${urlQuery}`)

    if (url.pathname.includes('/failure') || url.pathname.includes('/cancel') || !url.searchParams.has('invoice')) {
      return orderResult
    }

    const invoiceId = url.searchParams.get('invoice')
    const [frisbiiCharge, metadataResponse] = await Promise.all([
      context.dataSources.frisbii.getCharge({ invoiceId }),
      context.dataSources.frisbii.getMetadata({ invoiceId }),
    ])
    const { orderId } = metadataResponse

    if (!frisbiiCharge || !orderId) {
      const orderResult = new GQLOrderResult()

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `Cart not found`

      return orderResult
    }

    if (['settled', 'authorized'].includes(frisbiiCharge.state)) {
      return context.lock(`${frisbiiCharge.handle}::${frisbiiCharge.transaction}`, () =>
        this._confirmFrisbiiPayment(context, frisbiiCharge, orderId)
      )
    }

    return orderResult
  }

  /**
   * @param {GraphQLContext} context
   * @param {FrisbiiCharge} charge
   * @param {String} orderId
   * @returns {Promise<GQLOrderResult>}
   */
  async _confirmFrisbiiPayment(context, charge, orderId) {
    let apiCart
    try {
      apiCart = await context.loaders.commerceTools.cartsAsRead.load(orderId)
    } catch (e) {
      // do not throw yet
    }
    const orderResult = new GQLOrderResult()

    // still do not throw if we don't have cart - let's check if it is already transformed instead
    if (apiCart?.cartState !== CONST.commerceTools.cartStates.active) {
      const maxAttempts = config.commerceTools.maxOrderFetchingAttempts
      for (let i = 0; i < maxAttempts; i++) {
        const order = await context.dataSources.orders.getByCartId(apiCart.id)
        if (order) {
          context.log.info(`[confirm Frisbii] order retrieved from payment info: ${order.id} on attempt ${i}`)
          orderResult.order = CTOrderTransformer.toGQL(order, context, {
            distributionChannels: await getDistributionChannels(context),
          })
          orderResult.status = CONST.order.statuses.success
          return orderResult
        }
        await new Promise((resolve) => setTimeout(resolve, config.commerceTools.waitOrderDelay))
      }

      const error = new GraphQLApolloError(`no order found for cart ${apiCart.id}`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `No order found for cart ${apiCart.id}`

      return orderResult
    }

    if (!apiCart) {
      const error = new GraphQLApolloError(`no cart found`)
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = `Cart not found`

      return orderResult
    }

    try {
      const order = await this._convertCart(
        {
          cardType: this._mapFrisbiiPaymentTypeToCT(charge.source.type),
          cart: apiCart,
          details: charge,
          mode: this._mapFrisbiiPaymentTypeToCT(charge.source.type),
          provider: CONST.payment.provider.FRISBII,
          state: CONST.commerceTools.paymentStates.paid,
          transactionId: `${charge.handle}`,
        },
        context
      )

      const result = new GQLOrderResult()

      result.status = CONST.order.statuses.success
      result.order = CTOrderTransformer.toGQL(order, context)

      return result
    } catch (error) {
      apm.captureError(error, { custom: error })

      orderResult.status = 'ERROR'
      orderResult.statusCode = '404'
      orderResult.statusMessage = error.message

      return orderResult
    }
  }

  /**
   * Returns the paramter if not found.
   * @param {string} frisbiiPaymentType
   * @returns {string}
   */
  _mapFrisbiiPaymentTypeToCT(frisbiiPaymentType) {
    const frisbiiToCTPaymentMode = {
      [CONST.payment.frisbii.paymentTypes.cardToken]: CONST.payment.paymentMode.CARD,
      [CONST.payment.frisbii.paymentTypes.applepay]: CONST.payment.paymentMode.APPLEPAY,
      [CONST.payment.frisbii.paymentTypes.googlepay]: CONST.payment.paymentMode.GOOGLEPAY,
    }

    return frisbiiToCTPaymentMode[frisbiiPaymentType] ?? frisbiiPaymentType
  }
}

module.exports = OrderResolver
