const config = require('../../../config')
const CONST = require('../../../const')
const apm = require('elastic-apm-node')

const BaseResolver = require('./Base')

const {
  DatoFloristTownTransformer,
  DatoPageTransformer,
  DatoPageProductTranformer,
  DatoFloristPageTransformer,
  DatoStoreLocatorHomeTransformer,
} = require('../../transformers/datocms')

const { GraphQLApolloError, GraphQLDatoError } = require('../../errors')

const { getCategoryTree } = require('../../../helpers/categoryTree')
const LegalText = require('../../../helpers/dato/LegalText')
const { tagsToSeo } = require('../../../helpers/dato')
const { priceChannelKeyFromSlug, seaKeyFromSlug } = require('../../../helpers/sea')
const { getPageSlugForContext, getFooterNumberForContext } = require('../../../helpers/digitalCatalog')

const myAccountChildSlugs = new Set(config.cms.myAccount.children.map((c) => c.toLowerCase()))
const wellKnownPages = new Set(config.cms.wellKnownPages.map((s) => s.toLowerCase()))

class CmsResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *   fallback: boolean,
   *   previousRoute: GQLInputRoute,
   *   route: GQLInputRoute,
   *   zoneNames: string[]
   * }>} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLPage>}
   */
  async getPage(_parent, args, context) {
    const { fallback, route } = args

    let type = route.name
    let page
    let slug

    let canonical = null

    const datoConfiguration = await context.loaders.datocms.configuration.load(context.language)

    let headerBanner
    for (const banner of datoConfiguration.headerBanners || []) {
      if (banner.from && new Date(banner.from) > new Date()) continue
      if (banner.to && new Date(banner.to) < new Date()) continue

      let where = 'other'
      if (['category', 'parent_category', 'multiple_parents_category'].includes(route.name)) {
        where = 'category'
      } else if (route.name === 'index') {
        where = 'home'
      } else if (route.name === 'product') {
        where = 'product'
      } else if (route.name.startsWith('cart')) {
        where = 'funnel'
      }

      if (!banner.where.includes(where)) continue
      headerBanner = banner
    }

    const footerNumber = getFooterNumberForContext(context)
    const footers = await context.loaders.datocms.footers.load(footerNumber)

    const mainNavigation = await context.loaders.datocms.mainNavigation.load({
      locale: args.locale || context.language,
    })

    /** @type {DatoPageTransformerOptions} */
    const options = {
      canonical,
      categoryTree: await getCategoryTree(args.locale ?? context.language, context),
      footers,
      headerBanner,
      mainNavigation,
      route,
      zones: args.zoneNames,
    }

    switch (route.name) {
      case 'multiple_parents_category':
      case 'parent_category':
      case 'category':
        type = 'category'
        {
          const param = route.params.find((p) => p.name === 'slugCategory')
          const slug = param?.value ?? context.appConfig.product.category.allSlug

          let parentCategory

          const treeNode = options.categoryTree.find(slug)
          if (!treeNode) throw new GraphQLApolloError('Not found', 404)

          if (['parent_category', 'multiple_parents_category'].includes(route.name)) {
            const parentRootSlug = route.params.find((param) => param.name === 'slugRootParentCategory')?.value
            const parentSlug = route.params.find((param) => param.name === 'slugParentCategory')?.value

            const isParentSlugForcedAllowed = (config.cms.categories.allowedParentSlugs[slug] ?? []).includes(
              parentSlug
            )

            // wrong parent given
            if (
              (!isParentSlugForcedAllowed && parentSlug !== treeNode.parent?.slug) ||
              (parentRootSlug && parentRootSlug !== options.categoryTree.find(parentSlug)?.parent?.slug)
            ) {
              const parts = [slug]
              if (treeNode.parent) parts.unshift(treeNode.parent.slug)
              const error = new GraphQLApolloError('Moved permanently', 301)
              error.addError('location', 'location', `/c/${parts.join('/')}`)
              throw error
            }

            parentCategory = await context.loaders.datocms.categories.load({
              locale: context.language,
              slug: parentRootSlug ?? treeNode.parent?.slug ?? parentSlug,
            })
            if (!parentCategory && !isParentSlugForcedAllowed) throw new GraphQLApolloError('Not found', 404)
          } else if (treeNode.parent) {
            // has parent and none given
            const error = new GraphQLApolloError('Moved permanently', 301)
            error.addError('location', 'location', `/c/${treeNode.parent.slug}/${slug}`)
            throw error
          }

          page = await context.loaders.datocms.categories.load({
            locale: context.language,
            slug,
          })
          if (!page) throw new GraphQLApolloError('Not found', 404)
          const productType = page.legalTextProductType || parentCategory?.legalTextProductType || null
          let legalText = await LegalText.load(productType, context)

          const additionalComponents = await context.loaders.datocms.defaultBlock.load({
            locale: context.language,
            pageType: CONST.cms.dato.defaultBlocks.category,
          })

          context.dataSources.meta
            .registerCategoryPageView(context, new Date(), page)
            .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

          return DatoPageTransformer.toGQL(page, context, {
            ...options,
            additionalComponents,
            legalText,
            mainNavigation,
            parentCategory,
            type,
          })
        }
      case 'floristAddressDepartment':
      case 'floristDepartment': {
        slug = route.params.find((param) => param.name === 'slugUrl')?.value
        if (!slug) throw new GraphQLApolloError('Not found', 404)

        const storeLocatorHome = await context.loaders.datocms.storeLocatorHome.load(args.locale || context.language)
        page = await context.loaders.datocms.floristRegion.load({
          locale: args.locale || context.language,
          slug,
        })
        if (!page) throw new GraphQLApolloError('Not found', 404)

        const additionalComponents = await context.loaders.datocms.defaultBlock.load({
          locale: context.language,
          pageType: CONST.cms.dato.defaultBlocks.floristDepartment,
        })

        context.dataSources.meta
          .registerGenericPageView(context, new Date(), 'floristDepartment', page?.slug)
          .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

        return DatoPageTransformer.toGQL(
          {
            ...page,
            seoMeta: tagsToSeo(page._seoMetaTags),
          },
          context,
          {
            ...options,
            additionalComponents,
            metaTitleRef: 'metaTitle',
            storeLocatorHome,
            type,
          }
        )
      }
      case 'floristAddressDepartments':
      case 'floristDepartments': {
        page = await context.loaders.datocms.storeLocatorHome.load(args.locale || context.language)
        context.dataSources.meta
          .registerGenericPageView(context, new Date(), 'floristDepartments', page?.slug)
          .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))
        return DatoStoreLocatorHomeTransformer.toGQL(page, context, { ...options })
      }
      case 'floristAddressTown':
      case 'floristTown': {
        slug = route.params.find((param) => param.name === 'slugTown').value
        const storeLocatorHome = await context.loaders.datocms.storeLocatorHome.load(args.locale || context.language)
        page = await context.loaders.datocms.floristPage.load({
          locale: args.locale || context.language,
          slug,
        })
        if (!page) throw new GraphQLApolloError('Not found', 404)

        const additionalComponents = await context.loaders.datocms.defaultBlock.load({
          locale: context.language,
          pageType: CONST.cms.dato.defaultBlocks.floristTown,
        })

        context.dataSources.meta
          .registerGenericPageView(context, new Date(), 'floristTown', page?.slug)
          .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

        return DatoFloristPageTransformer.toGQL(page, context, {
          ...options,
          additionalComponents,
          storeLocatorHome,
          type,
        })
      }
      case 'product': {
        type = 'product'
        const slug = route.params.find((param) => param.name === 'slugProduct')?.value
        const seaKey = seaKeyFromSlug(slug)

        const datoProduct = await this._loadDatoProduct(
          {
            isAccessory: false,
            seaKey,
            slug,
          },
          context
        )

        let category
        if (context.categoryId) {
          const slug = await context.loaders.datocms.categorySlugsById.load({
            id: context.categoryId,
            locale: context.language,
          })
          if (slug) category = await context.loaders.datocms.categories.load({ locale: context.language, slug: slug })
        }
        if (!category && datoProduct.defaultCategory) {
          const slug = await context.loaders.datocms.categorySlugsById.load({
            id: datoProduct.defaultCategory.commercetoolsCategory,
            locale: context.language,
          })
          if (slug)
            category = await context.loaders.datocms.categories.load({
              locale: context.language,
              slug,
            })
        }

        const additionalComponents = await context.loaders.datocms.defaultBlock.load({
          locale: context.language,
          pageType: CONST.cms.dato.defaultBlocks.product,
        })

        const crossSellProducts = datoProduct.defaultCategory?.commercetoolsCategory
          ? (
              await context.loaders.commerceTools.productLists.load({
                id: datoProduct.defaultCategory.commercetoolsCategory,
                opts: {
                  filters: [],
                  limit: config.products.linked.desired,
                  locale: context.language,
                },
              })
            ).results
          : []

        // SEO : Temporary condition during Portugal launch
        if (context.countryId !== 'PT') {
          let channel = priceChannelKeyFromSlug(slug)
          if (seaKey) {
            channel = config.sea[seaKey].priceChannelKey
          }
          const code = await context.loaders.datocms.productCodesByIdOrKey.load({
            code: datoProduct.commercetoolsProduct,
            locale: context.language,
          })
          const gqlProduct = await this._loadProductFromCT({ channel, code }, context)
          if (gqlProduct.defaultVariant) {
            const baseUrl = config.apps[context.siteId]?.baseUrl || ''
            if (gqlProduct.defaultVariant.qualifiers && gqlProduct.defaultVariant.qualifiers.length > 1) {
              // color variant
              canonical = gqlProduct.defaultVariant.qualifiers
                .sort((qual1, qual2) => qual1.orderSlug - qual2.orderSlug)
                .reduce((acc, currentQual) => acc + '/' + currentQual.slugUrlPart, `${baseUrl}/p/${slug}`)
            } else {
              // size variant
              canonical = `${baseUrl}/p/${slug}/${gqlProduct.defaultVariant.id}`
            }
            context.dataSources.meta
              .registerProductPageView(context, new Date(), gqlProduct.defaultVariant)
              .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))
          }
        }

        return DatoPageProductTranformer.toGQL(datoProduct, context, {
          ...options,
          additionalComponents,
          category,
          crossSellProducts,
          type,
        })
      }
      default: {
        {
          type = 'cms'

          if (
            route.params.some((param) => param.name.includes('slug') && param.value.match(/\.[0-9a-z]+$/i)) || // if there is a point followed by letter/numbers (i.e file extension)
            route.params.some((param) => param.name.match(/slug\d/i)) // if there is more than one slug
          ) {
            throw new GraphQLApolloError('404: Not found', 404)
          }

          let slug = (
            route.name === 'cms' ? route.params.find((p) => p.name === 'slug')?.value || '404' : route.name
          ).toLowerCase()
          // Eventually pick up DatoCMS specific override
          slug = getPageSlugForContext(slug, context)

          page =
            (await context.loaders.datocms.landingPages.load({ locale: context.language, slug })) ||
            (await context.loaders.datocms.pages.load({ locale: context.language, slug }))

          context.dataSources.meta
            .registerGenericPageView(context, new Date(), type, slug)
            .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))

          if (!page && (fallback || wellKnownPages.has(slug))) {
            page = await context.loaders.datocms.landingPages.load({
              locale: context.language,
              slug: config.cms.wellKnownPageDefaultSlug,
            })

            if (!page) {
              return null
            }
          }

          if (myAccountChildSlugs.has(slug)) {
            options.parentPage = await context.loaders.datocms.landingPages.load({
              locale: context.language,
              slug: config.cms.myAccount.slug.toLowerCase(),
            })
          }

          return DatoPageTransformer.toGQL(page, context, {
            ...options,
            type,
          })
        }
      }
    }
  }

  /**
   * @param {GQLPage} page
   * @returns {Promise<GQLFestMode>}
   */
  getPageFest(page) {
    return page.festMode
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{ footerNumber: string }>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLMenu[]>}
   */
  async getPageFooters(page, _args, _context) {
    return page.footers
  }

  getPageAccessoryCategories() {
    return []
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {GQLGelluleType[]}
   */
  getPageGellules(page, _args, _context) {
    return page.gellules
  }

  /**
   * @param {GQLPage} page
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {GQLMenu[]}
   */
  async getPageMenu(page, _args, _context) {
    return page.menus
  }

  /**
   * @param {GQLRoute} route
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLRouteParamType[]>}
   */
  async getRouteParams(route, _args, context) {
    if (route.name === 'product') {
      const code = route.params.find((p) => p.name === 'code')?.value
      const variantId = route.params.find((p) => p.name === 'variant1')?.value
      if (code && variantId === undefined) {
        const product = await this._loadProductFromCT({ code, countryId: context.countryId }, context)
        if (product) {
          route.params.push({
            name: 'variant1',
            value: product.defaultVariant?.id,
          })
        }
      }
    }

    return route.params
  }

  // @todo
  /**
   * @param {{
   *  name: string,
   *  templates: GQLTemplate[]
   * }} componentZone
   * @returns {GQLTemplate[]}
   */
  getTemplates(componentZone) {
    return componentZone?.templates || []
  }

  /**
   *
   * @param {GQLTownPage} townPage
   * @returns {GQLTownPageTab[]}
   */
  getTownPageTabs(townPage) {
    return townPage.tabs
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ slugUrl: string }>} args
   * @param {*} context
   * @returns {Promise<GQLTownPage>}
   */
  async getTownProductInfoBySlug(_parent, args, context) {
    const { slugUrl } = args

    const apiFloristPage = await context.loaders.datocms.floristPage.load({
      locale: context.language,
      slug: slugUrl.split('/').pop(),
    })

    if (!apiFloristPage) {
      throw new GraphQLDatoError('Not found', slugUrl, 404)
    }

    return DatoFloristTownTransformer.toGQL(apiFloristPage, context)
  }
}

module.exports = CmsResolver
