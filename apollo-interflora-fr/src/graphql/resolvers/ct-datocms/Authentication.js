const apm = require('elastic-apm-node')

const config = require('../../../config')

const BaseResolver = require('./Base')

const { CTSessionTransformer, CTTokenTransformer, CTUserTransformer } = require('../../transformers/commerceTools')
const { GraphQLApolloError } = require('../../errors')

const { Session } = require('../../../plugins/session/session')
const { parseJwt } = require('../../../helpers/auth')

/** @typedef {import('../_typedef')} */
/** @typedef {import('../../types/_typedef')} */
/** @typedef {import('@commercetools/platform-sdk').Cart} Cart */
/** @typedef {import('@commercetools/platform-sdk').Customer} Customer */

class AuthenticationResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async anonymousLogin(_parent, _args, context) {
    if (context.session?.commerceTools) {
      const isAnonymous = !context.auth || context.auth.isAnonymous

      // already logged in - keep session and return new token
      const token = CTTokenTransformer.toGQL(
        {
          expires_in: config.interflora.accessTokenLifeTime,
          refresh_token_expires_in: isAnonymous ? undefined : 10 * 24 * 3600, // 30d - CT refresh tokens do not expire
          scope: '',
        },
        context
      )
      this._assertValidTokenSessionId(token.value)

      return token
    }

    const response = await context.dataSources.ctAuth.anonymousLogin()

    await this._setCtAuthContext(context, response)
    const token = CTTokenTransformer.toGQL(
      {
        ...response,
        expires_in: config.interflora.accessTokenLifeTime,
      },
      context
    )
    this._assertValidTokenSessionId(token.value)

    return token
  }

  checkEmailExists() {
    throw new Error('not implemented')
  }

  confirmUserEmail() {
    throw new Error('not implemented')
  }

  /**
   * Used for the fetch status of BankId in Sweden
   */
  fetchAuthStatus() {
    throw new Error('not implemented')
  }

  /**
   * @param {Token|undefined} token
   * @param {GraphQLContextArg<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser|undefined>}
   */
  async getTokenUser(token, _args, context) {
    if (token && token.user) {
      return token.user
    }

    if (context.auth?.isAnonymous || !context.session?.user?.email) {
      return
    }

    const customer = await context.loaders.commerceTools.customers.load(context.session.user.email)
    return CTUserTransformer.toGQL(customer, context)
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{
   *  password: string,
   *  username: string
   * }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLToken>}
   */
  async login(_parent, args, context) {
    const { username, password } = args
    const anonymousId = context.session?.commerceTools?.anonymousId

    const email = username?.toLowerCase()

    /** @type {GQLToken} */
    let gqlToken

    /** @type {Customer} */
    let ctCustomer

    try {
      /** @type {CTCustomerSignin} */
      const signinPayload = {
        anonymousCartSignInMode: 'UseAsNewActiveCustomerCart',
        anonymousId,
        email,
        password,
      }
      const { token, cart, customer, gqlToken: _gqlToken } = await this._customerSignin(signinPayload, context)
      ctCustomer = customer
      gqlToken = _gqlToken

      if (cart) {
        const promises = [
          context.loaders.commerceTools.carts.prime(cart.id, cart),
          context.loaders.commerceTools.cartsAsRead.prime(cart.id, cart),
        ]

        if (context.session.apiCart?.id && context.session.apiCart.id !== cart.id) {
          promises.push(context.loaders.commerceTools.carts.clear(context.session.apiCart.id))
          promises.push(context.loaders.commerceTools.cartsAsRead.clear(context.session.apiCart.id))

          context.session.apiCart = { id: cart.id }
          promises.push(context.session?.save?.())
        }
        await Promise.all(promises)
      } else if (context.session.apiCart?.id) {
        await this._clearSessionCart(context)
      }

      await this._setCtAuthContext(context, token)
    } catch (e) {
      apm.captureError(e, { custom: e })

      const message = context.appConfig.errors.login
      const error = new GraphQLApolloError(message, 401)
      error.addError('InvalidGrantError', 'username', message)
      error.addError('InvalidGrantError', 'password', '')
      throw error
    }

    /** @type {CTCustomer} */
    if (ctCustomer) {
      await context.loaders.commerceTools.customers.prime(ctCustomer.email, ctCustomer)
    } else {
      ctCustomer = await context.loaders.commerceTools.customers.load(username)
    }
    const user = CTUserTransformer.toGQL(ctCustomer, context)
    await this._setUserAuthContext(context, user, false)

    await this.onLoggedIn(user, context)

    this._assertValidTokenSessionId(gqlToken.value)

    return gqlToken
  }

  /**
   * post login
   *
   * @param {GQLUser} user
   * @param {GraphQLContext} context
   * @returns {Promise<undefined>}
   */
  async onLoggedIn(user, context) {
    let apiCart = await this._getApiCart(context)
    if (!apiCart) return

    if (!(await this._isInterfloraPlusAvailable(context))) return

    // if user cannot (re)subscribe to itf+, remove it from the cart is applicable
    if (!(await this._canOrderInterfloraPlus(apiCart?.customerEmail, context))) {
      const interfloraplusLineItem = apiCart.lineItems.find(
        (lineItem) => lineItem.productKey === config.products.interfloraplus.key
      )
      if (interfloraplusLineItem) {
        apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
          actions: [
            {
              action: 'removeLineItem',
              lineItemId: interfloraplusLineItem.id,
            },
          ],
          version: apiCart.version,
        })
        await context.loaders.carts.prime(apiCart.id, apiCart)
      }
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{refreshToken: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ value: string, refreshLifetime: Number, refreshToken: string, lifetime: Number, createdAt: Number}>}
   */
  async refreshToken(_parent, args, context) {
    const { refreshToken: jwt } = args
    const { sessionId } = parseJwt(jwt)
    const session = await Session.get(context.server.redis, sessionId)

    context.log.info(
      `refreshToken - session from jwt ${sessionId} | session from context ${context?.session?.id} | session from redis ${session?.id}`
    )

    if (!session.commerceTools?.refreshToken?.value) return null

    if (context.session?.id !== session.id) {
      context.session = session
    }

    const refreshToken = session.commerceTools.refreshToken.value

    const response = await context.dataSources.ctAuth.refreshToken(refreshToken)
    context.session.commerceTools = CTSessionTransformer.fromResponse({
      ...response,
      refresh_token: refreshToken,
    })
    await context.session.save()

    let expiresIn = config.interflora.accessTokenLifeTime

    if (context.session?.external?.refreshToken) {
      await this.refreshExternalToken(context.session.external.refreshToken, context)
      expiresIn = context.session.external.expiresIn
    }

    const token = CTTokenTransformer.toGQL(
      {
        ...response,
        expires_in: expiresIn,
        login: session.user?.email,
        refresh_token: refreshToken,
        refresh_token_expires_in: 10 * 24 * 3600, // 30d - CT refresh tokens do not expire
      },
      context
    )
    this._assertValidTokenSessionId(token.value)

    return token
  }

  registerUser() {
    throw new Error('not implemented')
  }

  /**
   * Used for refreshing the external token
   */
  refreshExternalToken() {
    throw new Error('not implemented')
  }

  /**
   * Used for soft login, for Sweden campaigns
   */
  softLogin() {
    throw new Error('not implemented')
  }

  /**
   * Used to connect with a QrCode or a BankId app link Sweden
   */
  startAuthentication() {
    throw new Error('not implemented')
  }

  /**
   * Authenticates a customer
   * and returns a token and the most recently modified active Cart
   *
   * @param {CTCustomerSignin} signinPayload
   * @param {GraphQLContext} context
   * @returns {{token: CTToken, customer: Customer, cart: Cart, gqlToken: GQLToken}}
   */
  async _customerSignin({ anonymousCartSignInMode, anonymousId, email, password }, context) {
    const token = await context.dataSources.ctAuth.customerLogin({
      email,
      password,
    })
    const { customer, cart } = await context.dataSources.ctAuth.customerSignin({
      anonymousCartSignInMode,
      anonymousId,
      email,
      password,
    })

    const gqlToken = CTTokenTransformer.toGQL(
      {
        ...token,
        expires_in: config.interflora.accessTokenLifeTime,
        login: customer.email,
        refresh_token_expires_in: 10 * 24 * 3600, // 30d - CT refresh tokens do not expire
      },
      context
    )

    return {
      cart,
      customer,
      gqlToken,
      token,
    }
  }

  /**
   * Updates session following login
   *
   * @param {GraphQLContext} context
   * @param {GQLUser} user
   * @param {boolean} _legacy
   * @returns {Promise<void>}
   * @protected
   */
  async _setUserAuthContext(context, user, _legacy = false) {
    const userChanged = !context.auth.isAnonymous && context.auth.login !== user.username

    context.auth.isAnonymous = false
    context.auth.login = user.username

    if (context.session.apiCart && userChanged) {
      // was logged in and changed user - kill current cart
      await this._clearSessionCart(context)
    }

    context.session.user = { email: user.email, id: user.id }

    await context.session?.save?.()
  }
}

module.exports = AuthenticationResolver
