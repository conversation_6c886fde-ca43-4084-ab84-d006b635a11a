const _ = require('lodash')
const CONST = require('../../../const')

const { getFeatures } = require('../../../helpers/features')

class RootResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<Object>}
   */
  async getConfig(_parent, _args, context) {
    /** tmp fix - serve a cached version of features to all users */
    const features = await getFeatures(context.server.redis, 'all')

    return {
      features,
    }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ form: { placement: string, step: string, userCategory: string } }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLForm}>}
   */
  async getForm(_parent, args, context) {
    const apiForm = await context.dataSources.datocms.getFormDefinition(args.form, context.language)
    if (!apiForm) return null

    const _fields = _.invert(CONST.form.fields)

    return {
      ...apiForm,
      fieldsets: apiForm.fieldsets.map((fieldset) => ({
        ...fieldset,
        fields: fieldset.fieldsList.map((f) => (_fields[f] ? f : CONST.form.fields.unknonwn)),
      })),
    }
  }
}

module.exports = RootResolver
