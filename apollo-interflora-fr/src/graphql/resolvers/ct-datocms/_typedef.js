/** @typedef {import('../../datasources/_typedef')} */
/** @typedef {import('../../dataloaders/availability').loaderDefinitions} AvailabilityDataLoaders */
/** @typedef {import('../../dataloaders/commercetools/_typedef').CommerceToolsDataLoaders} CommerceToolsDataLoaders */
/** @typedef {import('../../dataloaders/datocms/_typedef').DatocmsDataLoaders} DatocmsDataLoaders */
/** @typedef {import('../../dataloaders/gfs').loaderDefinitions} GFSDataLoaders */

/** @typedef {import('./Address')} AddressResolver */
/** @typedef {import('./Authentication')} AuthenticationResolver */
/** @typedef {import('./Cart')} CartResolver */
/** @typedef {import('./Cms')} CmsResolver */
/** @typedef {import('./Contact')} ContactResolver */
/** @typedef {import('./ForgotPassword')} ForgotPasswordResolver */
/** @typedef {import('./Geo')} GeoResolver */
/** @typedef {import('./Newsletter')} NewsletterResolver */
/** @typedef {import('./Order')} OrderResolver */
/** @typedef {import('./Product')} ProductResolver */
/** @typedef {import('./Root')} RootResolver */
/** @typedef {import('./Tracking')} TrackingResolver */
/** @typedef {import('./User')} UserResolver */
/**
 * @typedef {{
 *  address: AddressResolver
 *  authentication: AuthenticationResolver
 *  cart: CartResolver
 *  cms: CmsResolver
 *  contact: ContactResolver
 *  forgotPassword: ForgotPasswordResolver
 *  geo: GeoResolver
 *  newsletter: NewsletterResolver
 *  order: OrderResolver
 *  tracking: TrackingResolver
 *  user: UserResolver
 * }} CTDatocmsResolvers
 */

/**
 * @typedef {GraphQLContext & {
 *  dataSources: DataSources
 *  loaders: {
 *    availability: AvailabilityDataLoaders
 *    commerceTools: CommerceToolsDataLoaders
 *    datocms: DatocmsDataLoaders
 *    gfs: GFSDataLoaders
 *  }
 *  resolvers: CTDatocmsResolvers
 * }} CTDatocmsContext
 */

/**
 * @typedef {{
 *  amount: number
 *  cardCode: string
 *  cardPin: string
 *  cartId?: string
 *  cardType?: string
 * }} BuyboxCartPaymentInfo
 */

/**
 * @typedef {{
 *  payerId: string
 *  token: string
 *  cartId?: string
 *  isS2S?: string
 * }} BuyboxOrderPaymentInfo
 */

/**
 * @typedef {{
 *  code: string,
 *  customizations: {
 *    name: string,
 *      value: string
 *  }[],
 *  imageUrl: string|null,
 *  isAccessory?: boolean,
 *  productId: string,
 *  quantity: number,
 *  variantId: [string]
 *  }} Entry
 */

/**
 * @typedef {{
 *  cartId: string
 *  sessionId?: string
 *  method: string
 *  type: string
 *  scheme: string
 *  status: string
 *  id: string
 *  amount: string
 *  details?: string
 * }} TransformPaymentInfo
 */

/**
 * @callback postPaymentCallback
 * @param {{cardType: string, mode: string, provider: string}} paymentInfo
 * @param {GraphQLContext} context
 */

/**
 * @callback pendingOrderCallback
 * @param {string} cartId
 * @param {GraphQLContext} context
 */

/**
 * @callback cancelledOrderCallback
 * @param {GraphQLContext} context
 */

/**
 * @callback errorOrderCallback
 * @param {GraphQLContext} context
 */

/**
 * @callback transformOrderCallback
 * @param {TransformPaymentInfo} paymentInfo
 * @param {boolean} isS2S
 * @param {number} waitForOrderDelay
 * @param {string} provider
 * @param {GraphQLContext} context
 */
