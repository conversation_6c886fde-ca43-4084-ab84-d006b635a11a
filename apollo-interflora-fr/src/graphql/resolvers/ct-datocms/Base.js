const { randomUUID } = require('crypto')
const _ = require('lodash')
const apm = require('elastic-apm-node')

const config = require('../../../config')
const CONST = require('../../../const')
const { GraphQLApolloError, GraphQLCartError, GraphQLDatoError } = require('../../errors')
const { Session } = require('../../../plugins/session')
const CtSessionTransformer = require('../../transformers/commerceTools/Session')

const { GQLProduct, GQLOrderList } = require('../../models')

const { CTProductTransformer } = require('../../transformers/commerceTools')
const { DatoProductTransformer } = require('../../transformers/datocms')
const ITUserUserTransformer = require('../../transformers/ituser/User')
const PhoneTransformer = require('../../transformers/Phone')

const { isUUID } = require('../../../helpers/string')
const { getInterfloraPlus } = require('../../../helpers/commercetools/interfloraplus')
const { variantIds, variantAccessories } = require('../../../helpers/commercetools')
const { getFeatureVariant } = require('../../../helpers/features')
const { priceChannelKeyFromSlug } = require('../../../helpers/sea')
const { parseJwt } = require('../../../helpers/auth')
const AccessorySettings = require('../../../helpers/dato/AccessorySettings')

/** @typedef {import('../_typedef')} */

/**
 * @class BaseResolver
 */
class BaseResolver {
  /**
   * @param {GQLProduct | {code: string}} product
   * @returns {Promise<string>}
   */
  async getAccessoryAddMode(product) {
    const accessoryTypes = product.accessoryTypes ?? []
    const addTypes = Object.values(CONST.product.accessoryAddTypes)

    for (const addType of addTypes) {
      if (accessoryTypes.includes(addType)) {
        return addType
      }
    }

    return CONST.product.accessoryAddTypes.standard
  }

  /**
   * @param {{
   *  amount: number,
   *  cardType: string,
   *  cart: CTCart,
   *  custom: object,
   *  details: object,
   *  mode: string,
   *  provider: string,
   *  state: string,
   *  transactionId: string,
   *  transactionType: string
   * }} input
   * @param {GraphQLContext} context
   * @return {Promise<CTCart>}
   */
  async _addPayment(input, context) {
    let cart = input.cart

    if (!cart) {
      this._assertHasCart(context)
      cart = await context.loaders.commerceTools.cartsAsRead.load(context.session.apiCart.id)
    }

    const interactions = []
    if (input.details) {
      interactions.push({
        fields: {
          serialized: typeof input.details === 'string' ? input.details : JSON.stringify(input.details),
        },
        type: { key: CONST.commerceTools.customTypes.paymentInterfaceInteraction },
      })
    }

    const paymentToTransactionStatesMap = {
      [CONST.commerceTools.paymentStates.balanceDue]: CONST.commerceTools.paymentTransactionStates.pending,
      [CONST.commerceTools.paymentStates.creditOwed]: CONST.commerceTools.paymentTransactionStates.pending,
      [CONST.commerceTools.paymentStates.failed]: CONST.commerceTools.paymentTransactionStates.failure,
      [CONST.commerceTools.paymentStates.paid]: CONST.commerceTools.paymentTransactionStates.success,
      [CONST.commerceTools.paymentStates.pending]: CONST.commerceTools.paymentTransactionStates.pending,
    }

    /** @type {CTPaymentDraft} */
    const draft = {
      amountPlanned: input?.amount ?? cart.taxedPrice.totalGross,
      interfaceId: input.transactionId,
      interfaceInteractions: interactions,
      paymentMethodInfo: {
        method: input.mode,
        paymentInterface: input.provider,
      },
      paymentStatus: {
        interfaceCode: input.state,
      },
      transactions: [
        {
          amount: input?.amount ?? cart.taxedPrice.totalGross,
          interactionId: input.transactionId,
          state: paymentToTransactionStatesMap[input.state],
          timestamp: new Date(),
          type: input.transactionType || 'Charge',
        },
      ],
    }

    /**
     *  Add the custom fields to the payment
     *  used to store payment-specific information for non-standard payment types
     *  like reservationId for gift cards, bonus or promotion
     *
     *  @type {GQLPaymentCustom}
     */
    if (input.custom && Object.values(CONST.reward.types).includes(input.custom.fields?.rewardType)) {
      draft.custom = input.custom
    }

    if (input.cardType)
      draft.paymentMethodInfo.name = {
        en: input.cardType,
      }

    if (
      (cart.paymentInfo?.payments || []).some((payment) => {
        if (!payment.paymentMethodInfo) {
          context.log.error('empty payment.paymentMethodInfo id for cart %s', cart.id)
        }
        return (
          payment.interfaceId === draft.interfaceId &&
          payment.paymentMethodInfo?.paymentInterface === draft.paymentMethodInfo.paymentInterface
        )
      })
    ) {
      context.log.info(
        `[addPayment] already existing "${draft.paymentMethodInfo.paymentInterface}/${draft.interfaceId}"`
      )
      return cart
    }

    context.log.info(`[addPayment] %O`, draft)

    let payment
    try {
      payment = await context.dataSources.carts.createPayment(draft)
    } catch (e) {
      // @todo: find a better way to handle this
      if (/^The interface id '([^']+)' was already used without setting a payment interface\.$/.test(e.message)) {
        context.log.error('duplicated payment transaction id: %s', input.transactionId)

        try {
          payment = await context.dataSources.carts.getPaymentByInterfaceId(
            draft.interfaceId,
            draft.paymentMethodInfo.paymentInterface
          )
          cart = await new Promise((resolve) => setTimeout(resolve, 500)).then(() =>
            context.loaders.commerceTools.cartsAsRead.load(cart.id)
          )

          context.log.info(`[addPayment] payment ${payment.id} retrieved`)
        } catch (error) {
          apm.captureError(error, { custom: error })
        }
      }

      if (!payment) throw e
    }

    if (!(cart.paymentInfo?.payments ?? []).some((p) => p.id === payment.id)) {
      const payload = {
        actions: [
          {
            action: 'addPayment',
            payment: {
              id: payment.id,
              typeId: 'payment',
            },
          },
        ],
        version: cart.version,
      }
      cart = await context.dataSources.carts.updateCartAsAdmin(cart.id, payload)
    }

    await Promise.all([
      context.loaders.commerceTools.carts.prime(cart.id, cart),
      context.loaders.commerceTools.cartsAsRead.prime(cart.id, cart),
    ])

    return cart
  }

  /**
   * @param {GraphQLContext} context
   * @param {CTCart} apiCart
   * @throws {GraphQLCartError}
   */
  async _assertCartReady(context, apiCart) {
    const cart = apiCart || (await this._getApiCart(context))
    if (!cart) throw new GraphQLApolloError('No cart found', 404)

    if (cart.lineItems.length === 0 && cart.customLineItems.length === 0)
      throw new GraphQLCartError('empty-cart', 'Empty cart')

    // @todo: check with business what the rules are
    if (!cart.billingAddress) throw new GraphQLCartError('billing-adress', 'Missing billing address')
    if (!cart.billingAddress.email) throw new GraphQLCartError('billing-address-email', 'Missing billing address email')

    if (!cart.shippingAddress) throw new GraphQLCartError('shipping-address', 'Missing shipping address')
    if (!cart.shippingAddress.streetName)
      throw new GraphQLCartError('shipping-address-address', 'Missing shipping address street')
    if (!cart.shippingAddress.city) throw new GraphQLCartError('shipping-address-city', 'Missing shipping address city')
    if (!cart.shippingAddress.custom.fields.date) throw new GraphQLCartError('delivery-date', 'Missing delivery date')

    return true
  }

  /**
   * @param {GraphQLContext} context
   * @returns {void}
   * @throws
   * @protected
   */
  _assertHasCart(context) {
    if (!context.session?.apiCart?.id) {
      if (context.session?.accessoriesRelations) {
        delete context.session.accessoriesRelations
        // do not wait for response
        context.session?.save?.().catch((e) => apm.captureError(e, { custom: e }))
      }
      throw new GraphQLApolloError('Cart not found', 400)
    }
  }

  /**
   * @param {GQLToken} token
   * @returns {void}
   * @throws
   * @protected
   */
  _assertValidTokenSessionId(token) {
    const { sessionId } = parseJwt(token)
    if (sessionId === '00000000-0000-0000-0000-000000000000') {
      throw new GraphQLApolloError('Invalid session identifier in the token', 500)
    }
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<AxerveEncryptPayload>}
   */
  async _axerveEncryptPayload(context) {
    const cart = await this._getApiCart(context)
    this._assertHasCart(context)

    const amount = (cart.taxedPrice.totalGross.centAmount / 100).toFixed(2)
    const currency = cart.taxedPrice.totalGross.currencyCode
    let addressParts =
      `${cart.billingAddress?.streetName || ''} ${cart.billingAddress?.additionAddressInfo || ''}`
        .trim()
        .match(/.{1,50}/g) || []
    const billingAddress = {
      city: cart.billingAddress?.city,
      country: cart.billingAddress?.country,
      email: cart.billingAddress?.email,
      firstName: cart.billingAddress?.firstName,
      lastName: cart.billingAddress?.lastName,
      line1: addressParts[0],
      line2: addressParts[1],
      line3: addressParts[2],
      postCode: cart.billingAddress?.postalCode,
      state: cart.billingAddress?.region,
    }

    addressParts =
      `${cart.shippingAddress?.streetName || ''} ${cart.shippingAddress?.additionAddressInfo || ''}`
        .trim()
        .match(/.{1,50}/g) || []
    const shippingAddress = {
      city: cart.shippingAddress?.city,
      country: cart.shippingAddress?.country,
      email: cart.shippingAddress?.email,
      firstName: cart.shippingAddress?.firstName,
      lastName: cart.shippingAddress?.lastName,
      line1: addressParts[0],
      line2: addressParts[1],
      line3: addressParts[2],
      postCode: cart.shippingAddress?.postalCode,
      state: cart.shippingAddress?.region,
    }

    return {
      amount,
      billingAddress,
      currency,
      deliveryDate: cart.shippingAddress?.custom?.fields?.date
        ? new Date(cart.shippingAddress.custom.fields.date)
        : undefined,
      id: cart.id,
      language: context.language,
      phone: cart.billingAddress?.phone ? PhoneTransformer.toGQL(cart.billingAddress.phone) : undefined,
      shippingAddress,
    }
  }

  /**
   * @param {string} email
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _canOrderInterfloraPlus(email, context) {
    if (!(await this._isInterfloraPlusAvailable(context))) return false

    const interfloraPlus = await getInterfloraPlus(email, context)
    // if the user is ITF+ and his subscription does not expire within the next month, do not suggest to renew
    return interfloraPlus.isAboutToExpire()
  }

  /**
   * @param {GraphQLContext} context
   * @returns {string}
   */
  _getContextCartId(context) {
    const {
      session: { apiCart },
    } = context

    return apiCart?.id
  }

  /**
   * Mutates context to match login response and cart init or update
   *
   * @param {GraphQLContext} context
   * @param {CTToken} response
   * @param {string} [sessionId]
   * @returns {Promise<void>}
   * @protected
   */
  async _setCtAuthContext(context, response, sessionId) {
    const currentSessionId = context.auth?.sessionId || sessionId
    const login = response.login || CONST.user.anonymousLogin

    context.log.info(`_setCtAuthContext - before setting session ${currentSessionId}`)

    context.auth = {
      isAnonymous: login === CONST.user.anonymousLogin,
      login,
      sessionId: currentSessionId || randomUUID(),
    }

    context.log.info(`_setCtAuthContext - setting session ${context.auth.sessionId}`)
    context.session = await Session.get(context.server.redis, context.auth.sessionId, {
      expiresAt: Math.ceil(Date.now() / 1000) + config.session.lifetime,
    })

    context.session.commerceTools = CtSessionTransformer.fromResponse(response)

    await context.session?.save?.()

    context.log.info(`_setCtAuthContext - after saving the setting session ${context.auth.sessionId}`)
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<undefined>}
   */
  async _clearSessionCart(context) {
    if (!context.session) return

    const cartId = context.session.apiCart?.id

    delete context.session.accessoriesRelations
    delete context.session.apiCart

    const promises = [context.session?.save?.()]
    if (cartId) {
      promises.push(context.loaders.commerceTools.carts.clear(cartId))
      promises.push(context.loaders.commerceTools.cartsAsRead.clear(cartId))
    }

    return Promise.all(promises)
  }

  /**
   * @param {string} cartId
   * @param {GraphQLContext} context
   * @returns {Promise<undefined>}
   */
  async _clearCartLoaders(cartId, context) {
    return Promise.all([
      context.loaders.commerceTools.carts.clear(cartId),
      context.loaders.commerceTools.cartsAsRead.clear(cartId),
    ])
  }

  /**
   * @param {GraphQLContext} context
   * @return {Promise<CTCart>}
   */
  async _getApiCart(context) {
    if (!context.session?.apiCart?.id) {
      return null
    }
    if (!context.auth?.sessionId) {
      const error = new GraphQLApolloError(`cart id in session but no session id`, 500)
      apm.captureError(error, { custom: error })
      return null
    }

    /** @type {CTCart} */
    let apiCart
    try {
      apiCart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)
    } catch (error) {
      const code = error.statusCode || error.extensions?.code
      if ([400, 404].includes(code)) {
        // may come from another session, i.e. abandonned cart
        try {
          apiCart = await context.loaders.commerceTools.cartsAsRead.load(context.session.apiCart.id)
        } catch (e) {
          apm.captureError(e, { custom: e })
        }

        if (!apiCart) {
          // no cart found or invalid state - clear session
          await this._clearSessionCart(context)
          return null
        }
      } else {
        throw error
      }
    }

    if (!apiCart) {
      return null
    }

    if (!context.session.commerceTools?.accessToken) {
      const response = await context.dataSources.ctAuth.anonymousLogin()
      context.session.commerceTools = CtSessionTransformer.fromResponse(response)
    }

    try {
      const ctUserId = context.session.user?.id

      if (!ctUserId && apiCart.anonymousId !== context.session.commerceTools.anonymousId) {
        context.log.info(
          `restoring cart ownership (anonymous): [${apiCart.id}] ${apiCart.anonymousId} => ${context.session.commerceTools.anonymousId}`
        )
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: [
            {
              action: 'setAnonymousId',
              anonymousId: context.session.commerceTools.anonymousId,
            },
          ],
          version: apiCart.version,
        })
      } else if (config.commerceTools.hasUsers && ctUserId && ctUserId !== apiCart.customerId) {
        context.log.info(`restoring cart ownership (logged): [${apiCart.id}] ${apiCart.customerId} => ${ctUserId}`)
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: [
            {
              action: 'setCustomerId',
              customerId: context.session.user.id,
            },
          ],
        })
      }

      await Promise.all([context.loaders.commerceTools.carts.prime(apiCart.id, apiCart), context.session?.save?.()])
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    if (apiCart?.cartState !== CONST.commerceTools.cartStates.active) {
      await this._clearSessionCart(context)
      return null
    }

    return apiCart
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<GQLUser | undefined>}
   * @protected
   */
  async _getCurrentUser(context) {
    if (context.auth && context.auth.isAnonymous) {
      return
    }

    if (!context.session?.itUser) {
      return
    }

    const apiUser = await context.loaders.itUser.userById.load(context.session.itUser.userId)

    return ITUserUserTransformer.toGQL(apiUser, context)
  }

  /**
   * @param {string[]} accessoryCategoryIds
   * @param {string} countryId
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct[]>}
   */
  async _getAccessoriesByCategoryIds(accessoryCategoryIds, countryId, context) {
    const accessories = []
    for (const accessoryList of await context.loaders.commerceTools.productLists.loadMany(
      accessoryCategoryIds.map((id) => ({
        id,
        opts: {
          filters: [],
          limit: 99,
        },
      }))
    )) {
      if (accessoryList?.results?.length) accessories.push(...accessoryList.results)
    }

    const defaultSettings = AccessorySettings.merge(
      await Promise.all(
        accessoryCategoryIds.map((id) => context.loaders.datocms.accessoryCategoryDefaultSettings.load(id))
      )
    )

    return accessories.map((accessory) =>
      CTProductTransformer.toGQL(accessory, context, {
        accessoryTypes: defaultSettings[accessory.id]?.types ?? [],
        countryId,
        isAccessory: true,
        mainAssetId: defaultSettings[accessory.id]?.assetId ?? null,
        position: defaultSettings[accessory.id]?.position ?? -1,
      })
    )
  }

  /**
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _isInterfloraPlusAvailable(context) {
    if (!config.products?.interfloraplus?.key) return false

    const interfloraplus = await context.loaders.commerceTools.products.load({
      code: config.products.interfloraplus.key,
    })
    return Boolean(interfloraplus)
  }

  /**
   * @param {GQLProduct[]} cards
   * @param {GraphQLContext} context
   * @returns {Promise<GQLProduct[]>}
   */
  async _filterMessageCards(cards, context) {
    if (['ES', 'PT'].includes(context.countryId)) {
      const { payload: { value: rule } = { value: null } } = await getFeatureVariant(
        CONST.unleash.features.AB_PAID_MESSAGE_CARD,
        {
          sessionId: context.clientSessionId,
        }
      )
      if (rule) {
        const ruleJson = JSON.parse(rule)
        const included = ruleJson?.included ?? []
        const excluded = ruleJson?.excluded ?? []
        // check that all the cards to be included and excluded are present to apply the AB test
        const isEligibleAbTest = cards
          .reduce((a, c) => [included.includes(c.key) || a[0], excluded.includes(c.key) || a[1]], [false, false])
          .every((v) => v === true)
        if (isEligibleAbTest) {
          return cards.filter((card) => !excluded.includes(card.key))
        }
      }
    }
    return cards
  }

  /**
   * @param {GQLProductVariant | { code: string }} variant
   * @param {CTDatocmsContext} context
   * @return {Promise<CTProduct>}
   */
  async _loadCTProductFromVariant(variant, context) {
    const { channel, productId } = variantIds(variant.code)

    let ctProduct = await context.loaders.commerceTools.products.load({
      channel,
      code: productId,
    })

    if (!ctProduct && channel && channel !== config.commerceTools.priceChannelKey) {
      context.log.info(
        `[_loadCTProductFromVariant] product ${productId} not found for channel ${channel} --> fallbacking`
      )
      ctProduct = await context.loaders.commerceTools.products.load({
        code: productId,
      })
    }

    if (!ctProduct && variant.sku) {
      // id not found - product may have been deleted and created again - can happen on order history
      context.log.info(
        `[_loadCTProductFromVariant] product ${productId} not found --> fallbacking to sku ${variant.sku}`
      )
      ctProduct = await context.loaders.commerceTools.productsByVariantKey.load({
        channel,
        key: variant.sku,
      })
    }

    return ctProduct
  }

  /**
   * @param {{
   *  code: string,
   *  isAccessory: boolean,
   *  seaKey: [string],
   *  slug: string,
   * }} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<DatoProduct>}
   */
  async _loadDatoProduct(args, context) {
    const { code: inputCode, seaKey, slug } = args

    let code // is the actual value set in datocms to ref the product
    let product

    // if slug is given, get code from it
    if (slug) {
      try {
        code = await context.loaders.datocms.productCodesBySlug.load({
          locale: context.language,
          seaKey,
          slug,
        })
      } catch (e) {
        // do not log
      }
      if (!code && args.seaKey) {
        const slugSuffix = config.sea[args.seaKey].slugSuffix
        if (slug.endsWith(slugSuffix)) {
          context.log.info(
            `[_loadProductFromDato] slug ${slug} not found on sea channel ${args.seaKey} - trying without prefix`
          )
          code = await context.loaders.datocms.productCodesBySlug.load({
            locale: context.language,
            seaKey,
            slug: slug.replace(new RegExp(`${slugSuffix}$`), ''),
          })
        }
      }
    }

    // else, try to get it from cache
    if (!code && inputCode) {
      try {
        code = await context.loaders.datocms.productCodesByIdOrKey.load({
          code: inputCode,
          locale: context.language,
          seaKey,
        })
      } catch (e) {
        // do not throw
      }
    }

    if (!code && !inputCode) throw new GraphQLDatoError('Not found', 'none', 404)

    if (code) {
      try {
        product = await context.loaders.datocms.products.load({
          code,
          locale: context.language,
          seaKey,
        })
      } catch (e) {
        apm.captureError(e, { custom: e })
        throw new GraphQLDatoError('Not found', code, 404)
      }
    }

    // first try with what we were given
    if (!product) {
      try {
        product = await context.loaders.datocms.products.load({ code: inputCode, locale: context.language, seaKey })
        code = product.commercetoolsProduct
      } catch (e) {
        // do not throw
      }
    }

    // then, try to get the proper ref from commerce tools
    if (!product) {
      let channel
      if (seaKey) {
        channel = config.sea[seaKey].priceChannelKey
      }
      const ctProduct = isUUID(inputCode)
        ? await context.loaders.commerceTools.products.load({ channel, code: inputCode })
        : await context.loaders.commerceTools.productsByKey.load({ channel, key: inputCode })
      if (!ctProduct) throw new GraphQLDatoError('Not found', `ct:${inputCode}`, 404)

      code = isUUID(inputCode) ? ctProduct.key : ctProduct.id

      try {
        product = await context.loaders.datocms.products.load({ code, locale: context.language, seaKey })
      } catch (e) {
        apm.captureError(e, { custom: e })
        throw new GraphQLDatoError('Not found', `ref:${code}`, 404)
      }
    }

    if (product) {
      const primes = [
        context.loaders.datocms.productCodesByIdOrKey.prime(
          { code: inputCode, locale: context.language },
          product.commercetoolsProduct
        ),
      ]
      if (code && code !== inputCode)
        primes.push(
          context.loaders.datocms.productCodesByIdOrKey.prime(
            { code, locale: context.language },
            product.commercetoolsProduct
          )
        )
      await Promise.all(primes)

      return product
    }

    if (args.seaKey) {
      context.log.info(
        `[_loadProductFromDato] product ${code} not found on sea channel ${args.seaKey} - fallbacking to no channel`
      )
      const { seaKey: _, ...newArgs } = args
      return this._loadDatoProduct(
        {
          ...newArgs,
          slug: slug.replace(new RegExp(`${config.sea[seaKey].slugSuffix}$`, '')),
        },
        context
      )
    }

    throw new GraphQLDatoError('Not found', 'unhandled', 404)
  }

  /**
   * @param {GQLProduct | { code: string }} product
   * @param {GraphQLContext} context
   * @returns {Promise<{ accessories: GQLProduct[], cards: GQLProduct[], card: GQLProduct }>}
   */
  async _loadProductAccessories(product, context) {
    if (!(product instanceof GQLProduct)) {
      const _product = await this._loadProductFromDato(
        {
          code: product.code,
          isAccessory: false,
        },
        context
      )
      return this._loadProductAccessories(_product, context)
    }

    if (!product.isLoaded(CONST.dataSources.datocms)) {
      const _product = await this._loadProductFromDato(
        {
          code: product.code,
          isAccessory: false,
        },
        context
      )
      product.merge(_product)
    }

    const result = {
      accessories: [],
      card: undefined,
      cards: [],
    }

    if (!product.mainCategoryId && !product.categories?.length) return result

    if (!product.isLoaded(CONST.dataSources.commerceTools)) {
      const _product = await this._loadProductFromCT(
        { channel: priceChannelKeyFromSlug(product.slugUrl), code: product.code },
        context
      )
      product.merge(_product)
    }

    const accessoryCategoryIds = new Set()

    for (const category of product.categories ?? []) {
      accessoryCategoryIds.add(category.id)
    }

    if (!accessoryCategoryIds.size) return result
    const accessories = await this._getAccessoriesByCategoryIds([...accessoryCategoryIds], product.countryId, context)

    // split cards from accessories
    {
      for (const accessory of accessories) {
        if (accessory.isCardMessage) {
          result.cards.push(accessory)
        } else {
          result.accessories.push(accessory)
        }
      }
    }

    product.accessories = result.accessories
    product.cards = result.cards
    product.card = result.card

    const filteredMessageCards = await this._filterMessageCards(result.cards, context)
    return {
      accessories: result.accessories,
      card: filteredMessageCards?.find((card) => card.code === result.card?.code) ?? result.card,
      cards: filteredMessageCards,
    }
  }

  /**
   * @param {{
   *  channel: [string],
   *  code: string,
   *  countryId: string,
   *  isAccessory: boolean,
   * }} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLProduct>}
   */
  async _loadProductFromCT(args, context) {
    let ctProduct = await context.loaders.commerceTools.products.load({
      channel: args.channel,
      code: args.code,
    })
    if (!ctProduct && args.channel) {
      context.log.info(
        `[_loadProductFromCT] product ${args.code} not found for channel ${args.channel} --> fallback to no channel`
      )
      ctProduct = await context.loaders.commerceTools.products.load({
        code: args.code,
      })
    }

    if (!ctProduct) return null

    let linkedProducts = null
    const linkedProductAttribute = ctProduct.masterVariant?.attributes?.find(
      (attr) => attr.name === 'linked_products'
    )?.value

    if (linkedProductAttribute) {
      linkedProducts = (
        await Promise.allSettled(
          linkedProductAttribute.map((product) =>
            context.loaders.commerceTools.products.load({
              channel: args.channel,
              code: product.id,
            })
          )
        )
      )
        .filter((promise) => promise.status === 'fulfilled' && promise.value)
        .map((promise) => promise.value)
    }

    const product = CTProductTransformer.toGQL(ctProduct, context, {
      countryId: args.countryId,
      isAccessory: args.isAccessory,
      linkedProducts,
      priceChannelKey: args.channel,
    })

    if (args.countryId) {
      for (const variant of product.variants ?? []) {
        variant.countryId = product.countryId
      }
    }

    return product
  }

  /**
   * @param {{
   *  code: string,
   *  isAccessory: boolean,
   *  seaKey: [string],
   *  slug: string,
   * }} args
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLProduct>}
   */
  async _loadProductFromDato(args, context) {
    const datoProduct = await this._loadDatoProduct(args, context)
    return DatoProductTransformer.toGQL(datoProduct, context, {
      isAccessory: args.isAccessory,
      seaKey: args.seaKey,
    })
  }

  /**
   * @param {{
   *  code: string
   * }} args
   * @param {GraphQLContext} context
   * @returns {Promise<string>}
   */
  async _loadProductType(args, context) {
    const { code } = args

    const ctProduct = await context.loaders.commerceTools.products.load({ code })
    if (!ctProduct) {
      context.log.warn({ code }, 'could not load product type for product')
      return CONST.product.types.interflora
    }
    const ctProductType = await context.loaders.commerceTools.productTypes.load(ctProduct.productType.id)
    if (ctProduct.key === config.products.interfloraplus.key) {
      return CONST.product.types.service
    } else if (ctProduct.key === config.products.tombDelivery.key) {
      return CONST.product.types.mourning
    } else if (ctProductType.key === CONST.commerceTools.productTypes.bundle) {
      const bundleProductType = ctProduct.masterVariant.attributes.find((attr) => attr.name === 'product_type')?.value
        .key
      // TODO: to complete the mapping
      switch (bundleProductType) {
        case CONST.product.productType.MOURNING:
          return CONST.product.types.mourning
        default:
          return CONST.product.types.bundle
      }
    }

    const productType = CONST.product.types[CONST.commerceTools.productTypes[ctProductType.key]]
    if (!productType) {
      const err = new GraphQLApolloError(
        `Product type not found for key ${ctProductType.key} for product ${ctProduct.key}`
      )
      err.extensions.productType = ctProductType
      apm.captureError(err, { custom: err })
      return CONST.product.types.interflora
    }

    return productType
  }

  /**
   * @param {GQLProductVariant | { code: string }} variant
   * @param {GraphQLContext} context
   * @returns {Promise<{ accessories: GQLProduct[], cards: GQLProduct[] }>}
   */
  async _loadProductVariantAccessories(variant, context) {
    const { productId: variantCode, variantId } = variantIds(variant.code)
    if (variantId === undefined) return this._loadProductAccessories(variant.parent ?? { code: variantCode }, context)

    const result = {
      accessories: [],
      cards: [],
    }

    const ctProduct = await this._loadCTProductFromVariant(variant, context)
    const accessories = (await variantAccessories(ctProduct, variant, context)).map((accessory) =>
      CTProductTransformer.toGQL(accessory.ctProduct, context, {
        accessoryTypes: accessory.types,
        countryId: variant.countryId,
        isAccessory: true,
        mainAssetId: accessory.mainAssetId,
        position: accessory.position,
      })
    )

    for (const accessory of accessories) {
      if (accessory.isCardMessage) {
        result.cards.push(accessory)
      } else {
        result.accessories.push(accessory)
      }
    }

    const filteredMessageCards = await this._filterMessageCards(result.cards, context)
    return {
      accessories: result.accessories,
      cards: filteredMessageCards,
    }
  }

  async _loadSessionByToken(token, context) {
    context.log.info(`_loadSessionByToken - setting session ${token.sessionId}`)
    const session = await Session.get(context.server.redis, token.sessionId, {
      expiresAt: Math.ceil(Date.now() / 1000) + config.session.lifetime,
    })
    context.session = session

    const { itUser } = session
    if (itUser) {
      context.auth = {
        isAnonymous: token.isAnonymous,
        login: itUser.username,
        sessionId: token.sessionId,
      }
    }

    return session
  }

  /**
   * Used for the ct bundle product type in the availability check
   * @param {CTCart} cart
   * @returns {(string|[])[]}
   */
  _getProductVariantCodes(cart) {
    return (
      cart?.lineItems
        ?.map(({ productKey, variant }) => {
          if (productKey && variant?.key) {
            return `${productKey}#${variant.key}`
          }
          return null
        })
        ?.filter(Boolean) ?? []
    )
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<GQLOrderList>}
   */
  async _getUserOrders(user, _args, _context) {
    if (this._isUserCustomerCare(user)) {
      const orderList = new GQLOrderList()
      orderList.total = 0
      return orderList
    }
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   * @returns {boolean}
   */
  _isUserCustomerCare(user) {
    return user.type === CONST.user.userType.customerCare
  }
}

module.exports = BaseResolver
