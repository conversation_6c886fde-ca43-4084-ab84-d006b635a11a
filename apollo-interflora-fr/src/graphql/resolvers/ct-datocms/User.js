const { getInterfloraPlus } = require('../../../helpers/commercetools/interfloraplus')
const OccasionReminderTransformer = require('../../transformers/occasionreminders/OccasionReminder')
const BaseResolver = require('./Base')

const { CTCountryTransformer } = require('../../transformers/commerceTools')
const { createHash } = require('node:crypto')
const { config } = require('node:process')
const CONST = require('../../../const')

class UserResolver extends BaseResolver {
  generateFiscalCode() {
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   * @return {GQLAddressListType}
   */
  getUserAddressList(user) {
    if (this._isUserCustomerCare(user)) {
      return {
        addresses: [],
        total: 0,
      }
    }
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {GQLCountry|undefined}
   */
  getUserCountry(user, _args, context) {
    if (this._isUserCustomerCare(user)) {
      return CTCountryTransformer.toGQL(context.appConfig.country, context)
    }

    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   */
  getUserLastOrder(user) {
    if (this._isUserCustomerCare(user)) {
      return
    }
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   * @param {GraphqlContextArgs<{pagination: GQLInputPagination, filter: GQLInputOrderFilter}>} args
   * @param {GraphQLContext} context
   * @returns {GQLOrderList}
   */
  getUserOrders(user, args, context) {
    return this._getUserOrders(user, args, context)
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @param {GraphQLResolveInfo} info
   * @returns {Promise}
   */
  async getUserProperty(user, _args, context, info) {
    if (info.fieldName === 'premium') {
      if (user.premium === undefined) {
        user.premium = (await getInterfloraPlus(user.email, context)).expirationDate
      }
      return user.premium
    }

    if (info.fieldName === 'id' && config.site === CONST.site.fr) {
      return createHash('sha256')
        .update(user.id)
        .digest('hex')
        .slice(0, 30)
        .replace(/^(.{8})(.{4})(.{3})(.{3})(.{12})/, '$1-$2-4$3-a$4-$5')
    }

    return user[info.fieldName]
  }

  async updatePassword() {
    throw new Error('Not implemented')
  }

  updateUser() {
    throw new Error('Not implemented')
  }

  /**
   * @param {GQLUser} user
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOccasionReminder[]>}
   */
  async getUserReminders(user, _args, context) {
    const reminders = await context.loaders.occasionReminders.remindersByUser.load(user.id)
    return reminders.map((r) => OccasionReminderTransformer.toGQL(r))
  }

  /**
   * @param {any} _parent
   * @param {GraphQLContextArgs<{reminder: APICreateOccasionReminder}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOccasionReminder>}
   */
  async createOccasionReminder(_parent, args, context) {
    const reminder = args.reminder
    const user = await this._getCurrentUser(context)

    const result = await context.dataSources.occasionReminders.createReminder(user.id, reminder)
    await context.loaders.occasionReminders.remindersByUser.clear(user.id)

    return OccasionReminderTransformer.toGQL(result)
  }

  /**
   * @param {any} _parent
   * @param {{ id: string, updates: Partial<GQLOccasionReminder> }} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLOccasionReminder>}
   */
  async updateOccasionReminder(_parent, args, context) {
    const id = args.id
    const updates = args.updates
    const result = await context.dataSources.occasionReminders.updateReminder(id, updates)

    const user = await this._getCurrentUser(context)
    await context.loaders.occasionReminders.remindersByUser.clear(user.id)

    return OccasionReminderTransformer.toGQL(result)
  }

  /**
   * @param {any} _parent
   * @param {{ id: string }} args
   * @param {GraphQLContext} context
   */
  async deleteOccasionReminder(_parent, args, context) {
    const id = args.id
    await context.dataSources.occasionReminders.deleteReminder(id)

    const user = await this._getCurrentUser(context)
    await context.loaders.occasionReminders.remindersByUser.clear(user.id)

    return true
  }
}

module.exports = UserResolver
