const CONST = require('../../../const')
const { consentTrackers, setConsent, getAllConsents, setTracking } = require('../../../helpers/tracking')
const BaseResolver = require('./Base')

class TrackingResolver extends BaseResolver {
  /**
   * @param {GQLCart} _parent
   * @param {GraphQLContextArgs<{consents: GQLTrackingProdiver[]}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async updateConsents(_parent, args, context) {
    if (!context.clientSessionId) {
      return false
    }

    await Promise.all(
      consentTrackers.map((provider) => setConsent(context, provider, args.consents.includes(provider)))
    )

    return true
  }

  /**
   * @param {GQLCart} _parent
   * @param {GraphQLContextArgs<{provider: GQLTrackingProdiver, data: Object}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async setTrackingData(_parent, args, context) {
    if (args.provider === CONST.tracking.provider.abandonedCart) {
      const { email, consent } = args.data
      return this._updateAbandonedCartEligibility(email, consent, context)
    }

    if (!context.clientSessionId) {
      return false
    }

    const consents = await getAllConsents(context)
    const promises = []
    const provider = CONST.tracking.provider

    if (args.provider === provider.meta && consents[provider.meta]) {
      promises.push(
        setTracking(context, provider.meta, {
          fbc: args.data.fbc || null,
          fbp: args.data.fbp,
        })
      )
    }

    if (args.provider === provider.bloomreach && consents[provider.bloomreach]) {
      promises.push(
        setTracking(context, provider.bloomreach, {
          uid: args.data.uid,
        })
      )
    }

    await Promise.all(promises)

    return true
  }

  /**
   * @param {string} email
   * @param {boolean} consent
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _updateAbandonedCartEligibility(email, consent, context) {
    let apiCart = await this._getApiCart(context)
    const actions = [
      // update the sender email
      {
        action: 'setCustomerEmail',
        email,
      },
      {
        action: 'setBillingAddress',
        address: {
          country: context.countryId,
          email,
        },
      },
      // update the consent flag in CT
      {
        action: 'setCustomField',
        name: 'isEligibleToAbandonCartEmail',
        value: consent,
      },
    ]
    apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
      actions,
      version: apiCart.version,
    })

    await Promise.all([
      context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
    ])
    return true
  }
}

module.exports = TrackingResolver
