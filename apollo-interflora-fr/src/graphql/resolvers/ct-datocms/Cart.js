/** @import {CartAddLineItemAction} from '@commercetools/platform-sdk' */
/** @typedef {import('../../_typedef')} */
/**
 * @typedef {Object} LineItemInput
 * @property {string} [action]
 * @property {GQLProductVariant} variant
 * @property {GQLInputCartEntry} entry
 * @property {GQLProduct} product
 * @property {string} productId
 * @property {string} productType
 * @property {number} variantId
 * @property {{ key: string }} [seaChannel]
 * @property {CTDatocmsContext} context
 */

/**
 * @typedef {Object} ShippingMethodMap
 * @property {CTShippingMethod[]} ab
 * @property {CTShippingMethod[]} flowersAtChoice
 * @property {CTShippingMethod[]} mourning
 * @property {CTShippingMethod[]} reduced
 * @property {CTShippingMethod[]} customShippingFee
 * @property {CTShippingMethod[]} warehouse
 */

const _ = require('lodash')
const apm = require('elastic-apm-node')
const debug = require('debug')('itf:cart:it')

const config = require('../../../config')
const CONST = require('../../../const')

const { GraphQLApolloError, GraphQLDiscountCtError } = require('../../errors')

const BaseResolver = require('./Base')

const { GQLPrice } = require('../../models')

const {
  AvailabilityDateRangeTransformer,
  AvailabilityDeliveryTimeRangeResultTransformer,
  AvailabilityTownTransformer,
} = require('../../transformers/availability')
const {
  CTAddressTransformer,
  CTCartTransformer,
  CTPriceTransformer,
  CTProductTransformer,
} = require('../../transformers/commerceTools')

const { addTZOffsetToDate, getCeremonyTimeRange } = require('../../../helpers/dates')
const { getAvailablePaymentMethods, getAbElasticityDeliveryFees } = require('../../../helpers/features')
const { checkCode } = require('../../../helpers/ITFiscaleCode')
const { stripHTMLRootTag } = require('../../../helpers/string')
const {
  variantIds,
  priceChannelKeyFromProduct,
  getDistributionChannels,
  variantAttribute,
  sanitizeUserString,
} = require('../../../helpers/commercetools')
const {
  isInterfloraPlus,
  hasActiveItfPlusSubscription,
  itfPlusDiscountIdFromCart,
} = require('../../../helpers/commercetools/interfloraplus')
const { InvalidDestinationTransformer } = require('../../transformers/availability')
const { keysToLowerCase } = require('../../../helpers/object')
const LegalText = require('../../../helpers/dato/LegalText')
const { seaKeyFromPriceChannelKey } = require('../../../helpers/sea')
const setLineItemCustomField = require('../../../helpers/commercetools/actions/setLineItemCustomField')

class CartResolver extends BaseResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ dicountCode: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLCart}>}
   */
  async addDiscountCodeToCart(_parent, args, context) {
    const discountCode = args.discountCode.trim()
    const generalErrorMessage = context.appConfig.discountCode.unknown.replace('{discountCode}', discountCode)

    let code
    try {
      code = await context.loaders.commerceTools.discountCodesByCode.load(discountCode)
    } catch (e) {
      apm.captureError(e, { custom: e })
    }

    if (!code) {
      throw new GraphQLDiscountCtError(
        generalErrorMessage,
        CONST.commerceTools.discountCodeState.notValid,
        CONST.commerceTools.discountCodeNonApplicableReason.doesNotExist
      )
    }

    const actions = [
      {
        action: 'addDiscountCode',
        code: discountCode,
      },
    ]

    /** @type {CTCart} */
    let apiCart = await this._getApiCart(context)
    this._assertHasCart(context)

    let error
    try {
      apiCart = await context.dataSources.carts.updateCart(context.session.apiCart.id, {
        actions,
        version: apiCart.version,
      })
    } catch (e) {
      const discountError = e.originalError?.body?.errors?.[0]
      if (!discountError) {
        throw e
      }
      throw new GraphQLDiscountCtError(discountError.message, discountError.code, discountError.reason)
    }

    const appliedCode = (apiCart.discountCodes || []).find((c) => c.discountCode.id === code.id)

    if (appliedCode.state !== CONST.commerceTools.discountCodeState.matchesCart) {
      apiCart = await context.dataSources.carts.updateCart(context.session.apiCart.id, {
        actions: [
          {
            action: 'removeDiscountCode',
            discountCode: appliedCode.discountCode,
          },
        ],
        version: apiCart.version,
      })
      const customMessage =
        appliedCode.state === CONST.commerceTools.discountCodeState.doesNotMatchCart
          ? code?.custom?.fields?.customErrorMessage?.[context.language]
          : ''
      error = new GraphQLDiscountCtError(generalErrorMessage, appliedCode.state, undefined, customMessage)
    }

    const previousCodes = (apiCart.discountCodes || []).filter((c) => c.discountCode.id !== code.id)
    if (previousCodes.length > 0) {
      apiCart = await context.dataSources.carts.updateCart(context.session.apiCart.id, {
        actions: previousCodes.map((c) => ({
          action: 'removeDiscountCode',
          discountCode: c.discountCode,
        })),
        version: apiCart.version,
      })
    }

    await Promise.all([
      context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
    ])
    if (error) {
      throw error
    }

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {GQLCart} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async canPremium(_parent, _args, context) {
    const apiCart = await this._getApiCart(context)
    return this._canOrderInterfloraPlus(apiCart?.customerEmail, context)
  }

  // @todo
  celebrationTexts() {
    return []
  }

  /**
   * @param {GQLCart} _cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<null|number>}
   */
  async ageVerification(_cart, _args, _context) {
    return null
  }
  /**
   * @param {GQLCart} _cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} _context
   * @returns {Promise<boolean>}
   */
  async ageVerified(_cart, _args, _context) {
    return false
  }

  /**
   * @param {GQLDelivery} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<{ max: string, min: string }[]>}
   */
  async getAvailableDateRanges(_parent, _args, context) {
    this._assertHasCart(context)

    /** @type {CTCart} */
    const cart = await this._getApiCart(context)
    const productVariantSku = this._getProductVariantCodes(cart)

    const opts = {
      city: cart.shippingAddress?.city,
      country: cart.shippingAddress?.country || context.countryId,
      postalCode: cart.shippingAddress?.postalCode,
      productVariantSku: productVariantSku.at(0),
      province: cart.shippingAddress?.region,
    }

    const responses = await Promise.allSettled(
      cart.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return context.loaders.availability.unavailabilities.load({
          ...opts,
          code: product.key,
        })
      })
    )

    if (responses.some((r) => r.status === 'rejected')) return []

    const unavailabilities = [...new Set(responses.map((r) => r.value).flat())]

    return AvailabilityDateRangeTransformer.toGQL(unavailabilities, context)
  }

  getAvailableNoAddress() {
    // no iflora on IT
    return false
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<void | GQLCart>}
   */
  async getCart(_parent, _args, context) {
    const apiCart = await this._getApiCart(context)
    if (!apiCart) return

    const accessories = apiCart.lineItems
      .filter((lineItem) => lineItem.custom?.fields?.isAccessoryFor)
      .map((lineItem) => ({ code: `${lineItem.productId}#${lineItem.variant.id}` }))
    const accessoryProductRelations = await this._getAccessoryProductRelationsFromApiCart(
      apiCart,
      context,
      accessories,
      {}
    )

    const distributionChannels = await getDistributionChannels(context)
    const itfplusDiscountId = await itfPlusDiscountIdFromCart(apiCart, context)

    return CTCartTransformer.toGQL(apiCart, context, {
      accessoryProductRelations,
      distributionChannels,
      itfplusDiscountId,
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   */
  async getDeliveryFeasibility(_parent, _args, context) {
    this._assertHasCart(context)

    /** @type {CTCart} */
    const cart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)

    const codes = []
    let isFuneral = false

    for (const product of await Promise.all(
      cart.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return product
      })
    )) {
      // TODO FAM-800 => Back should make the product with code 'ITFPLUS' exist and always available. Then delete this filter.
      if (product.key !== config.products.interfloraplus.key && product.key !== config.products.tombDelivery.key)
        codes.push(product.key)
      const productType = await this._loadProductType({ code: product.code }, context)
      if (productType === CONST.product.types.mourning) {
        isFuneral = true
      }
    }

    const response = await context.dataSources.availability.checkAvailability({
      city: cart.shippingAddress?.city,
      codes,
      country: cart.shippingAddress?.country || context.countryId,
      date: cart.shippingAddress?.custom?.fields?.date,
      hasRibbon: false, // @todo
      isFuneral,
      moment: cart.shippingAddress?.custom?.fields?.moment,
      orderId: cart.id,
      postalCode: cart.shippingAddress?.postalCode,
      productVariantSKUs: this._getProductVariantCodes(cart),
      province: cart.shippingAddress?.region,
      street: cart.shippingAddress?.streetName,
    })

    return {
      feasible: response.Result,
    }
  }

  /**
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{ date: Date }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ endHour: Number, endMinute: Number, startHour: Number, startMinute: Number }[]>}
   */
  async getCartDeliveryRangeFixHour(parent, args, context) {
    const cart = await this._getApiCart(context)
    if (!cart) return []

    // is available for mourning products only
    {
      const hasMourningProduct = await this._hasMourningProduct(cart, context)
      if (!hasMourningProduct) return []
    }

    // is requested date ok
    {
      const apiMoments = await this._getApiMoments(args.date, context)
      if (apiMoments.length === 0) return []
    }

    return getCeremonyTimeRange(args.date, context)
  }

  /**
   * @param {null} _parent
   * @param {GraphQLContextArgs<{date: Date}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLDeliveryTimeRangesResult>}
   */
  async getCartDeliveryTimeRanges(_parent, args, context) {
    this._assertHasCart(context)

    const apiMoments = await this._getApiMoments(args.date, context)
    return AvailabilityDeliveryTimeRangeResultTransformer.toGQL(apiMoments || [], context)
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getCartDiscountCodes(cart, _args, context) {
    const apiCart = await this._getApiCart(context)
    if (!apiCart) return []

    const ctDiscountCodes = await context.loaders.commerceTools.discounts.loadMany(
      (apiCart.discountCodes || [])
        .filter((d) => d.state === CONST.commerceTools.discountCodeState.matchesCart)
        .map((d) => d.discountCode.id)
    )

    return ctDiscountCodes.map((d) => d.code)
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getDeliveryModes(cart, _args, context) {
    const apiCart = await this._getApiCart(context)
    if (!apiCart?.lineItems?.[0]) return []

    const defaultDeliveryModes = [CONST.cart.deliveryMode.self, CONST.cart.deliveryMode.standard]
    const products = await Promise.all(
      apiCart?.lineItems.map(async (lineItem) => {
        const ctProduct = await context.loaders.commerceTools.products.load({ code: lineItem.productId })
        const ctProductType = await context.loaders.commerceTools.productTypes.load(ctProduct?.productType.id)
        return {
          attributeProductType: ctProduct.masterVariant.attributes.find((attr) => attr.name === 'product_type')?.value
            .key,
          bundledProductsIds:
            ctProduct.masterVariant.attributes
              .find((attr) => attr.name === 'bundled_products')
              ?.value?.map((v) => v.id) ?? [],
          productType: ctProductType.key,
        }
      })
    )

    const hasMourningProduct = products?.some(
      (product) =>
        CONST.product.types[CONST.commerceTools.productTypes[product.productType]] === CONST.product.types.mourning
    )
    const hasBundleProduct = products?.some(
      (product) =>
        CONST.product.types[CONST.commerceTools.productTypes[product.productType]] === CONST.product.types.bundle
    )

    const mourningDeliveryModes = [
      ...(context.appConfig.mourningDeliveryModes || Object.values(CONST.cart.mourningDeliveryModeMap)),
    ].filter((mode) => mode !== CONST.cart.mourningDeliveryModeMap.TOMB_BUNDLE)

    if (hasMourningProduct) {
      return mourningDeliveryModes
    } else if (hasBundleProduct) {
      const bundledProducts = await Promise.all(
        products
          .flatMap((product) => product.bundledProductsIds)
          .filter(Boolean)
          .map((id) => this._loadProductFromCT({ code: id }, context))
      )
      if (bundledProducts?.some((product) => product?._defaultVariant.sku === config.products.tombDelivery.key)) {
        return [CONST.cart.deliveryMode.tombBundle]
      } else if (products.some((product) => product.attributeProductType === CONST.product.productType.MOURNING)) {
        return mourningDeliveryModes
      } else {
        return defaultDeliveryModes
      }
    }

    return defaultDeliveryModes
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<string>}
   */
  async getDeliveryOptionInfo(cart, _args, context) {
    const apiCart = await this._getApiCart(context)
    const productTypes = (
      await Promise.all(
        apiCart.lineItems.map((lineItem) => {
          return context.loaders.datocms.products.load({ code: lineItem.productId })
        })
      )
    ).map((product) => {
      return product.productType
    })

    let cartType = CONST.cms.dato.productType.STANDARD
    if (productTypes.includes(CONST.cms.dato.productType.MOURNING)) {
      cartType = CONST.cms.dato.productType.MOURNING
    } else if (productTypes.includes(CONST.cms.dato.productType.PLANT)) {
      cartType = CONST.cms.dato.productType.PLANT
    }

    const legalText = await LegalText.load(cartType, context)

    return legalText.cartDeliveryPriceOption
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async getEmailHasAccountPremium(cart, _args, context) {
    const apiCart = await this._getApiCart(context)

    return hasActiveItfPlusSubscription(apiCart.customerEmail, context)
  }

  /**
   * @param {*} _parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @return {Promise<*>}
   */
  async getInvalidDestination(_parent, args, context) {
    const apiCart = await this._getApiCart(context)
    const validateRegion = await context.loaders.availability.validateRegionCart.load({
      cartId: apiCart.id,
      postalCode: args.postalCode,
    })

    // MS returns an object with keys starting with an upper case letter
    // Transform the keys so that they start with a lower case letter
    return InvalidDestinationTransformer.toGQL(keysToLowerCase(validateRegion), context, { cart: apiCart })
  }

  /**
   * @param {GQLDelivery} _delivery
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getMaxDeliveryDays(_delivery, args, context) {
    const ranges = await this.getAvailableDateRanges(_delivery, args, context)
    const lastDay = ranges
      .map(({ max }) => max)
      .sort((a, b) => a.getTime() - b.getTime())
      .pop()

    if (!lastDay) return

    // lastDay is a pseudo UTC representation of the date
    // as our now may not be the backend now, we switch to locale dates to compute the diff
    const localeLastDay = addTZOffsetToDate(lastDay, config.apps[context.siteId].timezone)
    const localeNow = addTZOffsetToDate(new Date(), config.apps[context.siteId].timezone)

    return Math.ceil((localeLastDay.getTime() - localeNow.getTime()) / 1000 / 3600 / 24)
  }

  /**
   * @param {GQLDelivery} _parent
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<null|Number>}
   */
  async getUseFixHour(_parent, _args, context) {
    this._assertHasCart(context)

    const cart = await this._getApiCart(context)

    let isFuneral = false

    for (const productType of await Promise.all(
      cart.lineItems.map(async (lineItem) => {
        const productType = await this._loadProductType({ code: lineItem.productId }, context)
        return productType
      })
    )) {
      if (productType === CONST.product.types.mourning) {
        isFuneral = true
      }
    }

    return isFuneral
  }

  // @todo
  getNextCelebrationDayText() {
    return null
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ mode: string, provider: string, args: Object}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<Object>}
   */
  async getPaymentSetup(_parent, args, context) {
    this._assertHasCart(context)
    await this._assertCartReady(context)

    const cart = await this._getApiCart(context)

    const amount = (cart.taxedPrice.totalGross.centAmount / 100).toFixed(2)
    const currency = cart.taxedPrice.totalGross.currencyCode

    const { mode } = args

    let provider
    let result = {}

    switch (mode) {
      case CONST.payment.paymentMode.AMAZON:
        provider = CONST.payment.provider.AMAZON
        result = await context.dataSources.amazonPay.renderButton(cart.id, amount, currency)
        break
      case CONST.payment.paymentMode.FRISBII:
        provider = CONST.payment.provider.FRISBII
        result = await context.dataSources.frisbiiCheckout.createChargeSession(cart, context)
        break
      case CONST.payment.paymentMode.CARD: {
        provider = args.provider
        if (provider === CONST.payment.provider.FRISBII) {
          provider = CONST.payment.provider.FRISBII
          result = await context.dataSources.frisbiiCheckout.createChargeSession(cart, context, mode)
        } else {
          if (!context.dataSources.axerve) return {}
          const response = await context.dataSources.axerve.encrypt(await this._axerveEncryptPayload(context))
          result = { response }
        }
        break
      }
      case CONST.payment.paymentMode.APPLEPAY: {
        provider = args.provider
        if (provider === CONST.payment.provider.FRISBII) {
          provider = CONST.payment.provider.FRISBII
          result = await context.dataSources.frisbiiCheckout.createChargeSession(cart, context, mode)
        } else {
          provider = CONST.payment.provider.APPLEPAY
          result = await context.dataSources.applePay.getPaymentSetup()
        }
        break
      }
      case CONST.payment.paymentMode.GOOGLEPAY: {
        provider = args.provider
        if (provider === CONST.payment.provider.FRISBII) {
          provider = CONST.payment.provider.FRISBII
          result = await context.dataSources.frisbiiCheckout.createChargeSession(cart, context, mode)
        }
        break
      }
      case CONST.payment.paymentMode.PAYPAL: {
        provider = CONST.payment.provider.PAYPAL

        if (cart.paymentInfo?.payments) {
          /** @type {CTPayment[]} */
          const payments = await context.loaders.commerceTools.payments.loadMany(
            cart.paymentInfo.payments.map((p) => p.id)
          )

          if (
            payments.some((payment) => payment.paymentStatus?.interfaceCode === CONST.commerceTools.paymentStates.paid)
          ) {
            let id = '404'
            try {
              const order = await context.dataSources.orders.getByCartId(cart.id)
              id = order.id
            } catch (e) {
              apm.captureError(e, { custom: e })
              context.log.error(`[getPaymentSetup] order not found for paid cart`)
            }

            const error = new GraphQLApolloError('Already paid', 400)
            error.addError('already-paid', id, 'Payment is already paid')

            throw error
          }
        }

        /** @type {PaypalCreateOrderResponse} */
        result = await context.dataSources.paypal.createOrder(cart)
        break
      }

      case CONST.payment.paymentMode.MOBILE_PAY: {
        provider = CONST.payment.provider.FRISBII
        result = await context.dataSources.frisbiiCheckout.createChargeSession(cart, context, mode)
        break
      }
    }

    this._addPayment(
      {
        cardType: mode,
        cart,
        details: result,
        mode,
        provider,
        state: CONST.commerceTools.paymentStates.pending,
        transactionId: `${cart.id}#${cart.version}-setup`,
      },
      context
    ).catch((e) => {
      apm.captureError(e, { custom: e })
    })

    return result
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async getSuggestedMessages(_parent, args, context) {
    if (!context.session?.apiCart) return []

    const cart = await this._getApiCart(context)
    if (!cart) return []

    const categoryId = args.context.categoryId || cart.custom?.fields?.occasionCode
    if (!categoryId) return []

    const slug = await context.loaders.datocms.categorySlugsById.load({ id: categoryId, locale: context.language })
    if (!slug) return []

    const datoCategory = await context.loaders.datocms.categories.load({ locale: context.language, slug })
    return (datoCategory.cardSuggestions || []).map((s) => stripHTMLRootTag(s.message))
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ countryId: string, search: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{ town: GQLTown, valid: boolean }[]>}
   */
  async getTownsForCart(_parent, args, context) {
    this._assertHasCart(context)

    const cart = await this._getApiCart(context)
    const { countryId, search } = args

    const responses = await Promise.all(
      cart.lineItems.map(async (lineItem) => {
        const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
        return context.loaders.availability.towns.load({
          code: product.key,
          country: countryId || context.countryId,
          search,
        })
      })
    )
    const places = _.intersectionWith(...responses, _.isEqual)
    return places
      .slice(0, context.appConfig.towns.max)
      .map((place) => ({ town: AvailabilityTownTransformer.toGQL(place, context), valid: true }))
  }

  async getUserAddressesForCart() {
    throw new Error('Not implemented')
  }

  async paymentMethods(_parent, _args, context) {
    const cart = await this._getApiCart(context)
    return await getAvailablePaymentMethods(cart, context)
  }

  /**
   * @param {GQLDelivery} delivery
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLPrice>}
   */
  async priceWithoutPremium(delivery, _args, context) {
    const cart = await this._getApiCart(context)

    let isInterfloraPlus = false

    let amount = cart.shippingInfo?.discountedPrice?.value?.centAmount ?? cart.shippingInfo?.price?.centAmount

    const discounts = await context.loaders.commerceTools.cartDiscounts.loadMany(
      (cart.shippingInfo?.discountedPrice?.includedDiscounts || []).map((discountRef) => discountRef.discount.id)
    )

    for (const [i, discount] of discounts.entries()) {
      if (discount.key?.startsWith('interfloraplus')) {
        isInterfloraPlus = true
        amount += cart.shippingInfo.discountedPrice.includedDiscounts[i].discountedAmount.centAmount
      }
    }
    if (!isInterfloraPlus) return delivery.price

    const price = new GQLPrice()
    price.currencyIso = delivery.price.currencyIso
    price.value = amount

    return price
  }

  /**
   * does nothing - kept for backward compatibility
   *
   * @param {undefined} parent
   * @param {GraphQLContextArgs<{ codeVariantAccesssory: string }>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async refuseSuggestedAccessoryToCart(parent, _args, context) {
    const apiCart = await this._getApiCart(context)
    if (!apiCart) return

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ dicountCode: string }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{GQLCart}>}
   */
  async removeDiscountCodeToCart(_parent, args, context) {
    const { discountCode } = args

    this._assertHasCart(context)

    /** @type {CTCart} */
    let apiCart = await this._getApiCart(context)
    let discountId

    {
      const codes = await Promise.all(
        (apiCart.discountCodes || []).map((d) =>
          context.loaders.commerceTools.discounts
            .load(d.discountCode.id)
            .then((code) => ({ code, id: d.discountCode.id }))
        )
      )
      discountId = codes.find((c) => c.code.code === discountCode)?.id
    }

    if (!discountId) {
      return CTCartTransformer.toGQL(apiCart, context, {
        distributionChannels: await getDistributionChannels(context),
        itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
      })
    }

    const actions = [
      {
        action: 'removeDiscountCode',
        discountCode: {
          id: discountId,
          typeId: 'discount-code',
        },
      },
    ]

    apiCart = await context.dataSources.carts.updateCart(context.session.apiCart.id, {
      actions,
      id: apiCart.id,
      version: apiCart.version,
    })
    await Promise.all([
      context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
    ])

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCartProductItemType[]>}
   */
  async suggestedAccessories(cart, _args, context) {
    this._assertHasCart(context)

    const countryId = cart.delivery?.address?.country?.id
    if (countryId && countryId !== context.appConfig.country) {
      return context.resolvers.gfs.cart.suggestedAccessories(cart, _args, context)
    }

    const checkedAccessoryIds = new Set(
      (cart.accessoryItems || []).map((accessoryItem) => variantIds(accessoryItem.variant.code).productId)
    )

    const items = []
    for (const productItem of cart.productItems) {
      const { accessories } = await this._loadProductVariantAccessories(
        { code: productItem.variant.code, sku: productItem.variant.sku },
        context
      )
      for (const accessory of accessories) {
        const { productId: accessoryId } = variantIds(accessory.code)
        if (checkedAccessoryIds.has(accessoryId)) continue

        checkedAccessoryIds.add(accessoryId)

        if (accessory.accessoryTypes.includes(CONST.product.accessoryAddTypes.suggest)) {
          /** @type {CTProduct} */
          const ctProduct = await context.loaders.commerceTools.products.load({ code: accessoryId })

          // is available?
          let isAvailable = false
          if (config.site === CONST.site.fr) {
            // FR doesn't have where and when + unavailabilities MS is not yet implemented for CTFR + FR suggested accessories should always be in stock.
            isAvailable = true
          } else if (cart.delivery?.date && (cart.delivery?.address?.postalCode || cart.delivery?.address?.province)) {
            try {
              const productVariantSku = this._getProductVariantCodes(cart)
              const payload = {
                city: cart.delivery.address.city || cart.delivery.address.town?.label,
                code: ctProduct.key,
                country: cart.delivery.address.country?.id || context.countryId,
                postalCode: cart.delivery.address.postalCode || cart.delivery.address.town?.postalCode,
                productVariantSku: productVariantSku.at(0),
                province: cart.delivery.address.province,
              }
              const unavailabilities = await context.loaders.availability.unavailabilities.load(payload)
              isAvailable = !unavailabilities.some((d) => d.substring(0, 10) === cart.delivery.date)
            } catch (err) {
              apm.captureError(err, { custom: err })
            }
          }
          if (!isAvailable) continue

          // prices
          const ctVariant = ctProduct.masterVariant
          let taxCategory
          if (ctProduct.taxCategory) {
            taxCategory = await context.loaders.commerceTools.taxCategories.load(ctProduct.taxCategory.id)
          } else {
            apm.captureError(new GraphQLApolloError(`[non blocking] no tax category on product ${ctProduct.key}`))
          }

          const priceEntry = ctVariant.price || ctVariant.prices[0]

          let discount = null
          if (priceEntry.discounted)
            discount = CTPriceTransformer.toGQL(priceEntry.discounted, context, { taxCategory })

          const price = CTPriceTransformer.toGQL(priceEntry, context, { taxCategory })

          items.push({
            discount,
            price,
            quantity: 1,
            removable: true,
            total: accessory.price,
            variant: accessory.defaultVariant,
          })
        }
      }
    }

    return items
  }

  /**
   * Get external user id from session
   *
   * @param {GraphQLContext} context
   * @returns {string|null}
   * @private
   */
  async _getExternalUserId(context) {
    const { session } = context
    const itUserId = session?.itUser?.legacy ? session?.itUser?.userId : null
    const externalUserId = session?.external?.user?.id ?? null

    return itUserId || externalUserId || session?.user?.email || null
  }

  /**
   * @param {GQLCart} cart
   * @param {GraphQLContextArgs<{}>} _args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCartProductItemType[]>}
   */
  async serviceItems(cart, _args, context) {
    const apiCart = await this._getApiCart(context)

    const hasDeliveryMethodSet = apiCart.custom.fields.hasDeliveryMethodSelected ?? false
    // if user already selected services, don't change it
    if (hasDeliveryMethodSet) {
      return cart.serviceItems
    }

    const alreadyHasItfPlus = await this.getEmailHasAccountPremium(cart, _args, context)
    if (alreadyHasItfPlus) {
      return []
    }

    const shippingCost = apiCart.shippingInfo.discountedPrice?.value.centAmount ?? apiCart.shippingInfo.price.centAmount
    const deliveryPriceThreshold = config?.products?.interfloraplus?.deliveryPriceThreshold ?? 0
    const isItfPlusSelectedByDefault = !!config?.products?.interfloraplus?.selectedByDefault
    const canOrderItfPlus = await this.canPremium(cart, _args, context)

    // if shipping costs below threshold or user coming from google shopping, don't add interflora+ subscription by default
    if (
      !isItfPlusSelectedByDefault ||
      shippingCost < deliveryPriceThreshold ||
      !canOrderItfPlus ||
      context.partner === CONST.context.partner.google
    ) {
      return []
    }

    /** @type {CTProduct} */
    const ctItfProduct = await context.loaders.commerceTools.productsByKey.load({
      key: config.products?.interfloraplus?.key,
    })

    const itfProduct = await CTProductTransformer.toGQL(ctItfProduct, context, {
      isAccessory: false,
      isCardMessage: false,
    })
    const priceEntry = ctItfProduct.masterVariant.price || ctItfProduct.masterVariant.prices[0]
    const price = CTPriceTransformer.toGQL(priceEntry, context)

    return [
      {
        price,
        quantity: 1,
        removable: true,
        total: price,
        variant: itfProduct.defaultVariant,
      },
    ]
  }

  /**
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} inputArgs
   * @param {CTDatocmsContext} context
   * @returns {Promise<{
   *  apiCart?: CTCart,
   *  args?: GraphQLContextArgs<{ cart: GQLInputCart }>
   * }>} - args are a possibly updated copy of inputArgs
   */
  async handleCartSwitching(inputArgs, context) {
    let apiCart = await this._getApiCart(context)
    const cartCountry = apiCart?.shippingAddress?.country
    const inputCountry = inputArgs.cart.delivery?.address?.countryId
    let targetCountry = inputCountry || context.countryId
    if (
      (apiCart?.lineItems?.length === 0 && apiCart?.customLineItems?.length === 0) || // empty cart
      (apiCart?.customLineItems?.length > 0 && context.countryId !== inputCountry) // gfs cart -> main country
    ) {
      targetCountry = context.countryId
    }

    const args = structuredClone(inputArgs)
    if (inputCountry && inputCountry !== targetCountry) {
      args.cart.delivery.address.countryId = targetCountry
    }

    if (cartCountry && cartCountry !== targetCountry) {
      if (
        apiCart.customLineItems.some((item) => inputArgs.cart.products.some((product) => product.code === item.slug))
      ) {
        const err = new GraphQLApolloError(`cannot mix international and local products`, 400)
        err.addError('mix-destination', 'cannot mix international and local products')
        throw err
      }

      context.log.info(`gfs cart found (${cartCountry}) and asked for ${targetCountry} > inspect cart contents`)
      // the cart is empty or its content is entirely replaced. We can allow silently switching
      apiCart = await this._resetCart(apiCart, context)
    }

    return { apiCart, args }
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} inputArgs
   * @param {CTDatocmsContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateCart(_parent, inputArgs, context) {
    const cartSwitching = await this.handleCartSwitching(inputArgs, context)
    let { apiCart } = cartSwitching
    const { args } = cartSwitching

    {
      const cartCountry = apiCart?.shippingAddress?.country
      if (
        cartCountry &&
        cartCountry !== context.appConfig.country &&
        cartCountry === args.cart.delivery?.address?.countryId
      ) {
        context.log.info(`gfs cart to update found (${cartCountry}) > calling gfs resolver`)
        return context.resolvers.gfs.cart.updateCart(...arguments)
      }
    }

    const { categoryId } = args.context
    const {
      cart: { accessories = [], delivery, joinMessage, products = [], services = [] },
    } = args

    const itUserId = context.session?.itUser?.legacy ? context.session?.itUser?.userId : null

    // business rules
    await this._checkBusinessRules(products, accessories, delivery, context, apiCart)

    // @todo: externalReference

    const accessoriesToAdd = []
    /** @type {Object<string, string>} lineItem.id => code */
    const accessoriesRelations = apiCart
      ? Object.fromEntries(
          apiCart.lineItems
            .filter((lineItem) => !!lineItem.custom?.fields?.isAccessoryFor)
            .map((lineItem) => [lineItem.id, lineItem.custom.fields.isAccessoryFor])
        )
      : {}

    const trackingItems = []
    const externalUserId = await this._getExternalUserId(context)

    if (apiCart) {
      // update cart ----------------------------------------------------------
      const { actions, adminActions } = await this._updateCartActionsOutsideProducts(args, context)

      if (adminActions.length > 0) {
        debug(`update cart (admin): %O`, adminActions)
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: adminActions,
          version: apiCart.version,
        })
        await Promise.all([
          context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
          context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
        ])
      }

      const entries = await this._updateCartSplitEntries(args, 'lineItems', context)
      const { entriesToAdd, entriesToDelete, entriesToUpdate } = entries

      await this._checkStocks([...entriesToAdd, ...entriesToUpdate], context)

      for (const entry of entries.accessoriesToAdd) {
        accessoriesToAdd.push(entry)
      }

      for (const entry of entriesToAdd) {
        const { channel } = variantIds(entry.code)
        const product = await this._loadProductFromCT({ channel, code: entry.productId }, context)
        const ctProduct = await context.loaders.commerceTools.products.load({ code: entry.productId })
        const productType = ctProduct.masterVariant?.attributes?.find((attr) => attr.name === 'product_type')?.value
          ?.key
        const variant = product.variants.find((v) => v.id === entry.variantId)
        trackingItems.push({
          customerPrice: entry.customerPrice ?? null,
          isAccessory: entries.accessoriesToAdd.some((x) => x.code === entry.code),
          isService: services.some((x) => x.code === entry.code),
          quantity: entry.quantity,
          variant: variant,
          variantId: entry.variantId,
        })

        const action = await this._buildLineItem({
          action: 'addLineItem',
          context,
          entry,
          product,
          productId: entry.productId,
          productType,
          variant,
          variantId: entry.variantId,
        })

        if (channel) {
          const ctChannel = await context.loaders.commerceTools.channelsByKey.load(channel)
          action.custom.fields.seaChannel = {
            id: ctChannel.id,
            typeId: 'channel',
          }
        }

        actions.push({
          ...action,
          asAdmin: productType === CONST.product.productType.ROUND_UP && !!action.externalPrice,
        })
      }

      if (externalUserId) {
        actions.push({
          action: 'setCustomField',
          name: 'userId',
          value: externalUserId,
        })
      }

      {
        const ids = new Set()
        const targetedProductIds = new Set([
          ...products.map((p) => {
            const { productId } = variantIds(p.code)
            return productId
          }),
        ])

        for (const entry of entriesToDelete) {
          if (!ids.has(entry.lineItemId)) {
            actions.push({
              action: 'removeLineItem',
              lineItemId: entry.lineItemId,
            })
          }
          ids.add(entry.lineItemId)

          // check if we had accessori(es) linked to remove to
          const lineItem = apiCart.lineItems.find((l) => l.id === entry.lineItemId)
          for (const [lineItemId, code] of Object.entries(accessoriesRelations)) {
            if (code === `${lineItem.productId}#${lineItem.variant.id}`) {
              if (targetedProductIds.has(lineItem.productId)) {
                // is a change of size - update variant
                const newProduct = products.reverse().find((entry) => {
                  const { productId } = variantIds(entry.code)
                  return productId === lineItem.productId
                })
                accessoriesRelations[lineItemId] = newProduct.code
              } else {
                const accessoryLine = apiCart.lineItems.find((l) => l.id === lineItemId)
                if (accessoryLine && !ids.has(lineItemId)) {
                  actions.push({
                    action: 'removeLineItem',
                    lineItemId,
                  })
                }
                ids.add(lineItemId)
                // do not delete accessory relation here ; wait for cart being updated to clean it up based on the result
              }
              ids.add(lineItemId)
              // do not delete accessory relation here ; wait for cart being updated to clean it up based on the result
            }
          }
        }
      }

      for (const entry of entriesToUpdate) {
        const ctProduct = await context.loaders.commerceTools.products.load({ code: entry.productId })
        const productType = ctProduct.masterVariant?.attributes?.find((attr) => attr.name === 'product_type')?.value
          ?.key
        if (entry.customerPrice && productType === CONST.product.productType.ROUND_UP) {
          actions.push({
            action: 'setLineItemPrice',
            asAdmin: true,
            externalPrice: {
              centAmount: entry.customerPrice,
              currencyCode: config.apps[context.siteId].currency,
            },
            lineItemId: entry.lineItemId,
          })
        }
        actions.push({
          action: 'changeLineItemQuantity',
          lineItemId: entry.lineItemId,
          quantity: entry.quantity,
        })
      }

      for (const lineItem of [...accessories, ...products]) {
        const currentCustomizations = JSON.stringify(lineItem.customizations ?? [])
        const cartLineItem = apiCart.lineItems.find((li) => `${li.productId}#${li.variant.id}` === lineItem.code)
        const cartCustomizations = cartLineItem?.custom?.fields?.customizations ?? '[]'
        if (!cartLineItem) {
          continue
        }

        if (cartCustomizations !== currentCustomizations) {
          actions.push({
            action: 'setLineItemCustomField',
            lineItemId: cartLineItem.id,
            name: 'customizations',
            value: currentCustomizations,
          })
        }

        const cartItemImageUrl = cartLineItem?.custom?.fields?.imageUrl
        const hasCustomImage = cartLineItem?.variant
          ? variantAttribute(cartLineItem.variant, 'product_type')?.key ===
            CONST.product.productType.DESIGN_CARD_UPLOADED
          : false
        if (hasCustomImage && lineItem.imageUrl !== cartItemImageUrl) {
          actions.push({
            action: 'setLineItemCustomField',
            lineItemId: cartLineItem.id,
            name: 'imageUrl',
            value: lineItem.imageUrl,
          })
        }
      }

      debug(`update cart: %O`, actions)
      const [updatedApiCart] = await Promise.all([
        this._updateAndPrimeCart(apiCart, actions, context),
        context.loaders.commerceTools.cartShippingMethods.clear(apiCart.id),
      ])
      apiCart = updatedApiCart

      // actions that need actual cart state after the update of items / customer
      {
        const actions = []

        actions.push(...(await this._updateDeliveryMode(delivery, context)))

        const interfloraPlus = await isInterfloraPlus(apiCart, context)
        if (apiCart.custom?.fields?.interfloraplus !== interfloraPlus) {
          actions.push({
            action: 'setCustomField',
            name: 'interfloraplus',
            value: interfloraPlus,
          })
        }
        debug(`update cart: %O`, actions)
        if (actions.length > 0) {
          apiCart = await this._updateAndPrimeCart(apiCart, actions, context)
        }
      }

      for (const trackingItem of trackingItems) {
        const tItemCart = apiCart.lineItems.find(
          (lineItem) =>
            lineItem.productId === trackingItem.variant.parent.code && lineItem.variant.id === trackingItem.variant.id
        )
        if (!tItemCart) {
          context.log.warn(
            `[update cart] Should not happen! line item not found from tracking: ${trackingItem.variant.code}`
          )
          continue
        }
        trackingItem.variant.price = new GQLPrice()
        trackingItem.variant.price.value = trackingItem.customerPrice ?? tItemCart.price.value.centAmount
        trackingItem.variant.price.currencyIso = tItemCart.price.value.currencyCode
        context.dataSources.meta
          .registerAddCart(
            context,
            new Date(),
            trackingItem.variant,
            trackingItem.quantity,
            trackingItem.isAccessory,
            trackingItem.isService
          )
          .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))
      }
    } else {
      // create cart ----------------------------------------------------------

      /** @type {CTCartDraft} */
      const draft = {
        country: config.apps[context.siteId].country,
        currency: config.apps[context.siteId].currency,
        custom: {
          fields: {
            device: [
              CONST.devices.desktop.toLocaleUpperCase(),
              CONST.devices.mobile.toUpperCase(),
              CONST.devices.appAndroid.toUpperCase(),
              CONST.devices.appIOS.toUpperCase(),
              CONST.devices.deprecatedAppAndroid.toUpperCase(),
              CONST.devices.deprecatedAppIOS.toUpperCase(),
            ].includes(context.device?.toUpperCase())
              ? context.device?.toUpperCase()
              : 'UNKNOWN',
            hasDeliveryMethodSelected: false,
            ip: context.ip,
            sessionId: context.auth?.sessionId,
          },
          type: { key: CONST.commerceTools.customTypes.cart },
        },
      }
      const adminActions = []

      draft.shippingAddress = CTAddressTransformer.toCommerceTools(delivery?.address || {}, context, { delivery })
      if (delivery?.sender) {
        draft.billingAddress = CTAddressTransformer.toCommerceTools(delivery.sender, context, { delivery })

        draft.custom.fields.invoiceRequest = delivery.sender.invoiceRequest
      }

      if (categoryId) {
        if (config.site === CONST.site.fr) {
          const occasionCodeCategory = await context.loaders.commerceTools.categories.load(categoryId)
          draft.custom.fields.occasionCode = occasionCodeCategory?.key || ''
        } else {
          draft.custom.fields.occasionCode = categoryId
        }
      }

      if (joinMessage) {
        draft.custom.fields.message = sanitizeUserString(joinMessage.message, true) || undefined
        draft.custom.fields.signature = sanitizeUserString(joinMessage.signature, false) || undefined
      }

      draft.custom.fields.userId = externalUserId || undefined

      if (delivery?.sender?.email) {
        draft.customerEmail = delivery.sender.email
      } else if (context.session?.user?.email) {
        draft.customerEmail = context.session.user.email
      } else if (itUserId) {
        const apiUser = await context.loaders.itUser.userById.load(context.session.itUser.userId)
        draft.customerEmail = apiUser?.email
      }
      // itf+
      if (draft.customerEmail) {
        draft.custom.fields.interfloraplus = await hasActiveItfPlusSubscription(draft.customerEmail, context)
      }

      for (const entry of accessories) {
        accessoriesToAdd.push(entry)
      }

      draft.lineItems = []
      await this._checkStocks([...accessories.map((entry) => ({ ...entry, isAccessory: true })), ...products], context)
      for (const entry of [...accessories, ...products, ...services]) {
        const { channel, productId, variantId } = variantIds(entry.code)
        const [ctProduct, product] = await Promise.all([
          context.loaders.commerceTools.products.load({ code: productId }),
          this._loadProductFromCT({ channel, code: productId }, context),
        ])
        const productType = ctProduct.masterVariant?.attributes?.find((attr) => attr.name === 'product_type')?.value
          ?.key
        const variant = product.variants.find((variant) => variant.id === variantId)
        trackingItems.push({
          customerPrice: entry.customerPrice ?? null,
          isAccessory: accessories.some((x) => x.code === entry.code),
          isService: services.some((x) => x.code === entry.code),
          quantity: entry.quantity,
          variant,
          variantId,
        })

        const lineItem = await this._buildLineItem({
          context,
          entry,
          product,
          productId,
          productType,
          seaChannel: channel ? { key: channel } : undefined,
          variant,
          variantId,
        })

        if (channel) {
          const ctChannel = await context.loaders.commerceTools.channelsByKey.load(channel)
          lineItem.custom.fields.seaChannel = {
            id: ctChannel.id,
            typeId: 'channel',
          }
        }
        if (entry.customerPrice && productType === CONST.product.productType.ROUND_UP) {
          lineItem.action = 'addLineItem'
          adminActions.push(lineItem)
        } else {
          draft.lineItems.push(lineItem)
        }
      }

      const hasMourningProduct = await this._hasMourningProduct(draft, context)
      if (hasMourningProduct && (await this._isTombDelivery(draft, context))) {
        draft.custom.fields.deliveryMode = CONST.cart.deliveryMode.tomb
      } else if (delivery?.mode) {
        draft.custom.fields.deliveryMode = delivery.mode
      } else if (!hasMourningProduct) {
        // defaults to standard only if the cart does not contain a mourning product
        draft.custom.fields.deliveryMode = CONST.cart.deliveryMode.standard
      }

      draft.store = {
        key: config.apps[context.siteId].commerceTools.storeKey,
        typeId: 'store',
      }
      debug('create cart %O', draft)

      apiCart = await context.dataSources.carts.createCart(draft)
      if (adminActions.length > 0) {
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: adminActions,
          version: apiCart.version,
        })
      }

      for (const trackingItem of trackingItems) {
        const tItemCart = apiCart.lineItems.find(
          (lineItem) =>
            lineItem.productId === trackingItem.variant.parent.code && lineItem.variant.id === trackingItem.variant.id
        )
        if (!tItemCart) {
          context.log.warn(
            `[update cart] Should not happen! line item not found from tracking: ${trackingItem.variant.code}`
          )
          continue
        }
        trackingItem.variant.price = new GQLPrice()
        trackingItem.variant.price.value = trackingItem.customerPrice ?? tItemCart.price.value.centAmount
        trackingItem.variant.price.currencyIso = tItemCart.price.value.currencyCode
        context.dataSources.meta
          .registerAddCart(
            context,
            new Date(),
            trackingItem.variant,
            trackingItem.quantity,
            trackingItem.isAccessory,
            trackingItem.isService
          )
          .catch((e) => apm.captureError(e, { custom: e, message: e.response?.error_user_msg }))
      }
    }

    let accessoryProductRelations = {}
    // refresh accessory => product links -------------------------------------
    {
      // link added accessories to first matching product, starting from latest
      accessoryProductRelations = await this._getAccessoryProductRelationsFromApiCart(
        apiCart,
        context,
        accessories,
        accessoriesRelations
      )
      Object.entries(accessoryProductRelations).forEach(([key, value]) => {
        accessoriesRelations[key] = value.code
      })
    }
    // set isAccessoryFor custom field
    if (Object.keys(accessoryProductRelations).length) {
      const actions = await Promise.all(
        Object.entries(accessoryProductRelations).map(async ([lineItemId, item]) => {
          const { productId, variantId } = variantIds(item.code)
          const product = await this._loadProductFromCT({ code: productId }, context)
          const variant = product.variants.find((v) => v.id === variantId)

          return {
            action: 'setLineItemCustomField',
            lineItemId,
            name: 'isAccessoryFor',
            value: `${product.key}#${variant.sku}`,
          }
        })
      )

      if (actions.length > 0) {
        apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
          actions,
          version: apiCart.version,
        })
      }
    }

    apiCart = await setLineItemCustomField.updateCustomText({
      accessories,
      apiCart,
      context,
      products,
    })

    // shipping costs
    {
      const actions = await this._updateShippingCostsActions(apiCart, context)
      if (actions.length > 0) {
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions,
          version: apiCart.version,
        })
      }
    }

    // updating the cart can invalidate discount codes - we need to explicitly remove them if so
    {
      const actions = []
      for (const discount of apiCart.discountCodes || []) {
        if (discount.state !== CONST.commerceTools.discountCodeState.matchesCart) {
          actions.push({
            action: 'removeDiscountCode',
            discountCode: discount.discountCode,
          })
        }
      }

      if (actions.length > 0) {
        apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
          actions,
          version: apiCart.version,
        })
      }
    }

    {
      const actions = await this._updateAdditionalCartInfo(apiCart, context)
      if (actions.length > 0) {
        apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
          actions,
          version: apiCart.version,
        })
      }
    }

    if (context.session.apiCart?.id !== apiCart.id) {
      context.log.info(`updateCart - set cart id in session ${apiCart.id}`)
      context.session.apiCart = { id: apiCart.id }
    }
    await Promise.all([
      context.session?.save?.(),
      context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
    ])

    return CTCartTransformer.toGQL(apiCart, context, {
      accessoryProductRelations: accessoryProductRelations,
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{ cartDelivery: GQLInputCartDelivery }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>}
   */
  async updateDeliveryCart(_parent, args, context) {
    const actions = await this._updateDeliveryActions(args.cartDelivery, context)

    let apiCart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)
    const countryId = apiCart?.shippingAddress?.country || context.countryId
    if (countryId !== context.appConfig.country && countryId !== context.countryId)
      return context.resolvers.gfs.cart.updateDeliveryCart(...arguments)

    if (actions.length) {
      apiCart = await context.dataSources.carts.updateCart(apiCart.id, {
        actions,
        version: apiCart.version++,
      })
      if (context.session.apiCart?.id !== apiCart.id) {
        context.log.info(`updateDeliveryCart - set cart id in session ${apiCart.id}`)
        context.session.apiCart = { countryId: apiCart.shippingAddress?.country, id: apiCart.id }
      }

      await Promise.all([
        context.session?.save?.(),
        context.loaders.commerceTools.carts.prime(apiCart.id, apiCart),
        context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, apiCart),
      ])
    }

    return CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })
  }

  /**
   * @param {LineItemInput} lineItemInput
   * @returns {Promise<CartAddLineItemAction>}
   * @protected
   */
  async _buildLineItem({ action, variant, entry, product, productId, productType, variantId, seaChannel, context }) {
    let imageUrl = variant?.images?.at(0)?.url
    if (productType === CONST.product.productType.DESIGN_CARD_UPLOADED && entry.imageUrl) {
      imageUrl = entry.imageUrl
    }
    return {
      action,
      custom: {
        fields: {
          colour: variant?.qualifiers?.find((qualifier) => qualifier.type.value === CONST.product.qualifierTypes.color)
            ?.value,
          customizations: JSON.stringify(entry.customizations ?? []),
          imageUrl,
          marketingFee: variant?.marketingFee ? CTPriceTransformer.toCT(variant.marketingFee) : undefined,
          seaChannel,
          weight: variant?.weight,
        },
        type: { key: CONST.commerceTools.customTypes.lineItem },
      },
      distributionChannel: {
        key: await priceChannelKeyFromProduct(product, context),
      },
      productId,
      quantity: entry.quantity,
      ...(entry.customerPrice && productType === CONST.product.productType.ROUND_UP
        ? {
            externalPrice: {
              centAmount: entry.customerPrice,
              currencyCode: config.apps[context.siteId].currency,
            },
          }
        : {}),
      variantId,
    }
  }

  /**
   *
   * @param {GQLCart} cart
   * @param {GraphQLContext<{token: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async verifyAge(cart, args, context) {
    const { token } = args
    let ageVerified = await this._verifyAgeTokenValidation(token, context)

    if (!ageVerified) {
      return false
    }

    if (context.session.apiCart?.id) {
      let apiCart = await context.loaders.commerceTools.carts.load(context.session.apiCart.id)
      if (apiCart) {
        apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
          actions: [
            {
              action: 'setCustomField',
              name: 'ageVerificationToken',
              value: token,
            },
          ],
          version: apiCart.version,
        })
        await context.loaders.commerceTools.carts.prime(apiCart.id, apiCart)
      }
    }

    return true
  }

  /**
   * @param {string} _token
   * @param {GraphQLContext} _context
   * @returns {Promise<boolean>}
   * @protected
   */
  async _verifyAgeTokenValidation(_token, _context) {
    return false
  }

  /**
   * @param {GQLInputCartEntry[]} products
   * @param {GQLInputCartEntry[]} accessories
   * @param {GQLInputCartDelivery} delivery
   * @param {GraphQLContext} context
   * @param {CTCart} apiCart
   * @returns {Promise<void>}
   */
  async _checkBusinessRules(products, accessories, delivery, context, apiCart) {
    await this._checkMixingProducts(products, context)
    await this._checkMinMaxQuantity(products, accessories, context)
    await this._checkAvailabilityCompatibility(products, accessories, delivery, context, apiCart)
    await this._checkRoundUpProducts(products, context)
  }

  /**
   * @param {any[]} products
   * @param {GraphQLContext} context
   * @returns {Promise<void>}
   */
  async _checkMixingProducts(products, context) {
    const carriers = new Set()
    const productClassifications = new Set()
    const productTypes = new Set()

    for (const entry of products) {
      if (entry.quantity === 0) continue
      const { productId, variantId } = variantIds(entry.code)
      const productType = await this._loadProductType({ code: productId }, context)
      productTypes.add(productType)

      const product = await this._loadProductFromCT({ code: productId }, context)
      const variant = product.variants.find((v) => v.id === variantId)

      productClassifications.add(product.classification)
      carriers.add(variant?.delivery?.type)
    }

    if (productTypes.has(CONST.product.types.mourning) && productTypes.size > 1) {
      const err = new GraphQLApolloError(`cannot mix mourning and non-mourning products`, 400)
      err.addError('mix-mourning', 'cannot mix mourning and non-mourning products')
      throw err
    }

    // plants + florist flowers are not allowed
    if (carriers.has(CONST.product.deliveryTypes.carrier) && carriers.has(CONST.product.deliveryTypes.florist)) {
      const err = new GraphQLApolloError(`cannot mix plants and flowers`, 400)
      err.addError('mix-plants-flowers', 'cannot mix plants and flowers')
      throw err
    }
  }

  /**
   * @param {any[]} products
   * @param {any[]} accessories
   * @param {GraphQLContext} context
   * @returns {Promise<void>}
   */
  async _checkMinMaxQuantity(products, accessories, context) {
    for (const entry of [...products, ...accessories]) {
      const { productId, variantId } = variantIds(entry.code)

      const product = await this._loadProductFromCT({ code: productId }, context)
      const variant = product.variants.find((v) => v.id === variantId)
      if (variant?.minQuantity && entry.quantity < variant?.minQuantity) {
        const err = new GraphQLApolloError(`minimal quantity of product is ${variant?.minQuantity}`)
        err.addError('min-quantity', `minimal quantity of product is ${variant?.minQuantity}`)
        throw err
      }

      if (variant?.maxQuantity && entry.quantity > variant?.maxQuantity) {
        const err = new GraphQLApolloError(`maximum quantity of product is ${variant?.maxQuantity}`)
        err.addError('max-quantity', `maximum quantity of product is ${variant?.maxQuantity}`)
        throw err
      }
    }
  }

  /**
   * @param {any[]} products
   * @param {any[]} accessories
   * @param {GQLInputCartDelivery} delivery
   * @param {GraphQLContext} context
   * @param {CTCart} apiCart
   *
   * @returns {Promise<void>}
   */
  async _checkAvailabilityCompatibility(products, accessories, delivery, context, apiCart) {
    if (delivery?.address?.postalCode && (accessories.length > 0 || products.length > 0)) {
      const skus = new Set()
      const variantCodes = products.map((entry) => entry.code)
      const productVariantSKUs = []
      const floristsIds = []

      for (const product of await Promise.all(
        [...accessories, ...products].map((entry) => {
          const { productId } = variantIds(entry.code)
          return this._loadProductFromCT({ code: productId }, context)
        })
      )) {
        const floristIds = product.variants
          .filter((variant) => variantCodes.includes(variant.code))
          .flatMap((variant) => variant.florists || [])
        floristsIds.push(...floristIds)

        const productVariantSKU = product.variants
          .filter((variant) => variantCodes.includes(variant.code))
          .map((variant) => `${product.key}#${variant.sku}`)
        productVariantSKUs.push(...productVariantSKU)

        if (product.key !== config.products.interfloraplus.key && product.key !== config.products.tombDelivery.key)
          skus.add(product.key)
      }

      let checkAvailabilityResponse = null

      try {
        checkAvailabilityResponse = await context.dataSources.availability.checkAvailability({
          city: delivery.address.townLabel?.replace(/ *\(\d+\)/, '') ?? '', // Availability MS needs a value in this field even if it's empty on our side.
          codes: [...skus],
          country: delivery.address.countryId,
          date: delivery.date,
          floristsIds,
          orderId: apiCart?.id,
          postalCode: delivery.address.postalCode,
          productVariantSKUs,
          province: delivery.address.province,
        })
      } catch (error) {
        apm.captureError(error, { custom: error })
      }

      if (!checkAvailabilityResponse?.Result) {
        const err = new GraphQLApolloError(`availability check failed`, 400)
        err.addError('mix-availability', 'availability check failed')
        apm.captureError(err, {
          custom: err,
          message: `Availability check ${JSON.stringify(checkAvailabilityResponse)} for ${JSON.stringify({
            city: delivery.address.townLabel?.replace(/ *\(\d+\)/, ''),
            codes: [...skus],
            country: delivery.address.countryId,
            date: delivery.date,
            postalCode: delivery.address.postalCode,
            province: delivery.address.province,
          })}`,
        })
        throw err
      }
    }
  }

  /**
   * @param {GQLInputCartEntry[]} productCartEntries
   * @param {GraphQLContext} context
   *
   * @returns {Promise<void>}
   */
  async _checkRoundUpProducts(productCartEntries, context) {
    /** @type {CTProduct[]} */
    const ctProducts = await Promise.all(
      productCartEntries.map((productCartEntry) => {
        const { productId } = variantIds(productCartEntry.code)
        return context.loaders.commerceTools.products.load({ code: productId })
      })
    )
    const roundUpProducts = ctProducts.filter(({ masterVariant }) => {
      const productType = masterVariant.attributes.find((attr) => attr.name === 'product_type')?.value.key
      return productType === CONST.product.productType.ROUND_UP
    })

    if (roundUpProducts.length === 0) return

    /** @type {DatoConfiguration} */
    const datoConfiguration = await context.loaders.datocms.configuration.load(context.language)
    const datoRoundUpProducts = datoConfiguration?.roundUpProducts || []
    const datoRoundUpProductCodes = datoRoundUpProducts.map((roundUpProduct) => roundUpProduct.commercetoolsProduct)

    if (roundUpProducts.some((roundUpProduct) => !datoRoundUpProductCodes.includes(roundUpProduct.id))) {
      const err = new GraphQLApolloError('round up product not allowed', 403)
      err.addError('round-up-product-not-allowed', 'round up product not allowed')
      throw err
    }

    if (
      roundUpProducts.some((roundUpProduct) => {
        const productCartEntry = productCartEntries.find(
          (productCartEntry) => variantIds(productCartEntry.code).productId === roundUpProduct.id
        )
        const defaultPrice = roundUpProduct.masterVariant.prices?.[0]?.value?.centAmount
        const customerPrice = productCartEntry.customerPrice || defaultPrice
        return customerPrice < defaultPrice
      })
    ) {
      const err = new GraphQLApolloError('round up product price too low', 403)
      err.addError('round-up-product-price-too-low', 'round up product price too low')
      throw err
    }
  }

  /**
   * @param {CTCart} apiCart
   * @returns {{ action: string }[]} array of actions
   */
  _defaultTaxModeActions(apiCart) {
    if (apiCart.taxMode === 'Platform') return []

    return [
      {
        action: 'changeTaxMode',
        taxMode: 'Platform',
      },
    ]
  }

  /**
   * @param {CTCart} apiCart
   * @param {CTDatocmsContext} context
   * @param {Object} accessories
   * @param {Object<string, string>} accessoriesRelations
   * @returns {Promise<void | Object<string, { code: string, isCardMessage: boolean }>>}
   */
  async _getAccessoryProductRelationsFromApiCart(apiCart, context, accessories, accessoriesRelations) {
    const accessoryProductRelations = {}

    for (const entry of accessories) {
      const { productId, variantId } = variantIds(entry.code)

      const matchingLineItem = apiCart.lineItems
        .sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt))
        .find((lineItem) => productId === lineItem.productId && variantId === lineItem.variant.id)

      if (!matchingLineItem) continue

      let found = false
      for (const lineItem of apiCart.lineItems
        .filter(
          (lineItem) =>
            !accessoriesRelations[lineItem.id] && entry.code !== `${lineItem.productId}#${lineItem.variant.id}`
        )
        .sort((a, b) => new Date(b.addedAt) - new Date(a.addedAt))) {
        let code = `${lineItem.productId}#${lineItem.variant.id}`
        if (lineItem.custom?.fields.seaChannel?.id) {
          const channel = await context.loaders.commerceTools.channels.load(lineItem.custom.fields.seaChannel.id)
          const seaChannelKey = seaKeyFromPriceChannelKey(channel.key)
          if (seaChannelKey) {
            code = [lineItem.productId, seaKeyFromPriceChannelKey(channel.key), lineItem.variant.id].join('#')
          }
        }

        const relatedAccessories = await this._loadProductVariantAccessories({ code }, context)
        if (
          [...relatedAccessories.accessories, ...relatedAccessories.cards].some(
            (accessory) => accessory.code === productId
          )
        ) {
          found = true
          accessoryProductRelations[matchingLineItem.id] = {
            code: `${lineItem.productId}#${lineItem.variant.id}`,
            isCardMessage: relatedAccessories.cards.some((card) => card.code === productId),
          }
          break
        }
      }

      if (!found) {
        const error = new GraphQLApolloError(`No valid product found to link accessory ${productId}`)
        apm.captureError(error, { custom: error })
      }
    }
    return accessoryProductRelations
  }

  /**
   * @param {Date} date
   * @param {GraphQLContext} context
   * @returns {Promise<string[]>}
   */
  async _getApiMoments(date, context) {
    const cart = await this._getApiCart(context)
    if (!cart) return []

    const codes = await Promise.all(
      cart.lineItems
        // TODO FAM-800 => Back should make the product with code 'ITFPLUS' exist and always available. Then delete this filter.
        .filter((item) => item.productKey !== config.products.interfloraplus.key)
        .map(async (lineItem) => {
          const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
          return product.key
        })
    )

    return context.loaders.availability.moments.load({
      codes,
      country: cart.shippingAddress?.country || context.countryId,
      date,
      province: cart.shippingAddress?.region || undefined,
    })
  }

  /**
   * @param {ShippingMethodMap} methods
   * @param {Object} param
   * @param {boolean} param.isFlowersAtChoice
   * @param {boolean} param.isReduced
   * @param {boolean} param.isFuneral
   * @param {boolean} param.isWarehouse
   * @param {boolean} param.hasCustomShippingValue
   * @returns {ShippingMethodMap[][]}
   */
  _getOrderedSubsetShippingMethods(
    methods,
    { isFlowersAtChoice, isReduced, isFuneral, isWarehouse, hasCustomShippingValue }
  ) {
    const subsets = []
    if (hasCustomShippingValue) subsets.push(methods.customShippingFee)
    if (isWarehouse) subsets.push(methods.warehouse)
    if (isFuneral) subsets.push(methods.mourning)
    if (isReduced) subsets.push(methods.reduced)
    if (isFlowersAtChoice) subsets.push(methods.flowersAtChoice)
    subsets.push(methods.ab)
    // The order of priority is in descending order
    // Example: The first item on the list will be the last one prioritized
    return subsets.reverse()
  }

  /**
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @returns {Promise<Boolean>}
   */
  async _hasMourningProduct(apiCart, context) {
    return (
      await Promise.all(
        apiCart.lineItems.map(async (lineItem) => {
          const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
          return await this._loadProductType({ code: product.code }, context)
        })
      )
    ).some((t) => t === CONST.product.types.mourning)
  }

  /**
   * Return true if tomb delivery is selected (cart contains the right accessory)
   *
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @return {Promise<boolean>}
   * @private
   */
  async _isTombDelivery(apiCart, context) {
    for (const lineItem of apiCart.lineItems) {
      const product = await this._loadProductFromCT({ code: lineItem.productId }, context)
      if (product.key === config.products.tombDelivery.key) return true
    }
    return false
  }

  /**
   * @param {CTCart} apiCart
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>}
   */
  async _resetCart(apiCart, context) {
    const actions = []

    for (const lineItem of apiCart.lineItems) {
      actions.push({
        action: 'removeLineItem',
        lineItemId: lineItem.id,
      })
    }
    for (const lineItem of apiCart.customLineItems) {
      actions.push({
        action: 'removeCustomLineItem',
        customLineItemId: lineItem.id,
      })
    }
    actions.push({
      action: 'setBillingAddress',
    })
    if (apiCart.shippingInfo) {
      actions.push({
        action: 'setShippingMethod',
      })
    }
    actions.push({
      action: 'setShippingAddress',
    })
    actions.push({
      action: 'changeTaxMode',
      taxMode: 'Platform',
    })

    if (apiCart.custom.fields.hasDeliveryMethodSelected) {
      actions.push({
        action: 'setCustomField',
        name: 'hasDeliveryMethodSelected',
        value: false,
      })
    }

    context.log.info(`resetting cart [${apiCart.id}] ${JSON.stringify(actions)}`)
    const cart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
      actions,
      version: apiCart.version,
    })
    await Promise.all([
      context.loaders.commerceTools.carts.prime(apiCart.id, cart),
      context.loaders.commerceTools.cartsAsRead.prime(apiCart.id, cart),
    ])

    return cart
  }

  /**
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} args
   * @param {GraphQLContext} context
   * @returns {Promise<{actions: CTAction[], adminActions: CTAction[]}>}
   */
  async _updateCartActionsOutsideProducts(args, context) {
    const actions = []
    const adminActions = []

    const {
      cart: { delivery, joinMessage, hasDeliveryMethodSelected },
      context: { categoryId },
    } = args

    const itUserId = context.session?.itUser?.legacy ? context.session?.itUser?.userId : null
    const apiCart = await this._getApiCart(context)

    for (const action of await this._updateDeliveryActions(delivery, context)) {
      actions.push(action)
    }

    {
      let customerEmail = context.session?.user?.email
      if (delivery?.sender?.email) {
        customerEmail = delivery.sender.email
      } else if (itUserId) {
        const apiUser = await context.loaders.itUser.userById.load(context.session.itUser.userId)
        customerEmail = apiUser?.email
      }

      if (customerEmail !== apiCart.customerEmail) {
        adminActions.push({
          action: 'setCustomerEmail',
          email: customerEmail,
        })
      }
    }

    if (hasDeliveryMethodSelected !== apiCart.custom?.fields?.hasDeliveryMethodSelected) {
      actions.push({
        action: 'setCustomField',
        name: 'hasDeliveryMethodSelected',
        value: hasDeliveryMethodSelected,
      })
    }

    // nb: do not delete if was set and is unset
    if (
      config.site === CONST.site.fr &&
      context.categoryId &&
      context.categoryId !== apiCart.custom?.fields?.occasionCode
    ) {
      const occasionCodeCategory = await context.loaders.commerceTools.categories.load(context.categoryId)
      if (occasionCodeCategory?.key !== apiCart.custom?.fields?.occasionCode) {
        actions.push({
          action: 'setCustomField',
          name: 'occasionCode',
          value: occasionCodeCategory?.key || '',
        })
      }
    } else if (context.categoryId && context.categoryId !== apiCart.custom?.fields?.occasionCode) {
      actions.push({
        action: 'setCustomField',
        name: 'occasionCode',
        value: categoryId,
      })
    }

    for (const name of ['message', 'signature']) {
      if (joinMessage?.[name] && joinMessage[name] !== apiCart.custom?.fields?.[name]) {
        actions.push({
          action: 'setCustomField',
          name,
          value: sanitizeUserString(joinMessage[name], name === 'message'),
        })
      } else if (apiCart.custom?.fields?.[name] && !joinMessage?.[name]) {
        actions.push({
          action: 'setCustomField',
          name,
        })
      }
    }

    return { actions, adminActions }
  }

  /**
   * @param {GraphQLContextArgs<{ cart: GQLInputCart }>} args
   * @param {('customLineItems'|'lineItems')} source
   * @param {GraphQLContext} context
   * @returns {Promise<{
   *  accessoriesToAdd: Entry[],
   *  entriesToAdd: Entry[],
   *  entriesToDelete: Entry[],
   *  entriesToUpdate: Entry[],
   * }>}
   */
  async _updateCartSplitEntries(args, source, context) {
    const apiCart = await this._getApiCart(context)
    const {
      cart: { accessories = [], products = [], services = [] },
    } = args

    const currentItemsByCode = {}
    switch (source) {
      case 'customLineItems':
        for (const lineItem of apiCart.customLineItems) {
          const code = lineItem.slug
          currentItemsByCode[code] = lineItem
        }
        break
      case 'lineItems':
        for (const lineItem of apiCart.lineItems) {
          const code = `${lineItem.productId}#${lineItem.variant.id}`
          currentItemsByCode[code] = lineItem
        }
        break
    }

    const inputEntriesByCode = {}
    const accessoriesToAdd = []
    const entriesToAdd = []
    const entriesToDelete = []
    const entriesToUpdate = []

    for (const entry of [
      ...accessories.map((e) => ({ ...e, isAccessory: true })),
      ...products.map((e) => ({ ...e, isAccessory: false })),
      ...services.map((e) => ({ ...e, isAccessory: false })),
    ]) {
      const { productId, variantId } = variantIds(entry.code)
      /** @type {Entry} */
      const _entry = {
        ...entry,
        productId,
        variantId,
      }

      // reset code to exclude sea channel part
      const code = `${productId}#${variantId}`

      inputEntriesByCode[code] = _entry

      if (!currentItemsByCode[code]) {
        entriesToAdd.push(_entry)
        if (_entry.isAccessory) {
          accessoriesToAdd.push(_entry)
        }
      } else {
        if (
          currentItemsByCode[code].quantity !== entry.quantity ||
          currentItemsByCode[code].customerPrice !== entry.customerPrice
        ) {
          if (entry.quantity > 0) {
            entriesToUpdate.push({
              ..._entry,
              lineItemId: currentItemsByCode[code].id,
            })
          } else {
            entriesToDelete.push({
              lineItemId: currentItemsByCode[code].id,
            })
          }
        }
      }
    }

    for (const [code, entry] of Object.entries(currentItemsByCode)) {
      if (!inputEntriesByCode[code]) {
        entriesToDelete.push({
          lineItemId: entry.id,
        })
      }
    }

    // for active itf+ subscribtion of a user
    // 1) if trying to add to cart then filter out from new entries
    // 2) if currently in cart then remove
    const itfPlusId = (
      await context.loaders.commerceTools.productsByKey.load({ key: config.products?.interfloraplus?.key })
    )?.id
    const canOrderInterfloraPlus = await this._canOrderInterfloraPlus(apiCart.customerEmail, context)
    if (itfPlusId && !canOrderInterfloraPlus) {
      const toAddIndex = entriesToAdd.findIndex((entry) => entry.productId === itfPlusId)
      if (toAddIndex >= 0) {
        entriesToAdd.splice(toAddIndex, 1)
      }
      const interfloraplusLineItem = apiCart.lineItems.find(
        (lineItem) => lineItem.productKey === config.products?.interfloraplus?.key
      )
      if (interfloraplusLineItem) {
        entriesToDelete.push({
          lineItemId: interfloraplusLineItem.id,
        })
      }
    }

    const graveDeliverLineItem = apiCart.lineItems.find(
      (lineItem) => lineItem.productKey === config.products.tombDelivery.key
    )
    const graveDeliveryProduct = await context.loaders.commerceTools.productsByKey.load({
      key: config.products.tombDelivery.key,
    })

    // If the delivery mode is grave or tomb (ceremony + additional paid tomb delivery option) we add the grave delivery product in the cart if it's not already there.
    if (
      [CONST.cart.deliveryMode.grave, CONST.cart.deliveryMode.tomb].includes(apiCart.custom.fields.deliveryMode) &&
      graveDeliveryProduct
    ) {
      if (!graveDeliverLineItem) {
        entriesToAdd.push({
          code: `${graveDeliveryProduct.id}#1`,
          productId: graveDeliveryProduct.id,
          quantity: 1,
          variantId: 1,
        })
      }
    } else {
      if (graveDeliverLineItem) {
        entriesToDelete.push({
          lineItemId: graveDeliverLineItem.id,
        })
      }
    }

    return {
      accessoriesToAdd,
      entriesToAdd,
      entriesToDelete,
      entriesToUpdate,
    }
  }

  /**
   * @protected
   * @param {GQLInputCartDelivery} delivery
   * @param {GraphQLContext} context
   * @returns {Promise<{ action: string }[]>} array of actions
   */
  async _updateDeliveryActions(delivery, context) {
    this._assertHasCart(context)

    const apiCart = await this._getApiCart(context)
    const actions = []

    // if emtpy delivery, delete addresses if needed
    if (!delivery) {
      if (apiCart.billingAddress) {
        actions.push({ action: 'setBillingAddress' })
      }
      if (apiCart.shippingAddress) {
        // we cannot have a shipping method set without a shipping address
        if (apiCart.shippingInfo) {
          actions.push({ action: 'setShippingMethod' })
        }
        actions.push({ action: 'setShippingAddress' })
      }
      return actions
    }

    {
      const ctAddress = CTAddressTransformer.toCommerceTools(delivery?.address || {}, context, { delivery })

      if (!_.isEqual(apiCart.shippingAddress, ctAddress)) {
        if (apiCart.shippingAddress && ctAddress) ctAddress.id = apiCart.shippingAddress.id

        actions.push({
          action: 'setShippingAddress',
          address: ctAddress,
        })
      }
    }

    actions.push(...(await this._updateDeliveryMode(delivery, context)))

    if (delivery?.sender) {
      const ctAddress = CTAddressTransformer.toCommerceTools(delivery.sender, context, { delivery })
      if (!_.isEqual(apiCart.billingAddress, ctAddress)) {
        // @todo: move to it resolver
        if (delivery.sender.fiscalCode && !checkCode(delivery.sender.fiscalCode)) {
          context.log.error(`Invalid fiscal code ${delivery.sender.fiscalCode}`)
          const error = new GraphQLApolloError(`Invalid fiscal code`, 400)
          error.addError('invalid', 'fiscalCode', 'Campo Codice fiscale non valido!')
          throw error
        }

        if (apiCart.billingAddress) ctAddress.id = apiCart.billingAddress.id
        actions.push({
          action: 'setBillingAddress',
          address: ctAddress,
        })
      }

      {
        if (delivery.sender.invoiceRequest !== apiCart.custom?.fields?.invoiceRequest) {
          actions.push({
            action: 'setCustomField',
            name: 'invoiceRequest',
            value: delivery.sender.invoiceRequest,
          })
        }
      }
    } else if (apiCart.billingAddress) {
      actions.push({
        action: 'setBillingAddress',
      })
    }

    return actions
  }

  /**
   * @param {CTCart} apiCart
   * @param {CTDatocmsContext} context
   * @returns {Promise<{ action: string }[]>} array of actions
   */
  async _updateShippingCostsActions(apiCart, context) {
    const actions = []

    if (apiCart.taxMode !== 'Platform') {
      actions.push({
        action: 'changeTaxMode',
        taxMode: 'Platform',
      })
    }

    if (!apiCart.shippingAddress) {
      return actions
    }

    const gqlCart = CTCartTransformer.toGQL(apiCart, context, {
      distributionChannels: await getDistributionChannels(context),
      itfplusDiscountId: await itfPlusDiscountIdFromCart(apiCart, context),
    })

    const isFuneral = (
      await Promise.all(
        apiCart.lineItems.map((lineItem) => this._loadProductType({ code: lineItem.productId }, context))
      )
    ).some((productType) => productType === CONST.product.types.mourning)

    const abVariant = await getAbElasticityDeliveryFees(gqlCart, context, { centMultiplier: 100, isFuneral })

    let isWarehouse = false
    let hasCustomShippingValue = false
    const customShippingValue = (
      await Promise.all(
        apiCart.lineItems.map(async (lineItem) => {
          let priceChannelKey = config.commerceTools.priceChannelKey
          if (lineItem.custom?.fields?.seaChannel?.id) {
            const channel = await context.loaders.commerceTools.channels.load(lineItem.custom.fields.seaChannel.id)
            priceChannelKey = channel.key
          }

          const product = await this._loadProductFromCT({ channel: priceChannelKey, code: lineItem.productId }, context)
          const variant = product.variants.find((v) => v.id === lineItem.variant.id)

          // The warehouse delivery fee only applies in FR
          isWarehouse =
            context.appConfig.country === 'FR' && variant.delivery?.type === CONST.product.deliveryTypes.carrier

          if (Number.isFinite(variant.shippingFee?.value)) {
            hasCustomShippingValue = true
            return variant.shippingFee.value * lineItem.quantity
          }

          return 0
        })
      )
    ).reduce((totalScore, itemScore) => totalScore + itemScore, 0)

    const taxCategoryKeys = await Promise.all(
      apiCart.lineItems
        // ITFPLUS must not affect shipping tax
        .filter((lineItem) => lineItem.productKey !== config.products.interfloraplus.key)
        .map(async (lineItem) => {
          /** @type {CTProduct} */
          const product = await context.loaders.commerceTools.products.load({ code: lineItem.productId })
          /** @type {CTTaxCategory} */
          const taxCategory = await context.loaders.commerceTools.taxCategories.load(product.taxCategory.id)
          return taxCategory.key
        })
    )

    const currentShippingMethodId = apiCart.shippingInfo?.shippingMethod?.id
    let cartShippingMethod = await context.loaders.commerceTools.defaultShippingMethod.load(true)

    if (!taxCategoryKeys.length) {
      if (cartShippingMethod && cartShippingMethod.id !== currentShippingMethodId) {
        actions.push({
          action: 'setShippingMethod',
          shippingMethod: {
            id: cartShippingMethod.id,
            type: 'shippingMethod',
          },
        })
      }

      return actions
    }

    // cart is reduced if it contains at least one reduced product, and possibly one or more flowers at choice ones
    // will be true if contains reduced products only
    const isReduced =
      taxCategoryKeys.some((taxCategoryKey) => /(_low|reduced)/.test(taxCategoryKey)) &&
      taxCategoryKeys.every((taxCategoryKey) => /\b(flowers-at-choice|_low|reduced)\b/.test(taxCategoryKey))
    const isFlowersAtChoice = taxCategoryKeys.every((taxCategoryKey) => /flowers-at-choice/.test(taxCategoryKey))

    /**@type {ShippingMethodMap} */
    const methods = {
      ab: [],
      customShippingFee: [],
      flowersAtChoice: [],
      mourning: [],
      reduced: [],
      warehouse: [],
    }

    const ctShippingMethods = (await context.loaders.commerceTools.cartShippingMethods.load(apiCart.id)).filter(
      (shippingMethod) => {
        if (/-ab\d+$/.test(shippingMethod.key)) {
          return abVariant && new RegExp(`\\bab${abVariant}\\b`).test(shippingMethod.key)
        }
        if (/\bcustom-shipping-fee\b/.test(shippingMethod.key) && !hasCustomShippingValue) {
          return false
        }
        if (/\bmourning\b/.test(shippingMethod.key) && !isFuneral) {
          return false
        } else if (/\breduced\b/.test(shippingMethod.key) && !isReduced) {
          return false
        } else if (/\bflowers-at-choice\b/.test(shippingMethod.key) && !isFlowersAtChoice) {
          return false
        } else if (/\bwarehouse\b/.test(shippingMethod.key) && !isWarehouse) {
          return false
        }

        return true
      }
    )

    for (const shippingMethod of ctShippingMethods) {
      if (abVariant && new RegExp(`\\bab${abVariant}\\b`).test(shippingMethod.key)) {
        methods.ab.push(shippingMethod)
      }
      if (/\bcustom-shipping-fee\b/.test(shippingMethod.key)) {
        methods.customShippingFee.push(shippingMethod)
      }
      if (/\bmourning\b/.test(shippingMethod.key)) {
        methods.mourning.push(shippingMethod)
      }
      if (/\breduced\b/.test(shippingMethod.key)) {
        methods.reduced.push(shippingMethod)
      }
      if (/\bflowers-at-choice\b/.test(shippingMethod.key)) {
        methods.flowersAtChoice.push(shippingMethod)
      }
      if (/\bwarehouse\b/.test(shippingMethod.key)) {
        methods.warehouse.push(shippingMethod)
      }
    }

    // order is important!
    const subsets = this._getOrderedSubsetShippingMethods(methods, {
      hasCustomShippingValue,
      isFlowersAtChoice,
      isFuneral,
      isReduced,
      isWarehouse,
    })

    /** @type {CTShippingMethod} */
    let match
    do {
      match = _.intersection(...subsets)[0]
      subsets.shift()
    } while (subsets.length > 0 && !match)

    if (match) cartShippingMethod = match

    if (cartShippingMethod && cartShippingMethod.id !== currentShippingMethodId) {
      context.log.info(
        { isFlowersAtChoice, isFuneral, isReduced, isWarehouse },
        `switching shipping method to ${cartShippingMethod.name} [${cartShippingMethod.key}]`
      )

      actions.push({
        action: 'setShippingMethod',
        shippingMethod: {
          id: cartShippingMethod.id,
          type: 'shippingMethod',
        },
      })
    }

    if (hasCustomShippingValue) {
      let taxMultiplier = 1
      /*
       * ES and PT (and only those countries) custom shipping fees per product/variant/price line are contributed without taxes
       * so we need to do the cart update directly to get the correct taxes applied to the cart and recalculate the shipping fees with taxes
       * The code above is the least dirty way we found to do it.
       */
      if (['ES', 'PT'].includes(context.appConfig.country)) {
        if (actions.length > 0) {
          apiCart = await context.dataSources.carts.updateCartAsAdmin(apiCart.id, {
            actions,
            version: apiCart.version,
          })
        }
        // map product type (custom field in CT) with product tax rate
        const productTaxRate = new Map(
          apiCart.lineItems.map((lineItem) => {
            const productType = lineItem.variant?.attributes?.find((attr) => attr.name === 'product_type')?.value?.key
            return [productType, lineItem.taxRate.amount]
          })
        )

        // use the default tax rate at basket level
        let taxRate = apiCart.shippingInfo.taxRate?.amount || 0

        const excludedTypes = [
          CONST.product.productType.ACCESSORIES,
          CONST.product.productType.CARD,
          CONST.product.productType.SERVICES,
        ]

        // apply the tax rate of the main product if we have at least one excluded type in the cart
        if (excludedTypes.some((type) => productTaxRate.has(type))) {
          excludedTypes.forEach((type) => {
            if (productTaxRate.has(type)) {
              // remove excluded type from the map => to obtain the tax rate of the main product
              productTaxRate.delete(type)
            }
          })
          // modify the tax rate by the product tax rate
          taxRate = productTaxRate.values().next().value
        }

        taxMultiplier = 1 + taxRate
      }
      actions.push({
        action: 'setShippingRateInput',
        shippingRateInput: {
          score: Math.round(customShippingValue * taxMultiplier),
          type: 'Score',
        },
      })
    }
    return actions
  }

  /**
   * @param {CTCart} apiCart
   * @param {CTAction[]} actions
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>}
   */
  async _updateAndPrimeCart(apiCart, actions, context) {
    if (!actions || !actions.length) {
      return apiCart
    }

    let cart = apiCart

    {
      // security check.
      // Replicate cart if a payment is pending and the cart may change
      if (await this._isCartInPendingPaymentState(cart, context)) {
        const isAmountLikelyToChange = actions.some((action) =>
          [
            'addCustomLineItem',
            'addLineItem',
            'changeCustomLineItemQuantity',
            'changeLineItemQuantity',
            'removeCustomLineItem',
            'removeLineItem',
          ].includes(action.action)
        )

        const isDeliveryLikelyToChange = actions.some((action) =>
          ['setShippingAddress', 'setShippingMethod', 'setBillingAddress'].includes(action.action)
        )

        if (isAmountLikelyToChange || isDeliveryLikelyToChange) {
          /** @type {CTCart} */
          const replicatedCart = await context.dataSources.carts.replicateCart(cart.id)
          context.log.info(
            `[security] pending payment & amount or delivery may change > replicate cart [${cart.id}] => [${replicatedCart.id}]`
          )

          // replicated cart has != line ids
          for (const action of actions) {
            if (action.lineItemId) {
              const origLineItem = cart.lineItems.find((l) => l.id === action.lineItemId)
              const destLineItem = replicatedCart.lineItems.find(
                (l) => l.productId === origLineItem.productId && l.variant.id === origLineItem.variant.id
              )
              action.lineItemId = destLineItem.id
            } else if (action.customLineItemId) {
              const origCustomLineItem = cart.lineItems.find((l) => l.id === action.customLineItemId)
              const destCustomLineItem = replicatedCart.find((l) => l.sku === origCustomLineItem.sku)
              action.customLineItemId = destCustomLineItem.id
            }
          }

          cart = replicatedCart
        }
      }
    }

    const adminActions = actions.filter((action) => !!action.asAdmin)
    const nonAdminAction = actions.filter((action) => !action.asAdmin)
    let updatedApiCart = await context.dataSources.carts.updateCartAsAdmin(cart.id, {
      actions: adminActions,
      version: cart.version,
    })
    updatedApiCart = await context.dataSources.carts.updateCart(updatedApiCart.id, {
      actions: nonAdminAction,
      version: updatedApiCart.version,
    })

    await Promise.all([
      context.loaders.commerceTools.carts.prime(updatedApiCart.id, updatedApiCart),
      context.loaders.commerceTools.cartsAsRead.prime(updatedApiCart.id, updatedApiCart),
    ])

    return updatedApiCart
  }

  async _updateDeliveryMode(delivery, context) {
    const apiCart = await this._getApiCart(context)

    if (!delivery) {
      return []
    }

    let deliveryMode

    if (delivery.mode) {
      deliveryMode = delivery.mode
    } else if (!(await this._hasMourningProduct(apiCart, context))) {
      deliveryMode = CONST.cart.deliveryMode.standard
    }

    if (!deliveryMode || deliveryMode === apiCart.custom.fields.deliveryMode) {
      return []
    }

    return [
      {
        action: 'setCustomField',
        name: 'deliveryMode',
        value: deliveryMode,
      },
    ]
  }

  /**
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async _isCartInPendingPaymentState(cart, context) {
    /** @type {CTPayment[]} */
    const payments = []

    for (const paymentReference of cart.paymentInfo?.payments ?? []) {
      payments.push(await context.loaders.commerceTools.payments.load(paymentReference.id))
    }
    payments.sort((a, b) => a.lastModifiedAt.localeCompare(b.lastModifiedAt))

    const lastPayment = payments.at(-1)
    if (!lastPayment) {
      return false
    }

    switch (lastPayment.paymentMethodInfo.paymentInterface) {
      case CONST.payment.provider.AXERVE: {
        return (
          lastPayment.interfaceId.endsWith('-paypal-pending-review') ||
          (lastPayment.interfaceId.endsWith('-setup') &&
            lastPayment.paymentStatus.interfaceCode === CONST.commerceTools.paymentStates.pending)
        )
      }

      default: {
        return lastPayment.paymentStatus.interfaceCode === CONST.commerceTools.paymentStates.pending
      }
    }
  }

  /**
   * @param {Entry[]} entries
   * @param {GraphQLContext} context
   */
  // eslint-disable-next-line no-unused-vars
  async _checkStocks(entries, context) {
    // Stocks are only used in France for now so we don't do anything in here.
    return true
  }

  /**
   * Get the details of a reward
   * @param {undefined} _parent
   * @param {GraphQLContextArgs} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLRewardDetails>|null}
   */
  async getRewardDetails(_parent, args, context) {
    const { rewardType, code } = args
    if (!rewardType || !code) {
      throw new Error('Missing required parameters')
    }

    if (rewardType === CONST.reward.types.giftCard) {
      return this._getGiftCardDetails(code, context)
    }

    throw new GraphQLApolloError('Bad request', 400)
  }

  /**
   * Reserve an amount on a reward program
   * @param {undefined} _parent
   * @param {GraphQLContextArgs} args
   * @param {GraphQLContext} context
   * @returns {Promise<CTCart>|null}
   */
  async createReward(_parent, args, context) {
    const { rewardType, code, pin, desiredAmount } = args

    if (!rewardType || !code || !pin) {
      throw new GraphQLApolloError('Missing required parameters', 'MISSING_REQUIRED_PARAMETERS')
    }

    const cart = await this._getApiCart(context)
    if (!cart) {
      throw new GraphQLApolloError('Cart not found', 'CART_NOT_FOUND')
    }

    if (rewardType === CONST.reward.types.giftCard) {
      return this._createGiftCardTransaction(code, pin, desiredAmount, context)
    }

    throw new GraphQLApolloError('Bad request', 400)
  }

  /**
   * Confirm a reservation that has been made on a reward.
   * @param {undefined} _parent
   * @param {GraphQLContextArgs} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLCart>|null}
   */
  async confirmReward(_parent, args, context) {
    const { reservationId, rewardType } = args
    if (!reservationId || !rewardType) {
      throw new GraphQLApolloError('Missing required parameters', 'MISSING_REQUIRED_PARAMETERS')
    }

    const cart = await this._getApiCart(context)
    if (!cart) {
      throw new GraphQLApolloError('Cart not found', 'CART_NOT_FOUND')
    }

    switch (rewardType) {
      case CONST.reward.types.giftCard:
        return this._confirmGiftCardTransaction(reservationId, rewardType, context)
      case CONST.reward.types.bonus:
      case CONST.reward.types.promotion:
        return this._confirmRewardTransaction(reservationId, rewardType, context)
      default:
        throw new GraphQLApolloError('Invalid reward type', 'INVALID_REWARD_TYPE')
    }
  }

  /**
   * Cancel a reservation that has been made on a reward
   * @param {undefined} _parent
   * @param {GraphQLContextArgs} args
   * @param {GraphQLContext} context
   * @returns {Promise<GQLRewardEvent>|null}
   */
  async cancelReward(_parent, args, context) {
    const { reservationId, rewardType } = args
    if (!reservationId || !rewardType) {
      throw new GraphQLApolloError('Missing required parameters', 'MISSING_REQUIRED_PARAMETERS')
    }

    switch (rewardType) {
      case CONST.reward.types.giftCard:
        return this._cancelGiftCardReward(reservationId, rewardType, context)
      case CONST.reward.types.bonus:
      case CONST.reward.types.promotion:
        return this._cancelBonusReward(reservationId, rewardType, context)
      default:
        throw new GraphQLApolloError('Invalid reward type', 'INVALID_REWARD_TYPE')
    }
  }

  async _getGiftCardDetails() {
    throw new Error('Not implemented')
  }

  async _createGiftCardTransaction() {
    throw new Error('Not implemented')
  }

  async _confirmGiftCardTransaction() {
    throw new Error('Not implemented')
  }

  async _cancelGiftCardReward() {
    throw new Error('Not implemented')
  }

  async _confirmRewardTransaction() {
    throw new Error('Not implemented')
  }

  async _cancelBonusReward() {
    throw new Error('Not implemented')
  }

  /**
   * Retrieves the payment methods associated with the current cart.
   * Disabled in this context - Used only by Sweden
   */
  async payments() {
    return []
  }

  /**
   * Used only for longInfo, name and shortInfo
   * @param {GQLDelivery} delivery
   * @param {{}} _args
   * @param {GraphQLContext} context
   * @param {Object} info
   * @returns {Promise<*>}
   */
  async getCartDeliveryProperty(delivery, _args, context, info) {
    return delivery[info.fieldName]
  }

  /**
   *
   * @param {CTCart} cart
   * @param {GraphQLContext} context
   */
  // eslint-disable-next-line no-unused-vars
  async _updateAdditionalCartInfo(cart, context) {
    return []
  }
}

module.exports = CartResolver
