class ContactResolver {
  /**
   * @param {undefined} _parent
   * @param {GraphQLContextArgs<{email: string, numberOrder: string}>} args
   * @param {GraphQLContext} context
   * @returns {Promise<boolean>}
   */
  async checkAttachCommand(_parent, args, context) {
    const { numberOrder: orderNumber, email } = args
    return Boolean(await context.dataSources.orders.getByNumberAndEmail(orderNumber, email))
  }

  getContactFormSubjects() {
    throw new Error('not implemented')
  }

  sendContactMessage() {
    throw new Error('not implemented')
  }

  sendRetractation() {
    throw new Error('not implemented')
  }
}

module.exports = ContactResolver
