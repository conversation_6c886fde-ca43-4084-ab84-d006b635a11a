const GQLValidationResult = require('../../models/ValidationResult')

class AddressResolver {
  async addAddress() {
    throw new Error(`not implemented`)
  }

  async checkTwitterId() {
    throw new Error(`not implemented`)
  }

  /**
   * Only use by Sweden
   */
  async getAddresses() {
    throw new Error(`not implemented`)
  }
  /**
   * Only use by Sweden
   */
  async getFuneralAddresses() {
    throw new Error(`not implemented`)
  }

  async removeAddress() {
    throw new Error(`not implemented`)
  }

  async updateAddress() {
    throw new Error(`not implemented`)
  }

  async validateAddress() {
    return new GQLValidationResult()
  }
}

module.exports = AddressResolver
