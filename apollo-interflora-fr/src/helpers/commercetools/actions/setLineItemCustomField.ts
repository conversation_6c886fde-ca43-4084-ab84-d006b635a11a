import { Cart, LineItem, CartSetLineItemCustomFieldAction } from '@commercetools/platform-sdk'
import { getBundleLineItemsWithCustomText, variantIds } from '..'
import CONST from '../../../const'

type CustomTextParams = {
  apiCart: Cart
  accessories: GQLInputCartEntry[]
  products: GQLInputCartEntry[]
  context: CTDatocmsContext
}

type BuildActionsParms = {
  context: CTDatocmsContext
  lineItems: (CTLineItem | LineItem)[]
  entries: GQLInputCartEntry[]
  fieldName: string
  fieldValue?: string
  logName: 'ribbon' | 'message' | 'signature'
}

type ActionsParms = {
  apiCart: Cart
  entries: GQLInputCartEntry[]
  context: CTDatocmsContext
}

const buildActions = (
  { context, lineItems, entries, fieldName: name, fieldValue, logName }: BuildActionsParms,
  callbackAction: (entry: GQLInputCartEntry) => { value: string | undefined; resetValue?: boolean }
) => {
  const actions: CartSetLineItemCustomFieldAction[] = []
  for (const lineItem of lineItems) {
    const entry = entries.find((entry) => {
      const { productId } = variantIds(entry.code)
      return productId === lineItem.productId
    })

    if (!entry) {
      context.log.error(`[${logName}] empty message received`)
      continue
    }

    const { resetValue, value = fieldValue } = callbackAction(entry)

    if ((value && value !== lineItem.custom?.fields?.[name]) || resetValue) {
      actions.push({
        action: 'setLineItemCustomField',
        lineItemId: lineItem.id,
        name,
        value,
      })
    }
  }
  return actions
}

/**
 * Return the actions related to accessories with ribbon text
 */
const getAccessoriesRibbonTextActions = async ({ entries: accessories, apiCart, context }: ActionsParms) => {
  const lineItemAccessories = apiCart.lineItems.filter((lineItem) => Boolean(lineItem.custom?.fields?.isAccessoryFor))

  const accessoryInfo: DatoAccessoryInfo[] = await Promise.all(
    lineItemAccessories.map((lineItem) => context.loaders.datocms.accessoryInfos.load(lineItem.productId))
  )
  const accessoriesWithCustomText = lineItemAccessories.filter((_, index) => accessoryInfo[index]?.hasCustomText)

  return buildActions(
    {
      context,
      entries: accessories,
      fieldName: 'ribbonText',
      lineItems: accessoriesWithCustomText,
      logName: 'ribbon',
    },
    (entry) => ({ value: entry.customText })
  )
}

/**
 * Return the actions related to bundles with ribbon text
 */
const getBundlesRibbonTextActions = async ({ entries: products, apiCart, context }: ActionsParms) => {
  const bundlesWithCustomText = await getBundleLineItemsWithCustomText(apiCart.lineItems, context)
  return buildActions(
    {
      context,
      entries: products,
      fieldName: 'ribbonText',
      lineItems: bundlesWithCustomText,
      logName: 'ribbon',
    },
    (entry) => ({ value: entry.customText })
  )
}

/**
 * Return the actions related to accessories with personalized messages
 */
const getMessageCardsMessageActions = ({ entries: accessories, apiCart, context }: ActionsParms) => {
  const lineItemAccessories = apiCart.lineItems.filter((lineItem) => Boolean(lineItem.custom?.fields?.isAccessoryFor))
  const messageCardsWithCustomText = lineItemAccessories.filter((lineItem) =>
    lineItem.variant.attributes?.some(
      (attribute) =>
        attribute.name === 'product_type' &&
        [
          CONST.product.productType.DESIGN_CARD,
          CONST.product.productType.DESIGN_CARD_SAVED,
          CONST.product.productType.DESIGN_CARD_UPLOADED,
        ].includes(attribute.value?.key)
    )
  )

  const messageContentActions = buildActions(
    {
      context,
      entries: accessories,
      fieldName: 'messageContent',
      lineItems: messageCardsWithCustomText,
      logName: 'message',
    },
    (entry) => ({ resetValue: !entry.joinMessage?.message, value: entry.joinMessage?.message ?? '' })
  )
  const messageSignatureActions = buildActions(
    {
      context,
      entries: accessories,
      fieldName: 'messageSignature',
      lineItems: messageCardsWithCustomText,
      logName: 'signature',
    },
    (entry) => ({ resetValue: !entry.joinMessage?.signature, value: entry.joinMessage?.signature ?? '' })
  )

  return [...messageContentActions, ...messageSignatureActions]
}

const updateCustomText = async ({
  apiCart,
  accessories,
  products,
  context,
}: CustomTextParams): Promise<Cart | CTCart> => {
  const accessoriesRibbonTextActions =
    (await getAccessoriesRibbonTextActions({ apiCart, context, entries: accessories })) ?? []
  const bundlesRibbonTextActions = (await getBundlesRibbonTextActions({ apiCart, context, entries: products })) ?? []
  const messageCardsMessageActions = getMessageCardsMessageActions({ apiCart, context, entries: accessories })

  const actions = [...accessoriesRibbonTextActions, ...bundlesRibbonTextActions, ...messageCardsMessageActions]

  if (actions.length > 0) {
    return context.dataSources.carts.updateCart(apiCart.id, {
      actions,
      version: apiCart.version,
    })
  }

  return apiCart
}

export { updateCustomText }
