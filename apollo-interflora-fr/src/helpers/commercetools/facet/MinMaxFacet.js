const Facet = require('./Facet')

class MinMaxFacet extends Facet {
  /**
   * @param {string} field
   * @param {string} label
   * @param {CtRangeDefinition} ranges
   */
  constructor(field, label, ranges) {
    super(field, label, 'range')
    this.ranges = ranges
  }

  /**
   * @inheritdoc
   */
  toCtField() {
    const rangeValue = this.ranges.map(MinMaxFacet.rangeToString).join(',')

    return `${this.field}:range ${rangeValue}`
  }

  /**
   * @inheritdoc
   */
  transformResponseValues(ctFacet) {
    const [min, max] = ctFacet.ranges.reduceRight(
      (accumulator, currentValue) => {
        const max = Math.max(accumulator[1], currentValue.max)
        const min = currentValue.total > 0 ? currentValue.min : accumulator[0]
        return [min, max]
      },
      [0, 0]
    )

    return [
      {
        defaultValue: false,
        label: 'Minimum',
        value: `${min}`,
      },
      {
        defaultValue: false,
        label: 'Maximum',
        value: `${max}`,
      },
    ]
  }

  /**
   * @inheritdoc
   * @param {GQLInputProductFilter} filter
   */
  transformRequestFilterToCtFilters(filter) {
    if (filter.values.length < 2) {
      return []
    }
    const [min, max] = filter.values

    return [`${this.field}:range (${min} to ${max})`]
  }

  /**
   * @param {{from: number, to: number}} param0
   * @returns {string}
   */
  static rangeToString({ from, to }) {
    const min = from === null ? '*' : from
    const max = to === null ? '*' : to

    return `(${min} to ${max})`
  }
}

module.exports = MinMaxFacet
