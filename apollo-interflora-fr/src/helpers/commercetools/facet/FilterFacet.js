const Facet = require('./Facet')

class FilterFacet extends Facet {
  /**
   * @param {string} field
   * @param {string} label
   * @param {string|number} value
   */
  constructor(field, label, value) {
    super(field, label, 'filter')
    this.value = value
  }

  /**
   * @inheritdoc
   */
  toCtField() {
    const value = typeof this.value === 'string' ? `"${this.value}"` : this.value

    return `${this.field}:${value}`
  }

  /**
   * @inheritdoc
   */
  transformResponseValues() {
    return [
      {
        defaultValue: false,
        label: this.value,
        value: this.value,
      },
    ]
  }

  /**
   * @inheritdoc
   * @param {GQLInputProductFilter} filter
   */
  transformRequestFilterToCtFilters(filter) {
    if (!filter.values.length) {
      return []
    }

    return [`${this.field}:"${filter.values.join('","')}"`]
  }
}

module.exports = FilterFacet
