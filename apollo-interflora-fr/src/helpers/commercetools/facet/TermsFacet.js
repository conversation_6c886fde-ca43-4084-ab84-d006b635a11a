const Facet = require('./Facet')

class TermsFacet extends Facet {
  /**
   * @param {string} field
   * @param {string} label
   * @param {{
   *  labels: Object.<string, string>
   * }} options
   */
  constructor(field, label, options) {
    super(field, label, 'terms')
    this.options = options
  }

  /**
   * @inheritdoc
   */
  toCtField() {
    return this.field
  }

  /**
   * @inheritdoc
   */
  transformResponseValues(ctFacet) {
    return ctFacet.terms.map(({ term }) => ({
      defaultValue: false,
      label: this.getLabel(term),
      value: term,
    }))
  }

  /**
   * @inheritdoc
   * @param {GQLInputProductFilter} filter
   */
  transformRequestFilterToCtFilters(filter) {
    if (!filter.values.length) {
      return []
    }

    return [`${this.field}:"${filter.values.join('","')}"`]
  }

  /**
   * @param {string} term
   * @returns {string}
   */
  getLabel(term) {
    return this.options?.labels?.[term] || term
  }
}

module.exports = TermsFacet
