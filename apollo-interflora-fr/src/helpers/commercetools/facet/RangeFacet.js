const Facet = require('./Facet')

class RangeFacet extends Facet {
  /**
   * @param {string} field
   * @param {string} label
   * @param {CtRangeDefinition} ranges
   */
  constructor(field, label, ranges) {
    super(field, label, 'range')
    this.ranges = ranges
  }

  /**
   * @inheritdoc
   */
  toCtField() {
    const rangeValue = this.ranges.map(RangeFacet.rangeToString).join(',')

    return `${this.field}:range ${rangeValue}`
  }

  /**
   * @inheritdoc
   */
  transformResponseValues(ctFacet) {
    return ctFacet.ranges
      .map(({ from, to }) => {
        const rangeDef = this.ranges.find((def) => {
          const inMinBoundries = def.from === from || (def.from === null && from === 0)
          const inMaxBoundries = def.to === to || (def.to === null && to === 0)

          return inMinBoundries && inMaxBoundries
        })

        if (!rangeDef) {
          return null
        }

        return {
          defaultValue: false,
          label: rangeDef.label,
          value: rangeDef.label,
        }
      })
      .filter((rangeValue) => !!rangeValue)
  }

  /**
   * @inheritdoc
   * @param {GQLInputProductFilter} filter
   */
  transformRequestFilterToCtFilters(filter) {
    const rangeDef = this.ranges.filter((def) => filter.values.includes(def.label))

    if (!rangeDef.length) {
      return []
    }

    const rangeValues = rangeDef.map(RangeFacet.rangeToString).join(',')

    return [`${this.field}:range ${rangeValues}`]
  }

  /**
   * @param {{from: number, to: number}} param0
   * @returns {string}
   */
  static rangeToString({ from, to }) {
    const min = from === null ? '*' : from
    const max = to === null ? '*' : to

    return `(${min} to ${max})`
  }
}

module.exports = RangeFacet
