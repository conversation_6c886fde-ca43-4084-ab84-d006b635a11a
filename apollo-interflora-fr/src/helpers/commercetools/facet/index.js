const CONST = require('../../../const')
const TermsFacet = require('./TermsFacet')
const FilterFacet = require('./FilterFacet')
const RangeFacet = require('./RangeFacet')
const MinMaxFacet = require('./MinMaxFacet')

/**
 * @typedef {import('./Facet')} Facet
 */

/**
 * @param {CtFacetDefinition} config
 * @returns {Facet}
 */
function createFacet(config) {
  switch (config.type) {
    case CONST.commerceTools.facetTypes.terms:
      return new TermsFacet(config.field, config.label, {
        labels: config.labels,
      })

    case CONST.commerceTools.facetTypes.range:
      return new RangeFacet(config.field, config.label, config.ranges)

    case CONST.commerceTools.facetTypes.filter:
      return new FilterFacet(config.field, config.label, config.value)

    case CONST.commerceTools.facetTypes.minMax:
      return new MinMaxFacet(config.field, config.label, config.ranges)

    default:
      throw new Error(`Unsupported type ${this.type}`)
  }
}

module.exports = {
  createFacet,
}
