const CONST = require('../const')

module.exports = {
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.dk',
      breadcrumbsHome: 'Home breadcrumb label', // @todo translate
      ceremonyTimeRanges: {
        default: {
          from: {
            hours: 9,
            minutes: 30,
          },
          margin: 4 * 3600 * 1000, // 4h in ms
          to: {
            hours: 20,
            minutes: 0,
          },
        },
        // Specific supplementary rule if the ceremony is today
        today: {
          from: {
            hours: 10,
            minutes: 0,
          },
        },
      },
      commerceTools: {
        locale: {
          fallbacks: ['da'],
        },
        storeKey: 'ITD',
      },
      country: 'DK',
      currency: 'DKK',
      currencyName: 'Danish Krone',
      currencySymbole: 'kr',
      // @TODO translate errors to overwrite defaults
      // errors: {
      //   login: '',
      //   loginBlocked: '',
      //   loginUnconfirmed: '',
      //   password: '',
      // },
      language: 'da',
      // @TODO update media base url path for DK endpoint when domain is set
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.se/fstrz/r/s/www.datocms-assets.com',
      momentLabels: {
        // @TODO translate to Danish
        [CONST.commerceTools.moment.Morning]: 'Morning (9:00 - 14:00)',
        [CONST.commerceTools.moment.Afternoon]: 'Afternoon (15:00 - 20:00)',
        [CONST.commerceTools.moment.Wholeday]: 'All day (9:00 - 21:00)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [9, 14],
        [CONST.commerceTools.moment.Afternoon]: [15, 20],
        [CONST.commerceTools.moment.Wholeday]: [9, 21],
      },
      mourningDeliveryModes: [
        CONST.cart.mourningDeliveryModeMap.FUNERAL_PLACE,
        CONST.cart.mourningDeliveryModeMap.domicile,
      ],
      product: {
        category: {
          allSlug: 'search',
        },
      },
      // @TODO translate SEO to Danish
      seo: {
        category: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Blommor leverans i %s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: 'Blommor leverans i %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description: '',
          metaTitle: 'Florister %s – Interflora florister med hemleverans',
          title: 'Interflora florister %s',
        },
        home: {
          description:
            'Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: 'Interflora: Blommor hemleverans och växter online',
        },
        product: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
      },
      // @TODO storeLocato path - are they required
      storeLocator: {
        customPath: '/store-locator-dk', // needs to match route in front
        path: '/florist', // needs to match route in front
      },
      timezone: 'Europe/Copenhagen',
      towns: {
        max: 70,
      },
    },
  },
  availability: {
    // @todo confirm ms with infra team
    baseUrl: 'http://availability.itd-ms/availability/api/v1/Availability',
  },
  // @TODO: Update section
  axerve: {
    TRALimit: 250,
    redirect3DSUrl: 'https://sandbox.gestpay.net/pagam/pagam3d.aspx',
    redirectPaymentUrl: 'https://sandbox.gestpay.net/pagam/pagam.aspx',
    rest: {
      baseUrl: 'https://sandbox.gestpay.net/api/v1',
    },
    shopLogin: 'GESPAY90162',
    soap: {
      cryptWSDL: 'https://sandbox.gestpay.net/gestpay/GestPayWS/WsCryptDecrypt.asmx?wsdl',
    },
    vendorName: 'Interflora',
  },
  bloomreach: {
    enabled: false,
  },
  commerceTools: {
    hasUsers: false,
    priceChannelKey: 'interflora.dk',
    // @todo trasnslations for labels in productList
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Colore',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Leveranssätt',
          labels: {
            Carrier: 'Bud',
            Florist: 'Florist',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.price.centAmount',
          label: 'Pris',
          ranges: [{ from: 0, label: 'All', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Sortera efter',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Pris stigande', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Pris fallande', value: 'price desc' },
        ],
      },
    },
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Ange önskat leveransdatum',
    },
  },
  frisbii: {
    checkout: {
      locale: 'da_DK',
      options: {
        detailedInvoice: true, // sends the details of the items in the cart to frisbii for invoicing
        settle: false, // payments need to be captured afterward (manually or by back office),
        ttl: 'P3D', // weird format, check https://docs.frisbii.com/reference/createchargesession
      },
    },
  },
  gfs: {
    customShipping: {
      // centAmount
      externalTaxRate: {
        amount: 0,
        // 0 to 1,
      },

      name: 'International shipping method',
      shippingCosts: 149 * 100,
    },
    fromEurToLocalCurrencyRate: 11,
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600, // 12h in s - NB: FR value is in ms
  },
  order: {
    // @todo translate order reasons
    reasons: [
      { id: '1', label: 'Födelsedag' },
      { id: '3', label: 'För nöje' },
      { id: '5', label: 'Tack' },
      { id: '6', label: 'Sorg' },
      { id: '9', label: 'Födelse' },
      { id: '10', label: 'Kärlek' },
      { id: '11', label: 'Dop' },
      { id: '13', label: 'Bröllopsdag' },
      { id: '16', label: 'Alla hjärtans dag' },
      { id: '17', label: 'Mors dag' },
      { id: '23', label: 'Påsk' },
      { id: '24', label: 'Fars dag' },
      { id: '93', label: 'Alla helgons dag' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.FRISBII,
    methods: [
      {
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.MOBILE_PAY,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.FACTURE,
        provider: CONST.payment.provider.NONE,
      },
    ],
    rules: {
      [CONST.payment.paymentMode.MOBILE_PAY]: {
        disableIfDeliveryDateAndOrderDateDifferenceIs: 7, // Disable Mobile pay if the delivery is more than 7 days after the order date
      },
    },
  },
  products: {
    ribbon: {
      key: 'RIBBONTEXT', // needs to match CT product key
    },
  },
  // @todo confirm ms with infra team
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.itd-ms/itsequencegenerator/api/v1/Sequence',
  },
}
