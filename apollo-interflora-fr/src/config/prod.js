module.exports = {
  amazonPay: {
    // @todo
    merchantId: 'changeme',
    publicKey: 'changeme',
    storeId: 'changeme',
  },
  availability: {
    timeout: 5000,
  },
  bloomreach: {
    autosuggest: {
      baseUrl: 'https://suggest.dxpapi.com/api/v2/suggest/',
      timeout: 400,
    },
    defaultUid: 'uid=7797686432023:v=11.5:ts=1428617911187:hc=55',
    search: {
      baseUrl: 'https://core.dxpapi.com/api/v1/core/',
      timeout: 400,
    },
  },
  buybox: {
    baseUrl: 'https://www2.buybox.net/secure/express-checkout/nvp.php',
    redirectUrl: 'https://www2.buybox.net/secure/payment_login.php',
  },
  commerceTools: {
    auth: {
      // end-user scope only, is actually not secret.
      credentials: {
        clientId: 'KEgpD5irwPreUv8NGt3I7o3S',
        clientSecret: 'Qku-rB-ATA8raM4vLc550HAekeu1dR13',
      },
      projectKey: 'myflower-prod1',
      scopes: [
        'manage_my_orders:myflower-prod1 manage_my_payments:myflower-prod1 manage_my_profile:myflower-prod1 view_categories:myflower-prod1 view_published_products:myflower-prod1 manage_my_shopping_lists:myflower-prod1 create_anonymous_token:myflower-prod1',
      ],
    },
  },
  datasources: {
    timeout: 5000,
  },
  datocms: {
    token: 'd1f397171afe3f00fa748284cf5953', // read-only
  },
  paypal: {
    baseUrl: 'https://api-m.paypal.com',
  },
  server: {
    cors: { 'Access-Control-Allow-Origin': /^https:\/\/.*\.interflora\.(fr|it|es|pt|se|dk)$/ },
  },
}
