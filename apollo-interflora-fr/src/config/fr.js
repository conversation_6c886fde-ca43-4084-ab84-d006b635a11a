const { merge } = require('lodash')

const CONST = require('../const')

/** @type {AppConfiguration} */
const config = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 30,
    },
  },
  address: {
    mandatoryFields: {
      addressBook: [CONST.address.addressBookMandatoryFields.civility],
    },
  },
  apps: {
    // because apps = baseId for now
    apps: {
      baseUrl: process.env.BASE_URL || 'https://mobile.interflora.fr',
      breadcrumbsHome: 'Accueil - Livraison fleurs',
      cms: {
        emptyContentPageUid: 'defaultApiPage',
      },
      commerceTools: {
        locale: {
          fallbacks: ['fr-FR', 'fr'],
        },
        storeKey: 'ITF',
      },
      country: 'FR',
      currency: 'EUR',
      currencyName: 'Euro',
      currencySymbole: '€',
      defaultZone: 'FR',
      hybrisMediaBaseUrl:
        process.env.HYBRIS_MEDIA_BASE_URL ?? 'https://medias.interflora.fr/fstrz/r/s/c/medias.interflora.fr',
      language: 'fr',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.fr/fstrz/r/s/www.datocms-assets.com', // @todo change after all fr content has been migrated to dato
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Matin (8h - 13h)',
        [CONST.commerceTools.moment.Afternoon]: 'Après-midi (13h - 20h)',
        [CONST.commerceTools.moment.Evening]: 'Soir (17h - 20h)',
        [CONST.commerceTools.moment.Wholeday]: 'Dans la journée (8h - 20h)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [8, 13],
        [CONST.commerceTools.moment.Afternoon]: [13, 20],
        [CONST.commerceTools.moment.Evening]: [17, 20],
        [CONST.commerceTools.moment.Wholeday]: [8, 20],
      },
      mourningDeliveryModes: Object.values(CONST.cart.mourningDeliveryModeMap),
      product: {
        category: {
          allSlug: 'tout-voir',
        },
      },
      seo: {
        category: {
          description:
            'Entrega rápida de %s a domicilio por nuestros floristas. Regala %s con Interflora y disfruta de la mejor calidad.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            'Descubre nuestros consejos sobre el mundo de las flores y plantas. Cuidados y guía para regalar flores de la mejor calidad',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Consegna fiori in %s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Consegna fiori in %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description: '',
          metaTitle: 'Fleuristes %s – Fleuristes Interflora avec livraison à domicile',
          title: 'Fleuristes Interflora %s',
        },
        floristTown: {
          description:
            'Interflora entrega flores el mismo día en %s o donde sea. Los floristas entregan sus flores a domicilio en toda España.',
          title: 'Floristerías en %s con envío a domicilio - Interflora',
        },
        home: {
          description:
            'Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Interflora : Consegna Fiori a Domicilio e Piante Online',
        },
        product: {
          description:
            '%s - Enviar flores en España con entrega en el día o con el servicio internacional. Interflora envío de flores y plantas a domicilio.',
          title: '%s - Interflora',
        },
      },
      storeLocator: {
        customPath: '/carte-cadeau-fleuristes',
        path: '/fleuristes',
      },
      timezone: 'Europe/Paris',
    },
    // florist catalog dynamically created at bottom
  },
  availability: {
    baseUrl: 'http://availability.itf-ms/availability/api/v1/Availability',
    invalidPriceIsOutOfStock: true,
  },
  bloomreach: {
    enabled: false,
  },
  categories: {
    categoryTitle: {
      exclude: ['defaultCTCategory'],
    },
    defaultCategoryKey: 'default-fr',
  },
  checkout: {
    forced3DSProducts: [
      'D1', // Champagne Devaux Interflora
    ],
  },
  cms: {
    blocks: {
      teaser: {
        oneLineTeaser: true,
      },
    },
    breadcrumbs: {
      categories: {
        displayForRootCategories: true,
      },
    },
    categories: {
      allowedParentSlugs: {
        belier: ['bouquets-signes-astrologiques'],
        'cadeau-mari': ['cadeau-homme'],
        'cadeau-marraine': ['cadeau-femme'],
        'cadeau-parrain': ['cadeau-homme'],
        'coupe-plantes-deuil-aix-en-provence': ['deuil'],
        'coupe-plantes-deuil-amiens': ['deuil'],
        'coupe-plantes-deuil-lyon': ['deuil'],
        'coupe-plantes-deuil-toulouse': ['deuil'],
        'deuil-couronnes-le-havre': ['deuil'],
        'deuil-coussins-bordeaux': ['deuil'],
        'deuil-coussins-toulon': ['deuil'],
        'deuil-coussins-tours': ['deuil'],
        'fleurs-deuil-homme-aix-en-provence': ['deuil'],
        'fleurs-deuil-homme-amiens': ['deuil'],
        'fleurs-deuil-homme-besancon': ['deuil'],
        'fleurs-deuil-homme-brest': ['deuil'],
        'fleurs-deuil-homme-marseille': ['deuil'],
      },
    },
  },
  commerceTools: {
    priceChannelKey: 'interflora.fr',
    productList: {
      // "hidden" filters: usable but not exposed to user or facets
      extraFilters: {
        NO_EXTERNAL_PRODUCT: ['variants.attributes.external_url:missing'], // No cadeaux.com products
        NO_GIFT_CARD: ['key:range (* to "ITFGC"), ("ITFGCZZZZ" to *)'], // No Interflora giftcard
      },
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Couleur',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Envoyé par',
          labels: {
            Carrier: 'Transporteur',
            Florist: 'Fleuriste',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.scopedPrice.value.centAmount',
          label: 'Budget',
          ranges: [{ from: 0, label: 'Tout', to: null }],
          type: 'min-max',
        },
        ZONE_GEO: {
          // order is important, this filter must go last.
          field: 'variants.attributes.zone.key',
          label: 'Destination',
          labels: {
            CORSE: 'Corse',
            DOMTOM: 'DROM-COM',
          },
          type: 'terms',
        },
        // eslint-disable-next-line sort-keys
        STYLE: false,
      },
      sortFilter: {
        label: 'Trier par',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Prix croissant', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Prix décroissant', value: 'price desc' },
        ],
      },
    },
  },
  datocms: {
    product: {
      zonesMapping: {
        afterInfo: CONST.cms.zones.after_product,
      },
    },
  },
  discountCodeMs: {
    baseUrl: 'http://itfdiscountcodesync.itf-ms/itfdiscountcodesync/api/v1/DiscountCode/',
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Indiquez la date de livraison souhaitée',
    },
  },
  forms: {
    // nb: completed below
    [CONST.form.placements.account]: {
      [CONST.user.userType.b2c]: {
        [CONST.form.steps.create]: [
          {
            fields: [CONST.form.fields.email, CONST.form.fields.password],
            label: 'Vos identifiants',
          },
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.phone,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.townAndValid,
              CONST.form.fields.additionalInfo,
            ],
            label: 'Vos informations personnelles',
          },
        ],
        [CONST.form.steps.update]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.townAndValid,
              CONST.form.fields.additionalInfo,
            ],
            label: 'Vos informations personnelles',
          },
        ],
      },
      [CONST.user.userType.b2b]: {
        [CONST.form.steps.create]: [
          {
            fields: [CONST.form.fields.email, CONST.form.fields.password],
            label: 'Vos identifiants',
          },
          {
            fields: [
              CONST.form.fields.username,
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.townAndValid,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.legal,
            ],
            label: 'Vos informations personnelles',
          },
        ],
        [CONST.form.steps.update]: [
          {
            fields: [
              CONST.form.fields.username,
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.townAndValid,
              CONST.form.fields.additionalInfo,
            ],
            label: 'Vos informations personnelles',
          },
        ],
      },
    },
    [CONST.form.placements.funnel]: {
      [CONST.user.userType.b2c]: {
        [CONST.form.steps.default]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.city,
            ],
          },
        ],
        [CONST.form.steps.self]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.city,
            ],
          },
        ],
        [CONST.form.steps.sender]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.postalCode,
              CONST.form.fields.city,
            ],
          },
        ],
      },
    },
  },
  frisbii: {
    checkout: {
      locale: 'fr_FR',
      options: {
        detailedInvoice: false, // sends the details of the items in the cart to frisbii for invoicing
        settle: true, // payments need to be captured afterward (manually or by back office),
        ttl: 'P3D', // weird format, check https://docs.frisbii.com/reference/createchargesession
      },
    },
  },
  gfs: {
    categories: {
      all_occasions: { name: 'Tout voir' },
      birth: { name: 'Naissance' },
      birthday: { name: 'Anniversaire' },
      funeral: { name: 'Deuil' },
      love: { name: 'Amour' },
      wedding: { name: 'Mariage' },
    },
    customShipping: {
      // centAmount
      externalTaxRate: {
        amount: 0,
        // 0 to 1,
      },
      name: 'Internationnal shipping method',
      shippingCosts: 1790, // in cents,
    },
  },
  occasionReminders: {
    baseUrl: 'http://itoccasionreminders.itf-ms/itoccasionreminders/',
  },
  order: {
    reasons: [
      { id: '01', label: 'Anniversaire' },
      { id: '03', label: 'Pour le plaisir' },
      { id: '05', label: 'Remerciements' },
      { id: '06', label: 'Deuil' },
      { id: '09', label: 'Naissance' },
      { id: '10', label: 'Amour' },
      { id: '11', label: 'Baptême' },
      { id: '13', label: 'Anniversaire de mariage' },
      { id: '16', label: 'Saint-Valentin' },
      { id: '17', label: 'Fête des Mères' },
      { id: '23', label: 'Pâques' },
      { id: '24', label: 'Fête des Pères' },
      { id: '93', label: 'Toussaint' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.CHECKOUT,
    methods: [
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: true,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.APPLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GOOGLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.ALMA,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GIFT_CARD,
        provider: CONST.payment.provider.BUYBOX,
      },
      {
        allowedForMobileApp: true,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        allowedForMobileApp: true,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GOOGLEPAY,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        allowedForMobileApp: true,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.APPLEPAY,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.FRISBII,
      },
    ],
  },
  paymentMS: {
    baseUrl: 'http://payment.itf-ms/payment/api/v1/Payment/',
  },
  products: {
    interfloraplus: {
      deliveryPriceThreshold: 1,
      selectedByDefault: false,
    },
    notMainCartItemProducts: {
      keys: ['ITFPLUS', '02'],
      productTypes: [
        CONST.product.productType.ACCESSORIES,
        CONST.product.productType.CARD,
        CONST.product.productType.SERVICES,
      ],
    },
    qualifiers: {
      size: {
        slug: 'code',
      },
      zone: {
        enabled: true,
      },
    },
    tombDelivery: {
      key: '02',
    },
  },
  sea: {
    gs: {
      // google shopping
      datocmsEnv: 'googleshopping',
      priceChannelKey: 'googleshopping.fr',
      slugSuffix: '-gs',
    },
  },
  selfCare: {
    baseUrl: 'http://selfcare.itf-ms/selfcare/api/v1/selfcare',
  },
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.itf-ms/itsequencegenerator/api/v1/Sequence',
  },
  tokens: {
    checkoutProcessingChannelId: 'pc_7esv6iggsjqe3pfh56wcdne3uy',
  },
}

config.apps.catalog = merge({}, config.apps.apps)

config.forms[CONST.form.placements.funnel][CONST.user.userType.b2b] =
  config.forms[CONST.form.placements.funnel][CONST.user.userType.b2c]

module.exports = config
