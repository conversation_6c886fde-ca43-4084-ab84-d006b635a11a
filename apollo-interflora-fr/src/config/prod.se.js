// @TODO: Update keys, URLs and headers
const { join } = require('node:path')
const { rootDir } = require('../helpers/project')
/** @type {AppConfiguration} */
module.exports = {
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.se',
    },
  },
  awardit: {
    baseUrl: 'https://mvalue.mvoucher.se/api/',
  },
  bankId: {
    baseUrl: 'https://appapi2.bankid.com/rp/v6.0/',
    ca: join(rootDir(), 'cert/bankid/prod.ca'),
  },
  bloomreach: {
    enabled: false,
  },
  datocms: {
    token: '8c80895827ae527d359189748c8f08',
  },
  seServices: {
    api: {
      baseUrl: 'https://interflora-api-management.azure-api.net/octopusapi',
    },
    florist: {
      baseUrl: 'https://interflora-api-management.azure-api.net/florist-service-api/broker',
    },
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:se-prod.2f6a435489d8b9e9264df14a680faafaf78959e7d86469dee6332aa7',
    },
  },
}
