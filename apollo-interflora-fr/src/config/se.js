const CONST = require('../const')
const { join } = require('node:path')
const { rootDir } = require('../helpers/project')

module.exports = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 40,
    },
  },
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.se',
      breadcrumbsHome: 'Hem - Blomsterleverans',
      ceremonyTimeRanges: {
        default: {
          from: {
            hours: 9,
            minutes: 30,
          },
          margin: 4 * 3600 * 1000, // 4h in ms
          to: {
            hours: 20,
            minutes: 0,
          },
        },
        // Specific supplementary rule if the ceremony is today
        today: {
          from: {
            hours: 10,
            minutes: 0,
          },
        },
      },
      commerceTools: {
        locale: {
          fallbacks: ['sv-SE', 'sv'],
        },
        storeKey: 'ITS',
      },
      country: 'SE',
      currency: 'SEK',
      currencyName: 'Swedish Krona',
      currencySymbole: 'kr',
      errors: {
        login: 'Användarnamnet eller lösenordet är felaktigt.',
        loginBlocked: 'Ditt konto är blockerat. Vänligen kontakta kundservice',
        loginUnconfirmed: 'Ditt konto har redan skapats. Vi väntar på din bekräftelse via e-post',
        password:
          'Vi beklagar. Vi kunde inte identifiera dig med den angivna informationen. Försök att ange uppgifterna igen.',
      },
      language: 'sv',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.se/fstrz/r/s/www.datocms-assets.com',
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Morgon (9:00 - 14:00)',
        [CONST.commerceTools.moment.Afternoon]: 'Eftermiddag (15:00 - 20:00)',
        [CONST.commerceTools.moment.Wholeday]: 'Hela dagen (9:00 - 21:00)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [9, 14],
        [CONST.commerceTools.moment.Afternoon]: [15, 20],
        [CONST.commerceTools.moment.Wholeday]: [9, 21],
      },
      mourningDeliveryModes: Object.values(CONST.cart.mourningDeliveryModeMap).filter(
        (v) =>
          ![
            CONST.cart.mourningDeliveryModeMap.GRAVE,
            CONST.cart.mourningDeliveryModeMap.tombe,
            CONST.cart.mourningDeliveryModeMap.domicile,
          ].includes(v)
      ),
      product: {
        category: {
          allSlug: 'search',
        },
      },
      seo: {
        category: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Blommor leverans i %s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: 'Blommor leverans i %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description: '',
          metaTitle: 'Florister %s – Interflora florister med hemleverans',
          title: 'Interflora florister %s',
        },
        home: {
          description:
            'Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: 'Interflora: Blommor hemleverans och växter online',
        },
        product: {
          description:
            '%s - Interflora: första nätbutiken för blommor och växter för försäljning och hemleverans; leveranser över hela världen och Sverige samma dag.',
          title: '%s - Interflora',
        },
      },
      storeLocator: {
        customPath: '/hitta-butik',
        path: '/florister',
      },
      timezone: 'Europe/Stockholm',
      towns: {
        max: 70,
      },
    },
  },
  availability: {
    baseUrl: 'http://availability.its-ms/availability/api/v1/Availability',
  },
  awardit: {
    baseUrl: 'https://mvalue-stage.mvoucher.se/api/',
    reservationExpMinutes: 60,
  },
  // @TODO: Update section
  axerve: {
    TRALimit: 250,
    redirect3DSUrl: 'https://sandbox.gestpay.net/pagam/pagam3d.aspx',
    redirectPaymentUrl: 'https://sandbox.gestpay.net/pagam/pagam.aspx',
    rest: {
      baseUrl: 'https://sandbox.gestpay.net/api/v1',
    },
    shopLogin: 'GESPAY90162',
    soap: {
      cryptWSDL: 'https://sandbox.gestpay.net/gestpay/GestPayWS/WsCryptDecrypt.asmx?wsdl',
    },
    vendorName: 'Interflora',
  },
  bankId: {
    baseUrl: 'https://appapi2.test.bankid.com/rp/v6.0/',
    ca: join(rootDir(), 'cert/bankid/test.ca'),
  },
  bloomreach: {
    enabled: false,
  },
  commerceTools: {
    hasUsers: false,
    priceChannelKey: 'interflora.se',
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Colore',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Leveranssätt',
          labels: {
            Carrier: 'Bud',
            Florist: 'Florist',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.price.centAmount',
          label: 'Pris',
          ranges: [{ from: 0, label: 'All', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Sortera efter',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Pris stigande', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Pris fallande', value: 'price desc' },
        ],
      },
    },
    rootProductsCategoryKey: 'sweden-products',
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Ange önskat leveransdatum',
    },
  },
  frisbii: {
    checkout: {
      locale: 'sv_SE',
      options: {
        detailedInvoice: true, // sends the details of the items in the cart to frisbii for invoicing
        settle: false, // payments need to be captured afterward (manually or by back office),
        ttl: 'P3D', // weird format, check https://docs.frisbii.com/reference/createchargesession
      },
    },
  },
  gfs: {
    customShipping: {
      // centAmount
      externalTaxRate: {
        amount: 0,
        // 0 to 1,
      },
      name: 'International shipping method',
      shippingCosts: 149 * 100,
    },
    fromEurToLocalCurrencyRate: 11,
    highlightedCountries: ['NO', 'FI', 'DK', 'GB', 'DE', 'US', 'ES', 'IS', 'FR', 'PL', 'NL', 'EE', 'SE'],
  },
  graphql: {
    dataloaders: {
      categories: {
        ttl: 60 * 60, // 1 hour
      },
      configuration: {
        ttl: 60 * 60, // 1 hour
      },
      defaultBlocks: {
        ttl: 60 * 60, // 1 hour
      },
      delivery: {
        ttl: 30,
      },
      getDepartments: {
        ttl: 60 * 60, // 1 hour
      },
      mainNavigation: {
        ttl: 60 * 60, // 1 hour
      },
      pages: {
        ttl: 60 * 60, // 1 hour
      },
      productCodesBySlugUrls: {
        ttl: 60 * 60, // 1 hour
      },
      products: {
        ttl: 60 * 60, // 1 hour
      },
      storeLocatorHome: {
        ttl: 60 * 60, // 1 hour
      },
    },
  },
  hitta: {
    baseUrl: 'https://api.hitta.se',
    callerId: 'Interflora',
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600, // 12h in s - NB: FR value is in ms
  },
  order: {
    archive: {
      lifetime: {
        hours: 0,
        minutes: 0,
        mounths: 2,
        seconds: 0,
        years: 0,
      },
    },
    reasons: [
      { id: '1', label: 'Födelsedag' },
      { id: '3', label: 'För nöje' },
      { id: '5', label: 'Tack' },
      { id: '6', label: 'Sorg' },
      { id: '9', label: 'Födelse' },
      { id: '10', label: 'Kärlek' },
      { id: '11', label: 'Dop' },
      { id: '13', label: 'Bröllopsdag' },
      { id: '16', label: 'Alla hjärtans dag' },
      { id: '17', label: 'Mors dag' },
      { id: '23', label: 'Påsk' },
      { id: '24', label: 'Fars dag' },
      { id: '93', label: 'Alla helgons dag' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.FRISBII,
    methods: [
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.FRISBII,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.FACTURE,
        provider: CONST.payment.provider.NONE,
      },
    ],
  },
  products: {
    ribbon: {
      key: 'RIBBONTEXT',
    },
  },
  seServices: {
    api: {
      apiPath: {
        CUSTOMER: '/customer',
        CUSTOMER_FORGOT_PASSWORD: '/customer/forgotpassword',
        CUSTOMER_PASSWORD: '/customer/password',
        CUSTOMER_PRIVATE: '/customer/private',
        LOGIN_WITH_SSN: '/login/ssn',
        OAUTH_TOKEN: '/oauth/token',
        ORDER_HISTORY: '/order/history',
        ORGANIZATION: '/organization',
        SOFT_LOGIN: '/login/voyado',
      },
      baseUrl: 'https://interflora-api-management.azure-api.net/octopusapi-itg',
    },
    florist: {
      baseUrl: 'https://interflora-api-management.azure-api.net/florist-service-api-itg/broker',
    },
    timeout: 15000,
  },
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.its-ms/itsequencegenerator/api/v1/Sequence',
  },
}
