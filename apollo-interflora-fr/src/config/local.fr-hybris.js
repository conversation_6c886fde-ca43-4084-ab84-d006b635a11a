const { decrypt } = require('../helpers/crypto')
module.exports = {
  apps: {
    apps: {
      mediaBaseUrl:
        process.env.MEDIA_BASE_URL ?? 'https://medias-preprod.interflora.fr/fstrz/r/s/c/medias-preprod.interflora.fr',
    },
  },
  datocms: {
    environment: 'main-fr',
  },
  interflora: {
    host: 'https://preprod-api.interflora.fr/api',
  },
  selfCare: {
    baseUrl: 'https://microservices.recette.interflora.bzh/selfcare/api/v1/Selfcare/',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  solr: {
    baseUrl: 'https://preprod-solr-ovh.interflora.fr:10443/solr',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-dev.e5bfaa64d9a78183add6cf9b6d2ce5413c85f008f2342363b9ddd41d',
    },
  },
}
