module.exports = {
  commerceTools: {
    auth: {
      // end-user scope only, is actually not secret.
      credentials: {
        clientId: 'SZ1h1Ay2CDsOsoI-u1-XFvNx',
        clientSecret: 'HDt9_fTeDQmj9-AgPUJ8JumrC5_DF0Jj',
      },
      projectKey: 'myflower-prod1',
      scopes: [
        'manage_my_orders:myflower-prod1 manage_my_payments:myflower-prod1 manage_my_profile:myflower-prod1 view_categories:myflower-prod1 view_published_products:myflower-prod1 manage_my_shopping_lists:myflower-prod1 create_anonymous_token:myflower-prod1',
      ],
    },
  },
  datocms: {
    token: 'd1f397171afe3f00fa748284cf5953', // read-only
  },
  server: {
    cors: {
      'Access-Control-Allow-Origin': /^https:\/\/.*\.interflora\.(fr|it|es|pt|se|dk)$/,
    },
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-test.f15419396425ffd84045dbf62fff1e5254ace1128ea9ec246583e555',
    },
  },
}
