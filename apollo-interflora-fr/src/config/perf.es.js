const { decrypt } = require('../helpers/crypto')

module.exports = {
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.perf.interflora.es',
      checkout: {
        processingChannelId: 'pc_lgn4mxnnab2eljtfdlwsqodli4',
      },
    },
  },
  availability: {
    baseUrl: 'https://microservices.recette.interflora.it/availability/api/v1/Availability', // @todo
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    checkoutAuthSk: 'sk_sbox_ngmvxbbg7fcehcjnth3u645beyp',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:es-test.d0a742948236ffbf5e7a68c7f88786ec9f0cd47b7c2392d74b6fe8ac',
    },
  },
}
