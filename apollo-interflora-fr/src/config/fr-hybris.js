const { merge } = require('lodash')

const CONST = require('../const')

const config = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 30,
    },
  },
  address: {
    mandatoryFields: {
      addressBook: [CONST.address.addressBookMandatoryFields.civility],
    },
  },
  apps: {
    // because apps = baseId for now
    apps: {
      baseUrl: process.env.BASE_URL || 'https://mobile.interflora.fr',
      bloomreach: {
        enabled: false,
      },
      breadcrumbsHome: 'Accueil - Livraison fleurs',
      cms: {
        emptyContentPageUid: 'defaultApiPage',
      },
      commerceTools: {
        locale: {
          fallbacks: ['fr-FR', 'fr'],
        },
        storeKey: 'ITF',
      },
      country: 'FR',
      currency: 'EUR',
      defaultZone: 'FR',
      language: 'fr',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.fr',
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Matin (8h - 13h)',
        [CONST.commerceTools.moment.Afternoon]: 'Après-midi (13h - 20h)',
        [CONST.commerceTools.moment.Evening]: 'Soir (17h - 20h)',
        [CONST.commerceTools.moment.Wholeday]: 'Dans la journée (8h - 20h)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [8, 13],
        [CONST.commerceTools.moment.Afternoon]: [13, 20],
        [CONST.commerceTools.moment.Evening]: [17, 20],
        [CONST.commerceTools.moment.Wholeday]: [8, 20],
      },
      product: {
        category: {
          allSlug: 'tout-voir',
        },
      },
      seo: {
        floristDepartment: {
          description: '',
          metaTitle: 'Fleuristes %s – Fleuristes Interflora avec livraison à domicile',
          title: 'Fleuristes Interflora %s',
        },
      },
      storeLocator: {
        customPath: '/carte-cadeau-fleuristes',
        path: '/fleuristes',
      },
    },
    // florist catalog dynamically created at bottom
  },
  categories: {
    pageSize: 21,
  },
  checkout: {
    forced3DSProducts: [
      'D1', // Champagne Devaux Interflora
    ],
  },
  commerceTools: {
    priceChannelKey: 'interflora.fr',
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Couleur',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Livré par',
          labels: {
            Carrier: 'Transporteur',
            Florist: 'Fleuriste',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.scopedPrice.value.centAmount',
          label: 'Budget',
          ranges: [{ from: 0, label: 'Tout', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Trier par',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Prix croissant', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Prix décroissant', value: 'price desc' },
        ],
      },
    },
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Indiquez la date de livraison souhaitée',
    },
  },
  forms: {
    // nb: completed below
    [CONST.form.placements.account]: {
      [CONST.user.userType.b2c]: {
        [CONST.form.steps.create]: [
          {
            fields: [CONST.form.fields.email, CONST.form.fields.password],
            label: 'Vos identifiants',
          },
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.phone,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.townAndValid,
            ],
            label: 'Vos informations personnelles',
          },
        ],
        [CONST.form.steps.update]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.townAndValid,
            ],
            label: 'Vos informations personnelles',
          },
        ],
      },
      [CONST.user.userType.b2b]: {
        [CONST.form.steps.create]: [
          {
            fields: [CONST.form.fields.email, CONST.form.fields.password],
            label: 'Vos identifiants',
          },
          {
            fields: [
              CONST.form.fields.username,
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.townAndValid,
              CONST.form.fields.legal,
            ],
            label: 'Vos informations personnelles',
          },
        ],
        [CONST.form.steps.update]: [
          {
            fields: [
              CONST.form.fields.username,
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.townAndValid,
            ],
            label: 'Vos informations personnelles',
          },
        ],
      },
    },
    [CONST.form.placements.funnel]: {
      [CONST.user.userType.b2c]: {
        [CONST.form.steps.default]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.city,
            ],
          },
        ],
        [CONST.form.steps.self]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.city,
            ],
          },
        ],
        [CONST.form.steps.sender]: [
          {
            fields: [
              CONST.form.fields.title,
              CONST.form.fields.firstName,
              CONST.form.fields.lastName,
              CONST.form.fields.company,
              CONST.form.fields.phone,
              CONST.form.fields.email,
              CONST.form.fields.country,
              CONST.form.fields.address,
              CONST.form.fields.additionalInfo,
              CONST.form.fields.postalCode,
              CONST.form.fields.city,
            ],
          },
        ],
      },
    },
  },
  hybris: {
    timeout: 30000,
  },
  order: {
    reasons: [
      { id: '1', label: 'Anniversaire' },
      { id: '3', label: 'Pour le plaisir' },
      { id: '5', label: 'Remerciements' },
      { id: '6', label: 'Deuil' },
      { id: '9', label: 'Naissance' },
      { id: '10', label: 'Amour' },
      { id: '11', label: 'Baptême' },
      { id: '13', label: 'Anniversaire de mariage' },
      { id: '16', label: 'Saint-Valentin' },
      { id: '17', label: 'Fête des Mères' },
      { id: '23', label: 'Pâques' },
      { id: '24', label: 'Fête des Pères' },
      { id: '93', label: 'Toussaint' },
    ],
  },
  product: {
    stockErrors: {
      productNotFound: 'UnknownIdentifierError',
      stockIsUnlimited: 'StockLevelNotFoundError',
      updateFailed: 'StockLevelUpdateError',
    },
  },
  selfCare: {
    baseUrl: 'http://selfcare.itf-ms/selfcare/api/v1/selfcare',
  },
  tokens: {
    checkoutProcessingChannelId: 'pc_7esv6iggsjqe3pfh56wcdne3uy',
  },
}

config.apps.catalog = merge({}, config.apps.apps, {
  showCrossSells: false,
})

config.forms[CONST.form.placements.funnel][CONST.user.userType.b2b] =
  config.forms[CONST.form.placements.funnel][CONST.user.userType.b2c]

module.exports = config
