const { decrypt } = require('../helpers/crypto')

module.exports = {
  apps: {
    apps: {
      checkout: {
        processingChannelId: 'pc_3yl5nbrnfesurk77axgav6ipne',
      },
    },
  },
  availability: {
    baseUrl: 'https://microservices.recette.interflora.pt/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  checkout: {
    mbway: {
      testMode: true,
    },
  },
  datocms: {
    environment: 'main-pt',
  },
  gfs: {
    baseUrl: 'https://microservices.dev.interflora.pt/gfssupplier/api/v1',
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.pt/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    checkoutAuthSk: 'sk_sbox_armayj756zyxdtpv3uwhujiycqg',
    checkoutAuthWebhook: 'ca62d4e4-0e65-45f6-a6fe-8d62781609de',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:pt-dev.1f6a65508e8060e2de52eaadcf496ab8bb0bb0857594234ae6a9cac2',
    },
  },
}
