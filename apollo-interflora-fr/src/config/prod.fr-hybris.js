module.exports = {
  apps: {
    apps: {
      baseUrl: 'https://www.interflora.fr',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://medias.interflora.fr/fstrz/r/s/c/medias.interflora.fr',
    },
  },
  datasources: {
    timeout: 15000,
  },
  datocms: {
    token: '0a3b49d5a90ba97c0feb0f9bee15ec', // read-only
  },
  interflora: {
    host: 'https://prod-api.interflora.fr/api',
  },
  server: {
    cors: { 'Access-Control-Allow-Origin': /^https:\/\/.*\.interflora\.fr$/ },
  },
  solr: {
    baseUrl: 'https://prod-solr-ovh.interflora.fr:10443/solr',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-prod.e020a186023b46a537f51fd6f67acecb1f55b07d1873f2952fe0c17d',
    },
  },
}
