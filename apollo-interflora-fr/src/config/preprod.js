module.exports = {
  bloomreach: {
    enabled: false,
  },
  commerceTools: {
    auth: {
      // end-user scope only, is actually not secret.
      credentials: {
        clientId: 'vCq3LUNz6J-N3N08nL-O2QqQ',
        clientSecret: 'WH4QvZu434nFrzO_hM--VSflsQKbMP5a',
      },
      projectKey: 'myflower-preprod',
      scopes: [
        'manage_my_orders:myflower-preprod manage_my_payments:myflower-preprod manage_my_profile:myflower-preprod view_categories:myflower-preprod view_published_products:myflower-preprod manage_my_shopping_lists:myflower-preprod create_anonymous_token:myflower-preprod',
      ],
    },
  },
  datocms: {
    token: '9f91c292487bdf15c870a5c30d7b8c', // read-only
  },
  server: {
    cors: { 'Access-Control-Allow-Origin': /^https:\/\/.*\.interflora\.(fr|it|es|pt|se|dk)$/ },
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-test.f15419396425ffd84045dbf62fff1e5254ace1128ea9ec246583e555',
    },
  },
}
