const CONST = require('../const')
const { merge } = require('lodash')

/** @type {AppConfiguration} */
const config = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 40,
    },
  },
  address: {
    mandatoryFields: {
      addressBook: [CONST.address.addressBookMandatoryFields.civility],
    },
  },
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.it',
      breadcrumbsHome: 'Home - Fiori a domicilio',
      ceremonyTimeRanges: {
        default: {
          from: {
            hours: 9,
            minutes: 30,
          },
          margin: 4 * 3600 * 1000, // 4h in ms
          to: {
            hours: 20,
            minutes: 0,
          },
        },
        // Specific supplementary rule if the ceremony is today
        today: {
          from: {
            hours: 10,
            minutes: 0,
          },
        },
      },
      commerceTools: {
        locale: {
          fallbacks: ['it-IT', 'it'],
        },
        storeKey: 'ITI',
      },
      country: 'IT',
      currency: 'EUR',
      currencyName: 'Euro',
      currencySymbole: '€',
      discountCode: {
        unknown: "C'è stato un errore.",
      },
      errors: {
        login: 'Il nome utente o la password sono errati.',
        loginBlocked: 'Il suo conto è bloccato. Si prega di contattare il servizio clienti',
        loginUnconfirmed: 'Il suo account è già stato creato. Attendiamo la vostra conferma via e-mail',
        password:
          'Ci dispiace. Non siamo stati in grado di identificarti con le informazioni fornite. Prova a inserire di nuovo i dati.',
      },
      language: 'it',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.it/fstrz/r/s/www.datocms-assets.com',
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Mattina (9:00 - 14:00)',
        [CONST.commerceTools.moment.Afternoon]: 'Pomeriggio (15:00 - 20:00)',
        [CONST.commerceTools.moment.Wholeday]: 'Intera Giornata (9:00 - 21:00)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [9, 14],
        [CONST.commerceTools.moment.Afternoon]: [15, 20],
        [CONST.commerceTools.moment.Wholeday]: [9, 21],
      },
      mourningDeliveryModes: Object.values(CONST.cart.mourningDeliveryModeMap).filter(
        (v) => ![CONST.cart.mourningDeliveryModeMap.GRAVE, CONST.cart.mourningDeliveryModeMap.tombe].includes(v)
      ),
      product: {
        category: {
          allSlug: 'search',
        },
      },
      seo: {
        category: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Consegna fiori in %s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Consegna fiori in %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description: '',
          metaTitle: 'Fioristi %s – Fiorai Interflora con consegna a domicilio',
          title: 'Fiorai Interflora %s',
        },
        home: {
          description:
            'Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Interflora : Consegna Fiori a Domicilio e Piante Online',
        },
        product: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
      },
      storeLocator: {
        path: '/fiorai',
      },
      timezone: 'Europe/Rome',
      towns: {
        max: 70,
      },
    },
  },
  availability: {
    baseUrl: 'http://availability.iti-ms/availability/api/v1/Availability',
  },
  axerve: {
    TRALimit: 250,
    redirect3DSUrl: 'https://sandbox.gestpay.net/pagam/pagam3d.aspx',
    redirectPaymentUrl: 'https://sandbox.gestpay.net/pagam/pagam.aspx',
    rest: {
      baseUrl: 'https://sandbox.gestpay.net/api/v1',
    },
    shopLogin: 'GESPAY90162',
    soap: {
      cryptWSDL: 'https://sandbox.gestpay.net/gestpay/GestPayWS/WsCryptDecrypt.asmx?wsdl',
    },
    vendorName: 'Interflora',
  },
  bloomreach: {
    domainKey: 'interflora_eu_it',
  },
  commerceTools: {
    priceChannelKey: 'interflora.it',
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Colore',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Metodo di consegna',
          labels: {
            Carrier: 'Corriere',
            Florist: 'Fiorista',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.price.centAmount',
          label: 'Prezzo',
          ranges: [{ from: 0, label: 'All', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Ordina per',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Prezzo crescente', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Prezzo decrescente', value: 'price desc' },
        ],
      },
    },
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Indicare la data di consegna desiderata',
    },
  },
  gfs: {
    categories: {
      all_occasions: { name: 'Tutte le ricorrenze' },
      birth: { name: 'Nascita' },
      birthday: { name: 'Compleanno' },
      funeral: { name: 'Condoglianze' },
      love: { name: 'Amore' },
      wedding: { name: 'Nozze' },
    },
    customShipping: {
      // centAmount
      externalTaxRate: {
        amount: 0,
        // 0 to 1,
      },

      name: 'Internationnal shipping method',
      shippingCosts: 1499, // in cents,
    },
  },
  graphql: {
    dataloaders: {
      categories: {
        ttl: 60 * 60, // 1 hour
      },
      configuration: {
        ttl: 60 * 60, // 1 hour
      },
      defaultBlocks: {
        ttl: 60 * 60, // 1 hour
      },
      getDepartments: {
        ttl: 60 * 60, // 1 hour
      },
      mainNavigation: {
        ttl: 60 * 60, // 1 hour
      },
      pages: {
        ttl: 60 * 60, // 1 hour
      },
      productCodesBySlugUrls: {
        ttl: 60 * 60, // 1 hour
      },
      products: {
        ttl: 60 * 60, // 1 hour
      },
      storeLocatorHome: {
        ttl: 60 * 60, // 1 hour
      },
    },
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600, // 12h in s - NB: FR value is in ms
  },
  itUser: {
    baseUrl: 'https://stageoctopus.interflora.it',
  },
  order: {
    reasons: [
      { id: '1', label: 'Compleanno' },
      { id: '3', label: 'Per piacere' },
      { id: '5', label: 'Ringraziamenti' },
      { id: '6', label: 'Lutto' },
      { id: '9', label: 'Nascita' },
      { id: '10', label: 'Amore' },
      { id: '11', label: 'Battesimo' },
      { id: '13', label: 'Anniversario di matrimonio' },
      { id: '16', label: 'San Valentino' },
      { id: '17', label: 'Festa della mamma' },
      { id: '23', label: 'Pasqua' },
      { id: '24', label: 'Festa del papa' },
      { id: '93', label: 'Ognissanti' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.AXERVE,
    methods: [
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.AMAZON,
        provider: CONST.payment.provider.AMAZON,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.AXERVE,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.AXERVE,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.APPLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GOOGLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
    ],
  },
  products: {
    interfloraplus: {
      selectedByDefault: false,
    },
    ribbon: {
      key: 'RIBBONTEXT',
    },
  },
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.iti-ms/itsequencegenerator/api/v1/Sequence',
  },
  tokens: {
    checkoutProcessingChannelId: 'pc_v5qqg3g6vpmuxl5xzd7wd2g35q',
  },
}

config.apps.catalog = merge({}, config.apps.apps)

module.exports = config
