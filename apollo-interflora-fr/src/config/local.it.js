const { decrypt } = require('../helpers/crypto')

module.exports = {
  availability: {
    baseUrl: 'https://microservices.recette.interflora.it/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  axerve: {
    shopLogin: 'GESPAY92615',
  },
  datocms: {
    environment: 'main-it',
  },
  itUser: {
    baseUrl: 'https://preprod-api.interflora.it/',
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.it/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    axerveApiKey: decrypt(
      // 'wNmMAG2TnOmHLD5MW2qkG+tS0EufmU+tZoA8Csok7CpiRYSB6wbUSzza9339xHlUH/KD/P79VkhZCRqucHi6c6JYz8X6HuOO4zXwD+Yv8a9EKzQ4uqT3nft9Vpy+MTNRX6dprqquRsRL1nzY' // preprod
      'TkVkHvBpbWjDbrDwG3JKcyJGacmu0oKcFWtoPsVex6GmnrHZcjhb62MOGHc9RRPLSZha4tfnvRHwXAwjtdyw5CTMx8wj4InGYzZPp2BgqCekCP79ILUPxshberec4s5ZXw8r6y2ng1b+Sgn+' // uat
    ),
    checkoutAuthSk: 'sk_sbox_jxl26uy24boctmgzoegtj25qzm*',
    checkoutAuthWebhook: 'a08c1c25-e8f8-4082-8d64-79cd98422238',
    itUserBasicAuthHeader: decrypt(
      '1Yh/xDjkKKi/MDRn29o9iS5TJvsX8sTY5WEihyxRrrJ2LRSNZb3EIEWPKF3dJ8DUyXSdmlau7BpstOjUeqjNssyilW6wM6EIQ5oFqKJGcYrad7d8f5HVlvz3hvFCFSyE/yk='
    ),
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:it-dev.6804a8d0efff5802ac6768ae0cbe50f919b6d2bb5d35c2e6214e8952',
    },
  },
}
