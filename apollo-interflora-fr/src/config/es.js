const CONST = require('../const')
const { merge } = require('lodash')

/** @type {AppConfiguration} */
const config = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 40,
    },
  },
  address: {
    mandatoryFields: {
      addressBook: [CONST.address.addressBookMandatoryFields.civility],
      billing: [],
      shipping: ['address'],
    },
    strippedChars: /#/g,
  },
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.es',
      breadcrumbsHome: 'Home - Flores a domicilio',
      ceremonyTimeRanges: {
        // sunday 10 - 15h, with a cut off at 12.30 on current day
        0: {
          from: {
            hours: 10,
            minutes: 0,
          },
          margin: 2.5 * 3600 * 1000, // 2,5h in ms
          to: {
            hours: 15,
            minutes: 0,
          },
        },
        // 10 - 20h, with a cutloff at 6pm on current day
        default: {
          from: {
            hours: 10,
            minutes: 0,
          },
          margin: 2 * 3600 * 1000, // 2h in ms
          to: {
            hours: 20,
            minutes: 0,
          },
        },
      },
      commerceTools: {
        locale: {
          fallbacks: ['es-ES', 'es'],
        },
        storeKey: 'ITE',
      },
      country: 'ES',
      currency: 'EUR',
      currencyName: 'Euro',
      currencySymbole: '€',
      discountCode: {
        unknown: 'Se ha producido un error.',
      },
      errors: {
        login: 'Nombre de usuario o contraseña no válidos',
        loginBlocked: 'Tu cuenta está bloqueada. Ponte en contacto con nuestro centro de atención al cliente.',
        loginUnconfirmed: 'Tu cuenta está pendiente de confirmación por correo electrónico.',
      },
      language: 'es',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.es/fstrz/r/s/www.datocms-assets.com',
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Mañana (10:00 - 15:00)',
        [CONST.commerceTools.moment.Afternoon]: 'Tarde (17:00 - 21:00)',
        [CONST.commerceTools.moment.Wholeday]: 'Todo el dia (10:00 - 21:00)',
        [CONST.commerceTools.moment.Evening]: 'Noche (18:00 - 21:00)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [10, 15],
        [CONST.commerceTools.moment.Afternoon]: [17, 21],
        [CONST.commerceTools.moment.Wholeday]: [10, 21],
        [CONST.commerceTools.moment.Evening]: [18, 21],
      },
      mourningDeliveryModes: [
        CONST.cart.mourningDeliveryModeMap.FUNERAL_PLACE,
        CONST.cart.mourningDeliveryModeMap.domicile,
      ],
      priceChannelKey: 'interflora.es',
      product: {
        category: {
          allSlug: 'search',
        },
      },
      seo: {
        category: {
          description:
            'Entrega rápida de %s a domicilio por nuestros floristas. Regala %s con Interflora y disfruta de la mejor calidad.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            'Descubre nuestros consejos sobre el mundo de las flores y plantas. Cuidados y guía para regalar flores de la mejor calidad',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Consegna fiori in %s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Consegna fiori in %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description:
            'Interflora entrega flores el mismo día en %s o donde sea. Los floristas entregan sus flores a domicilio en toda España.',
          metaTitle: 'Floristerías en %s : enviar flores a domicilio - Interflora',
          title: 'Enviar flores a %s',
        },
        floristTown: {
          description:
            'Interflora entrega flores el mismo día en %s o donde sea. Los floristas entregan sus flores a domicilio en toda España.',
          title: 'Floristerías en %s con envío a domicilio - Interflora',
        },
        home: {
          description:
            'Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Interflora : Consegna Fiori a Domicilio e Piante Online',
        },
        product: {
          description:
            '%s - Enviar flores en España con entrega en el día o con el servicio internacional. Interflora envío de flores y plantas a domicilio.',
          title: '%s - Interflora',
        },
      },
      storeLocator: {
        customPath: '/floristerias',
        path: '/floristerias',
      },
      timezone: 'Europe/Madrid',
    },
  },
  availability: {
    baseUrl: 'http://availability.ite-ms/availability/api/v1/Availability', // @todo
  },
  bloomreach: {
    domainKey: 'interflora_eu_es',
  },
  commerceTools: {
    priceChannelKey: 'interflora.es',
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Color',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Modo de entrega',
          labels: {
            Carrier: 'Transportista',
            Florist: 'Floristería',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.scopedPrice.value.centAmount',
          label: 'Precio',
          ranges: [{ from: 0, label: 'All', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Ordenar por',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Precio ascendente', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Precio decreciente', value: 'price desc' },
        ],
      },
    },
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Indicar la fecha de entrega deseada',
    },
  },
  gfs: {
    baseUrl: 'http://gfssupplier.ite-ms/gfssupplier/api/v1',
    categories: {
      all_occasions: { name: 'Todas las ocasiones' },
      birth: { name: 'Nascimentos' },
      birthday: { name: 'Cumpleaños' },
      funeral: { name: 'Condolencias' },
      love: { name: 'Amor' },
      wedding: { name: 'Bodas' },
    },
    customShipping: {
      externalTaxRate: {
        amount: 0,
      },
      name: 'Internationnal shipping method',
      shippingCosts: 1499, // in cents
    },
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600, // 12h in s - NB: FR value is in ms
  },
  order: {
    reasons: [
      { id: '1', label: 'Cumpleaños' },
      { id: '3', label: 'Por placer' },
      { id: '5', label: '¡Gracias!' },
      { id: '6', label: 'Luto' },
      { id: '9', label: 'Nacimiento' },
      { id: '10', label: 'Amor' },
      { id: '11', label: 'Bautismo' },
      { id: '13', label: 'Aniversario de boda' },
      { id: '16', label: 'San Valentín' },
      { id: '17', label: 'Día de la Madre' },
      { id: '23', label: 'Pascua' },
      { id: '24', label: 'Día del Padre' },
      { id: '93', label: 'Día de Todos los Santos' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.CHECKOUT,
    methods: [
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.APPLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.PAYCOMET,
        provider: CONST.payment.provider.PAYCOMET,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.PAYCOMET,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GOOGLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
    ],
  },
  products: {
    ribbon: {
      key: 'CO0025',
    },
  },
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.ite-ms/itsequencegenerator/api/v1/Sequence',
  },
  tokens: {
    checkoutProcessingChannelId: 'pc_lgn4mxnnab2eljtfdlwsqodli4',
  },
  tracking: {
    meta: {
      // this pixel is used in all ES envs.
      pixelId: '1768368610043577',
    },
  },
}

config.apps.catalog = merge({}, config.apps.apps)

module.exports = config
