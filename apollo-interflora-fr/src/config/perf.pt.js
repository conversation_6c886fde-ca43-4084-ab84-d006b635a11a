const { decrypt } = require('../helpers/crypto')

module.exports = {
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.perf.interflora.pt',
      checkout: {
        processingChannelId: 'pc_3yl5nbrnfesurk77axgav6ipne',
      },
    },
  },
  availability: {
    baseUrl: 'https://microservices.recette.interflora.pt/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:pt-test.2647e6b8af983afd451f1bf1c2138a46047b779fca600a00d2f81f2c',
    },
  },
}
