const { decrypt } = require('../helpers/crypto')

module.exports = {
  apps: {
    apps: {
      checkout: {
        processingChannelId: 'pc_lgn4mxnnab2eljtfdlwsqodli4',
      },
    },
  },
  availability: {
    baseUrl: 'https://microservices.recette.interflora.es/availability/api/v1/Availability', // @todo
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  datocms: {
    environment: 'main-es',
  },
  gfs: {
    baseUrl: 'https://microservices.dev.interflora.es/gfssupplier/api/v1',
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.es/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    checkoutAuthSk: 'sk_sbox_ngmvxbbg7fcehcjnth3u645beyp',
    checkoutAuthWebhook: 'f5f0d242-c517-4ded-a59a-cb8fc1387505',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:es-dev.b0d87d4429272d15f1541016d2726bcc8f2db16fb2e800abb0604d55',
    },
  },
}
