const { decrypt } = require('../helpers/crypto')

// @TODO: Update URLs and authentication headers
module.exports = {
  availability: {
    baseUrl: 'https://microservices.recette.interflora.se/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  axerve: {
    shopLogin: 'GESPAY92615',
  },
  commerceTools: {
    rootProductsCategoryKey: 'sweden-products-root',
  },
  datocms: {
    environment: 'main-se',
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.it/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    OctopusApiKey: decrypt('0WkfjdPt7smB4gHSj225wffX5Bbzyq1LqrNqc26YX7vI5wsdS8jZA8HeDydceoQa4x3NfGB8yCTidfWPkMZVpQ=='),
    aesIV: decrypt('gKZBUSgxX/Wl5Q7E6sy6XrZ8QFGcyQfKgMs4TSVKaoPG0b13UeSyn2TSFHdB3IezjLgNylloGgU='),
    aesSecret: decrypt(
      'y21Do1uSdE2oQzlisqtzdpcuRcWBuYHTK9LyBXJr6NrGj05YPtqb2Iayn0Jco2Ybj1R7EC7TrbbQr6Rav1Oq1WgG7Wn0rIxniu6wvg=='
    ),
    awarditCert: decrypt(
      '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'
    ),
    awarditKey: decrypt(
      '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'
    ),
    axerveApiKey: decrypt(
      // 'wNmMAG2TnOmHLD5MW2qkG+tS0EufmU+tZoA8Csok7CpiRYSB6wbUSzza9339xHlUH/KD/P79VkhZCRqucHi6c6JYz8X6HuOO4zXwD+Yv8a9EKzQ4uqT3nft9Vpy+MTNRX6dprqquRsRL1nzY' // preprod
      'TkVkHvBpbWjDbrDwG3JKcyJGacmu0oKcFWtoPsVex6GmnrHZcjhb62MOGHc9RRPLSZha4tfnvRHwXAwjtdyw5CTMx8wj4InGYzZPp2BgqCekCP79ILUPxshberec4s5ZXw8r6y2ng1b+Sgn+' // uat
    ),
    bankIdPassPhrase: decrypt('XkCJLa8+6K6m7Ul/d98gCo0w86oNi0UZZNwNXR4kZVVu6P2d0C6sTlg='),
    bankIdPfx: decrypt(
      '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'
    ),
    billwerkKey: decrypt(
      'TdNV0+rfReCLOAX8w1VYDVWOX4hHyUtqwU82yWIDLdOGhTrwArTvY4vdGhdWdwCnujpZbwXv1h1nH28qWTDeQ83D1jQ7'
    ),
    billwerkSignatureSecret: decrypt(
      'PIf2nVc9dR0oBP6vRBBlN6VTWP6tEJXdfgSRouyirqjRS3aSykbydigbGMa9rg+1866Ns+m7+HrvqhBOpyXo+xfN289IERl4+40sTSzPQg=='
    ),
    funeralBasic: decrypt(
      '9SIPvSUSDmVs0lQ+6577ouJCIBubfQKyZeRgJQrZ6AyDFeBlBo4kFEodZ3cQzCX39m5g4PvOylcOtiH4zvJZnn1H7XZYNlEbApg+FoHjtOQ='
    ),
    hittaApiKey: decrypt(
      'UNPL6m2fp4QDG/QcroUkAYcCdfnHt7gA6TTXXhwcStEyPK+s7Rio5yzg1oLYzhWkD67Fr0VxNelBvBcLWxO0AfbQGeNH+BHU'
    ),
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:se-dev.ec3e1dae4e0319403ae9c6462df168c5e1c869cd7136c6a09b5268fa',
    },
  },
}
