const { decrypt } = require('../helpers/crypto')

module.exports = {
  availability: {
    baseUrl: 'https://microservices.recette.interflora.bzh/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  datocms: {
    environment: 'main-fr',
  },
  discountCodeMs: {
    baseUrl: 'https://microservices.recette.interflora.bzh/itfdiscountcodesync/api/v1/DiscountCode/',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  interflora: {
    host: 'https://preprod-api.interflora.fr/api',
  },
  occasionReminders: {
    baseUrl: 'https://microservices.recette.interflora.bzh/itoccasionreminders/',
  },
  paymentMS: {
    baseUrl: 'https://microservices.dev.interflora.bzh/payment/api/v1/Payment/',
    headers: {
      Authorization: `Basic aW50ZXJmbG9yYTp4bzg3ZHM0cG5pNzY5Nzg0bjNzdDh2MXE=`,
      'Content-Type': 'application/json',
    },
  },
  selfCare: {
    baseUrl: 'https://microservices.recette.interflora.bzh/selfcare/api/v1/Selfcare/',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.bzh/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  solr: {
    baseUrl: 'https://preprod-solr-ovh.interflora.fr:10443/solr',
  },
  tokens: {
    billwerkKey: 'priv_0305a46566c18759b79656f740a59522',
    billwerkSignatureSecret: 'webhook_secret_128fb3193bd0f860e88ede80ad60664c',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-dev.e5bfaa64d9a78183add6cf9b6d2ce5413c85f008f2342363b9ddd41d',
    },
  },
}
