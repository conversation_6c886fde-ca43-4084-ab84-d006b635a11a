const { decrypt } = require('../helpers/crypto')
const secrets = require('./local.secrets')

/** @typedef {import('../../_typedef')} */

/** @type {AppConfiguration} */
module.exports = {
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
    },
  },
  datocms: {
    token: 'b81c2cb9b930ceff89e98d9937414b', // read-only
  },
  gfs: {
    baseUrl: 'https://microservices.dev.interflora.pt/gfssupplier/api/v1',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  logger: {
    main: {
      level: 'debug',
    },
  },
  paypal: {
    webhookId: '6R94435750175653S',
  },
  server: {
    connectionTimeout: 30000,
    cors: {
      'Access-Control-Allow-Origin':
        /^https?:\/\/(localhost(:\d+)?|[^.]+\.local\.interflora\.fr(:\d+)?|10\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?|172\.[1-3]\d\.\d{1,3}\.\d{1,3}(:\d+)?|192\.168\.\d{1,3}\.\d{1,3}(:\d+)?|127\.0\.0\.1(:\d+)?|[^.]+\.127\.0\.0\.1\.nip\.io(:\d+)?|[^.]+\.127-0-0-1\.nip\.io(:\d+)?|studio\.apollographql\.com)$/,
    },
    vlanCIDR: ['10.0.0.0/8', '**********/12', '***********/16', '*********/16'],
  },
  tokens: {
    amazonPayPrivateKey: decrypt(secrets.amazonPay.privateKey),
    apiCacheToken: decrypt('nRzuIYwak9qbBCyJsCRf+9DK+S0nTuFPF3+/FBK5iNgQLiPatDZppuyGKaG+f50='),
    applePayKey:
      '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    applePayPem:
      '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',
    bloomreachKey: 'AM7aAPspVXsuP9PIIurJGtaKyKuu9/OUjZQwbCkFZ6bIh3dIUXfqZ1Sne7EnYzJe',
    buyboxApiSignature: '78prVPyYuAQNx4PgQgvFdT8OjCIkfBYFreJscFv2srFX0Fdc4C1pM6oAWQqM',
    buyboxPassword: 'vnI2IYcToXlfbnAZ6QU5',
    buyboxUser: 'fr-interflora-gift2-sandbox',
    checkoutAuthSk: 'sk_sbox_tbuiiozjramviwwlniebwwqz6ia',
    checkoutAuthWebhook: '67e4739c-b9b2-4eca-add9-b8e8ce7b9437',
    commerceToolsAdminClientId: decrypt('LRxPrLlVjZhoLiCbfytaTOdZCalR5/+dnMSwHScvH6o+DbILaa1RIbXZLEYr3u9cJBdKFs1lafU='),
    commerceToolsAdminSecretId: decrypt(
      'QhF5zSLxm/rb9+4e1XYCWRpTmU8S8W/z5kp+4yMHoDrvpnIPCPDPTBfNDfGmt0MaRpyFAekvH7Cc/101pwHuVA=='
    ),
    commerceToolsReadClientId: decrypt('xZc+l+eXcWXPCXDekU0k86Bxgc0D+wTcUBRFb4EhYpNQXcPHBX+PotlReSA6GrNzwR5FwFIo84c='),
    commerceToolsReadSecretId: decrypt(
      'cjfHlwNxfaqTMtFx9XhDc0jQP5ixnQThDPPvsbFcW+e/+oEFTU8b3WEd/s2phYw2IjrqK62jRwcajZI7ff42Ow=='
    ),
    jwt: 'jwt secret',
    meta: 'generate_and_change_in_dashboard',
    paycometApiKey: decrypt(
      'Eeg2ad8ARp1ckIkfdlLP+FBWkDXL2ARygzbxwKPXRuB6nL1/KIpqSpZUSDS70IAO7QbJtKMk1PZnzAR7JPXUbFlFwvneyuTS'
    ),
    paycometTerminal: 58761,
    paypalAppSecret: decrypt(
      'cNrTmlUWoEXzIzzoYCOHqB+carqieo+SqbteZDebczmYWBvZbYJ+0hVJxh30Zn6dvCJtItuMejSkqi5h7PgvC04xaozUrtHytj/GW7GUc3JrpUftw91GXoOXaX+Gk9guedZBZ0tbBm5L84ubwDBXdA=='
    ),
    paypalClientId: decrypt(
      'dmYZih1ALk70mxtz1NE2MIx/xT/T27+6K0dy0wpjBNcz/R03P26oy4pUBG/APr5k8stvP+eFUXAwx6fzsG6tV1yk97CABEqxO9PO37bnE/tDZX+ikLJxI4cUVqDHCwXjkNPA2rDNMiTWeUTZ/wxqfg=='
    ),
  },
}
