module.exports = {
  apps: {
    apps: {
      baseUrl: 'https://www.recette.interflora.bzh',
      mediaBaseUrl:
        process.env.MEDIA_BASE_URL ?? 'https://medias-preprod.interflora.fr/fstrz/r/s/c/medias-preprod.interflora.fr',
    },
  },
  datocms: {
    environment: 'main-fr',
  },
  interflora: {
    host: 'https://preprod-api.interflora.fr/api',
  },
  paypal: {
    webhookId: '6LL107289H085794U',
  },
  server: {
    cors: { 'Access-Control-Allow-Origin': /^https:\/\/(studio\.apollographql\.com|.*\.interflora\.(bzh|fr|it))$/ },
  },
  solr: {
    baseUrl: 'https://preprod-solr-ovh.interflora.fr:10443/solr',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-test.f15419396425ffd84045dbf62fff1e5254ace1128ea9ec246583e555',
    },
  },
}
