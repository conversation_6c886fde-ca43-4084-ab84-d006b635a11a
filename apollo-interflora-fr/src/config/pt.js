const CONST = require('../const')
const { merge } = require('lodash')

/** @type {AppConfiguration} */
const config = {
  abtests: {
    priceThreshold: {
      AB_ELASTICITY_DELIVERY_FEES: 0,
    },
  },
  address: {
    mandatoryFields: {
      addressBook: [CONST.address.addressBookMandatoryFields.civility],
      billing: [],
      shipping: ['address'],
    },
    strippedChars: /#/g,
  },
  apps: {
    apps: {
      baseUrl: process.env.BASE_URL || 'https://www.interflora.pt',
      breadcrumbsHome: 'Home - Entrega de flores',
      ceremonyTimeRanges: {
        0: {
          from: {
            hours: 10,
            minutes: 0,
          },
          margin: 2.5 * 3600 * 1000, // 2,5h in ms
          to: {
            hours: 15,
            minutes: 0,
          },
        },
        default: {
          from: {
            hours: 10,
            minutes: 0,
          },
          margin: 2 * 3600 * 1000, // 2h in ms
          to: {
            hours: 20,
            minutes: 0,
          },
        },
      },
      commerceTools: {
        locale: {
          fallbacks: ['pt-PT', 'pt'],
        },
        storeKey: 'ITP',
      },
      country: 'PT',
      currency: 'EUR',
      currencyName: 'Euro',
      currencySymbole: '€',
      discountCode: {
        unknown: 'Ocorreu um erro.',
      },
      errors: {
        login: 'Login ou password inválidos',
        loginBlocked: 'A tua conta está bloqueada. Por favor contacta o centro de apoio ao cliente.',
        loginUnconfirmed: 'A tua conta está à espera de confirmação por email. ',
      },
      language: 'pt',
      mediaBaseUrl: process.env.MEDIA_BASE_URL ?? 'https://www.interflora.pt/fstrz/r/s/www.datocms-assets.com',
      momentLabels: {
        [CONST.commerceTools.moment.Morning]: 'Manhã (10:00 - 15:00)',
        [CONST.commerceTools.moment.Afternoon]: 'Tarde (17:00 - 21:00)',
        [CONST.commerceTools.moment.Wholeday]: 'Durante o dia (10:00 - 21:00)',
      },
      momentTimes: {
        [CONST.commerceTools.moment.Morning]: [10, 15],
        [CONST.commerceTools.moment.Afternoon]: [17, 21],
        [CONST.commerceTools.moment.Wholeday]: [10, 21],
      },
      mourningDeliveryModes: [
        CONST.cart.mourningDeliveryModeMap.FUNERAL_PLACE,
        CONST.cart.mourningDeliveryModeMap.domicile,
      ],
      product: {
        category: {
          allSlug: 'search',
        },
      },
      seo: {
        category: {
          description:
            'Entrega rápida de %s em domicílio pelos nossos floristas. Surpreende %s com a Interflora e desfruta da melhor qualidade.',
          title: '%s - Interflora',
        },
        cms: {
          description:
            'Descobre as nossas dicas sobre o mundo das flores e plantas. Cuidados e guia de presentes para ofereceres flores de alta qualidade.',
          title: '%s - Interflora',
        },
        country: {
          description:
            'Consegna fiori in %s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Consegna fiori in %s - Interflora',
        },
        default: {
          description:
            '%s - Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: '%s - Interflora',
        },
        floristDepartment: {
          description:
            'A Interflora entrega flores no mesmo dia em %s ou onde estiveres. Os floristas entregam as tuas flores ao domicílio em Portugal.',
          metaTitle: 'Floristas em %s : enviar flores ao domicílio - Interflora',
          title: 'Enviar flores para %s',
        },
        floristTown: {
          description: 'Descobre o nosso serviço de entrega de flores em %s. Envia flores em %s de alta qualidade.',
          title: 'Entrega de flores em %s ao domicilio - Interflora',
        },
        home: {
          description:
            'Interflora : primo negozio di fiori e piante online per la vendita e la consegna a domicilio; spedizioni in tutto il mondo e Italia in giornata.',
          title: 'Interflora : Consegna Fiori a Domicilio e Piante Online',
        },
        product: {
          description:
            '%s - Envio de flores em Portugal com entrega no dia ou com serviço internacional. A Interflora entrega flores e plantas ao domicílio.',
          title: '%s - Interflora',
        },
      },
      storeLocator: {
        customPath: '/floristas',
        path: '/floristas',
      },
      timezone: 'Europe/Lisbon',
    },
  },
  availability: {
    baseUrl: 'http://availability.itp-ms/availability/api/v1/Availability', // @todo
  },
  bloomreach: {
    domainKey: 'interflora_eu_pt',
  },
  commerceTools: {
    priceChannelKey: 'interflora.pt',
    productList: {
      facets: {
        COLOR: {
          field: 'variants.attributes.colour.key',
          label: 'Cor',
          type: 'terms',
        },
        COULEUR: false, // legacy filter
        DELIVERY_MODE: {
          field: 'variants.attributes.delivery_type.label',
          label: 'Tipo de entrega',
          labels: {
            Carrier: 'Transportadora',
            Florist: 'Florista',
          },
          type: 'terms',
        },
        IDEAL_ORCHID: false,
        IDEAL_PLANTS: false,
        LUMINOSITY: false,
        PERSONALITY: false,
        PLANTS_COLOR: false,
        PRICE_RANGE: {
          field: 'variants.scopedPrice.value.centAmount',
          label: 'Preço',
          ranges: [{ from: 0, label: 'All', to: null }],
          type: 'min-max',
        },
        STYLE: false,
      },
      sortFilter: {
        label: 'Ordenar por',
        types: [
          { id: CONST.product.sorts.priceAsc, label: 'Preço ascendente', value: 'price asc' },
          { id: CONST.product.sorts.priceDesc, label: 'Preço decrescente', value: 'price desc' },
        ],
      },
    },
  },
  filters: {
    date: {
      id: 'DELIVERY_DATE',
      label: 'Indicar a data de entrega pretendida',
    },
  },
  gfs: {
    baseUrl: 'http://gfssupplier.itp-ms/gfssupplier/api/v1',
    categories: {
      all_occasions: { name: 'Ver tudo' },
      birth: { name: 'Nacimento' },
      birthday: { name: 'Aniversário' },
      funeral: { name: 'Condolências' },
      love: { name: 'Amor' },
      wedding: { name: 'Casamento' },
    },
    customShipping: {
      externalTaxRate: {
        amount: 0,
      },

      name: 'Internationnal shipping method',
      shippingCosts: 1499, // in cents
    },
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600, // 12h in s - NB: FR value is in ms
  },
  order: {
    reasons: [
      { id: '1', label: 'Aniversário' },
      { id: '3', label: 'Por prazer' },
      { id: '5', label: 'Obrigado!' },
      { id: '6', label: 'Luto' },
      { id: '9', label: 'Nascimento' },
      { id: '10', label: 'Amor' },
      { id: '11', label: 'Batizado' },
      { id: '13', label: 'Aniversário de casamento' },
      { id: '16', label: 'Dia dos Namorados' },
      { id: '17', label: 'Dia da Mãe' },
      { id: '23', label: 'Páscoa' },
      { id: '24', label: 'Dia do Pai' },
      { id: '93', label: 'Dia de Todos os Santos' },
    ],
  },
  payment: {
    defaultProvider: CONST.payment.provider.CHECKOUT,
    methods: [
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.CARD,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [CONST.cart.deliveryMode.noAddress],
        mode: CONST.payment.paymentMode.PAYPAL,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.APPLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.PAYCOMET,
        provider: CONST.payment.provider.PAYCOMET,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.MULTIBANCO,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.GOOGLEPAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
      {
        allowedForMobileApp: false,
        availableForCart: true,
        encrypted: null,
        excludedDeliveryModes: [],
        mode: CONST.payment.paymentMode.MBWAY,
        provider: CONST.payment.provider.CHECKOUT,
      },
    ],
  },
  products: {
    ribbon: {
      key: 'CO0025',
    },
  },
  sequenceGenerator: {
    baseUrl: 'http://itsequencegenerator.itp-ms/itsequencegenerator/api/v1/Sequence',
  },
  tokens: {
    checkoutProcessingChannelId: 'pc_3yl5nbrnfesurk77axgav6ipne',
  },
}

config.apps.catalog = merge({}, config.apps.apps)

module.exports = config
