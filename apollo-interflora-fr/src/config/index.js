const _ = require('lodash')
const apm = require('elastic-apm-node')
const fs = require('node:fs')

const CONST = require('../const')

const defaults = require('./defaults')

const environment = process.env.APP_ENV || 'prod'
let site = process.env.SITE || 'fr'

if (!Object.values(CONST.environment).includes(environment)) {
  throw new Error(`Unknown environment "${environment}"`)
}

/** @type {AppConfiguration} */
let config
{
  const globalSiteConfigPath = `${__dirname}/${site}.js`
  const globalSiteConfig = fs.existsSync(globalSiteConfigPath) ? require(globalSiteConfigPath) : {}

  const siteConfigPath = `${__dirname}/${environment}.${site}.js`
  const siteConfig = fs.existsSync(siteConfigPath) ? require(siteConfigPath) : {}

  const envConfigPath = `${__dirname}/${environment}.js`
  const envConfig = fs.existsSync(envConfigPath) ? require(envConfigPath) : {}

  config = _.merge({ environment, site }, defaults, globalSiteConfig, envConfig, siteConfig)
}

// decrypt and inject secrets (common + env overrides)
if (!config.tokens) {
  config.tokens = {}
}

if (fs.existsSync('/vault/secrets/apollo.pass')) {
  // secrets are injected by k8s
  const raw = fs.readFileSync('/vault/secrets/apollo.pass', 'utf-8')
  for (const line of raw.split(/\n\r?/)) {
    const matches = line.match(/^(?<key>.*?): (?<value>.*)$/)
    if (matches) {
      config.tokens[matches.groups.key] = matches.groups.value
    }
  }
}

if (!config.tokens.jwt) {
  const error = new Error('Error starting middleware! Could not load secrets. Exiting')

  console.error(error)
  apm.captureError(error, { custom: error })

  process.exit(1)
}

module.exports = config
