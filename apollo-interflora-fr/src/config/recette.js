module.exports = {
  commerceTools: {
    auth: {
      // end-user scope only, is actually not secret.
      credentials: {
        clientId: 'nQ1sOy8M5UkliZAdYOAHQyLD',
        clientSecret: 'L2Lzwho8GzqV8feqnkYQSTw92ppGgiAy',
      },
      host: 'https://auth.europe-west1.gcp.commercetools.com',
      projectKey: 'interfloratest',
      scopes: [
        'manage_my_orders:interfloratest manage_my_payments:interfloratest manage_my_profile:interfloratest view_categories:interfloratest view_published_products:interfloratest manage_my_shopping_lists:interfloratest create_anonymous_token:interfloratest',
      ],
    },
  },
  datocms: {
    token: 'b81c2cb9b930ceff89e98d9937414b', // read-only
  },
  server: {
    cors: {
      'Access-Control-Allow-Origin':
        /^(https?:\/\/localhost(?::\d+)?|https:\/\/(studio\.apollographql\.com|.*\.interflora\.(bzh|fr|it|es|pt|se|dk)))$/,
    },
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:fr-test.f15419396425ffd84045dbf62fff1e5254ace1128ea9ec246583e555',
    },
  },
}
