const { URL } = require('url')
const { merge } = require('lodash')
const CONST = require('../const')

/** @typedef {import('../../_typedef')} */

/** @type {AppConfiguration} */
const config = {
  abtests: {
    priceThreshold: {},
  },
  address: {
    mandatoryFields: {
      // check only if given and after being stripped
      addressBook: [
        CONST.address.addressBookMandatoryFields.country,
        CONST.address.addressBookMandatoryFields.firstName,
        CONST.address.addressBookMandatoryFields.lastName,
        CONST.address.addressBookMandatoryFields.phoneNumber,
        CONST.address.addressBookMandatoryFields.address,
        CONST.address.addressBookMandatoryFields.townLabel,
        CONST.address.addressBookMandatoryFields.postalCode,
      ],
      billing: [],
      shipping: [],
    },
    strippedChars: undefined,
  },
  ageVerification: {
    jwksUri: 'https://pp.netseidbroker.dk/op/.well-known/openid-configuration/jwks',
  },
  amazonPay: {
    buttonColor: 'Gold',
    merchantId: 'AKNBMF4PQHG7P',
    publicKey: 'SANDBOX-AFOQGXGLBAJFQMJG2X34WRB7',
    storeId: 'amzn1.application-oa2-client.6904829afbea455aaeab8349864d3162',
  },
  applePay: {
    gatewayUrl: 'https://apple-pay-gateway.apple.com/paymentservices/paymentSession',
  },
  apps: {
    apps: {
      datoSpecificValues: {
        footerNumber: CONST.cms.footerNumber.ONE,
        mainNavigationType: 'DEFAULT',
        pageSlugs: {
          index: 'index',
        },
      },
      discountCode: {
        unknown: 'The discount code {discountCode} was not found.',
      },
      errors: {
        login: 'Invalid login or password',
        loginBlocked: 'Your account is locked. Please contact customer service',
        loginUnconfirmed: 'Your account has already been created. Waiting for your confirmation by e-mail',
        password: 'Sorry. We were unable to identify you with the information provided.',
      },
      showCrossSells: true,
      towns: {
        max: 20,
      },
    },
    defaultSiteId: 'apps',
  },
  availability: {
    daysToCheck: 45,
    invalidPriceIsOutOfStock: false,
    minCutOffDuration: 1800 * 1000, //  in ms
    timeout: 10000,
  },
  awardit: {
    certificates: {
      cert: '/vault/secrets/awardit.cert',
      key: '/vault/secrets/awardit.key',
    },
    paths: {
      CARDS: 'cards',
      TEMPLATES: 'cards/templates',
    },
  },
  bitnet: {
    baseUrl: 'https://app.bit-net.se/',
  },
  bloomreach: {
    accountId: '7282',
    autosuggest: {
      baseUrl: 'https://staging-suggest.dxpapi.com/api/v2/suggest/',
      timeout: 1000,
    },
    defaultUid: 'uid=*************:v=11.5:ts=*************:hc=55',
    enabled: true,
    search: {
      baseUrl: 'https://staging-core.dxpapi.com/api/v1/core/',
      timeout: 3000,
    },
  },
  buybox: {
    apiVersion: '60.0',
    baseUrl: 'https://sandbox.buybox.net/secure/express-checkout/nvp.php',
    cancelPath: '/cart/checkout/buybox/cancel',
    lang: 'fr_FR',
    pendingCheckInterval: 2000, // in ms, time between 2 status update queries
    redirectUrl: 'https://sandbox.buybox.net/secure/payment_login.php',
    returnPath: '/cart/checkout/buybox/success',
    waitForOrderDelay: 2000, // in ms
  },
  cache: {
    tagsPrefix: 'cache:tag:',
  },
  cadeaux: {
    baseUrl: 'https://www.cadeaux.com',
  },
  categories: {
    categoryTitle: {
      // getProductList.categoryTitle
      // exclude: ['defaultCTCategory']
    },
    pageSize: 20,
  },
  checkout: {
    maxPendingChecks: 3, // max number of status update queries for pending payments
    mbway: {
      testMode: false,
    },
    pendingCheckInterval: 2000, // in ms, time between 2 status update queries
    waitForOrderDelay: 2000, // in ms, time to wait before attempting to fetch order if cart was not found
  },
  cms: {
    blocks: {
      teaser: {
        oneLineTeaser: false,
      },
    },
    breadcrumbs: {
      categories: {
        displayForRootCategories: false,
      },
    },
    categories: {
      // { [childSlug]: [parenSlug, ..] } do not redirect on given parent in the list even if not configured in datocms
      allowedParentSlugs: {},
    },
    myAccount: {
      children: [
        'userMyAddressBook',
        'userMyLoyalty',
        'userMyOrder',
        'userMyOrderDetail',
        'userMyOrders',
        'userMyProfile',
      ],
      slug: 'userMyAccount',
    },
    wellKnownPageDefaultSlug: 'fallback',
    wellKnownPages: [
      'cartAccessories',
      'cartCardSelection',
      'cartCart',
      'cartCheckout',
      'cartCheckoutRedirectPaypalWithAxerve',
      'cartConfirmation',
      'cartJoinMessage',
      'cartRelancePanier',
      'cartSenderAddress',
      'cartShippingAddress',
      'confirmEmail',
      'cartSummary',
      'orderAwaitingCapture',
      'orderBirthday',
      'orderComplete',
      'orderConfirmation',
      'orderFollow',
      'orderFollowDelivery',
      'orderConfirmation',
      'orderOrder',
      'toConfirm',
      'userMyAddressBook',
      'userMyLoyalty',
      'userMyOrder',
      'userMyOrderDetail',
      'userMyOrders',
      'userMyProfile',
      'userRegister',
      'userResetPassword',
    ],
  },
  commerceTools: {
    auth: {
      // end-user scope only, is actually not secret.
      credentials: {
        clientId: 'bL-CHWoHngGTQCAZKNXi0Yhn',
        clientSecret: '1uxXYpU51QfAzb4SjPbWA98YaMrw2KC5',
      },
      host: 'https://auth.europe-west1.gcp.commercetools.com',
      projectKey: 'interfloratest',
      scopes: [
        'manage_my_orders:interfloratest manage_my_payments:interfloratest manage_my_profile:interfloratest view_categories:interfloratest view_published_products:interfloratest manage_my_shopping_lists:interfloratest create_anonymous_token:interfloratest',
      ],
    },
    customerCare: {
      customerGroupKey: 'customer-care',
    },
    hasUsers: true,
    http: {
      host: 'https://api.europe-west1.gcp.commercetools.com',
    },
    interfloraplus: {
      customerGroupKey: 'interflora-plus',
      redisCustomerITFPlusMutexKey: 'customerITFPlusmutex::',
    },
    maxAttempts: 3,
    maxOrderFetchingAttempts: 7,
    waitForOrderDelay: 2000, //in ms
  },
  datasources: {
    timeout: 15000,
  },
  datocms: {
    endpointUrl: 'https://graphql.datocms.com/',
    environment: undefined,
    product: {
      zonesMapping: {
        afterHeader: CONST.cms.zones.after_header,
        afterInfo: CONST.cms.zones.after_info,
        beforeFooter: CONST.cms.zones.before_footer,
      },
    },
    token: '4585fbde117fc4ad43a8b261577f8f', // read-only
  },
  elasticsearch: {
    baseUrl: process.env.ELASTICSEARCH_BASE_URL || 'http://elasticsearch:9200',
  },
  frisbii: {
    baseUrl: 'https://api.reepay.com/',
    checkout: {
      baseUrl: 'https://checkout-api.reepay.com/',
      locale: 'en_US',
      options: {
        detailedInvoice: false, // don't send the details of the items in the cart to frisbii for invoicing
        settle: true, // capture payment directly,
        ttl: 'P3D', // weird format, check https://docs.frisbii.com/reference/createchargesession
      },
      redirections: {
        cases: {
          failure: '/checkout/payment/billwerk/failure',
          success: '/checkout/payment/billwerk/success/',
        },
      },
      urlPath: '/billwerk',
    },
  },
  gfs: {
    baseUrl: 'http://gfssupplier.iti-ms/gfssupplier/api/v1',
    categories: {
      all_occasions: { ids: [], name: 'All Occasions', order: 1 },
      birth: { ids: [406, 424], name: 'Baby Birth', order: 6 },
      birthday: { ids: [400, 418], name: 'Birthday', order: 2 },
      flower: {
        exclude: true,
        ids: [
          393, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 461, 462, 463,
          464, 465,
        ],
        name: 'Flower',
        order: NaN,
      },
      funeral: { ids: [395, 459, 460], name: 'Funeral', order: 3 },
      love: { ids: [401, 422], name: 'Love', order: 4 },
      wedding: { ids: [404, 422], name: 'Wedding', order: 5 },
    },
    customShipping: {
      // centAmount
      externalTaxRate: {
        amount: 0,
        // 0 to 1,
      },

      fractionDigits: 2,
      name: 'Internationnal shipping method',
      shippingCosts: 1599, // in cents,
    },
    highlightedCountries: ['DE', 'FR', 'US', 'CH', 'GB', 'ES', 'AU', 'BE', 'NL', 'CA', 'PL', 'AT'],
  },
  graphql: {
    apqTtl: 900,
    dataloaders: {
      availabilities: {
        ttl: 60 * 30, // 30 minutes
      },
      cadeauxCustomizations: {
        ttl: 60 * 60 * 24, // 1 day
      },
      celebrationTexts: {
        ttl: 60 * 30, // 30 minutes
      },
      channels: {
        ttl: 60 * 60 * 24 * 365, // 1 year
      },
      countries: {
        ttl: 60 * 60 * 5, // 5h
      },
      defaultBlocks: {
        ttl: 60 * 60 * 24, // 1 day
      },
      defaults: {
        ttl: 60 * 5, // 5 minutes
      },
      discounts: {
        ttl: 60 * 5, // 5 minutes
      },
      gfsCountries: {
        ttl: 60 * 60 * 24 * 365, // 1 year
      },
      isPremiumUser: {
        ttl: 60 * 15, // 15 minutes
      },
      mainNavigation: {
        ttl: 60 * 15, // 15 minutes
      },
      orderIdempotency: {
        cacheKey: 'order-number::',
        ttl: 60 * 60, // 3 days
      },
      paymentMethodsByCarts: {
        ttl: 60 * 10, // 10 minutes
      },
      productTypes: {
        ttl: 60 * 60 * 24 * 365, // 1 year
      },
      rewards: {
        ttl: 60 * 60, // 1 hour
      },
      shippingMethods: {
        ttl: 60 * 60 * 24, // 1 day
      },
      solr: {
        ttl: 60 * 5, // 5 minutes
      },
      storeLocatorHome: {
        ttl: 60 * 60 * 24, // 1 day
      },
      suggestedMessages: {
        ttl: 60 * 30, // 30 minutes
      },
      taxes: {
        ttl: 60 * 60 * 24, // 1 day
      },
      tokens: {
        ttl: 60 * 60 * 2, // 2 hours
      },
      towns: {
        ttl: 60 * 60 * 5, // 5 hours
      },
      users: {
        ttl: 60 * 5, // 5 minutes
      },
    },
    introspection: process.env.GQL_INTROSPECTION_ENABLED === 'true',
    ttl: {
      cms: {
        getMessageCards: 3600 * 24,
        getPage: 3600 * 24,
        getTown: 3600 * 24,
      },
      geo: {
        getCountries: 3600 * 24,
        getDepartment: 3600 * 24 * 365,
        getDepartments: 3600 * 24 * 365,
        getTowns: 3600 * 24,
      },
      product: {
        getProduct: 3600,
        getProductList: 60,
      },
      root: {
        getConfig: 60 * 15, // 15 minutes
      },
    },
    useCache: process.env.USE_CACHE !== 'false',
  },
  i18n: {
    lang2Locale: {
      en: 'en-GB',
      fr: 'fr-FR',
      it: 'it-IT',
    },
  },
  interflora: {
    accessTokenLifeTime: 12 * 3600 * 1000, // 12h
    clientId: 'trusted_client',
    clientSecret: 'secret',
    defaultSiteId: 'apps',
    host: process.env.HYBRIS_BASE_URL || 'https://interflora.aws.insign.fr/api',
    initUserPasswordLength: 16,
    timezone: 'Europe/Paris',
  },
  itAuthentication: {
    baseUrl: 'https://microservices.recette.interflora.es/itauthenticationapi/',
  },
  jwt: {
    headers: ['x-jwt'],
  },
  logger: {
    main: {
      level: 'info',
    },
    transports: {
      elasticsearch: {
        clientOpts: {
          node: process.env.ELASTICSEARCH_BASE_URL || 'http://elasticsearch:9200',
        },
        indexPrefix: 'apollogs',
        source: 'apollo',
      },
    },
  },
  order: {
    archive: {
      lifetime: {
        hours: 0,
        minutes: 0,
        mounths: 2,
        seconds: 0,
        years: 0,
      },
    },
    reasons: [
      { id: '1', label: 'Birthday' },
      { id: '3', label: 'For pleasure' },
      { id: '5', label: 'Thank you' },
      { id: '6', label: 'Bereavement' },
      { id: '9', label: 'Birth' },
      { id: '10', label: 'Love' },
      { id: '11', label: 'Baptism' },
      { id: '13', label: "Wedding's anniversary" },
      { id: '16', label: "Valentine's Day" },
      { id: '17', label: "Mother's Day" },
      { id: '23', label: 'Easter' },
      { id: '24', label: "Father's Day" },
      { id: '93', label: "All Saints' Day" },
    ],
  },
  paycomet: {
    baseUrl: 'https://rest.paycomet.com/v1',
    maxPendingChecks: 3, // max number of status update queries for pending payments
    pendingCheckInterval: 2000, // in ms, time between 2 status update queries
    waitForOrderDelay: 2000, // in ms, time to wait before attempting to fetch order if cart was not found
  },
  payment: {
    refund: {
      allowedProviders: [
        CONST.payment.provider.CHECKOUT,
        CONST.payment.provider.PAYPAL,
        CONST.payment.provider.FRISBII,
      ],
      processingDelay: 60 * 60, // 1 hour,
    },
  },
  paypal: {
    baseUrl: 'https://api-m.sandbox.paypal.com',
  },
  products: {
    interfloraplus: {
      deliveryPriceThreshold: 0,
      key: 'ITFPLUS',
      selectedByDefault: true,
      shipping: {
        price: 0,
      },
      subscriptionRenewTimeLimit: 1000 * 3600 * 24 * 30, // ~1 months
    },
    linked: {
      desired: 5,
      min: 3,
    },
    nextCelebrationsInterval: 1000 * 3600 * 24 * 30, // 30 days
    qualifiers: {
      size: {
        slug: 'id', // 'id' | 'code' - if 'id' (default), url = /p/slug/<variant.id>/ZONE. if 'code', url = /p/slug/<size code>/ZONE
      },
      zone: {
        enabled: false, // if set to true, adds a slug part dedicated to the zone (i.e. FR, CORSE) to product urls
      },
    },
    tombDelivery: {
      key: '02',
    },
  },
  redis: {
    // additional configuration at the bottom
    commandTimeout: 15000, // 15s
    defaultTTL: 7200, // 2h
    lazyConnect: true,
    sentinelCommandTimeout: 15000, // 15s
  },
  seServices: {
    florist: {
      baseUrl: 'https://interflora-api-management.azure-api.net/florist-service-itg/api/broker',
    },
  },
  sea: {},
  server: {
    allowedApiOrigins: [/\.appspot\.dataiads\.io$/],
    connectionTimeout: 8000, // in ms
    cors: {
      'Access-Control-Allow-Method': 'DELETE,GET,OPTIONS,POST,PUT,HEAD',
      'Access-Control-Max-Age': 86400,
    },
    host: '0.0.0.0',
    logs: {
      access: {
        format:
          ':remote-addr :req[x-forwarded-for] :remote-user [:date[clf]] ":method :req[host]:url HTTP/:http-version" :status :res[content-length] :response-time ms :total-time ms ":referrer" ":user-agent"',
      },
    },
    port: process.env.APP_PORT || 8000,
    requestTimeout: 8000, // in ms
    vlanCIDR: process.env.VLAN_CIDR
      ? process.env.VLAN_CIDR.split(',').map((s) => s.trim())
      : ['10.0.0.0/8', '************/32'],
  },
  session: {
    lifetime: 3600 * 24, // in s, 24h
    prefix: 'itf_apollo_',
    ttl: 3600,
  },
  solr: {
    baseUrl: process.env.SOLR_BASE_URL
      ? process.env.SOLR_BASE_URL
      : 'https://solr.interflora.aws.insign.fr/solrfacetsearch',
    catalogs: {
      fr: {
        desktop: '0-interflora-frProductCatalog',
        mobile: '0-interflora-frMobileProductCatalog',
      },
      qf: 'name^3.0 typeProduit_string^2.0 description_text_fr remarks_text item_type_string altImg_string',
      ww: {
        desktop: 'interflora-{country}ProductCatalog',
        mobile: 'interflora-{country}ProductCatalog', // one catalog for desktop & mobile on int
      },
    },
    cores: {
      fr: 'master_interflora-fr_Product',
      ww: 'master_interflora-ww_Product',
    },
  },
  storeLocator: {
    florist: {
      path: '/florist-stores',
    },
    regions: {
      pageSize: 50,
    },
  },
  tracking: {
    meta: {
      pixelId: undefined,
    },
    timeOne: {
      baseUrl: 'https://tracking.publicidees.com/PIk-back',
      comid: '116960',
      iu: '343511f9e685109be028a58e6358ec39',
      progid: '1582',
    },
    ttl: 3600 * 24,
  },
  unleash: {
    appName: 'default',
    enablesTtl: 5 * 60, // 5 min
    healthDelay: 30000, // in ms
    instanceId: 'cc0858f0-6d0e-47f6-9821-e368dd32c00e',
    ttl: 3600 * 24, // in s, 24h
    url: 'https://eu.app.unleash-hosted.com/eubb1003/api/',
  },
}

config.apps.catalog = merge({}, config.apps.apps, {
  datoSpecificValues: {
    footerNumber: CONST.cms.footerNumber.CATALOG_ONE,
    mainNavigationType: CONST.cms.dato.mainNavigationType.catalog,
    pageSlugs: {
      index: 'catalog-index',
    },
  },
  filters: {
    product: [
      `variants.attributes.product_type.key:"${[
        CONST.product.productType.ACCESSORIES,
        CONST.product.productType.CARD,
        CONST.product.productType.FLOWERS,
        CONST.product.productType.GIFT_CARD,
        CONST.product.productType.MOURNING,
        CONST.product.productType.ROSES,
        CONST.product.productType.ROUND_UP,
        CONST.product.productType.SERVICES,
      ].join('","')}"`,
      `variants.attributes.delivery_type.key:"${[CONST.product.deliveryTypes.florist].join('","')}"`,
    ],
  },
  showCrossSells: false,
})

if (process.env.REDIS_SENTINELS) {
  config.redis.name = process.env.REDIS_MASTER_NAME || 'mymaster'
  config.redis.sentinels = process.env.REDIS_SENTINELS.split(',').map((strval) => {
    const u = new URL(`tcp://${strval.trim()}`)

    return {
      host: u.hostname,
      port: u.port,
    }
  })
} else {
  config.redis.host = process.env.REDIS_HOST || 'redis'
  config.redis.port = process.env.REDIS_PORT || 6379
}

module.exports = config
