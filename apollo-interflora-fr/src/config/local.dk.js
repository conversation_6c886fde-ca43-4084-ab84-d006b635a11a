const { decrypt } = require('../helpers/crypto')
// @TODO: update base url paths to dk endpoints when infra ready
module.exports = {
  availability: {
    baseUrl: 'https://microservices.recette.interflora.se/availability/api/v1/Availability',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  datocms: {
    environment: 'main-dk',
  },
  sequenceGenerator: {
    baseUrl: 'https://microservices.recette.interflora.se/itsequencegenerator/api/v1/Sequence',
    headers: {
      Authorization: `Basic ${decrypt('XFTpTnNMlRPP7nmRSQwiVG2yQKDRkjFblYxkIy6VeXTtQ6epNcPeikaL4psM4NL+IWLvXg==')}`,
    },
  },
  tokens: {
    billwerkKey: 'priv_3faca5e3da356c85a2b9882e9b34189b',
    billwerkSignatureSecret: 'webhook_secret_d569280acc23f15775e4b59e4531d27b',
  },
  unleash: {
    customHeaders: {
      Authorization: 'default:dk-dev.5cb0baf9c6c1028872fb0924c4c695c09a7f371aa592e4f9fbbc749e',
    },
  },
}
