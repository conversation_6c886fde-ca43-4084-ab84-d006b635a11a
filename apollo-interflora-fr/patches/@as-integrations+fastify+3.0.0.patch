diff --git a/node_modules/@as-integrations/fastify/build/cjs/drain-plugin.js b/node_modules/@as-integrations/fastify/build/cjs/drain-plugin.js
index a5fa3d1..1f7c22f 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/drain-plugin.js
+++ b/node_modules/@as-integrations/fastify/build/cjs/drain-plugin.js
@@ -1,6 +1,6 @@
 "use strict";
 Object.defineProperty(exports, "__esModule", { value: true });
-exports.fastifyApolloDrainPlugin = void 0;
+exports.fastifyApolloDrainPlugin = fastifyApolloDrainPlugin;
 function fastifyApolloDrainPlugin(fastify) {
     return {
         async serverWillStart() {
@@ -9,7 +9,7 @@ function fastifyApolloDrainPlugin(fastify) {
                     if ("closeAllConnections" in fastify.server) {
                         const timeout = setTimeout(() => {
                             fastify.server.closeAllConnections();
-                        }, 10000);
+                        }, 10_000);
                         await fastify.close();
                         clearTimeout(timeout);
                     }
@@ -18,4 +18,3 @@ function fastifyApolloDrainPlugin(fastify) {
         },
     };
 }
-exports.fastifyApolloDrainPlugin = fastifyApolloDrainPlugin;
diff --git a/node_modules/@as-integrations/fastify/build/cjs/fastify-request-to-graphql-request.d.ts b/node_modules/@as-integrations/fastify/build/cjs/fastify-request-to-graphql-request.d.ts
index 7878e3b..f728dbd 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/fastify-request-to-graphql-request.d.ts
+++ b/node_modules/@as-integrations/fastify/build/cjs/fastify-request-to-graphql-request.d.ts
@@ -1,3 +1,3 @@
 import { HTTPGraphQLRequest } from "@apollo/server";
 import type { FastifyRequest, RawServerBase, RawServerDefault, RouteGenericInterface } from "fastify";
-export declare const fastifyRequestToGraphQLRequest: <RawServer extends RawServerBase = RawServerDefault>(request: FastifyRequest<RouteGenericInterface, RawServer, import("fastify").RawRequestDefaultExpression<RawServer>, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, RouteGenericInterface>>) => HTTPGraphQLRequest;
+export declare const fastifyRequestToGraphQLRequest: <RawServer extends RawServerBase = RawServerDefault>(request: FastifyRequest<RouteGenericInterface, RawServer>) => HTTPGraphQLRequest;
diff --git a/node_modules/@as-integrations/fastify/build/cjs/handler.js b/node_modules/@as-integrations/fastify/build/cjs/handler.js
index f9fa10b..abdda2d 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/handler.js
+++ b/node_modules/@as-integrations/fastify/build/cjs/handler.js
@@ -1,6 +1,6 @@
 "use strict";
 Object.defineProperty(exports, "__esModule", { value: true });
-exports.fastifyApolloHandler = void 0;
+exports.fastifyApolloHandler = fastifyApolloHandler;
 const node_stream_1 = require("node:stream");
 const fastify_request_to_graphql_request_js_1 = require("./fastify-request-to-graphql-request.js");
 const utils_js_1 = require("./utils.js");
@@ -28,4 +28,3 @@ function fastifyApolloHandler(apollo, options) {
         return reply.send(readable);
     };
 }
-exports.fastifyApolloHandler = fastifyApolloHandler;
diff --git a/node_modules/@as-integrations/fastify/build/cjs/plugin.js b/node_modules/@as-integrations/fastify/build/cjs/plugin.js
index 9c6d153..67626a6 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/plugin.js
+++ b/node_modules/@as-integrations/fastify/build/cjs/plugin.js
@@ -1,12 +1,12 @@
 "use strict";
 Object.defineProperty(exports, "__esModule", { value: true });
-exports.fastifyApollo = void 0;
+exports.fastifyApollo = fastifyApollo;
 const fastify_plugin_1 = require("fastify-plugin");
 const handler_js_1 = require("./handler.js");
 const utils_js_1 = require("./utils.js");
 const pluginMetadata = {
-    fastify: "^4.4.0",
-    name: "@as-integrations/fastify",
+    fastify: "^5.2.2",
+    name: "@xzyfer/as-integrations-fastify",
 };
 function fastifyApollo(apollo) {
     if (apollo === undefined || apollo === null || !(0, utils_js_1.isApolloServerLike)(apollo)) {
@@ -22,4 +22,3 @@ function fastifyApollo(apollo) {
         });
     }, pluginMetadata);
 }
-exports.fastifyApollo = fastifyApollo;
diff --git a/node_modules/@as-integrations/fastify/build/cjs/types.d.ts b/node_modules/@as-integrations/fastify/build/cjs/types.d.ts
index 0fb0588..3e85c82 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/types.d.ts
+++ b/node_modules/@as-integrations/fastify/build/cjs/types.d.ts
@@ -1,7 +1,7 @@
 import type { BaseContext, ContextFunction } from "@apollo/server";
 import type { FastifyReply, FastifyRequest, HTTPMethods, RawServerBase, RawServerDefault, RouteGenericInterface } from "fastify";
 type ValueOrArray<T> = T | T[];
-export type ApolloFastifyContextFunctionArgument<RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = [request: FastifyRequest<RouteGeneric, RawServer>, reply: FastifyReply<RawServer>];
+export type ApolloFastifyContextFunctionArgument<RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = [request: FastifyRequest<RouteGeneric, RawServer>, reply: FastifyReply<RouteGeneric, RawServer>];
 export type ApolloFastifyContextFunction<Context extends BaseContext, RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = ContextFunction<ApolloFastifyContextFunctionArgument<RawServer, RouteGeneric>, Context>;
 export interface ApolloFastifyHandlerOptions<Context extends BaseContext = BaseContext, RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> {
     context?: ApolloFastifyContextFunction<Context, RawServer, RouteGeneric>;
diff --git a/node_modules/@as-integrations/fastify/build/cjs/utils.js b/node_modules/@as-integrations/fastify/build/cjs/utils.js
index f70bb7c..6b85cc7 100644
--- a/node_modules/@as-integrations/fastify/build/cjs/utils.js
+++ b/node_modules/@as-integrations/fastify/build/cjs/utils.js
@@ -1,7 +1,6 @@
 "use strict";
 Object.defineProperty(exports, "__esModule", { value: true });
-exports.isApolloServerLike = void 0;
+exports.isApolloServerLike = isApolloServerLike;
 function isApolloServerLike(maybeServer) {
     return !!(maybeServer && typeof maybeServer === "object" && "assertStarted" in maybeServer);
 }
-exports.isApolloServerLike = isApolloServerLike;
diff --git a/node_modules/@as-integrations/fastify/build/esm/fastify-request-to-graphql-request.d.ts b/node_modules/@as-integrations/fastify/build/esm/fastify-request-to-graphql-request.d.ts
index f207def..f728dbd 100644
--- a/node_modules/@as-integrations/fastify/build/esm/fastify-request-to-graphql-request.d.ts
+++ b/node_modules/@as-integrations/fastify/build/esm/fastify-request-to-graphql-request.d.ts
@@ -1,3 +1,3 @@
 import { HTTPGraphQLRequest } from "@apollo/server";
 import type { FastifyRequest, RawServerBase, RawServerDefault, RouteGenericInterface } from "fastify";
-export declare const fastifyRequestToGraphQLRequest: <RawServer extends RawServerBase = RawServerDefault>(request: FastifyRequest<RouteGenericInterface, RawServer, import("fastify").RawRequestDefaultExpression<RawServer>, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, RouteGenericInterface>>) => HTTPGraphQLRequest;
+export declare const fastifyRequestToGraphQLRequest: <RawServer extends RawServerBase = RawServerDefault>(request: FastifyRequest<RouteGenericInterface, RawServer>) => HTTPGraphQLRequest;
diff --git a/node_modules/@as-integrations/fastify/build/esm/plugin.js b/node_modules/@as-integrations/fastify/build/esm/plugin.js
index 6bfb4b0..f72f2fb 100644
--- a/node_modules/@as-integrations/fastify/build/esm/plugin.js
+++ b/node_modules/@as-integrations/fastify/build/esm/plugin.js
@@ -2,8 +2,8 @@ import { fastifyPlugin } from "fastify-plugin";
 import { fastifyApolloHandler } from "./handler.js";
 import { isApolloServerLike } from "./utils.js";
 const pluginMetadata = {
-    fastify: "^4.4.0",
-    name: "@as-integrations/fastify",
+    fastify: "^5.2.2",
+    name: "@xzyfer/as-integrations-fastify",
 };
 export function fastifyApollo(apollo) {
     if (apollo === undefined || apollo === null || !isApolloServerLike(apollo)) {
diff --git a/node_modules/@as-integrations/fastify/build/esm/types.d.ts b/node_modules/@as-integrations/fastify/build/esm/types.d.ts
index 0fb0588..3e85c82 100644
--- a/node_modules/@as-integrations/fastify/build/esm/types.d.ts
+++ b/node_modules/@as-integrations/fastify/build/esm/types.d.ts
@@ -1,7 +1,7 @@
 import type { BaseContext, ContextFunction } from "@apollo/server";
 import type { FastifyReply, FastifyRequest, HTTPMethods, RawServerBase, RawServerDefault, RouteGenericInterface } from "fastify";
 type ValueOrArray<T> = T | T[];
-export type ApolloFastifyContextFunctionArgument<RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = [request: FastifyRequest<RouteGeneric, RawServer>, reply: FastifyReply<RawServer>];
+export type ApolloFastifyContextFunctionArgument<RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = [request: FastifyRequest<RouteGeneric, RawServer>, reply: FastifyReply<RouteGeneric, RawServer>];
 export type ApolloFastifyContextFunction<Context extends BaseContext, RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> = ContextFunction<ApolloFastifyContextFunctionArgument<RawServer, RouteGeneric>, Context>;
 export interface ApolloFastifyHandlerOptions<Context extends BaseContext = BaseContext, RawServer extends RawServerBase = RawServerDefault, RouteGeneric extends RouteGenericInterface = RouteGenericInterface> {
     context?: ApolloFastifyContextFunction<Context, RawServer, RouteGeneric>;
diff --git a/node_modules/@as-integrations/fastify/readme.md b/node_modules/@as-integrations/fastify/readme.md
index 117ef53..6089ccb 100644
--- a/node_modules/@as-integrations/fastify/readme.md
+++ b/node_modules/@as-integrations/fastify/readme.md
@@ -1,8 +1,8 @@
 <a href='https://www.apollographql.com/'><img src='https://avatars.githubusercontent.com/u/17189275?s=200' style="border-radius: 6px; margin-right: 6px" height='100' alt='Apollo Server'></a>
 <a href='https://www.fastify.io/'><img src='https://avatars.githubusercontent.com/u/24939410?s=200' style="border-radius: 6px" height='100' alt='Fastify'></a>
 
-[![NPM version](https://badge.fury.io/js/@as-integrations%2Ffastify.svg)](https://www.npmjs.com/package/@as-integrations/fastify)
-[![NPM downloads](https://img.shields.io/npm/dm/@as-integrations/fastify.svg?style=flat)](https://www.npmjs.com/package/@as-integrations/fastify)
+[![NPM version](https://badge.fury.io/js/@xzyfer%2Fas-integrations-fastify.svg)](https://www.npmjs.com/package/@xzyfer/as-integrations-fastify)
+[![NPM downloads](https://img.shields.io/npm/dm/@xzyfer/as-integrations-fastify.svg?style=flat)](https://www.npmjs.com/package/@xzyfer/as-integrations-fastify)
 
 # Apollo Server Integration for Fastify
 
@@ -14,15 +14,15 @@ This is a simple package that easily allows you to connect your own Fastify serv
 
 ## **Requirements**
 
-- **[Node.js v16](https://nodejs.org/)** or later
-- **[Fastify v4.4](https://www.fastify.io/)** or later
+- **[Node.js v20](https://nodejs.org/)** or later
+- **[Fastify v5.3.0](https://www.fastify.io/)** or later
 - **[GraphQL.js v16](https://graphql.org/graphql-js/)** or later
 - **[Apollo Server v4](https://www.apollographql.com/docs/apollo-server/)** or later
 
 ## **Installation**
 
 ```bash
-npm install @as-integrations/fastify @apollo/server graphql fastify
+npm install @xzyfer/as-integrations-fastify @apollo/server graphql fastify
 ```
 
 ## **Usage**
@@ -32,7 +32,7 @@ Setup [Fastify](https://www.fastify.io/) & [Apollo Server](https://www.apollogra
 ```typescript
 import Fastify from "fastify";
 import { ApolloServer, BaseContext } from "@apollo/server";
-import fastifyApollo, { fastifyApolloDrainPlugin } from "@as-integrations/fastify";
+import fastifyApollo, { fastifyApolloDrainPlugin } from "@xzyfer/as-integrations-fastify";
 // ...
 
 const fastify = Fastify();
@@ -58,7 +58,7 @@ This allows you to explicitly set all routing options like the URL path and acce
 Examples shown below:
 
 ```typescript
-import { fastifyApolloHandler } from "@as-integrations/fastify";
+import { fastifyApolloHandler } from "@xzyfer/as-integrations-fastify";
 
 // ... setup Fastify & Apollo
 
@@ -89,7 +89,7 @@ import { ApolloServer } from "@apollo/server";
 import fastifyApollo, {
   fastifyApolloHandler,
   ApolloFastifyContextFunction,
-} from "@as-integrations/fastify";
+} from "@xzyfer/as-integrations-fastify";
 // ...
 
 interface MyContext {
