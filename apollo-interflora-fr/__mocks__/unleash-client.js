/** @type {Object.<string, {enabled: boolean, value: *}>} */
let mockFeature = {}

const startUnleash = () =>
  Promise.resolve({
    getFeatureToggleDefinitions: jest.fn(() => ([])),
    getVariant: jest.fn((name) => {
      return mockFeature[name]?.value ?? {}
    }),
    isEnabled: jest.fn((name) => {
      return !!mockFeature[name]?.enabled
    }),
  })

/**
 * @param {string} name
 * @param {*} value
 * @param {boolean} enabled
 */
const __setMockFeature = (name, value, enabled = true) => {
  mockFeature[name] = {
    enabled,
    value,
  }
}

module.exports = {
  __setMockFeature,
  startUnleash,
}
