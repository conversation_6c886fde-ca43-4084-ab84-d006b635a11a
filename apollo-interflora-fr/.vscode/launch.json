{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Docker: Attach to Backend Apollo",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/app",
      "sourceMaps": true,
      "restart": true,
      "skipFiles": [
        "${workspaceRoot}/node_modules/**/*.js",
        "<node_internals>/**/*.js"
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Jest Tests",
      "program": "${workspaceFolder}/node_modules/jest/bin/jest.js",
      "args": [
        "--runInBand",
        "--color"
      ],
      "env": {
        "DEBUG": "",
        "NODE_ENV": "production",
        "SITE": "${input:site}",
        "NODE_OPTIONS": "--experimental-vm-modules"
      },
      "console": "integratedTerminal"
    }
  ],
  "inputs": [
    {
      "id": "site",
      "type": "pickString",
      "description": "Which site to run",
      "options": [
        "fr",
        "it",
        "es",
        "pt",
        "se",
        "dk"
      ]
    }
  ]
}