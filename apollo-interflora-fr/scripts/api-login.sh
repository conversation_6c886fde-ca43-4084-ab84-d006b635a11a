#!/bin/sh

set -e

usage(){
  echo "${0} <username> <password>"
}

username="${1}"
password="${2}"
env="${3}"

case "${env}" in
  prod)
    endpoint="https://prod-api.interflora.fr"
    ;;
  *)
    endpoint="https://preprod-api.interflora.fr"
    ;;
esac


if [ -z "${1}" ] || [ -z "${2}" ]; then
  usage
  exit 1
fi

username="$(echo "${username}" | sed 's/@/%40/' | sed 's/+/%2B/')"

response="$(curl -s -H "Content-type: application/x-www-form-urlencoded" -d "grant_type=password&client_id=trusted_client&client_secret=secret&username=${username}&password=${password}" "${endpoint}/api/oauth/token")"

token="$(echo "${response}" | docker run --rm -i jetbrainsinfra/jq jq -r '.access_token')"

echo "${response}" | docker run --rm -i jetbrainsinfra/jq jq -C .
echo
echo 'curl -H "Authorization: Bearer '"${token}"'"' '"'${endpoint}/api/v2/mobile-FR'"'
