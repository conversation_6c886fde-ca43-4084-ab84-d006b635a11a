#!/bin/sh

ips=
sep=
for domain in \
  features.dev.interflora.bzh \
  features.recette.interflora.fr \
  features.recette.interflora.bzh \
  features.preprod.interflora.fr \
  features.prod.interflora.fr \
  interflora.aws.insign.fr \
  microservices.dev.interflora.it \
  microservices.recette.interflora.it \
  microservices.preprod.interflora.it \
  microservices.prod.interflora.it \
  preprod-solr-ovh.interflora.fr \
; do
  ips="${ips}${sep}$(dig +short "${domain}")"
  sep=" "
done

# macOs does not have procfs
if [ -d /proc ]; then
  for ip in ${ips}; do
    docker run -ti \
      --privileged \
      --net host \
      alpine:3.12 \
        ip route add "${ip}/32" via $(ip a s tun0 | awk '/inet / { gsub("/.+$", "", $4); print $4 }')
  done
else
  for ip in ${ips}; do
    sudo ip route add "${ip}/32" via $(ip a s utun2 | awk '/inet / { gsub("/.+$", "", $4); print $4 }')
  done
fi
