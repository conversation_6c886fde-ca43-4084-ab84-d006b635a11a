#!/bin/bash
vpn_ip=$(ip route show table local | grep vpn | awk '{print $2}')

if [ -n "$vpn_ip" ]; then
    echo "Your vpn IP is $vpn_ip"
    subnet="${vpn_ip%.*}.0/24"
    echo "remove interflora_common docker network"
    docker network rm interflora_common
    echo "create interflora_common docker network in order to use forticlient VPN"
    echo "gateway = $vpn_ip"
    echo "subnet = $subnet"
    docker network create --gateway $vpn_ip --subnet $subnet interflora_common
else
    echo "We can't find your vpn IP"
fi