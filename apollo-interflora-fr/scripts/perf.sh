#!/bin/sh

set -e

env="${1}"
count="${2}"
freq="${3}"

usage() {
  echo "${0} <env> <count> <freq>"
}

token() {
  response="$(curl -s -k ${user} "https://${host}/graphql" -H 'Content-Type: application/json' --data-binary '{"query":"mutation { anonymousLogin(context: { language: \"fr\", siteId: \"apps\" }){ value }}"}')"
  token="$(echo "${response}" | docker run --rm -i jetbrainsinfra/jq jq -r '.data.anonymousLogin.value')"
  echo "${token}"
}

case "${env}" in
  local)
    host="localhost"
    ;;
  dev|recette|preprod)
    host="back.${env}.interflora.bzh"
    ;;
esac

if [ -z "${host}" ]; then
  usage
  exit 1
fi

jwt=$(token)
tag=$(date +"%Y%m%d%H%M%S")

i=0

while [ $i -lt $count ]; do
  let i=$((i + 1))
  reqId=$(uuidgen | awk '{print tolower($0)}')

  curl -sf "https://${host}/graphql/?tag=${tag}" \
    -H 'Content-Type: application/json' \
    -H "X-jwt: ${jwt}" \
    -H "X-Request-Id: ${reqId}" \
    --data-binary '{"query":"query { \n\tgetTowns(context: {language: \"fr\", siteId: \"apps\"}, search: \"34\") { label } \n}"}' \
    --compressed \
    -k \
    -o /dev/null \
    -w "${reqId};%{time_starttransfer};%{time_total}\\n" &


  sleep $freq
done

for job in $(jobs -p); do
  wait $job
done
