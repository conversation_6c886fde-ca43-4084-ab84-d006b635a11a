#!/bin/sh

env="${1}"

case "${env}" in
  prod)
    endpoint="https://prod-api.interflora.fr"
    ;;
  *)
    endpoint="https://preprod-api.interflora.fr"
    ;;
esac

response="$(curl -s -H "Content-type: application/x-www-form-urlencoded" -d "grant_type=client_credentials&client_id=trusted_client&client_secret=secret" "${endpoint}/api/oauth/token")"

token="$(echo "${response}" | docker run --rm -i jetbrainsinfra/jq jq -r '.access_token')"

echo "${response}" | docker run --rm -i jetbrainsinfra/jq jq -C .
echo
echo 'curl -H "Authorization: Bearer '"${token}"'"' '"'${endpoint}/api/v2/mobile-FR'"'
