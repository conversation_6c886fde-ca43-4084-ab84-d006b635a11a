#!/bin/sh
#set -a && . .env && set +a
set -x

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

order_types=$(jq -rc << EOF
{
  "key": "cart-custom",
  "name": {
    "en": "custom fields on cart & order"
  },
  "resourceTypeIds": [
    "order"
  ],
  "fieldDefinitions": [
    {
      "type": {
        "name": "String"
      },
      "name": "message",
      "label": {
        "en": "message"
      },
      "required": false
    },
    {
      "type": {
        "name": "String"
      },
      "name": "signature",
      "label": {
        "en": "signature"
      },
      "required": false
    }
  ]
}
EOF
)

echo "• creating type"
echo ${order_types} | _curl "${base}/types" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "userId",
        "label": {
          "en": "external user id"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding user id field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "occasionCode",
        "label": {
          "en": "occasion code"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding user occasion code"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-


current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
         "name": "Enum",
          "values": [
            {
              "key": "DESKTOP",
              "label": "desktop"
            },
            {
              "key": "MOBILE",
              "label": "mobile"
            },
            {
              "key": "UNKNOWN",
              "label": "unknown"
            }
          ]
        },
        "name": "device",
        "label": {
          "en": "device"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "ip",
        "label": {
          "en": "ip"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "sessionId",
        "label": {
          "en": "session id"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding device, ip & session id"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Number"
        },
        "name": "cart_abandoned_tag",
        "label": {
          "en": "abandonned cart iteration count"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding abandonned cart counter"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
         "name": "Enum",
          "values": [
            {
              "key": "CEREMONY",
              "label": "ceremony"
            },
            {
              "key": "NO_ADDRESS",
              "label": "no address"
            },
            {
              "key": "SELF",
              "label": "self"
            },
            {
              "key": "STANDARD",
              "label": "standard"
            },
            {
              "key": "TOMB",
              "label": "tomb"
            }
          ]
        },
        "name": "deliveryMode",
        "label": {
          "en": "delivery mode"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding delivery mode field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "invoiceRequest",
        "label": {
          "en": "customer requests an invoice"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding invoice request field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "interfloraplus",
        "label": {
          "en": "interflora plus"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding interfloraplus field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addEnumValue",
      "fieldName": "deliveryMode",
      "value": {
        "key": "FUNERAL_PLACE",
        "label": "funeral place"
      }
    },
    {
      "action": "addEnumValue",
      "fieldName": "deliveryMode",
      "value": {
        "key": "GRAVE",
        "label": "grave"
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding values in delivery mode field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "partnerPaymentId",
        "label": {
          "en": "partner payment id"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding partner payment id field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "isEligibleToAbandonCartEmail",
        "label": {
          "en": "identify abandoned carts where it is possible to send an email"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• addind a field to identify abandoned carts with an eligible email address"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addEnumValue",
      "fieldName": "deliveryMode",
      "value": {
        "key": "TOMB_BUNDLE",
        "label": "tomb bundle"
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding values in delivery mode field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "voucher",
        "required": false,
        "label": {
          "en": "Voucher",
          "fr": "Voucher"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new field 'voucher'"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "removeFieldDefinition",
      "fieldName": "partnerPaymentId"
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• removing partner payment id field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "billUrl",
        "required": false,
        "label": {
          "en": "Bill url",
          "fr": "Url de la facture"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new field 'billUrl'"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "additional_data",
        "required": false,
        "label": {
          "en": "Order additional data",
          "fr": "Données additionnelles"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new field 'additional_data'"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "hasDeliveryMethodSelected",
        "label": {
          "en": "identify delivery method selection"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding a field to identify delivery method selection"
echo "${more_fields}" | _curl "${base}/types/key=cart-custom" -d @-

# additional data
# -----------------------------------------------------------------------------
current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "additional_data",
        "required": false,
        "label": {
          "en": "Order additional data",
          "fr": "Données additionnelles"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new field 'additional_data'"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addEnumValue",
      "fieldName": "device",
      "value": {
        "key": "APP_IOS",
        "label": "Mobile App IOS"
      }
    },
    {
      "action": "addEnumValue",
      "fieldName": "device",
      "value": {
        "key": "APP_ANDROID",
        "label": "Mobile App Android"
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new device values"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-


current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "changeEnumValueLabel",
      "fieldName": "device",
      "value": {
        "key": "APP_IOS",
        "label": "DEPRECATED - Mobile App IOS"
      }
    },
    {
      "action": "changeEnumValueLabel",
      "fieldName": "device",
      "value": {
        "key": "APP_ANDROID",
        "label": "DEPRECATED - Mobile App Android"
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• changing device deprecated values label"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addEnumValue",
      "fieldName": "device",
      "value": {
        "key": "APPIOS",
        "label": "Mobile App IOS"
      }
    },
    {
      "action": "addEnumValue",
      "fieldName": "device",
      "value": {
        "key": "APPANDROID",
        "label": "Mobile App Android"
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding new adapted device values"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-

current_version=$(_curl "${base}/types/key=cart-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "ageVerificationToken",
        "required": false,
        "label": {
          "en": "Age verification token"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding values in delivery mode field"
echo ${more_fields} | _curl "${base}/types/key=cart-custom" -d @-