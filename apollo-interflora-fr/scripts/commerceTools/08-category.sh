#!/bin/sh

set -x

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="custom-category"

line_item_types=$(jq -rc << EOF
{
  "key": "${key}",
  "name": {
    "en": "custom fields on custom category"
  },
  "resourceTypeIds": [
    "category"
  ]
}
EOF
)

echo "• creating type"
echo ${line_item_types} | _curl "${base}/types" -d @-

# Category zone available values should be the same as product's variant available values.
# Please maintain both those enums at the same time.
current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Zone",
          "fr": "Zone"
        },
        "name": "zone",
        "required": false,
        "type": {
          "name": "Enum",
          "values": [
            {
              "key": "DOMTOM",
              "label": "FR-DOMTOM"
            },
            {
              "key": "CORSE",
              "label": "FR-CORSICA"
            }
          ]
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• adding zone" 
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-
