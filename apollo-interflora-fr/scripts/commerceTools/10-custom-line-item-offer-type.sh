#!/bin/sh

set -e

key="custom-line-item-rewards"

here="$(cd "$(dirname "$0")" && pwd)"

## Dev
#CTP_PROJECT_KEY="myflower-dev"
#CTP_CLIENT_ID="..."
#CTP_CLIENT_SECRET="..."


source "${here}/_auth.sh"

### Create a new type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')

if [ "$current_version" = "null" ]; then
  echo "• Type '${key}' not found. Creating it..."

  line_item_type=$(jq -rc << EOF
{
  "key": "${key}",
  "name": {
    "en": "Custom fields Reward on custom line item and payment objects"
  },
  "resourceTypeIds": [
    "custom-line-item",
    "payment"
  ]
}
EOF
  )

  response=$(echo "${line_item_type}" | _curl "${base}/types" -d @-)

  if echo "$response" | jq -e '.errors' > /dev/null; then
    echo "Error creating type: $(echo "$response" | jq '.errors')"
    exit 1
  fi

  echo "✅ Type '${key}' created successfully."
  current_version=$(echo "$response" | jq -r '.version')
else
  echo "• Type '${key}' already exists. Proceeding to update..."
fi

### Add a new field to the type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Reward type"
        },
        "name": "rewardType",
        "required": true,
        "type": {
          "name": "LocalizedEnum",
          "values": [
            {
              "key": "GIFT_CARD",
              "label": {
                "en": "Gift card"
              }
            },
            {
              "key": "BONUS",
              "label": {
                "en": "Bonus"
              }
            },
             {
               "key": "PROMOTION",
               "label": {
                 "en": "Promotion"
               }
             }
          ]
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• Adding 'Reward type' field..."
response=$(echo "${more_fields}" | _curl "${base}/types/key=${key}" -d @-)

if echo "$response" | jq -e '.errors' > /dev/null; then
  echo "Error updating type: $(echo "$response" | jq '.errors')"
else
  echo "✅ Successfully added 'Reward type' field."
fi

#### Add new fields to the type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Transaction Id"
        },
        "name": "transactionId",
        "required": false,
        "type": {
          "name": "String"
        }
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Provider"
        },
      "name": "provider",
      "required": false,
        "type": {
        "name": "String"
        }
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Provider Response"
        },
      "name": "providerResponse",
      "required": false,
        "type": {
        "name": "String"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• Adding Payment Infos fields..."
response=$(echo "${more_fields}" | _curl "${base}/types/key=${key}" -d @-)

if echo "$response" | jq -e '.errors' > /dev/null; then
  echo "Error updating type: $(echo "$response" | jq '.errors')"
else
  echo "✅ Successfully added Payment Infos fields."
fi


#### Add new fields to the type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Id"
        },
        "name": "id",
        "required": false,
        "type": {
          "name": "String"
        }
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Label"
        },
        "name": "label",
        "required": false,
        "type": {
          "name": "String"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• Adding id and label fields..."
response=$(echo "${more_fields}" | _curl "${base}/types/key=${key}" -d @-)

if echo "$response" | jq -e '.errors' > /dev/null; then
  echo "Error updating type: $(echo "$response" | jq '.errors')"
else
  echo "✅ Successfully added id and label fields."
fi

#### Add new fields to the type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Reservation Id"
        },
        "name": "reservationId",
        "required": false,
        "type": {
          "name": "String"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• Adding Reservation Id field..."
response=$(echo "${more_fields}" | _curl "${base}/types/key=${key}" -d @-)

if echo "$response" | jq -e '.errors' > /dev/null; then
  echo "Error updating type: $(echo "$response" | jq '.errors')"
else
  echo "✅ Successfully added Reservation Id fields."
fi

#### Add new fields to the type

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Balance"
        },
        "name": "balance",
        "required": false,
        "type": {
          "name": "Money"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• Adding Balance field..."
response=$(echo "${more_fields}" | _curl "${base}/types/key=${key}" -d @-)

if echo "$response" | jq -e '.errors' > /dev/null; then
  echo "Error updating type: $(echo "$response" | jq '.errors')"
else
  echo "✅ Successfully added Balance field."
fi
