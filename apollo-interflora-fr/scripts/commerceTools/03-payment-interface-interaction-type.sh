#!/bin/sh

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

type=$(jq -rc << EOF
{
  "key": "payment-interface-interaction-custom",
  "name": {
    "en": "custom fields on payment"
  },
  "resourceTypeIds": [
    "payment-interface-interaction"
  ],
  "fieldDefinitions": [
    {
      "type": {
        "name": "String"
      },
      "name": "serialized",
      "label": {
        "en": "serialized"
      },
      "required": false
    }
  ]
}
EOF
)

echo "• creating type"
echo ${type} | _curl "${base}/types" -d @-
