#!/bin/sh

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"


# *****************************************************************************
# ⚠️⚠️ THIS WILL DELETE ALL EXISTING CUSTOM ATTRIBUTES
# ⚠️⚠️ NEVER USE ON PRODUCTION!!!!!
# *****************************************************************************
_delete() {
  current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
  _curl -XDELETE "${base}/types/key=address-custom?version=${current_version}"
}
#_delete

address_types=$(jq -rc << EOF
{
  "key": "address-custom",
  "name": {
    "en": "custom fields on address"
  },
  "resourceTypeIds": [
    "address"
  ],
  "fieldDefinitions": [
    {
      "type": {
        "name": "Date"
      },
      "name": "date",
      "label": {
        "en": "date"
      },
      "required": false
    },
    {
      "type": {
        "name": "String"
      },
      "name": "fiscalCode",
      "label": {
        "en": "fiscal code"
      },
      "required": false
    },
    {
      "type": {
        "name": "Enum",
        "values": [
          {
            "key": "0",
            "label": "Morning"
          },
          {
            "key": "1",
            "label": "Afternoon"
          },
          {
            "key": "2",
            "label": "Wholeday"
          }
        ]
      },
      "name": "moment",
      "label": {
        "en": "moment"
      },
      "required": false
    },
    {
      "type": {
        "name": "Time"
      },
      "name": "time",
      "label": {
        "en": "time"
      },
      "required": false
    }
  ]
}
EOF
)
echo "• creating type"
echo ${address_types} | _curl "${base}/types" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "companyNumber",
        "label": {
          "en": "company number"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "pec",
        "label": {
          "en": "PEC"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "vatNumber",
        "label": {
          "en": "VAT number"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding b2b fields"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-


current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Enum",
          "values": [
            {
              "key": "CEREMONY",
              "label": "Ceremony"
            },
            {
              "key": "NO_ADDRESS",
              "label": "IFlora / no address"
            },
            {
              "key": "STANDARD",
              "label": "Standard"
            },
            {
              "key": "SELF",
              "label": "self purchase"
            },
            {
              "key": "TOMB",
              "label": "Tomb"
            }
          ]
        },
        "name": "mode",
        "label": {
          "en": "delivery mode"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding delivery mode"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Enum",
          "values": [
            {
              "key": "MR",
              "label": "MR"
            },
            {
              "key": "MRS",
              "label": "MRS"
            }
          ]
        },
        "name": "contactTitle",
        "label": {
          "en": "contact title"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "contactFirstName",
        "label": {
          "en": "contact first name"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "contactLastName",
        "label": {
          "en": "contact last name"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "contactPhone",
        "label": {
          "en": "contact phone"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding contact fields"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Enum",
          "values": [
            {
              "key": "COMPANY",
              "label": "Company"
            },
            {
              "key": "FREELANCE",
              "label": "Freelance"
            },
            {
              "key": "INDIVIDUAL",
              "label": "Individual"
            },
            {
              "key": "INDIVIDUAL_COMPANY",
              "label": "Individual company"
            }
          ]
        },
        "name": "type",
        "label": {
          "en": "type"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding address type"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "invoiceLastName",
        "label": {
          "en": "invoice last name"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "invoiceFirstName",
        "label": {
          "en": "invoice first name"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding contact fields"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "invoiceEmail",
        "label": {
          "en": "invoice email"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding address invoice email"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "comments",
        "label": {
          "en": "comments"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding address comments"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-


current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
payload=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addEnumValue",
      "fieldName": "moment",
      "value": {
        "key": "3",
        "label": "Evening"
      }
    },
    {
      "action": "changeEnumValueOrder",
      "fieldName": "moment",
      "keys": [
        "0",
        "1",
        "3",
        "2"
      ]
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• allow evening moment"
echo ${payload} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "optinSMS",
        "label": {
          "en": "customer opt-in for SMS promotions"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding optinSMS field"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Boolean"
        },
        "name": "optinEmail",
        "label": {
          "en": "customer opt-in for email promotions"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding optinEmail field"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-

# moment description
# -----------------------------------------------------------------------------
current_version=$(_curl "${base}/types/key=address-custom" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "momentDescription",
        "label": {
          "en": "moment description"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding momentDescription field"
echo ${more_fields} | _curl "${base}/types/key=address-custom" -d @-
