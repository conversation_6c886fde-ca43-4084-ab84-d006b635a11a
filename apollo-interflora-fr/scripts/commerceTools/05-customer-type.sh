#!/bin/sh

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="customer-custom"

customer_type=$(jq -rc << EOF
{
  "key": "${key}",
  "name": {
    "en": "custom fields on customers"
  },
  "resourceTypeIds": [ "customer" ],
  "fieldDefinitions": [
    {
      "label": {
        "en": "Interflora+ expiration date [ES]"
      },
      "name": "interfloraplusExpirationDateES",
      "required": false,
      "type": {
        "name": "Date"
      }
    },
    {
      "label": {
        "en": "Interflora+ expiration date [PT]"
      },
      "name": "interfloraplusExpirationDatePT",
      "required": false,
      "type": {
        "name": "Date"
      }
    }
  ]
}
EOF
)
echo "• creating type"
echo ${customer_type} | _curl "${base}/types" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [ ],
  "version": ${current_version}
}
EOF
)
echo "• extending type"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action" : "addFieldDefinition",
      "fieldDefinition" : {
        "label": {
          "en": "Interflora+ subcription date [ES]"
        },
        "name": "interfloraplusSubcriptionDateES",
        "required": false,
        "type": {
          "name": "Date"
        }
      }
    },
    {
      "action" : "addFieldDefinition",
      "fieldDefinition" : {
        "label": {
          "en": "Interflora+ subcription date [PT]"
        },
        "name": "interfloraplusSubcriptionDatePT",
        "required": false,
        "type": {
          "name": "Date"
        }
      }
    },
  ],
  "version": ${current_version}
}
EOF
)
echo "• extending type"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action" : "addFieldDefinition",
      "fieldDefinition" : {
        "label": {
          "en": "Interflora+ expiration date [IT]"
        },
        "name": "interfloraplusExpirationDateIT",
        "required": false,
        "type": {
          "name": "Date"
        }
      }
    },
    {
      "action" : "addFieldDefinition",
      "fieldDefinition" : {
        "label": {
          "en": "Interflora+ subcription date [IT]"
        },
        "name": "interfloraplusSubcriptionDateIT",
        "required": false,
        "type": {
          "name": "Date"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo "• extending type => add Interflora+ subcription range date for Italy"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-