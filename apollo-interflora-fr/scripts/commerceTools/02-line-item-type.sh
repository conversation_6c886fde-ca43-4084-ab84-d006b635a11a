#!/bin/sh

set -x

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="line-item-custom"

line_item_types=$(jq -rc << EOF
{
  "key": "line-item-custom",
  "name": {
    "en": "custom fields on line item"
  },
  "resourceTypeIds": [
    "line-item"
  ],
  "fieldDefinitions": [
    {
      "type": {
        "name": "String"
      },
      "name": "isAccessoryFor",
      "label": {
        "en": "Accessory for product variant"
      },
      "required": false
    }
  ]
}
EOF
)

echo "• creating type"
echo ${line_item_types} # | _curl "${base}/types" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "ribbonText",
        "label": {
          "en": "mourning ribbon text"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  
echo
echo "• adding Mourning ribbonText"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "imageUrl",
        "label": {
          "en": "image url"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Money"
        },
        "name": "marketingFee",
        "label": {
          "en": "Marketing fee"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Number"
        },
        "name": "weight",
        "label": {
          "en": "weight"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  
echo
echo "• adding image url, weight and marketing fee"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "colour",
        "label": {
          "en": "colour"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo
echo "• adding color"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "customizations",
        "label": {
          "en": "customizations"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo
echo "• adding customizations"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Reference",
          "referenceTypeId": "channel"
        },
        "name": "seaChannel",
        "label": {
          "en": "SEA channel"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo
echo "• adding sea channel"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "extra_bundle_infos",
        "label": {
          "en": "Extra JSON data form bundle informations, used in CTFR",
          "fr": "Données JSON supplémentaires pour la gestion des bundles en CTFR"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo
echo "• adding extra JSON bundle infos"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "messageContent",
        "label": {
          "en": "message content for message cards or other personalized messages"
        },
        "required": false
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "messageSignature",
        "label": {
          "en": "message signature for message cards or other signatures"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  
echo
echo "• adding personalized message data => messageContent, messageSignature"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-