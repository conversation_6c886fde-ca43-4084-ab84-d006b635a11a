#!/bin/sh

set -x

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="custom-line-item-custom"

line_item_types=$(jq -rc << EOF
{
  "key": "${key}",
  "name": {
    "en": "custom fields on custom line item"
  },
  "resourceTypeIds": [
    "custom-line-item"
  ]
}
EOF
)

echo "• creating type"
echo ${line_item_types} | _curl "${base}/types" -d @-


current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "String"
        },
        "name": "description",
        "label": {
          "en": "product description"
        },
        "required": true
      }
    },
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
            "name": "String"
        },
        "name": "sku",
        "label": {
          "en": "product sku"
        },
        "required": true
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• adding sku, description"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "label": {
          "en": "Size"
        },
        "name": "size",
        "required": false,
        "type": {
          "name": "LocalizedEnum",
          "values": [
            {
              "key": "small",
              "label": {
                "en": "Small",
                "es": "Normal",
                "fr": "Petit",
                "it": "Standard",
                "pt": "Normal"
              }
            },
            {
              "key": "medium",
              "label": {
                "en": "Medium",
                "es": "Grande",
                "fr": "Grand",
                "it": "Premium",
                "pt": "Grande"
              }
            },
            {
              "key": "large",
              "label": {
                "en": "Large",
                "es": "Superior",
                "fr": "Très Grand",
                "it": "Deluxe",
                "pt": "Extra"
              }
            },
            {
              "key": "one_size",
              "label": {
                "en": "One size",
                "es": "Normal",
                "fr": "Normal",
                "it": "Standard",
                "pt": "Normal"
              }
            }
          ]
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• adding size"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
        "type": {
          "name": "Money"
        },
        "name": "marketingFee",
        "label": {
          "en": "Marketing fee"
        },
        "required": false
      }
    }
  ],
  "version": ${current_version}
}
EOF
)

echo "• adding marketing fee"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-
