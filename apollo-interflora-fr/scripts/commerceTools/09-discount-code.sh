#!/bin/sh
#set -a && source .env && set +a
#set -x

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="cartDiscountCustomErrorMessageType"

discount_type=$(jq -rc << EOF
{
  "key": "${key}",
  "name": {
    "en": "Cart Discount Custom Error Message"
  },
  "resourceTypeIds": ["discount-code"],
  "fieldDefinitions": [
    {
      "name": "customErrorMessage",
      "label": {
        "en": "Custom discount error message"
      },
      "required": false,
      "type": {
        "name": "LocalizedString"
      },
      "inputHint": "SingleLine"
    }
  ]
}
EOF
)
echo
echo "• creating custom discount error message type"
echo ${discount_type} | _curl "${base}/types" -d @-


