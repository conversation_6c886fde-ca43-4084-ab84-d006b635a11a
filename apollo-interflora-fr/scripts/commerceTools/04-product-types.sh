#!/bin/sh

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Sort priority"
          },
          "name": "sort_priority",
          "type": {
            "name": "enum",
            "values": [
              {
                "key": "000_low",
                "label": "Low"
              },
              {
                "key": "100_medium",
                "label": "Medium"
              },
              {
                "key": "200_high",
                "label": "High"
              }
            ]
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding sort fields"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-

  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "label": {
            "en": "CMS published"
          },
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "name": "cms_published_on",
          "type": {
            "name": "set",
            "elementType": {
              "name": "enum",
              "values": [
                {
                  "key": "ITD",
                  "label": "ITD"
                },
                {
                  "key": "ITE",
                  "label": "ITE"
                },
                {
                  "key": "ITF",
                  "label": "ITF"
                },
                {
                  "key": "ITI",
                  "label": "ITI"
                },
                {
                  "key": "ITP",
                  "label": "ITP"
                }
              ]
            }
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding new cms published field"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done


for type in bundle gfs mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  payload=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "removeAttributeDefinition",
        "name": "cms_published"
      }
    ],
    "version": ${current_version}
  }
EOF
)
  echo
  echo "• removing cms_published field"
  echo ${payload} | _curl "${base}/product-types/key=${type}" -d @-

done

type="gfs"
current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addAttributeDefinition",
      "attribute": {
        "attributeConstraint": "SameForAll",
        "inputTip": {
          "en": "cf Confluence to get the list of GFS categories"
        },
        "isRequired": false,
        "isSearchable": false,
        "label": {
          "en": "GFS category ids"
        },
        "name": "gfs_category_ids",
        "type": {
          "name": "set",
          "elementType": {
            "name": "number"
          }
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• adding gfs categories for marketing fees"
echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-


for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addAttributeDefinition",
      "attribute": {
        "attributeConstraint": "SameForAll",
        "inputTip": {
          "en": "is automatically updated on datocms update"
        },
        "isRequired": false,
        "isSearchable": false,
        "label": {
          "en": "Date modified in cms"
        },
        "name": "cms_updated_at",
        "type": {
          "name": "datetime"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding cms_updated_at fields"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addAttributeDefinition",
      "attribute": {
        "attributeConstraint": "SameForAll",
        "inputTip": {
          "en": "Suggest lists of accessories linked to a category"
        },
        "isRequired": false,
        "isSearchable": true,
        "label": {
          "en": "Default accessories category"
        },
        "name": "default_accessories_category",
        "type": {
          "name": "reference",
          "referenceTypeId": "category"
        }
      }
    },
    {
      "action": "addAttributeDefinition",
      "attribute": {
        "attributeConstraint": "None",
        "inputTip": {
          "en": "Suggest lists of accessories linked to a category"
        },
        "isRequired": false,
        "isSearchable": true,
        "label": {
          "en": "Accessories category"
        },
        "name": "accessories_category",
        "type": {
          "name": "reference",
          "referenceTypeId": "category"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding new accessories category fields"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addLocalizedEnumValue",
      "attributeName": "size",
      "value": {
        "key": "free_price",
        "label": {
          "en": "Free price",
          "fr": "Prix libre"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding new enum value free price"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addAttributeDefinition",
      "attribute": {
        "isRequired": false,
        "isSearchable": false,
        "label": {
          "en": "Days to delivery",
          "fr": "Délai de livraison (en jours)"
        },
        "name": "daysToDelivery",
        "type": {
          "name": "number"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding new daysToDelivery field"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Shipping fees",
            "fr": "Frais de livraison"
          },
          "inputTip": {
            "en": "Shipping fees specific to this variant",
            "fr": "Frais de livraison spécifiques au variant"
          },
          "name": "shipping_fees",
          "type": {
            "name": "money"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding variant specific shipping fees"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Shipping fees",
            "fr": "Frais de livraison"
          },
          "inputTip": {
            "en": "Shipping fees specific to this product",
            "fr": "Frais de livraison spécifiques au produit"
          },
          "name": "product_shipping_fees",
          "type": {
            "name": "money"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding product specific shipping fees"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addLocalizedEnumValue",
      "attributeName": "product_type",
      "value": {
        "key": "WAREHOUSE",
        "label": {
          "en": "Warehouse",
          "fr": "Atelier"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding warehouse product_type"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addLocalizedEnumValue",
      "attributeName": "product_type",
      "value": {
        "key": "MOURNING",
        "label": {
          "en": "Mourning",
          "fr": "Deuil"
        }
      }
    }
  ],
  "version": ${current_version}
}
EOF
)
  echo
  echo "• adding mourning product_type"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Delivery start date",
            "fr": "Date de début de livraison"
          },
          "name": "delivery_start_date",
          "type": {
            "name": "date"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding product delivery start date"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Delivery end date",
            "fr": "Date de fin de livraison"
          },
          "name": "delivery_end_date",
          "type": {
            "name": "date"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding product delivery end date"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

# Category zone available values should be the same as product's variant available values.
# Please maintain both those enums at the same time.
for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Zone",
            "fr": "Zone"
          },
          "inputTip": {
            "en": "Specific delivery zone for this variant",
            "fr": "Zone de livraison spécifique pour ce variant"
          },
          "name": "zone",
          "type": {
            "name": "enum",
            "values": [
              {
                "key": "DOMTOM",
                "label": "FR-DOMTOM"
              },
              {
                "key": "CORSE",
                "label": "FR-CORSICA"
              }
            ]
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding delivery zone to variant"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done


for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "Linked products have their variants sold as variants of the product they're linked to on its page."
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Linked products"
          },
          "name": "linked_products",
          "type": {
            "name": "set",
            "elementType": {
              "name": "reference",
              "referenceTypeId": "product"
            }
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding linked products"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  more_fields=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "inputTip": {
            "en": "Informations for florist about flowers and foliage"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist variant flowers and foliage"
          },
          "name": "florist_flowers_foliage",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "inputTip": {
            "en": "Informations for florist about a potential included container"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist included container"
          },
          "name": "florist_included_container",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "inputTip": {
            "en": "Informations for florist about the variant preparation time"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist preparation time"
          },
          "name": "florist_preparation_time",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "None",
          "inputTip": {
            "en": "Informations for florist about the variant dimensions"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist variant dimensions"
          },
          "name": "florist_product_dimensions",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "Informations for florist about the product description"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist product description"
          },
          "name": "florist_product_description",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "Informations for florist about the product mandatory flowers"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist mandatory flowers"
          },
          "name": "florist_product_mandatory_flowers",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "Informations for florist about the product composition substitution"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist product composition substitution"
          },
          "name": "florist_product_substitution",
          "type": {
            "name": "ltext"
          }
        }
      },
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "Informations for florist about the product mandatory colors"
          },
          "isRequired": false,
          "isSearchable": true,
          "label": {
            "en": "Florist mandatory colors"
          },
          "name": "florist_product_mandatory_colors",
          "type": {
            "name": "ltext"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
  )
  echo
  echo "• adding pfs fields"
  echo ${more_fields} | _curl "${base}/product-types/key=${type}" -d @-
done

for type in bundle gfs mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  payload=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "label": {
            "en": "Old product key"
          },
          "attributeConstraint": "SameForAll",
          "isRequired": false,
          "isSearchable": true,
          "name": "old_product_key",
          "type": {
            "name": "ltext"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
)
  echo
  echo "• adding old_product_key field"
  echo ${payload} | _curl "${base}/product-types/key=${type}" -d @-

done

type="bundle"
current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
payload=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "changeIsSearchable",
      "attributeName": "bundled_products",
      "isSearchable": true
    }
  ],
  "version": ${current_version}
}
EOF
)
echo
echo "• set isSearchable attribute to true"
echo "${payload}" | _curl "${base}/product-types/key=${type}" -d @-

for type in bundle gfs mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  payload=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "inputTip": {
            "en": "Used to override the variant label on the product page"
          },
          "label": {
            "en": "Label"
          },
          "attributeConstraint": "None",
          "isRequired": false,
          "isSearchable": true,
          "name": "label",
          "type": {
            "name": "ltext"
          }
        }
      }
    ],
    "version": ${current_version}
  }
EOF
)
  echo
  echo "• adding label field"
  echo ${payload} | _curl "${base}/product-types/key=${type}" -d @-

done

for type in bundle gfs mourning product; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  payload=$(jq -rc << EOF
  {
    "actions": [
      {
       "action": "changeIsSearchable",
       "attributeName": "product_type",
       "isSearchable": true
      }
    ],
    "version": ${current_version}
  }
EOF
)
  echo
  echo "• set isSearchable attribute to true"
  echo ${payload} | _curl "${base}/product-types/key=${type}" -d @-

done

for type in bundle gfs mourning product bundle-with-package-variants; do
  current_version=$(_curl "${base}/product-types/key=${type}" | jq -r '.version')
  payload=$(jq -rc << EOF
  {
    "actions": [
      {
        "action": "addAttributeDefinition",
        "attribute": {
          "type": {
            "name": "set",
            "elementType": {
              "name": "enum",
              "values": [
                {
                  "key": "DE",
                  "label": "ITD"
                },
                {
                  "key": "ES",
                  "label": "ITE"
                },
                {
                  "key": "FR",
                  "label": "ITF"
                },
                {
                  "key": "IT",
                  "label": "ITI"
                },
                {
                  "key": "PT",
                  "label": "ITP"
                },
                {
                  "key": "SE",
                  "label": "ITS"
                }
              ]
            }
          },
          "name": "skip_availability_on",
          "label": {
            "en": "Skip the availability check on ?"
          },
          "isRequired": false,
          "attributeConstraint": "SameForAll",
          "inputTip": {
            "en": "By default, availability is checked for all products. Use this field to specify for which countries the availability check should be skipped, e.g. for generic accessories"
          },
          "inputHint": "SingleLine"
        }
      }
    ],
    "version": ${current_version}
  }
EOF
)
  echo
  echo "• adding skip_availability_on attribute to specify for which countries the availability check should be skipped"
  echo ${payload} | _curl "${base}/product-types/key=${type}" -d @-

done