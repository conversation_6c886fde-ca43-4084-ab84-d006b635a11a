#!/bin/sh

set -e

CTP_API_URL="https://api.europe-west1.gcp.commercetools.com"
CTP_AUTH_URL="https://auth.europe-west1.gcp.commercetools.com"
if [ -z "${CTP_PROJECT_KEY}" ]; then 
  CTP_PROJECT_KEY=interfloratest
fi
CTP_SCOPES="manage_project:${CTP_PROJECT_KEY}"

_credentials() {
  echo "Missing CTP_CLIENT_ID, CTP_CLIENT_SECRET environment variable"
  echo "CTP_PROJECT_KEY is likely to be required too (defaults to interfloratest)"
}

if [ -z "${CTP_CLIENT_ID}" ]; then
  _credentials
  exit 1
fi
if [ -z "${CTP_CLIENT_SECRET}" ]; then
  _credentials
  exit 1
fi
  
token="$(curl -s -u "${CTP_CLIENT_ID}:${CTP_CLIENT_SECRET}" \
  -d "grant_type=client_credentials&scope=manage_project:${CTP_PROJECT_KEY}" \
  "https://auth.europe-west1.gcp.commercetools.com/oauth/token" \
  | jq -r '.access_token' \
)"

_curl() {
  curl -s -H "Authorization: Bearer ${token}" $@
}

# To get all the current custom types
_get_types() {
  echo _curl "${base}/types"
}

base="${CTP_API_URL}/${CTP_PROJECT_KEY}"