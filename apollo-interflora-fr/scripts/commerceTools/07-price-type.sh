#!/bin/sh

here="$(cd "$(dirname "$0")" && pwd)"
source "${here}/_auth.sh"

key="PriceFields"

current_version=$(_curl "${base}/types/key=${key}" | jq -r '.version')
more_fields=$(jq -rc << EOF
{
  "actions": [
    {
      "action": "addFieldDefinition",
      "fieldDefinition": {
      "label": {
        "en": "Shipping fees for this price",
        "fr": "Frais de livraison spécifiques à cette ligne de prix"
      },
      "name": "shippingFees",
      "required": false,
      "type": {
        "name": "Money"
      }
    }
    }
  ],
  "version": ${current_version}
}
EOF
)
  
echo
echo "• adding shipping fees"
echo ${more_fields} | _curl "${base}/types/key=${key}" -d @-