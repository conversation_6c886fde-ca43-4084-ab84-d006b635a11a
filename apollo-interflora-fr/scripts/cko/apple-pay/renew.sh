#!/bin/sh

# This script aims to make Checkout.com's Apple pay's certs renewal easier by automating most of the tasks.

api_key=$1

if [ $# -eq 0 ]
  then
    echo "Please put the Checkout.com public api key as first argument, you can find it in the frontend repo chart values\n"
    exit 1
fi

echo "Detected key: '${api_key}'\n"
isnt_prod=$(echo $api_key | grep "_sbox_")

if [ $isnt_prod ]; then
    api_url="https://api.sandbox.checkout.com/"
else

    api_url="https://api.checkout.com/"
    echo "You're going to renew a production apple config, press any key to continue OR exit this script."
    read continue
fi;

echo "Now executing Step 2 from here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Step_2:_Generate_a_Certificate_Signing_Request_(CSR) \n"

signing_cert_data=$(curl -s POST \
  ''${api_url}'applepay/signing-requests' \
  -H 'Authorization: '${api_key}'' \
  -H 'Content-Type: application/json' \
  -d '{
    "protocol_version": "ec_v1"
  }' | jq -r ".content")

echo ${signing_cert_data} > cko.csr
echo "The cko certificate (cko.csr) was created.\n\n"

[ -f "apple_pay.cer" ]
apple_pay_cer_detected=$?
while [ "$apple_pay_cer_detected" -eq 1 ]
do
    echo "--- ACTION NEEDED ---"
    echo "- Using the newly created cko.csr, execute step 3 here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Step_3:_Create_an_Apple_Pay_Payment_Processing_Certificate"
    echo "- Put the applepay.cert file in the directory of this script"
    echo "- Then press any key to continue\n"
    read continue
    [ -f "apple_pay.cer" ]
apple_pay_cer_detected=$?
done

echo "Now executing Step 4 from here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Step_4:_Upload_the_signed_Apple_Pay_Payment_Processing_Certificate\n"

openssl x509 -inform der -in apple_pay.cer -out base64_converted.cer

base64_cert=$(cat base64_converted.cer)
base64_cert_to_send=${base64_cert#"-----BEGIN CERTIFICATE-----"}
base64_cert_to_send=${base64_cert_to_send%"-----END CERTIFICATE-----"}
base64_cert_to_send=$(echo $base64_cert_to_send | tr -d '\n ')

cert_renew_result=$(curl -s POST \
  ''${api_url}'applepay/certificates' \
  -H 'Authorization: '${api_key}'' \
  -H 'Content-Type: application/json' \
  --data-raw '{
    "content": "'${base64_cert_to_send}'"
  }')

echo "The certificate generation request was made, here is the result: $cert_renew_result \n"
echo "If anything is wrong compared to the documentation example here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Response_example_1 don't proceed.\n\n"
echo "--- ACTION NEEDED ONLY IF ALSO NEED TO REVERIFY DOMAINS ---"
echo "- Execute step 5 here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Step_5:_Prepare_your_domain_for_Apple_Pay_verification"
echo "- Please check the 'Specific configuration files for Apple pay part' in this doc to know where to put the .txt file: https://myflower.atlassian.net/wiki/spaces/ITFFR/pages/1430781975/Technical+informations"
echo "- Then press any key to continue and enter the information asked to generate the certficate, don't put any password.\n"
read continue



openssl req -out uploadMe.csr -new -newkey rsa:2048 -nodes -keyout certificate_sandbox.key
echo "The certificate to upload (uploadMe.csr) was created with its key (certificate_sandbox.key).\n"

[ -f "merchant_id.cer" ]
merchant_id_detected=$?
while [ "$merchant_id_detected" -eq 1 ]
do
    echo "\n--- ACTION NEEDED ---"
    echo "- Using the uploadMe.csr file execute the steps starting at 6.3 here: https://www.checkout.com/docs/payments/add-payment-methods/apple-pay#Step_5:_Prepare_your_domain_for_Apple_Pay_verification"
    echo "- Put the merchant_id.cer file in the directory of this script"
    echo "- Then press any key to continue\n\n"
    read continue
    [ -f "merchant_id.cer" ]
    merchant_id_detected=$?
done

openssl x509 -inform der -in merchant_id.cer -out certificate_sandbox.pem

echo "The file certificate_sandbox.pem was created.\n"

base_64_key="$(cat certificate_sandbox.key | base64)"
base_64_key_to_send=$(echo $base_64_key| tr -d '\n ')
base_64_cert="$(cat certificate_sandbox.pem | base64)"
base_64_cert_to_send=$(echo $base_64_cert| tr -d '\n ')

echo "" > message.txt
echo "Hello guys, can you please change the following vault values for apollo {COUNTRY} in {ENV}\n" >> message.txt
echo "- \`applePayKey\` : \`${base_64_key_to_send}\`\n" >> message.txt
echo "- \`applePayPem\` : \`${base_64_cert_to_send}\`\n" >> message.txt
echo "Thank you very much (maybe this message was 95% generated by a script we don't know :o)\n" >> message.txt


echo "You can now send the message.txt content to your infra team on slack (don't forget to change the {COUNTRY} and {ENV} values) :)\n\n"

echo "--- ACTION NEEDED ---"
echo "- Press enter to clean up files.\n"
read continue

ls | grep -xvF renew.sh | xargs rm -f --