#!/bin/sh

lint() {
  checksum="$(docker compose exec -T backend sh -c 'tar c --exclude node_modules . | sha256sum')"

  echo
  echo "• prettier"
  echo "_________________________________________________________________________"
  echo
  # hackish - always run eslint even if prettier failed
  set +e
  docker compose exec -T backend npm run format
  code=$?

  set -e

  echo
  echo "• eslint"
  echo "_________________________________________________________________________"
  echo
  docker compose exec -T backend npx eslint --color --fix src tests

  if [ $code -ne 0 ]; then 
    echo "prettier reported error ${code}"
    exit $code
  fi

  new_checksum="$(docker compose exec -T backend sh -c 'tar c --exclude node_modules . | sha256sum')"

  if [ "${checksum}" != "${new_checksum}" ]; then
    echo "either eslint or prettier changed files"
    exit 1
  fi
}
