apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "application.fullname" . }}-test-connection"
  labels:
    {{- include "application.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install, post-upgrade, test
    "helm.sh/hook-weight": "5"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  containers:
    - name: curl-apollo
      imagePullPolicy: IfNotPresent
      image: itfcacheacr.azurecr.io/curlimages/curl:7.73.0
      command: ['curl']
      args: 
        - -vf
        - -H
        - 'apollo-require-preflight: true'
        - '{{ include "application.fullname" . }}:{{ .Values.service.port }}/graphql?query=%7B__typename%7D'
      resources:
        limits:
          memory: "128Mi"
          cpu: "100m"
        requests:
          memory: "32Mi"
          cpu: "32m"
  restartPolicy: Never