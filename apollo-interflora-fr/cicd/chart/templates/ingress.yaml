{{- if .Values.ingress.apollo.enabled -}}
{{- $fullName := include "application.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $kubeVersion := .Capabilities.KubeVersion.GitVersion -}}

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
  {{- with .Values.ingress.apollo.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.ingress.apollo.ingressClass }}
  ingressClassName: {{ .Values.ingress.apollo.ingressClass | quote }}
  {{- end }}
  {{- if .Values.ingress.apollo.tls }}
  tls:
    {{- range .Values.ingress.apollo.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
  {{- range .Values.ingress.apollo.hosts }}
    - host: {{ .host }}
      http:
        paths:
    {{- range .paths }}
        - path: {{ . }}
          pathType: Prefix
          backend:
            service:
              name: {{ $fullName }}
              port: 
                number: {{ $svcPort }}        
    {{- end }}      
  {{- end }}
  {{- end }}