{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "application.fullname" . }}
  labels:
    {{- include "application.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "application.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
  {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
  {{- end }}
  {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
  {{- end }}
  # {{- if .Values.autoscaling.targetIngressRequestsSecondsCount }}
  # - type: Object
  #   object:
  #     metric:
  #       name: {{ .Values.autoscaling.targetIngressMetricName }}
  #     describedObject:
  #       apiVersion: networking.k8s.io/v1
  #       kind: Ingress
  #       name: {{ include "application.fullname" . }}
  #     target:
  #       type: AverageValue
  #       averageValue: {{ .Values.autoscaling.targetIngressRequestsSecondsCount }}
  # {{- end }}
{{- end }}
