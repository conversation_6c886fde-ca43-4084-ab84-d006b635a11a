{{- if .Values.ingress.apollocache.enabled -}}
{{- $fullName := include "application.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $ingressPath := .Values.ingress.apollocache.path -}}

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "apollo-cache"
  labels:
    {{- include "application.labels" . | nindent 4 }}
  {{- with .Values.ingress.apollocache.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.ingress.apollocache.ingressClass }}
  ingressClassName: {{ .Values.ingress.apollocache.ingressClass | quote }}
  {{- end }}

  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
  {{- range .Values.ingress.apollocache.hosts }}
    - host: {{ . }}
      http:
        paths:
          - path: {{ $ingressPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port: 
                  number: {{ $svcPort }}
    {{- end }}
  {{- end }}
