# Default values for prod environment.

#debug with
#helm install --dry-run --debug chart/ --generate-name --values chart-values/values-prod.yml
#helm install chart/ --generate-name --values chart-values/values-prod.yaml -n apollo2 --create-namespace --set 'image.tag=latest,image.repository=itfprodacr.azurecr.io/apollo-interflora-fr'
#helm diff upgrade apollo chart/ --values chart-values/values-prod.yaml -n apollo --set 'image.tag=latest,image.repository=itfprodacr.azurecr.io/apollo-interflora'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""



imagePullSecrets: []
nameOverride: "apollo"
fullnameOverride: "apollo"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "itp-apollo"

podAnnotations:
  vault.hashicorp.com/agent-inject: "true"
  vault.hashicorp.com/agent-image: "itfcacheacr.azurecr.io/hashicorp/vault:1.10.3"
  vault.hashicorp.com/role: "itp-apollo"
  vault.hashicorp.com/agent-inject-secret-apollo.pass: "applications/itp-apollo"
  vault.hashicorp.com/agent-inject-template-apollo.pass: |
    {{ with secret "applications/itp-apollo" -}}
    apiCacheToken: {{ .Data.apiCacheToken }}
    applePayKey: {{ .Data.applePayKey }}
    applePayMerchantId: {{ .Data.applePayMerchantId }}
    applePayPem: {{ .Data.applePayPem }}
    bloomreachKey: {{ .Data.bloomreachKey }}
    checkoutAuthSk: {{ .Data.checkoutAuthSk }}
    checkoutAuthWebhook: {{ .Data.checkoutAuthWebhook }}
    checkoutProcessingChannelId: {{ .Data.checkoutProcessingChannelId }}
    commerceToolsAdminClientId: {{ .Data.commerceToolsAdminClientId }}
    commerceToolsAdminSecretId: {{ .Data.commerceToolsAdminSecretId }}
    commerceToolsReadClientId: {{ .Data.commerceToolsReadClientId }}
    commerceToolsReadSecretId: {{ .Data.commerceToolsReadSecretId }}
    jwt: {{ .Data.jwt }}
    paycometApiKey: {{ .Data.paycometApiKey }}
    paycometTerminal: {{ .Data.paycometTerminal }}
    paypalAppSecret: {{ .Data.paypalAppSecret }}
    paypalClientId: {{ .Data.paypalClientId }}
    {{- end }}


service:
  type: ClusterIP
  port: 80

ingress:
  #Configure apollo ingress 
  apollo:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - host: back.prod.interflora.pt
      paths:
      - "/graphql"
      - "/api"
    tls:
    - hosts:
      - back.prod.interflora.pt
      secretName: "prod-interflora-pt-cert"


    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-ciphers: "HIGH:!aNULL:!MD5"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "6m"

  apollocache:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - apollo-cache.prod.interflora.pt
    tls:
    - hosts:
      - apollo-cache.prod.interflora.pt
      secretName: "prod-interflora-pt-cert"
    path: "/_cache"

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      # nginx.ingress.kubernetes.io/auth-type: basic
      # nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
      # nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
      # nginx.ingress.kubernetes.io/configuration-snippet: |-
      #   ###
      #   # Whitelist 
      #   ###
      #   satisfy any;
      #   ## MPLS Interflora
      #   allow ************/32;
      #   deny all;
resources:
  requests:
    cpu: 0.05
    memory: 256Mi

maxUnavailable: "90%"

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 50
  targetCPUUtilizationPercentage: 200
  targetMemoryUtilizationPercentage: 200
  targetIngressRequestsSecondsCount: 2
  targetIngressMetricName: "nginx_itp_apollo_request_rate_per_seconds"

nodeSelector:
  "workload": "itf-fr"

tolerations: []

affinity: {}

env:
  - name: APP_ENV
    value: prod
  - name: APM_BASE_URL
    value: "http://apm-server.logging:8200"
  - name: DEBUG
    value: ""
  - name: ELASTICSEARCH_BASE_URL
    value: "http://elasticsearch-master-headless.logging:9200"
  - name: GQL_INTROSPECTION_ENABLED
    value: "false"
  - name: MEDIA_BASE_URL
    value: https://www.interflora.pt/fstrz/r/s/www.datocms-assets.com
  - name: NODE_ENV
    value: production
  - name: REDIS_MASTER_NAME
    value: "mymaster"
  # coma separated list of redis sentinels
  - name: REDIS_SENTINELS
    value: "itp-redis.itp-apollo:26379"
  - name: VLAN_CIDR
    value: "10.0.0.0/8,************/32,77.246.89.227/32,217.181.213.230/32"
  - name: SITE
    value: pt
