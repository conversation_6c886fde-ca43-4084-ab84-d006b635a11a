# Default values for recette environment.

#debug with
#helm install --dry-run --debug chart/ --generate-name --values chart-values/values-recette.yml
#helm install chart/ --generate-name --values chart-values/values-recette.yaml -n apollo22 --create-namespace --set 'image.tag=latest,image.repository=itfrecetteacr.azurecr.io/apollo2-interflora-fr'
#helm diff upgrade apollo2 chart/ --values chart-values/values-recette.yaml -n apollo2 --set 'image.tag=latest,image.repository=itfrecetteacr.azurecr.io/apollo2-interflora'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: "apollo2"
fullnameOverride: "apollo2"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "itf-apollo2"

podAnnotations:
  vault.hashicorp.com/agent-inject: "true"
  vault.hashicorp.com/agent-image: "itfcacheacr.azurecr.io/hashicorp/vault:1.10.3"
  vault.hashicorp.com/role: "itf-apollo2"
  vault.hashicorp.com/agent-inject-secret-apollo.pass: "applications/itf-apollo2"
  vault.hashicorp.com/agent-inject-template-apollo.pass: |
    {{ with secret "applications/itf-apollo2" -}}
    amazonPayPrivateKey: {{ .Data.amazonPayPrivateKey }}
    apiCacheToken: {{ .Data.apiCacheToken }}
    applePayKey: {{ .Data.applePayKey }}
    applePayMerchantId: {{ .Data.applePayMerchantId }}
    applePayPem: {{ .Data.applePayPem }}
    axerveApiKey: {{ .Data.axerveApiKey }}
    billwerkKey: {{ .Data.frisbiiKey }}
    billwerkSignatureSecret: {{ .Data.frisbiiSignatureSecret }}
    bloomreachKey: {{ .Data.bloomreachKey }}
    buyboxApiSignature: {{ .Data.buyboxApiSignature }}
    buyboxPassword: {{ .Data.buyboxPassword }}
    buyboxUser: {{ .Data.buyboxUser }}
    checkoutAuthSk: {{ .Data.checkoutAuthSk }}
    checkoutAuthWebhook: {{ .Data.checkoutAuthWebhook }}
    checkoutProcessingChannelId: {{ .Data.checkoutProcessingChannelId }}
    commerceToolsAdminClientId: {{ .Data.commerceToolsAdminClientId }}
    commerceToolsAdminSecretId: {{ .Data.commerceToolsAdminSecretId }}
    commerceToolsReadClientId: {{ .Data.commerceToolsReadClientId }}
    commerceToolsReadSecretId: {{ .Data.commerceToolsReadSecretId }}
    jwt: {{ .Data.jwt }}
    paypalAppSecret: {{ .Data.paypalAppSecret }}
    paypalClientId: {{ .Data.paypalClientId }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  #Configure apollo2 ingress 
  apollo:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - host: back2.recette.interflora.bzh
      paths:
      - "/graphql"
      - "/api"
    tls:
    - hosts:
      - back2.recette.interflora.bzh
      secretName: "back2-recette-interflora-bzh-cert"

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "6m"

  apollocache:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - apollo2-cache.recette.interflora.bzh
    tls:
    - hosts:
      - apollo2-cache.recette.interflora.bzh
      secretName: "apollo2-cache-recette-interflora-bzh-cert"
    path: "/_cache"

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
      nginx.ingress.kubernetes.io/configuration-snippet: |-
        ###
        # Whitelist 
        ###
        satisfy any;
        ## MPLS Interflora
        allow ************/32;
        allow *************/32;
        allow ***************/32;
        allow *************/32;
        allow ************/32;
        deny all;
resources:
  requests:
    cpu: 0.05
    memory: 256Mi

maxUnavailable: "90%"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 15
  targetCPUUtilizationPercentage: 200
  targetMemoryUtilizationPercentage: 80
  targetIngressRequestsSecondsCount: 2
  targetIngressMetricName: "nginx_itf_apollo_request_rate_per_seconds"

nodeSelector:
  "workload": "itf-fr"

tolerations: []

affinity: {}

env:
  - name: APP_ENV
    value: recette
  - name: APM_BASE_URL
    value: "http://apm-server.logging:8200"
  - name: DEBUG
    value: ""
  - name: ELASTICSEARCH_BASE_URL
    value: "http://elasticsearch-master-headless.logging:9200"
  - name: GQL_INTROSPECTION_ENABLED
    value: "true"
  - name: NODE_ENV
    value: "production"
  - name: REDIS_MASTER_NAME
    value: "mymaster"
  # coma separated list of redis sentinels
  - name: REDIS_SENTINELS
    value: "itf-redis2.itf-apollo2:26379"
  - name: SOLR_BASE_URL
    value: "https://preprod-solr-ovh.interflora.fr:10443/solr"
  - name: VLAN_CIDR
    value: "10.0.0.0/8,************/32,************/32,*************/32,***************/32"
  - name: SITE
    value: fr
