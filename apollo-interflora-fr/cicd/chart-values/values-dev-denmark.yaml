# Default values for dev environment.

#debug with
#helm diff upgrade apollo chart/ --values chart-values/values-dev-denmark.yaml -n apollo --set 'image.tag=latest,image.repository=itddevacr.azurecr.io/apollo-interflora'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""



imagePullSecrets: []
nameOverride: "apollo"
fullnameOverride: "apollo"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "itd-apollo"

podAnnotations:
  vault.hashicorp.com/agent-inject: "true"
  vault.hashicorp.com/agent-image: "itfcacheacr.azurecr.io/hashicorp/vault:1.10.3"
  vault.hashicorp.com/role: "itd-apollo"
  vault.hashicorp.com/agent-inject-secret-apollo.pass: "applications/itd-apollo"
  vault.hashicorp.com/agent-inject-template-apollo.pass: |
    {{ with secret "applications/itd-apollo" -}}
    apiCacheToken: {{ .Data.apiCacheToken }}
    # applePayKey: {{ .Data.applePayKey }}
    # applePayMerchantId: {{ .Data.applePayMerchantId }}
    # applePayPem: {{ .Data.applePayPem }}
    # axerveApiKey: {{ .Data.axerveApiKey }}
    # bloomreachKey: {{ .Data.bloomreachKey }}
    # buyboxApiSignature: {{ .Data.buyboxApiSignature }}
    # buyboxPassword: {{ .Data.buyboxPassword }}
    # buyboxUser: {{ .Data.buyboxUser }}
    # checkoutAuthSk: {{ .Data.checkoutAuthSk }}
    # checkoutAuthWebhook: {{ .Data.checkoutAuthWebhook }}
    checkoutProcessingChannelId: {{ .Data.checkoutProcessingChannelId }}
    commerceToolsAdminClientId: {{ .Data.commerceToolsAdminClientId }}
    commerceToolsAdminSecretId: {{ .Data.commerceToolsAdminSecretId }}
    commerceToolsReadClientId: {{ .Data.commerceToolsReadClientId }}
    commerceToolsReadSecretId: {{ .Data.commerceToolsReadSecretId }}
    jwt: {{ .Data.jwt }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  #Configure apollo ingress 
  apollo:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - host: back.dev.interflora.dk
      paths:
      - "/graphql"
      - "/api"
    tls:
    - hosts:
      - back.dev.interflora.dk
      secretName: "back-dev-interflora-dk-cert"

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      nginx.ingress.kubernetes.io/proxy-body-size: "6m"

  apollocache:
    enabled: true
    ingressClass: "nginx"
    hosts:
    - apollo-cache.dev.interflora.dk
    tls:
    - hosts:
      - apollo-cache.dev.interflora.dk
      secretName: "apollo-cache-dev-interflora-dk-cert"
    path: "/_cache"

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt
      kubernetes.io/tls-acme: "true"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/http2-push-preload: "true"
      nginx.ingress.kubernetes.io/auth-type: basic
      nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
      nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
      nginx.ingress.kubernetes.io/configuration-snippet: |-
        ###
        # Whitelist 
        ###
        satisfy any;
        ## MPLS Interflora
        allow ************/32;
        allow *************/32;
        allow ***************/32;
        allow *************/32;
        allow ***********/32;
        deny all;
resources:
  requests:
    cpu: 0.05
    memory: 256Mi

maxUnavailable: "90%"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 200
  targetMemoryUtilizationPercentage: 80
  targetIngressRequestsSecondsCount: 2
  targetIngressMetricName: "nginx_itd_apollo_request_rate_per_seconds"
nodeSelector:
  "workload": "itf-fr"

tolerations: []

affinity: {}

env:
  - name: APP_ENV
    value: dev
  - name: APM_BASE_URL
    value: "http://apm-server.logging:8200"
  - name: DEBUG
    value: "itd:*"
  - name: ELASTICSEARCH_BASE_URL
    value: "http://elasticsearch-master-headless.logging:9200"
  - name: GQL_INTROSPECTION_ENABLED
    value: "true"
  - name: MEDIA_BASE_URL
    value: https://medias-preprod.interflora.fr/fstrz/r/s/c/medias-preprod.interflora.fr
  - name: NODE_ENV
    value: "production"
  - name: REDIS_MASTER_NAME
    value: "mymaster"
  # coma separated list of redis sentinels
  - name: REDIS_SENTINELS
    value: "itd-redis.itd-apollo:26379"
  - name: SOLR_BASE_URL
    value: "https://preprod-solr-ovh.interflora.fr:10443/solr"
  - name: USE_CACHE
    value: "false"
  - name: VLAN_CIDR
    value: "10.0.0.0/8,************/32,***********/32,*************/32,***************/32"
  - name: SITE
    value: se
