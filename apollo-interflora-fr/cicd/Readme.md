### CICD Pipelines

## About 

This folder contains the implentation of the Octopus CICD pipelines. 

CICD-pipelines folder contains two templates pipelines (CI & CD). Those templates are called for each kubernetes environment which specific variables. 
- dev-pipeline
- recette-pipeline
- preprod-pipeline
- prod-pipeline

${env}-pipeline is triggered when a code is push into the corresponding ${env} branch.   
${env}.cd-pipeline is trigerred by the end of ci-pipeline.

## Branch name <-> Kubernetes env

Each branch is published on a corresponding kubernetes environment. 

| Branch name   |     Kubernetes environment       
| ------------- | -------------: 
| develop       |        dev        
| uat           |        recette         
| preprod       |        preprod        
| master        |        prod

## Tag conventions

Docker image is tagged with last commit id (8 first characters)   
Helm chart is tagged with the build id (int)

You may pull itf${env}acr.azurecr.io/apollo:latest to get the latest build. 

## Folder structure

    ./cicd/
    ├── chart                       # Helm chart folder
        ├── templates               # Defines kubernetes resources (ingress, deployment,...) 
    ├── chart-values                # Helm chart values for each kubernetes environment
        ├── values-dev-${country}.yaml         # Defines values for dev environment (hostname, replicas, ...)
        ├── values-prod-${country}.yaml        # Defines values for prod environment (hostname, replicas, ...)
        ├── ...
    ├── cicd-pipelines              # Azure DevOps pipelines for each branch / environment
        |__ templates               # Contains build (CI) & deployment (CD) pipelines
        ├── dev-pipeline.yml        # CI pipeline for dev 
        ├── recette-pipeline.yml    # CD pipeline for recette (uat) 
        ├── preprod-pipeline.yml    # CI pipeline for preproduction (staging)
        ├── prod-pipeline.yml       # CD pipeline for prod 
        ├── ...
    ├── dockerfile.build            # Dockerfile for build instructions
    └── README.md


## Import a new pipeline

- Use the Azure DevOps to import pipeline (new -> import from yaml file) 
- Rename from apollo-interflora-fr to your pipeline name (ex: dev-pipeline) 
- To define the secret variable (edit pipeline -> new variable) named "containerRegistryPwd"
- To change the defaultBranch : edit the pipeline then on the three dots on the top right corner pick Triggers. Then choose the YAML tab, and change the "default branch for manual and scheduled build" to your working branch. 

## Check result

- Go to Pipeline -> Environment
- Select your environment and click on Apollo to view the kubernetes resources (pods, service, ...)