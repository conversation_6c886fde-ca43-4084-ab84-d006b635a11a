FROM node:22.16-alpine AS builder

ENV NODE_ENV=production
ENV NODE_OPTIONS=--enable-source-maps

WORKDIR /usr/app
COPY --chown=node:node package*.json tsconfig*.json *.js *.mjs ./
COPY --chown=node:node src ./src
COPY --chown=node:node cert ./cert
COPY --chown=node:node patches ./patches

RUN chown -R node:node /usr/app

USER node
RUN  set -x \
  && npm ci --include dev \
  && npm run build \
  && npm ci --omit=dev

# production image
FROM node:22.16-alpine

ENV NODE_ENV=production
ENV NODE_OPTIONS=--enable-source-maps
ENV HOST=0.0.0.0

WORKDIR /usr/app
COPY --from=builder /usr/app /usr/app
USER node

EXPOSE 8000

CMD ["npm", "run", "start"]
