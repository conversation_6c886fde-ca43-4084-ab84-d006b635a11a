trigger:
- develop

pool:
  vmImage: ubuntu-latest
  
variables:
  #####################################
  ## common-variables
  #####################################

  projectName: 'apollo'

  helmVersion: 3.2.3
  HELM_EXPERIMENTAL_OCI: '1'

  #Must match chart/Chart.yaml name
  helmChartName: 'apollo'

  #Update chart/Chart.yaml according to this value 
  helmChartVersion: "1.0.1"

  #Helm Release name 
  helmReleaseName: 'apollo'

  #Use Azure devOps build id to tag image --> must be remplaced by commitID 
  imageTag: $(build.SourceVersion)

  #Registry name to store docker image and helm chart (create if not exist, must be lowercase)
  imageRepository: 'apollo-interflora'

  #####################################
  ##dev-variables
  #####################################
  
  containerRegistry: 'itfdevacr.azurecr.io'
  containerRegistryLogin: 'itfdevacr'
  #containerRegistryPwd: 'must be defined in azure pipeline variables'

  #itfdevacr.azurecr.io/apollo-interflora-fr
  containerFullPath: '$(containerRegistry)/$(imageRepository)'
  
  #Azure service connection name(Project settings -> Service connection -> Azure Resource Manager)
  Azure.ServiceConnection: 'itf-dev-cicd-sp'

  #Azure resource group name (output resource_group_name from terraform)
  Azure.resourceGroup: 'itf-dev-k8s-rg'

  #Azure kubernetes cluster name (output from terraform)
  Azure.kubernetesClusterName: 'itf-dev-k8s-aks'

  #Azure Container Registry name (output from terraform)
  ACR.Name: 'itfdevacr'

  #Chart version 
  chartVersion: "1.0.1"

  #Enable Azure devops debug mode
  System.Debug: 'false'

stages:
- stage: Build_Stage
  displayName: Build image
  jobs:  
  - job: Build_Job
    displayName: Build and push Docker/Helm image
    steps: 
    - template: templates/build.yml

- stage: Deploy_Italy
  dependsOn: Build_Stage
  displayName: Deploy apollo Italy (k8s)
  jobs:  
  - deployment: Deploy_Italy
    displayName: Deploy Italy app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-italy.yaml"
      K8S.Namespace: 'iti-apollo'
    environment: dev-italy-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_France
  dependsOn: Build_Stage
  displayName: Deploy apollo France (k8s)
  jobs: 
  - deployment: Deploy_France
    displayName: Deploy France app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-france.yaml"
      K8S.Namespace: 'apollo'
    environment: dev-france-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Spain
  dependsOn: Build_Stage
  displayName: Deploy apollo Spain (k8s)
  jobs:  
  - deployment: Deploy_Spain
    displayName: Deploy Spain app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-spain.yaml"
      K8S.Namespace: 'ite-apollo'
    environment: dev-spain-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Portugal
  dependsOn: Build_Stage
  displayName: Deploy apollo Portugal (k8s)
  jobs:  
  - deployment: Deploy_Portugal
    displayName: Deploy Portugal app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-dev-portugal.yaml"
      K8S.Namespace: 'itp-apollo'
    environment: dev-portugal-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Sweden
  dependsOn: Build_Stage
  displayName: Deploy apollo Sweden (k8s)
  jobs:
    - deployment: Deploy_Sweden
      displayName: Deploy Sweden app
      variables:
        commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
        chartValuesFile: "./cicd/chart-values/values-dev-sweden.yaml"
        K8S.Namespace: 'its-apollo'
      environment: dev-sweden-$(projectName)
      strategy:
        runOnce:
          deploy:
            steps:
              - checkout: self
              - template: templates/deploy.yml

- stage: Deploy_Denmark
  dependsOn: Build_Stage
  displayName: Deploy apollo Denmark (k8s)
  jobs:
    - deployment: Deploy_Denmark
      displayName: Deploy Denmark app
      variables:
        commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
        chartValuesFile: "./cicd/chart-values/values-dev-denmark.yaml"
        K8S.Namespace: 'itd-apollo'
      environment: dev-denmark-$(projectName)
      strategy:
        runOnce:
          deploy:
            steps:
              - checkout: self
              - template: templates/deploy.yml
