steps:
  - bash: |
      # Define variables : 
      #   - docker image tag : 8 first characters of git commit id
      #   - helmChartVersion : build.buildId (int)
      # -----------   
      
      commitID=$(echo $(imageTag) | cut -c1-8)
      helmChartVersion=$(echo $(helmChartVersion))
      
      echo "helmChartVersion: $helmChartVersion"

      if [ ! -z $commitID ] && [ ! -z $helmChartVersion ]
      then
        #Import variable in current pipeline  
        echo "##vso[task.setvariable variable=commitID;isOutput=true]$commitID" 
      else
        echo "Err: can't find commitID or helmChartVersion "
        echo "helmChartVersion  = $helmChartVersion"
        echo "commitID          = $commitID"
        exit -1
      fi
    displayName: 'Docker: Find commit id'
    name: versioningTask

  - bash: |
      docker build -t $(containerFullPath):$(versioningTask.commitID) -t $(containerFullPath):latest -f cicd/dockerfile.build .
      docker login -u $(containerRegistryLogin) -p '$(containerRegistryPwd)' $(containerRegistry)
      echo "Pushing $(containerFullPath):$(versioningTask.commitID) "
      docker push $(containerFullPath):$(versioningTask.commitID)
      docker push $(containerFullPath):latest
    displayName: 'Docker: Build and Push image'

  - task: AzureCLI@2
    displayName: "Helm: push chart"
    inputs:
      azureSubscription: "$(Azure.ServiceConnection)"
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        ACCESS_TOKEN=$(az acr login --name $(ACR.Name) --expose-token --output tsv --query accessToken)
        echo $ACCESS_TOKEN | helm registry login $(ACR.Name).azurecr.io -u 00000000-0000-0000-0000-000000000000 --password-stdin
        helm package ./cicd/chart
        helm push $(helmChartName)-$(helmChartVersion).tgz oci://$(ACR.Name).azurecr.io/