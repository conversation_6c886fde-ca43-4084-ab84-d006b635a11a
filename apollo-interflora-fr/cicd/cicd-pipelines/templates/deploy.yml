steps:
  - task: AzureCLI@2
    displayName: 'Helm: Deploy'
    inputs:
      azureSubscription: '$(Azure.ServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        ACCESS_TOKEN=$(az acr login --name $(ACR.Name) --expose-token --output tsv --query accessToken)
        echo $ACCESS_TOKEN | helm registry login $(ACR.Name).azurecr.io -u 00000000-0000-0000-0000-000000000000 --password-stdin

        az aks install-cli --kubelogin-version v0.1.7
        az aks get-credentials --resource-group $(Azure.resourceGroup) --name $(Azure.kubernetesClusterName)

        helm pull oci://$(ACR.Name).azurecr.io/$(helmChartName)
        helm upgrade \
            --namespace $(K8S.Namespace) \
            --install \
            --values $(chartValuesFile) \
            --set image.tag=$(commitID),image.repository=$(containerFullPath) \
            --wait \
            --create-namespace \
            --version $(chartVersion) \
            --wait \
            --atomic $(helmChartName) $(helmChartName)-$(helmChartVersion).tgz
