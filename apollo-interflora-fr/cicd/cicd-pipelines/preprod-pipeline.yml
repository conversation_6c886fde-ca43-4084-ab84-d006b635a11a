trigger:
- preprod

pool:
  vmImage: ubuntu-latest
  
variables:
  #####################################
  ## common-variables
  #####################################

  projectName: 'apollo'

  helmVersion: 3.2.3
  HELM_EXPERIMENTAL_OCI: '1'

  #Must match chart/Chart.yaml name
  helmChartName: 'apollo'

  #Update chart/Chart.yaml according to this value 
  helmChartVersion: "1.0.1"

  #Helm Release name 
  helmReleaseName: 'apollo'

  #Use Azure devOps build id to tag image --> must be remplaced by commitID 
  imageTag: $(build.SourceVersion)

  #Registry name to store docker image and helm chart (create if not exist, must be lowercase)
  imageRepository: 'apollo-interflora'

  #####################################
  ##preprod-variables
  #####################################
  
  containerRegistry: 'itfpreprodacr.azurecr.io'
  containerRegistryLogin: 'itfpreprodacr'
  #containerRegistryPwd: 'must be defined in azure pipeline variables'

  #itfpreprodacr.azurecr.io/apollo-interflora-fr
  containerFullPath: '$(containerRegistry)/$(imageRepository)'
  
  #Azure service connection name(Project settings -> Service connection -> Azure Resource Manager)
  Azure.ServiceConnection: 'itf-preprod-cicd-sp'

  #Azure resource group name (output resource_group_name from terraform)
  Azure.resourceGroup: 'itf-preprod-k8s-rg'

  #Azure kubernetes cluster name (output from terraform)
  Azure.kubernetesClusterName: 'itf-preprod-k8s-aks'

  #Azure Container Registry name (output from terraform)
  ACR.Name: 'itfpreprodacr'

  #Chart version 
  chartVersion: "1.0.1"

  #Enable Azure devops debug mode
  System.Debug: 'false'

stages:
- stage: Build_Stage
  displayName: Build image
  jobs:  
  - job: Build_Job
    displayName: Build and push Docker/Helm image
    steps: 
    - template: templates/build.yml

- stage: Deploy_Italy
  dependsOn: Build_Stage
  displayName: Deploy apollo Italy (k8s)
  jobs:  
  - deployment: Deploy_Italy
    displayName: Deploy Italy app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-preprod-italy.yaml"
      K8S.Namespace: 'iti-apollo'
    environment: preprod-italy-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_France
  dependsOn: Build_Stage
  displayName: Deploy apollo France (k8s)
  jobs: 
  - deployment: Deploy_France
    displayName: Deploy France app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-preprod-france.yaml"
      K8S.Namespace: 'apollo'
    environment: preprod-france-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_France2
  dependsOn: Build_Stage
  displayName: Deploy apollo France 2 (to new backend) (k8s)
  jobs: 
  - deployment: Deploy_France2
    displayName: Deploy France 2 app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-preprod-france2.yaml"
      K8S.Namespace: 'itf-apollo2'
    environment: preprod-france-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Spain
  dependsOn: Build_Stage
  displayName: Deploy apollo Spain (k8s)
  jobs:  
  - deployment: Deploy_Spain
    displayName: Deploy Spain app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-preprod-spain.yaml"
      K8S.Namespace: 'ite-apollo'
    environment: preprod-spain-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Portugal
  dependsOn: Build_Stage
  displayName: Deploy apollo Portugal (k8s)
  jobs:  
  - deployment: Deploy_Portugal
    displayName: Deploy Portugal app
    variables: 
      commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
      chartValuesFile: "./cicd/chart-values/values-preprod-portugal.yaml"
      K8S.Namespace: 'itp-apollo'
    environment: preprod-portugal-$(projectName)
    strategy:
        runOnce:
          deploy:
            steps: 
            - checkout: self
            - template: templates/deploy.yml

- stage: Deploy_Sweden
  dependsOn: Build_Stage
  displayName: Deploy apollo Sweden (k8s)
  jobs:
    - deployment: Deploy_Sweden
      displayName: Deploy Sweden app
      variables:
        commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
        chartValuesFile: "./cicd/chart-values/values-preprod-sweden.yaml"
        K8S.Namespace: 'its-apollo'
      environment: preprod-sweden-$(projectName)
      strategy:
        runOnce:
          deploy:
            steps:
              - checkout: self
              - template: templates/deploy.yml

- stage: Deploy_Denmark
  dependsOn: Build_Stage
  displayName: Deploy apollo Denmark (k8s)
  jobs:
    - deployment: Deploy_Denmark
      displayName: Deploy Denmark app
      variables:
        commitID: $[ stageDependencies.Build_Stage.Build_Job.outputs['versioningTask.commitID'] ]
        chartValuesFile: "./cicd/chart-values/values-preprod-denmark.yaml"
        K8S.Namespace: 'itd-apollo'
      environment: preprod-denmark-$(projectName)
      strategy:
        runOnce:
          deploy:
            steps:
              - checkout: self
              - template: templates/deploy.yml