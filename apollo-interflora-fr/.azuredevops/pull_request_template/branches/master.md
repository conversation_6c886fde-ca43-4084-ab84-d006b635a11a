This a release PR please check the informations below and execute (of have executed) the tasks below before deploying.

## Manual tasks to do before releasing

- ADD YOURS: Description of the task
    - Related ticket:
    - Related dev:
    - To do:
        - ???

- ⚠️This is an example⚠️ Update the DatoCMS block for ITF+
    - Related ticket: [GGT-2059](https://myflower.atlassian.net/browse/GGT-2059)
    - Related dev: @emmanuel.copanel
    - To do:
        - FOR EACH PROD DATOCMS ENV
            - Read this ✅
            - Add a new Single-line String field nammed `Title` in the Interflora plus block and save it ❌
            - Add a new Single-line String field nammed `Legals` in the Interflora plus block and save it ❌
            - Change the `Description` field of the Interflora plus block:
                - In `Validations`:
                    - untoggle `Required` ❌
                    - toggle `Prevent the use of dangerous HTML attributes` ❌
                    - toggle `Remove potentially dangerous attributes before saving the record.` ❌
                - In `Presentation`:
                    - toggle `Mardown editor` ❌
                    - untoggle `Heading`, `Code`, `Add link`, `Add image`, `Fullscreen` ❌
                - Save the changes ❌