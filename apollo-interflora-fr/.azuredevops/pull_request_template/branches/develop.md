Thank you for commiting a new ticket, please fill the description below to help the reviwers.

## What type of PR is this? (check all applicable)

- [ ] Feature
- [ ] Bug Fix
- [ ] Refactor
- [ ] Optimization
- [ ] Docs/Nice to have

## Description
Complete this part

## Related Tickets & Documents

- Related Jira issue:
- Related documentation:

## Included changes (check and do all applicable)
- [ ] Adding/Using a new Commercetools field and/or value - I updated the [custom fields documentation](https://myflower.atlassian.net/wiki/spaces/ITFFR/pages/1638924289/CommerceTools+Custom+fields)
- [ ] Adding a new external dependency/system/api calls - I created a documentation in the [confluence services folder](https://myflower.atlassian.net/wiki/spaces/ITFFR/pages/1824129043/Services)
- [ ] Adding a new feature - I created a documentation in the [confluence features folder](https://myflower.atlassian.net/wiki/spaces/ITFFR/pages/1823899692/Features) **and added tests.**
- [ ] Optimization - I explained what I optimized in the description above.

## Manual tasks to execute with this task (add yours + check when done)
- [ ] None

## Guidelines checklist
- [ ] <PERSON><PERSON> passed
- [ ] Tests passed
- [ ] My code is commented, readable and could be easily understood/maintained by anyone other dev
- [ ] I followed [the developer guidelines (security/perf/legal-gdpr/middle)](https://myflower.atlassian.net/wiki/spaces/TECH/pages/2328985651/Developer+guidelines)
- [ ] I followed [the middle-specific guidelines](https://myflower.atlassian.net/wiki/spaces/TECH/pages/2351267842/Octopus+Middleware+guidelines+WIP)